const mqttClient = require("../connection/mqttClient");
const logger = require("../services/logger");

/**
 * @description Publishes a message to the specified MQTT topic.
 * @param {Object} req - The request object.
 * @param {Object} res - The response object.
 */
const publishEvent = (req, res) => {
  const { topic, msg } = req.body;
  let packet = "";
  logger.info("/v1/event/publish", topic);

  if (!topic || !msg) {
    logger.critical(`[MQTT_Publish_Error] Topic or message missing, topic=${topic} and message=${msg}`);
    return res.status(400).json({ err: "Topic or message missing" });
  }

  packet = typeof msg !== "string" ? JSON.stringify(msg) : msg;
  try {
    mqttClient.publish(topic, packet);
    return res.status(200).json({
      status: true,
      topic,
      msg,
    });
  } catch (error) {
    logger.critical(error);
    logger.error(`[MQTT_Publish_Error] Error while publish, topic=${topic} and message=${msg}`);
    return res.status(500).json({ err: "[Publish Event] Internal Server Error" });
  }
};

module.exports = {
  publishEvent,
};
