// const moment = require("moment-timezone");
const AWS = require("aws-sdk");
AWS.config.update({ region: process.env?.REGION, logger: console });
module.exports = {

  "getData": function(tableName, { primaryName, secondaryName }, { primaryVal, secondaryVal }, filterCallback) {

    const docClient = new AWS.DynamoDB.DocumentClient();
    let type = 0; // tells if data have secondary key or not

    let changeableName = primaryName, changeableVal = "";
    let params = {
      "TableName": tableName,
    };
    if (secondaryName) {
      if (secondaryVal.length == 1) {
        params["KeyConditionExpression"] = "#p = :p and #s gt :s1";
        params["ExpressionAttributeNames"] = {
          "#p": primaryName,
          "#s": secondaryName,
        };
        params["ExpressionAtrributeValues"] = {
          ":p": primaryVal,
          ":s1": secondaryVal[0],
        };
      } else if (secondaryVal.length == 2) {
        params["ExpressionAttributeValues"] = {
          ":p": primaryVal,
          ":s1": secondaryVal[0],
          ":s2": secondaryVal[1],
        };
        params["KeyConditionExpression"] = "#p=:p and #s between :s1 and :s2";
        params["ExpressionAttributeNames"] = {
          "#p": primaryName,
          "#s": secondaryName,
        };


      } else {
        throw new Error("Illegal supply of arguments");
      }
      changeableVal = ":s1";
      changeableName = secondaryName;
      type = 2;
    } else {
      params["ExpressionAtrributeValues"] = { ":p": primaryVal };
      params["KeyConditionExpression"] = "#p = :p";
      params["ExpressionAttributeValues"] = {
        "#p": primaryName,
      };

      changeableVal = ":p";
      type = 1;
    }

    //  now we have param done
    function getDataParts() {
      return new Promise(function(resolve, reject) {
        docClient.query(params, (err, data) => {
          if (err) {
            sails.log.error(err);
            reject("err in getdata parts");
          } else {
            if (data.LastEvaluatedKey != undefined) {
              params.ExpressionAttributeValues[changeableVal] = data.LastEvaluatedKey[changeableName];
              resolve({ "data": data, "done": false });
            } else {
              resolve({ "data": data, "done": true });
            }
          }
        });
      });
    }

    function updateSecondary(docClient, data, tableName, pname, sname) {
      let allMapper = 97;

      function getNext() {
        let old = allMapper;
        allMapper += 1;
        return String.fromCharCode(old);
      }

      let now_use = "";
      data.forEach((d) => {
        let updObject = {
          "TableName": tableName,
          "Key": {},
          "UpdateExpression": "",
          "ExpressionAttributeValues": {},
          "ExpressionAttributeNames": {},

        };
        allMapper = 97;
        (updObject.Key)[pname] = d[pname];
        (updObject.Key)[sname] = d[sname];
        delete (d[pname]);
        delete (d[sname]);
        let temp = "SET ";

        Object.keys(d).map((d1, i1) => {

          let temp_data = d[d1];
          now_use = getNext();
          if (typeof (temp_data) == "object") {
            temp_data = String(JSON.stringify(temp_data));
          } else {
            temp_data = String(temp_data);
          }
          if (i1 == 0)
            temp += ("#" + now_use + " = :" + now_use);
          else
            temp += (" ,#" + now_use + " = :" + now_use);
          updObject.ExpressionAttributeValues[":" + now_use] = temp_data;
          updObject.ExpressionAttributeNames["#" + now_use] = d1;

        });
        updObject.UpdateExpression = temp;
        docClient.update(updObject, function(err, data) {
          if (err) {
            sails.log.error(err);
          } else {
            return;
          }
        });
      });

    }

    function updatePrimary(docClient, data, tableName, name) {
      let allMapper = 97;
      let now_use = "";

      function getNext() {
        let old = allMapper;
        allMapper += 1;
        return String.fromCharCode(old);
      }

      data.forEach((d) => {
        let updObject = {
          "TableName": tableName,
          "Key": {},
          "UpdateExpression": "",
          "ExpressionAttributeValues": {},
          "ExpressionAttributeNames": {},

        };
        allMapper = 97;
        (updObject.Key)[name] = d[name];
        delete (d[name]);
        let temp = "SET ";

        Object.keys(d).map((d1, i1) => {

          let temp_data = d[d1];
          now_use = getNext();
          if (typeof (temp_data) == "object") {
            temp_data = String(JSON.stringify(temp_data));
          } else {
            temp_data = String(temp_data);
          }
          if (i1 == 0)
            temp += ("#" + now_use + " = :" + now_use);
          else
            temp += (" ,#" + now_use + " = :" + now_use);
          updObject.ExpressionAttributeValues[":" + now_use] = temp_data;
          updObject.ExpressionAttributeNames["#" + now_use] = d1;

        });
        updObject.UpdateExpression = temp;
        docClient.update(updObject, function(err, data) {
          if (err) {
            sails.log.error(err);
          } else {
            return;
          }
        });
      });

    }

    async function process() {
      try {
        let res = { "done": false };
        while (!res.done) {
          try {
            res = await getDataParts();
          } catch (e) {
            sails.log.error(e);
            return;
          }

          let main_data = res.data.Items;
          filterCallback(main_data, function(changedData) {
            if (type == 1) {
              // only have prmary key
              updatePrimary(docClient, main_data, tableName, primaryName);
            } else if (type == 2) {
              // have secondary key
              updateSecondary(docClient, main_data, tableName, primaryName, secondaryName);
            }
          });

        }
      } catch (e) {
        sails.log.error(e);
      }


    }

    process();


  },
};
