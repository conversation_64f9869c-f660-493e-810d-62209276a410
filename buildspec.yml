
version: 0.2
phases:
  build:
    commands:
      - timestamp=$(date +%sdejoule-v3)
      - sed -i -- "s/version/$timestamp/g" Dockerrun.aws.json
      - echo $CODEBUILD_RESOLVED_SOURCE_VERSION
      - docker build . -t sj-api:latest
      - docker tag sj-api:latest 878252606197.dkr.ecr.us-west-2.amazonaws.com/dejoule:$timestamp
      - docker push 878252606197.dkr.ecr.us-west-2.amazonaws.com/dejoule:$timestamp
  pre_build:
    commands:
      - $(aws ecr get-login --no-include-email --region us-west-2)
artifacts:
  files:
    - Dockerrun.aws.json
    