const mqtt = require("mqtt");
const fs = require("fs");
const os = require("os");
const path = require("path");
const uuid = require("uuid");
const config = require("../config/config");
const logger = require("../services/logger");

class MQTTClient {
  constructor() {
    if (MQTTClient.instance) return MQTTClient.instance;
    this.hostname = os.hostname();
    this.mqttConnectionConfig = {
      reconnectPeriod: 5000,
      port: config.BROKER_PORT,
      host: config.BROKER_HOST,
      protocol: "mqtts",
      key: fs.readFileSync(path.join(__dirname, "../", config.BROKER_KEY)),
      cert: fs.readFileSync(path.join(__dirname, "../", config.BROKER_CERT)),
      ca: fs.readFileSync(path.join(__dirname, "../", config.BROKER_TRUSTED_CA)),
      keepalive: 10,
      clientId: `mqttjs_${this.hostname}-${uuid.v4()}`,
      clean: false,
    };
    this.mqttClient = MQTTClient.instance = mqtt.connect(this.mqttConnectionConfig);
    if (!this.mqttClient.listeners("on").length) {
      this.mqttClient.on("connect", () => {
        logger.info("MQTT client connected");
      });
    }

    if (!this.mqttClient.listeners("reconnect").length) {
      this.mqttClient.on("reconnect", () => {
        logger.critical("MQTT client Reconnecting...");
      });
    }

    if (!this.mqttClient.listeners("disconnect").length) {
      this.mqttClient.on("disconnect", () => {
        logger.info("Server disconnected from MQTT broker.");
      });
    }

    if (!this.mqttClient.listeners("error").length) {
      this.mqttClient.on("error", (err) => {
        logger.error("MQTT error: ", err);
      });
    }
  }

  getConnection() {
    if (!MQTTClient.instance) throw new Error("MQTT instance is not ready yet");
    return MQTTClient.instance;
  }

  publish(topic, data) {
    if (!MQTTClient.instance) throw new Error("MQTT instance is not ready yet");
    if (typeof topic !== "string" || typeof data !== "string") {
      logger.critical(`[MQTT_Publish_Error] MQTT topic/data must be a string, received topic =${topic} data=${JSON.stringify(data)}`);
      return;
    }
    MQTTClient.instance.publish(topic, data);
  }
}

const mqttClientInstance = new MQTTClient();
Object.freeze(mqttClientInstance);
module.exports = mqttClientInstance;
