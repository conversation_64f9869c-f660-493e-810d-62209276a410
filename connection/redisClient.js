const redis = require("redis");
const os = require("os");
const { promisify } = require("util");
const logger = require("../services/logger");
const config = require("../config/config");

class RedisClient {
  constructor() {
    if (RedisClient.instance) return RedisClient.instance;

    this.hostname = os.hostname();
    this.logger = logger;
    this.cachePort = config.CACHE_PORT;
    this.cacheHost = config.CACHE_HOST;
    this.redisCommandDefaultValues = {
      set: "OK",
      expire: 0,
      del: 0,
      get: null,
      sadd: 0,
      lpush: 0,
      ltrim: "OK",
      type: "none",
      lrange: [],
      smembers: [],
      srem: 0,
      mset: "OK",
      hmset: "OK",
      hgetall: {},
      hset: 0,
      exists: 0,
      sismember: 0,
    };
    this.createRedisClientConnection();
  }

  createRedisClientConnection() {
    this.client = redis.createClient({
      host: this.cacheHost,
      port: this.cachePort,
      retry_strategy: (options) => {
        const delay = 1000 * 30;
        this.logger.error(`[Redis] Reconnecting to Redis, attempt ${options.attempt}, delay=${delay}ms`);
        this.logger.error(`[Redis] Reconnecting to Redis,message="${options?.error?.message}" and errorCode="${options?.error?.code}"`);
        return delay;
      },
    });

    this.client.on("error", (err) => {
      this.logger.error("[Redis] Redis error:");
      this.logger.error(err);
    });

    this.client.on("connect", () => {
      this.client.client("SETNAME", this.hostname);
      RedisClient.instance = this.client;
    });

    this.client.on("ready", async () => {
      this.logger.info("[Redis] Redis connection established successfully");
    });

    this.client.on("warning", (warning) => {
      this.logger.warn(`[Redis] - Warning: ${warning}`);
    });

    this.client.on("end", () => {
      this.logger.error("[Redis] Application Ended");
    });
  }

  async safeExecute(command, ...args) {
    if (this.client.ready) {
      return promisify(command).bind(this.client)(...args);
    } else {
      this.logger.error(`[Redis] Client is not connected. Returning default value for command: ${command?.name}`);
      return -111;
    }
  }

  async set(...args) {
    return await this.safeExecute(this.client.set, ...args);
  }

  async expire(...args) {
    return await this.safeExecute(this.client.expire, ...args);
  }

  async del(...args) {
    return await this.safeExecute(this.client.del, ...args);
  }

  quit() {
    return this.client.quit();
  }

  async get(...args) {
    return await this.safeExecute(this.client.get, ...args);
  }

  async sadd(...args) {
    return await this.safeExecute(this.client.sadd, ...args);
  }

  async lpush(...args) {
    return await this.safeExecute(this.client.lpush, ...args);
  }

  async ltrim(...args) {
    return await this.safeExecute(this.client.ltrim, ...args);
  }

  async type(...args) {
    return await this.safeExecute(this.client.type, ...args);
  }

  async lrange(...args) {
    return await this.safeExecute(this.client.lrange, ...args);
  }

  async smembers(...args) {
    return await this.safeExecute(this.client.smembers, ...args);
  }

  async srem(...args) {
    return await this.safeExecute(this.client.srem, ...args);
  }

  async mset(...args) {
    return await this.safeExecute(this.client.mset, ...args);
  }

  async hmset(...args) {
    return await this.safeExecute(this.client.hmset, ...args);
  }

  async hgetall(...args) {
    return await this.safeExecute(this.client.hgetall, ...args);
  }

  async hset(...args) {
    return await this.safeExecute(this.client.hset, ...args);
  }

  async exists(...args) {
    return await this.safeExecute(this.client.exists, ...args);
  }

  async sismember(...args) {
    return await this.safeExecute(this.client.sismember, ...args);
  }

  getClient() {
    if (!RedisClient.instance || !RedisClient.instance.ready) {
      throw new Error("Redis client is not ready yet");
    }
    return this;
  }
}

const redisClientInstance = new RedisClient();
Object.freeze(redisClientInstance);
module.exports = redisClientInstance;
