{"iftttlog": {"Recipe_Log": {"route": ["/test/ifttt/getlogs/:sid"], "action": "getLogs", "access": 1, "description": "Joule Recipe Logs", "req_type": ["GET"]}, "_description": {"displayname": "Joule Recipies", "room": "recipies"}}, "diagnostics": {"Configuration_Edit": {}, "Remote SSH_Read": {}, "CICD Update_Read": {}, "CICD Update_Write": {}, "Request(IPs NS)_Write": {}, "Calculate DQI_Read": {}, "Recalab device_Write": {}, "Maintainance Mode_Write": {}, "_description": {"displayname": "Diagnostics", "room": "diagnostics"}}, "config": {"Config_Read": {}, "Config_Create": {}, "Config_Edit": {}, "Config_Burn": {}, "Config_Delete": {}, "Site Info_Read": {}, "Site Info_Create": {}, "Site Info_Edit": {}, "Site Info_Burn": {}, "Site Info_Delete": {}, "_description": {"displayname": "Cofiguration", "room": "config"}}, "sites": {"Site_Read": {"action": ["getsitedetails"], "description": "Show site details", "req_type": ["GET"]}, "_description": {"displayname": "Site Info", "room": "public"}}, "users": {"UserRole_Create": {"action": ["createRole"], "description": "Can this user create user role", "req_type": ["POST"]}, "UserRole_Update": {"action": ["updateRole"], "description": "Can this user update user role", "req_type": ["POST"]}, "UserRole_Delete": {"action": ["deleteRole"], "description": "Can this user delete a user role", "req_type": ["POST"]}, "User_Create": {"action": ["create"], "description": "Can this user create new user", "req_type": ["POST"]}, "User_Read": {"action": ["getusers"], "description": "Can this user view other users", "req_type": ["GET"]}, "_description": {"displayname": "User", "room": "public"}}, "recipe": {"Recipe_Create": {"action": ["saveRec<PERSON>ie"], "description": "Joule Recipe", "req_type": ["POST"]}, "Recipe_Read": {"action": ["getRecipeOf"], "description": "Joule Recipe", "req_type": ["GET"]}, "Recipe_Update": {}, "Schedule_Stage": {}, "Schedule_Update": {}, "Schedule_Pause": {}, "Schedule_Resume": {}, "Schedule_Deploy": {}, "Recipe_Delete": {"route": ["removeRecipe"], "access": 2, "description": "Joule Recipe"}, "Schedule_Read": {"route": ["showSchedule"], "access": 1, "description": "Schedule"}, "Schedule_Create": {"route": ["/test/ifttt/schedule/"], "access": 2, "description": "Schedule"}, "Schedule_Delete": {"route": ["removeSchedule"], "access": 2, "description": "Schedule"}, "_description": {"displayname": "Joule Recipies", "room": "recipies"}}, "command": {"Command_Write": {"action": ["registerCommand"], "description": "Give Command", "req_type": ["POST"]}, "Command_Read": {}, "_description": {"displayname": "AC Plant", "room": "recipies"}}, "components": {"Component_Read": {}, "Component_Create": {}, "Component_Edit": {}, "Component_Delete": {}, "_description": {"displayname": "Cofiguration", "room": "public"}}, "device": {"MODE_Set": {"route": ["/devices/mode/:siteId/:id"], "access": 2, "description": "Set Mode of a device"}, "_description": {"displayname": "Devices", "room": "public"}}}