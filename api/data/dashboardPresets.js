/**
 * For custom dashboards,
 * queryFns will be custom
 * which will call a function in datadeviceservice
 * to query the parameters
 * and depending on the graph type it will
 * return results
 */
let dashboardPresetConfig = {
	"elecLiveInfo": {
		"dataType": "text",
		"name": "elecLiveInfo",
		"icon": true,
		"title": "Live Electrical Load",
		"queryFns": ["recentData"],
		"data": "NA",
	},
	"steamLiveInfo": {
		"dataType": "text",
		"icon": true,
		"name": "steamLiveInfo",
		"title": "Live Steam Load",
		"queryFns": ["recentData"],
		"data": "NA",
	},
	"elecMonthInfo": {
		"dataType": "text",
		"name": "elecMonthInfo",
		"icon": true,
		"title": "Monthly Electricity Expenditure",
		"queryFns": ["recentData", "monthStartData", "billRate"],
		"data": "NA",
	},
	"weekElecChart": {
		"dataType": "chart",
		"name": "weekElec<PERSON>hart",
		"title": "Last 7 Days Electricity Consumption",
		"meta": {
			"axisType": "datetime",
			"unit": "conselec",
		},
		"data": [],
		"queryFns": ["dayWiseConsumption"],
	},
	"powerCurrentVsLast": {
		"dataType": "text",
		"name": "powerCurrentVsLast",
		"title": "Power Consumption Current vs Last Month",
		"data": "NA",
		"queryFns": [
			"recentData",
			"monthStartData",
			"sameDayLastMonthData",
			"lastMonthStartData",
		],
	},
	"totalMonthsElectricityConsumption": {
		"name": "totalMonthsElectricityConsumption",
		"dataType": "text",
		"title": "This Month's Electricity consumption",
		"data": "NA",
		"queryFns": [
			"monthStartData",
			"recentData"
		],
	},
	"totalMonthsSteamConsumption": {
		"name": "totalMonthsSteamConsumption",
		"dataType": "text",
		"title": "This Month's Steam consumption",
		"data": "NA",
		"queryFns": [
			"aggreagateBetween2TSMonthSteam"
		],
	},
	"steamMonthInfo": {
		"name": "steamMonthInfo",
		"dataType": "text",
		"icon": true,
		"title": "Monthly Steam Expenditure",
		"data": "NA",
		"queryFns": ["aggreagateBetween2TSMonthSteam", "billRate"],
	},
	"weekSteamChart": {
		"dataType": "chart",
		"name": "weekSteamChart",
		"title": "Last 7 Days Steam Consumption",
		"meta": {
			"axisType": "datetime",
			"unit": "kg",
		},
		"data": [],
		"queryFns": ["dayWiseConsumptionSteam"],
	},
	"steamCurrentVsLast": {
		"dataType": "text",
		"name": "steamCurrentVsLast",
		"title": "Steam Consumption Current vs Last Month",
		"queryFns": [
			"recentData",
			"monthStartData",
			"sameDayLastMonthData",
			"lastMonthStartData",
			"aggreagateBetween2TSLastMonthSteam"
		],
		"data": "NA",
	},
};

const paperMills = [
	"elecLiveInfo",
	"steamLiveInfo",
	"elecMonthInfo",
	"weekElecChart",
	"powerCurrentVsLast",
	"steamMonthInfo",
	"weekSteamChart",
	"steamCurrentVsLast",
	"totalMonthsElectricityConsumption",
	"totalMonthsSteamConsumption"
];
module.exports = {
	dashboardPresetConfig,
	paperMills,
};
