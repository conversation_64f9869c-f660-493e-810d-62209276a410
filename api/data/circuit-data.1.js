let jiocontrol = [{
	"type": "jiocontrol",
	"name": "Voltage Input",
	"abbr": "VI",
	"communicationType": "NMB",
	"id": 0,
	"index": 0,
	"devices": [{
		"name": "2 Way Valve",
		"deviceType": "actuator",
		"driverTypes": [{ "name": "Belimo", "value": 1 }, { "name": "Honeywell", "value": 0 }, { "name": "Danfoss", "value": 0 }],
		"communicationCategory": "AVI"
	},
	{
		"name": "Air Damper",
		"deviceType": "actuatorDamper",
		"communicationCategory": "AVI",
		"driverTypes": [{ "name": "Default", "value": 0 }]
	},
	]
},
{
	"type": "jiocontrol",
	"name": "Voltage Input",
	"abbr": "VI",
	"communicationType": "NMB",
	"id": 1,
	"index": 1,
	"devices": [{
		"name": "2 Way Valve",
		"deviceType": "actuator",
		"communicationCategory": "AVI",
		"driverTypes": [{ "name": "<PERSON><PERSON>", "value": 1 }, { "name": "<PERSON><PERSON>", "value": 0 }, { "name": "Danfoss", "value": 0 }],
	}, {
		"name": "Air Damper",
		"deviceType": "actuatorDamper",
		"communicationCategory": "AVI",
		"driverTypes": [{ "name": "Default", "value": 0 }],
	}]
},
{
	"type": "jiocontrol",
	"name": "Current Input",
	"abbr": "AI",
	"communicationType": "NMB",
	"id": 2,
	"index": 0,
	"devices": [{
		"name": "2 Way Valve",
		"deviceType": "actuator",
		"driverTypes": [{ "name": "Belimo", "value": 1 }, { "name": "Honeywell", "value": 0 }, { "name": "Danfoss", "value": 0 }],
		"communicationCategory": "ACI"
	}, {
		"name": "Air Damper",
		"deviceType": "actuatorDamper",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "ACI"
	}]
},
{
	"type": "jiocontrol",
	"name": "Current Input",
	"abbr": "AI",
	"communicationType": "NMB",
	"id": 3,
	"index": 1,
	"devices": [{
		"name": "2 Way Valve",
		"deviceType": "actuator",
		"driverTypes": [{ "name": "Belimo", "value": 1 }, { "name": "Honeywell", "value": 0 }, { "name": "Danfoss", "value": 0 }],
		"communicationCategory": "ACI"
	}, {
		"name": "Air Damper",
		"deviceType": "actuatorDamper",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "ACI"
	}]
},
{
	"type": null,
	"name": null,
	"abbr": null,
	"communicationType": null,
	"id": null,
	"index": null
},
{
	"type": "jiocontrol",
	"name": "Voltage/Current Output",
	"communicationType": "NMB",
	"abbr": "V/A O",
	"id": 4,
	"index": 1,
	"deps": [1],
	"devices": [{
		"name": "2 Way Valve-Current",
		"deviceType": "actuator",
		"driverTypes": [{ "name": "Belimo", "value": 1 }, { "name": "Honeywell", "value": 0 }, { "name": "Danfoss", "value": 0 }],
		"communicationCategory": "AVO"
	},
	{
		"name": "2 Way Valve-Voltage",
		"deviceType": "actuator",
		"driverTypes": [{ "name": "Belimo", "value": 1 }, { "name": "Honeywell", "value": 0 }, { "name": "Danfoss", "value": 0 }],
		"communicationCategory": "AVO"
	}, {
		"name": "Air Damper-Current",
		"deviceType": "actuator",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "AVO"
	}, {
		"name": "Air Damper-Voltage",
		"deviceType": "actuatorDamper",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "AVO"
	}
	]
},
{
	"type": "jiocontrol",
	"name": "Voltage/Current Output",
	"abbr": "V/A O",
	"communicationType": "NMB",
	"devices": [{
		"name": "2 Way Valve-Current",
		"deviceType": "actuator",
		"driverTypes": [{ "name": "Belimo", "value": 1 }, { "name": "Honeywell", "value": 0 }, { "name": "Danfoss", "value": 0 }],
		"communicationCategory": "AVO"
	},
	{
		"name": "2 Way Valve-Voltage",
		"deviceType": "actuator",
		"driverTypes": [{ "name": "Belimo", "value": 1 }, { "name": "Honeywell", "value": 0 }, { "name": "Danfoss", "value": 0 }],
		"communicationCategory": "AVO"
	}, {
		"name": "Air Damper-Current",
		"deviceType": "actuator",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "AVO"
	}, {
		"name": "Air Damper-Voltage",
		"deviceType": "actuatorDamper",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "AVO"
	}
	],
	"id": 5,
	"deps": [0],
	"index": 0,
	"divider": true
},
{
	"type": "jiocontrol",
	"name": "Modbus",
	"abbr": "Modbus",
	"id": 6,
	"index": 0,
	// communicationCategory: 'DF',
	"communicationType": "MB",
	"devices": [{
		"name": "VFD",
		"deviceType": "vfd",
		"communicationCategory": "VFD",
		"driverTypes": [{ "name": "Danfoss", "value": 0 }, { "name": "Fuji", "value": 1 }],
	},
	{
		"name": "Chiller",
		"deviceType": "chiller",
		"communicationCategory": "CHL",
		"driverTypes": [{ "name": "Carrier", "value": 0 }, { "name": "Trane", "value": 1 }],

	},
	{
		"name": "Energy Meter",
		"deviceType": "em",
		"communicationCategory": "EM",
		"driverTypes": [{ "name": "NF29", "value": 0 }, { "name": "EM 8400", "value": 1 },
			{ "name": "EM 6400", "value": 2 }, { "name": "EM 6436", "value": 2 }
		],
	},
	{
		"name": "Flow Meter",
		"deviceType": "fm",
		"communicationCategory": "FM",
		"driverTypes": [{ "name": "Default", "value": 0 }],
	},
	{
		"name": "Fuel Flow Meter",
		"deviceType": "fum",
		"communicationCategory": "FUM",
		"driverTypes": [{ "name": "Default", "value": 0 }],
	}
	]
},
{
	"type": "jiocontrol",
	"name": "Feedback - On/Off",
	"id": 7,
	"abbr": "FB",
	"index": 0,
	"devices": [{
		"name": "AHU",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DF"
	}, {
		"name": "Heater",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DF"
	}, {
		"name": "Cooling Tower",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DF"
	}, {
		"name": "Condenser Pump",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DF"
	}, {
		"name": "Chilled Water Pump",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DF"
	}, {
		"name": "Chiller",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DF"
	}],
	"communicationType": "NMB"
},
{
	"type": "jiocontrol",
	"name": "Feedback - Auto/Manual",
	"index": 1,
	"abbr": "FB",
	"id": 8,
	"devices": [{
		"name": "AHU",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DF"
	}, {
		"name": "Heater",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DF"
	}, {
		"name": "Cooling Tower",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DF"
	}, {
		"name": "Condenser Pump",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DF"
	}, {
		"name": "Chilled Water Pump",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DF"
	}, {
		"name": "Chiller",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DF"
	}],
	"communicationType": "NMB"
},
{
	"type": "jiocontrol",
	"name": "Feedback - On/Off",
	"abbr": "FB",
	"index": 2,
	"id": 9,
	"devices": [{
		"name": "AHU",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DF"
	}, {
		"name": "Heater",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DF"
	}, {
		"name": "Cooling Tower",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DF"
	}, {
		"name": "Condenser Pump",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DF"
	}, {
		"name": "Chilled Water Pump",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DF"
	}, {
		"name": "Chiller",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DF"
	}],
	"communicationType": "NMB"
},
{
	"type": "jiocontrol",
	"name": "Feedback - Auto/Manual",
	"abbr": "FB",
	"index": 3,
	"id": 10,
	"devices": [{
		"name": "AHU",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DF"
	}, {
		"name": "Heater",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DF"
	}, {
		"name": "Cooling Tower",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DF"
	}, {
		"name": "Condenser Pump",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DF"
	}, {
		"name": "Chilled Water Pump",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DF"
	}, {
		"name": "Chiller",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DF"
	}],
	"communicationType": "NMB"
}, {
	"type": "jiocontrol",
	"abbr": "Relay",
	"name": "Relay",
	"communicationType": "NMB",
	"id": 11,
	"index": 0,
	"devices": [{
		"name": "AHU",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DR"
	}, {
		"name": "Heater",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DR"
	}, {
		"name": "Cooling Tower",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DR"
	}, {
		"name": "Condenser Pump",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DR"
	}, {
		"name": "Chilled Water Pump",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DR"
	}, {
		"name": "Chiller",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DR"
	}],
	"deps": [7, 8]
},
{
	"type": "jiocontrol",
	"abbr": "Relay",
	"name": "Relay",
	"devices": [{
		"name": "AHU",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DR"
	}, {
		"name": "Heater",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DR"
	}, {
		"name": "Cooling Tower",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DR"
	}, {
		"name": "Condenser Pump",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DR"
	}, {
		"name": "Chilled Water Pump",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DR"
	}, {
		"name": "Chiller",
		"deviceType": "relay",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "DR"
	}],
	"index": 1,
	"id": 12,
	"deps": [9, 10],
	"communicationType": "NMB",
}
];

const jiosense = [{
	"type": "jiosense",
	"name": "Supply Air Humidity",
	"abbr": "S.A.H",
	"communicationType": "NMB",
	"id": 0,
	"index": 0,
	"devices": [{
		"name": "Supply Air",
		"deviceType": "temperatureHumiditySensor",
		"driverTypes": [{ "name": "Default", "value": 0 }],
		"communicationCategory": "I2C"
	}]
},
{
	"type": "jiosense",
	"name": "Return Air Humidity",
	"abbr": "R.A.H",
	"communicationType": "NMB",
	"id": 1,
	"index": 1,
	"devices": [{
		"name": "Return Air",
		"deviceType": "temperatureHumiditySensor",
		"communicationCategory": "I2C",
		"driverTypes": [{ "name": "Default", "value": 0 }]
	}]
},
{
	"type": "jiosense",
	"name": "Fresh Air Humidity/Temperature",
	"abbr": "F.A.H.T",
	"communicationType": "NMB",
	"id": 2,
	"index": 2,
	"devices": [{
		"name": "Fresh Air",
		"deviceType": "temperatureHumiditySensor",
		"communicationCategory": "I2C",
		"driverTypes": [{ "name": "Default", "value": 0 }]
	}]
},
{
	"type": "jiosense",
	"name": "Humidity and Temperature",
	"abbr": "H.T",
	"communicationType": "NMB",
	"id": 3,
	"index": 3,
	"devices": [{
		"name": "N/A",
		"deviceType": "temperatureHumiditySensor",
		"communicationCategory": "I2C",
		"driverTypes": [{ "name": "Default", "value": 0 }]
	}]
},
{
	"type": "jiosense",
	"name": "Actuator",
	"abbr": "Act. Ctrl",
	"communicationType": "NMB",
	"id": 4,
	"index": 2,
	"deps": [5],
	"devices": [{
		"name": "2 Way Valve",
		"deviceType": "actuator",
		"communicationCategory": "AVO",
		"driverTypes": [{ "name": "Belimo", "value": 1 }, { "name": "Honeywell", "value": 0 }, { "name": "Danfoss", "value": 0 }],
	},
	{
		"name": "Air Damper",
		"deviceType": "actuator",
		"communicationCategory": "AVO",
		"driverTypes": [{ "name": "Default", "value": 0 }]
	}
	]
},
{
	"type": "jiosense",
	"name": "Actuator",
	"abbr": "Act. FB",
	"communicationType": "NMB",
	"id": 5,
	"index": 2,
	"devices": [{
		"name": "2 Way Valve",
		"deviceType": "actuator",
		"communicationCategory": "AVI",
		"driverTypes": [{ "name": "Belimo", "value": 1 }, { "name": "Honeywell", "value": 0 }, { "name": "Danfoss", "value": 0 }],
	},
	{
		"name": "Air Damper",
		"deviceType": "actuator",
		"communicationCategory": "AVI",
		"driverTypes": [{ "name": "Default", "value": 0 }]
	}
	],
	"divider": true,
	"isHidden": true
},
{
	"type": "jiosense",
	"name": "Water In Temperature",
	"abbr": "W.I.T",
	"communicationType": "NMB",
	"id": 6,
	"index": 0,
	"devices": [{
		"name": "Water-In DS18B20",
		"deviceType": "temperatureSensor",
		"communicationCategory": "DS",
		"driverTypes": [{ "name": "DS18B20", "value": 0 }]
	},
	{
		"name": "Water-In PT100",
		"deviceType": "temperatureSensor",
		"communicationCategory": "ARI",
		"driverTypes": [{ "name": "PT100", "value": 0 }]
	}
	]
},
{
	"type": "jiosense",
	"name": "Water Out Temperature",
	"abbr": "W.O.T",
	"communicationType": "NMB",
	"id": 7,
	"index": 1,
	"devices": [{
		"name": "Water-Out DS18B20",
		"deviceType": "temperatureSensor",
		"communicationCategory": "DS",
		"driverTypes": [{ "name": "DS18B20", "value": 0 }]
	},
	{
		"name": "Water-Out PT100",
		"deviceType": "temperatureSensor",
		"communicationCategory": "ARI",
		"driverTypes": [{ "name": "PT100", "value": 0 }]
	}
	]

},
{
	"type": "jiosense",
	"name": "Supply Air Temperature",
	"abbr": "S.A.T",
	"communicationType": "NMB",
	"id": 8,
	"index": 2,
	"devices": [{
		"name": "Supply Air DS18B20",
		"deviceType": "temperatureSensor",
		"communicationCategory": "DS",
		"driverTypes": [{ "name": "DS18B20", "value": 0 }]
	},
	{
		"name": "Supply Air PT100",
		"deviceType": "temperatureSensor",
		"communicationCategory": "ARI",
		"driverTypes": [{ "name": "PT100", "value": 0 }]
	}
	]
},
{
	"type": "jiosense",
	"name": "Return Air Temperature",
	"abbr": "R.A.T",
	"communicationType": "NMB",
	"id": 9,
	"index": 3,
	"devices": [{
		"name": "Return Air DS18B20",
		"deviceType": "temperatureSensor",
		"communicationCategory": "DS",
		"driverTypes": [{ "name": "DS18B20", "value": 0 }]
	},
	{
		"name": "Return Air PT100",
		"deviceType": "temperatureSensor",
		"communicationCategory": "ARI",
		"driverTypes": [{ "name": "PT100", "value": 0 }]
	}
	]
},
{
	"type": "jiosense",
	"name": "Thermostat",
	"abbr": "T.Stat",
	"communicationType": "NMB",
	"id": 10,
	"deps": [11],
	"index": 0,
	"devices": [{
		"name": "Thermostat",
		"deviceType": "thermostat",
		"communicationCategory": "TH",
		"driverTypes": [{ "name": "Belimo", "value": 0 }, { "name": "Honeywell", "value": 1 }, { "name": "Danfoss", "value": 2 }],
	}]

},
{
	"type": "jiosense",
	"name": "Thermostat Feedback",
	"abbr": "T.Stat FB.",
	"communicationType": "NMB",
	"id": 11,
	"index": 0,
	"devices": [{
		"name": "Thermostat",
		"deviceType": "thermostat",
		"communicationCategory": "THF",
		"driverTypes": [{ "name": "Belimo", "value": 0 }, { "name": "Honeywell", "value": 1 }, { "name": "Danfoss", "value": 2 }],
	}],
	"isHidden": true
}
];

const jLogger = [{
	"type": "jiocontrol",
	"name": "Modbus",
	"abbr": "Modbus",
	"id": 6,
	"index": 0,
	"communicationCategory": "DF",
	"communicationType": "MB",
	"devices": [{
		"name": "VFD",
		"deviceType": "vfd",
		"communicationCategory": "VFD",
		"driverTypes": [{ "name": "Danfoss", "value": 0 }, { "name": "Fuji", "value": 1 }],
	},
	{
		"name": "Chiller",
		"deviceType": "chiller",
		"communicationCategory": "CHL",
		"driverTypes": [{ "name": "Carrier", "value": 0 }, { "name": "Trane", "value": 1 }],

	},
	{
		"name": "Energy Meter",
		"deviceType": "em",
		"communicationCategory": "EM",
		"driverTypes": [{ "name": "NF29", "value": 0 }, { "name": "EM 8400", "value": 1 },
			{ "name": "EM 6400", "value": 2 }, { "name": "EM 6436", "value": 2 }
		],
	},
	{
		"name": "Flow Meter",
		"deviceType": "fm",
		"communicationCategory": "FM",
		"driverTypes": [{ "name": "Default", "value": 0 }],
	},
	{
		"name": "Fuel Flow Meter",
		"deviceType": "fum",
		"communicationCategory": "FUM",
		"driverTypes": [{ "name": "Default", "value": 0 }],
	}
	]
}];
module.exports = {
	"jiocontrol": jiocontrol,
	"jiosense": jiosense,
	"jLogger": jLogger
};