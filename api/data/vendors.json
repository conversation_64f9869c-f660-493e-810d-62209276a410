[{"vendorId": "smartjoules", "displayName": "Smart Joules", "controllerType": "parent", "controllers": [{"displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "versions": ["v1"], "deviceType": "joule<PERSON>", "boardConfig": {"v1": [{"portNumber": -1, "communicationCategory": ["TCP"], "deviceTypes": [{"displayName": "Fire Alarm Gateway", "deviceType": "fireAlarm"}]}, {"portNumber": 2, "communicationCategory": ["AVO"], "deviceTypes": [{"displayName": "Actuator Control", "deviceType": "actuatorControl"}, {"displayName": "Damper Control", "deviceType": "damperControl"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "thyristor"}, {"displayName": "EC Fan Unit", "deviceType": "ecfan"}]}, {"portNumber": 3, "communicationCategory": ["AVO"], "deviceTypes": [{"displayName": "Actuator Control", "deviceType": "actuatorControl"}, {"displayName": "Damper Control", "deviceType": "damperControl"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "thyristor"}, {"displayName": "EC Fan Unit", "deviceType": "ecfan"}]}, {"portNumber": 4, "communicationCategory": ["AVO"], "deviceTypes": [{"displayName": "Actuator Control", "deviceType": "actuatorControl"}, {"displayName": "Damper Control", "deviceType": "damperControl"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "thyristor"}, {"displayName": "EC Fan Unit", "deviceType": "ecfan"}]}, {"portNumber": 5, "communicationCategory": ["AVO"], "deviceTypes": [{"displayName": "Actuator Control", "deviceType": "actuatorControl"}, {"displayName": "Damper Control", "deviceType": "damperControl"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "thyristor"}, {"displayName": "EC Fan Unit", "deviceType": "ecfan"}]}, {"portNumber": 6, "communicationCategory": ["AVO"], "deviceTypes": [{"displayName": "Actuator Control", "deviceType": "actuatorControl"}, {"displayName": "Damper Control", "deviceType": "damperControl"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "thyristor"}, {"displayName": "EC Fan Unit", "deviceType": "ecfan"}]}, {"portNumber": 7, "communicationCategory": ["AVO"], "deviceTypes": [{"displayName": "Actuator Control", "deviceType": "actuatorControl"}, {"displayName": "Damper Control", "deviceType": "damperControl"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "thyristor"}, {"displayName": "EC Fan Unit", "deviceType": "ecfan"}]}, {"portNumber": 8, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}]}, {"portNumber": 9, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}]}, {"portNumber": 10, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}]}, {"portNumber": 11, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}]}, {"portNumber": 12, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}]}, {"portNumber": 13, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}]}, {"portNumber": 14, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}]}, {"portNumber": 15, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}]}, {"portNumber": 16, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 17, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 18, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 19, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 20, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 21, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 22, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 23, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 24, "communicationCategory": ["CHL", "TDS", "EM", "VFD", "SN", "BTU", "HPC", "FM", "ORP", "PH", "CMP", "VRF", "MCU", "THM", "UPS", "DG", "EV", "CSU", "ECFAN", "PLC", "UVL"], "deviceTypes": [{"displayName": "Chiller Controller", "deviceType": "chillerController"}, {"displayName": "TDS Meter", "deviceType": "tdsMeter"}, {"displayName": "Energy Meter", "deviceType": "em"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "Outside Air/Humidity Sensor", "deviceType": "outsideTmpHumSensor"}, {"displayName": "Temperature Humidity Sensor", "deviceType": "temperatureHumiditySensor"}, {"displayName": "BTU Meter", "deviceType": "btumeter"}, {"deviceType": "heatPumpController", "displayName": "Heat Pump Controller"}, {"deviceType": "flowMeter", "displayName": "Flow Sensor"}, {"displayName": "ORP Sensor", "deviceType": "orpSensor"}, {"displayName": "PH Sensor", "deviceType": "phSensor"}, {"deviceType": "compressorController", "displayName": "Compressor Controller"}, {"deviceType": "vrfController", "displayName": "VRF Controller"}, {"displayName": "Add Tech Motor Control Unit", "deviceType": "mcu"}, {"displayName": "Motor Control Unit", "deviceType": "mcuController"}, {"displayName": "Thermostat", "deviceType": "thermostat"}, {"deviceType": "upsController", "displayName": "UPS Controller"}, {"deviceType": "dgController", "displayName": "DG Controller"}, {"displayName": "Energy Valve", "deviceType": "energyvalve"}, {"displayName": "Cassette Unit", "deviceType": "csu"}, {"displayName": "Miztek China EC Fan Unit", "deviceType": "ecfan"}, {"displayName": "Radiant PLC", "deviceType": "plcController"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 25, "communicationCategory": ["AVI"], "deviceTypes": [{"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}]}, {"portNumber": 26, "communicationCategory": ["AVI"], "deviceTypes": [{"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}]}, {"portNumber": 27, "communicationCategory": ["AVI"], "deviceTypes": [{"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}]}, {"portNumber": 28, "communicationCategory": ["AVI"], "deviceTypes": [{"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}]}, {"portNumber": 29, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 30, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 31, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 32, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 33, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 34, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 35, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 36, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 37, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 38, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}]}}, {"displayName": "Joule IO Control", "versions": ["v1", "v2", "v3"], "deviceType": "jouleiocontrol", "boardConfig": {"v1": [{"portNumber": 1, "communicationCategory": ["AVO", "ACO"], "deviceTypes": [{"displayName": "Actuator Control", "deviceType": "actuatorControl"}, {"displayName": "Damper Control", "deviceType": "damperControl"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "thyristor"}, {"displayName": "EC Fan Unit", "deviceType": "ecfan"}]}, {"portNumber": 2, "communicationCategory": ["AVO", "ACO"], "deviceTypes": [{"displayName": "Actuator Control", "deviceType": "actuatorControl"}, {"displayName": "Damper Control", "deviceType": "damperControl"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "thyristor"}, {"displayName": "EC Fan Unit", "deviceType": "ecfan"}]}, {"portNumber": 3, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "ET Vibration Sensor", "deviceType": "vibrationSensor"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Humidity Sensor", "deviceType": "temperatureHumiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 4, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "ET Vibration Sensor", "deviceType": "vibrationSensor"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Humidity Sensor", "deviceType": "temperatureHumiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 5, "communicationCategory": ["AVI"], "deviceTypes": [{"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}]}, {"portNumber": 6, "communicationCategory": ["AVI"], "deviceTypes": [{"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}]}, {"portNumber": 7, "communicationCategory": ["CHL", "TDS", "EM", "VFD", "SN", "BTU", "HPC", "FM", "ORP", "PH", "CMP", "VRF", "MCU", "THM", "UPS", "DG", "EV", "CSU", "ECFAN", "PLC", "UVL"], "deviceTypes": [{"displayName": "Thermostat", "deviceType": "thermostat"}, {"displayName": "Add Tech Motor Control Unit", "deviceType": "mcu"}, {"displayName": "Motor Control Unit", "deviceType": "mcuController"}, {"displayName": "Temperature Humidity Sensor", "deviceType": "temperatureHumiditySensor"}, {"displayName": "TDS Meter", "deviceType": "tdsMeter"}, {"displayName": "ORP Sensor", "deviceType": "orpSensor"}, {"displayName": "PH Sensor", "deviceType": "phSensor"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "Chiller Controller", "deviceType": "chillerController"}, {"displayName": "Energy Meter", "deviceType": "em"}, {"displayName": "Outside Air/Humidity Sensor", "deviceType": "outsideTmpHumSensor"}, {"displayName": "BTU Meter", "deviceType": "btumeter"}, {"deviceType": "flowMeter", "displayName": "Flow Sensor"}, {"deviceType": "heatPumpController", "displayName": "Heat Pump Controller"}, {"deviceType": "compressorController", "displayName": "Compressor Controller"}, {"deviceType": "vrfController", "displayName": "VRF Controller"}, {"deviceType": "upsController", "displayName": "UPS Controller"}, {"deviceType": "dgController", "displayName": "DG Controller"}, {"displayName": "Energy Valve", "deviceType": "energyvalve"}, {"displayName": "Cassette Unit", "deviceType": "csu"}, {"displayName": "Miztek China EC Fan Unit", "deviceType": "ecfan"}, {"displayName": "Radiant PLC", "deviceType": "plcController"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 8, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "AHU-Feedback", "deviceType": "relayFeedback"}, {"displayName": "<PERSON>er-<PERSON><PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "Cooling Tower Feedback", "deviceType": "relayFeedback"}, {"displayName": "Condenser Water Pump Feedback", "deviceType": "relayFeedback"}, {"displayName": "Chilled WaterPump Feedback", "deviceType": "relayFeedback"}, {"displayName": "DP Switch Feedback", "deviceType": "relayFeedback"}]}, {"portNumber": 9, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "AHU-Feedback", "deviceType": "relayFeedback"}, {"displayName": "<PERSON>er-<PERSON><PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "Cooling Tower Feedback", "deviceType": "relayFeedback"}, {"displayName": "Condenser Water Pump Feedback", "deviceType": "relayFeedback"}, {"displayName": "Chilled WaterPump Feedback", "deviceType": "relayFeedback"}, {"displayName": "DP Switch Feedback", "deviceType": "relayFeedback"}]}, {"portNumber": 10, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "AHU-Feedback", "deviceType": "relayFeedback"}, {"displayName": "<PERSON>er-<PERSON><PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "Cooling Tower Feedback", "deviceType": "relayFeedback"}, {"displayName": "Condenser Water Pump Feedback", "deviceType": "relayFeedback"}, {"displayName": "Chilled WaterPump Feedback", "deviceType": "relayFeedback"}, {"displayName": "DP Switch Feedback", "deviceType": "relayFeedback"}]}, {"portNumber": 11, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "AHU-Feedback", "deviceType": "relayFeedback"}, {"displayName": "<PERSON>er-<PERSON><PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "Cooling Tower Feedback", "deviceType": "relayFeedback"}, {"displayName": "Condenser Water Pump Feedback", "deviceType": "relayFeedback"}, {"displayName": "Chilled Water Pump Fe<PERSON>back", "deviceType": "relayFeedback"}, {"displayName": "DP Switch Feedback", "deviceType": "relayFeedback"}]}, {"portNumber": 12, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "AHU-StartStop", "deviceType": "relay"}, {"displayName": "Heater-StartStop", "deviceType": "relay"}, {"displayName": "Cooling Tower StartStop", "deviceType": "relay"}, {"displayName": "Condenser Water Pump StartStop", "deviceType": "relay"}, {"displayName": "Chilled PumpStartStop", "deviceType": "relay"}]}, {"portNumber": 13, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "AHU-StartStop", "deviceType": "relay"}, {"displayName": "Heater-StartStop", "deviceType": "relay"}, {"displayName": "Cooling Tower StartStop", "deviceType": "relay"}, {"displayName": "Condenser Water Pump StartStop", "deviceType": "relay"}, {"displayName": "Chilled PumpStartStop", "deviceType": "relay"}]}], "v2": [{"portNumber": 1, "communicationCategory": ["AVO", "ACO"], "deviceTypes": [{"displayName": "Actuator Control", "deviceType": "actuatorControl"}, {"displayName": "Damper Control", "deviceType": "damperControl"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "thyristor"}, {"displayName": "EC Fan Unit", "deviceType": "ecfan"}]}, {"portNumber": 2, "communicationCategory": ["AVO", "ACO"], "deviceTypes": [{"displayName": "Actuator Control", "deviceType": "actuatorControl"}, {"displayName": "Damper Control", "deviceType": "damperControl"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "thyristor"}, {"displayName": "EC Fan Unit", "deviceType": "ecfan"}]}, {"portNumber": 3, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "ET Vibration Sensor", "deviceType": "vibrationSensor"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 4, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "ET Vibration Sensor", "deviceType": "vibrationSensor"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 5, "communicationCategory": ["AVI"], "deviceTypes": [{"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}]}, {"portNumber": 6, "communicationCategory": ["AVI"], "deviceTypes": [{"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}]}, {"portNumber": 7, "communicationCategory": ["CHL", "TDS", "EM", "VFD", "SN", "HPC", "BTU", "FM", "ORP", "PH", "CMP", "VRF", "MCU", "THM", "UPS", "DG", "EV", "CSU", "ECFAN", "PLC", "UVL"], "deviceTypes": [{"displayName": "Thermostat", "deviceType": "thermostat"}, {"displayName": "Add Tech Motor Control Unit", "deviceType": "mcu"}, {"displayName": "Motor Control Unit", "deviceType": "mcuController"}, {"displayName": "Temperature Humidity Sensor", "deviceType": "temperatureHumiditySensor"}, {"displayName": "TDS Meter", "deviceType": "tdsMeter"}, {"displayName": "ORP Sensor", "deviceType": "orpSensor"}, {"displayName": "PH Sensor", "deviceType": "phSensor"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "Chiller Controller", "deviceType": "chillerController"}, {"displayName": "Energy Meter", "deviceType": "em"}, {"displayName": "Outside Air/Humidity Sensor", "deviceType": "outsideTmpHumSensor"}, {"displayName": "BTU Meter", "deviceType": "btumeter"}, {"deviceType": "flowMeter", "displayName": "Flow Sensor"}, {"deviceType": "heatPumpController", "displayName": "Heat Pump Controller"}, {"deviceType": "compressorController", "displayName": "Compressor Controller"}, {"deviceType": "vrfController", "displayName": "VRF Controller"}, {"deviceType": "upsController", "displayName": "UPS Controller"}, {"deviceType": "dgController", "displayName": "DG Controller"}, {"displayName": "Energy Valve", "deviceType": "energyvalve"}, {"displayName": "Cassette Unit", "deviceType": "csu"}, {"displayName": "Miztek China EC Fan Unit", "deviceType": "ecfan"}, {"displayName": "Radiant PLC", "deviceType": "plcController"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 8, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "AHU-Feedback", "deviceType": "relayFeedback"}, {"displayName": "<PERSON>er-<PERSON><PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "Cooling Tower Feedback", "deviceType": "relayFeedback"}, {"displayName": "Condenser Water Pump Feedback", "deviceType": "relayFeedback"}, {"displayName": "Chilled WaterPump Feedback", "deviceType": "relayFeedback"}, {"displayName": "DP Switch Feedback", "deviceType": "relayFeedback"}]}, {"portNumber": 9, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "AHU-Feedback", "deviceType": "relayFeedback"}, {"displayName": "<PERSON>er-<PERSON><PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "Cooling Tower Feedback", "deviceType": "relayFeedback"}, {"displayName": "Condenser Water Pump Feedback", "deviceType": "relayFeedback"}, {"displayName": "Chilled WaterPump Feedback", "deviceType": "relayFeedback"}, {"displayName": "DP Switch Feedback", "deviceType": "relayFeedback"}]}, {"portNumber": 10, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "AHU-Feedback", "deviceType": "relayFeedback"}, {"displayName": "<PERSON>er-<PERSON><PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "Cooling Tower Feedback", "deviceType": "relayFeedback"}, {"displayName": "Condenser Water Pump Feedback", "deviceType": "relayFeedback"}, {"displayName": "Chilled WaterPump Feedback", "deviceType": "relayFeedback"}, {"displayName": "DP Switch Feedback", "deviceType": "relayFeedback"}]}, {"portNumber": 11, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "AHU-Feedback", "deviceType": "relayFeedback"}, {"displayName": "<PERSON>er-<PERSON><PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "Cooling Tower Feedback", "deviceType": "relayFeedback"}, {"displayName": "Condenser Water Pump Feedback", "deviceType": "relayFeedback"}, {"displayName": "Chilled Water Pump Fe<PERSON>back", "deviceType": "relayFeedback"}, {"displayName": "DP Switch Feedback", "deviceType": "relayFeedback"}]}, {"portNumber": 12, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "AHU-StartStop", "deviceType": "relay"}, {"displayName": "Heater-StartStop", "deviceType": "relay"}, {"displayName": "Cooling Tower StartStop", "deviceType": "relay"}, {"displayName": "Condenser Water Pump StartStop", "deviceType": "relay"}, {"displayName": "Chilled PumpStartStop", "deviceType": "relay"}]}, {"portNumber": 13, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "AHU-StartStop", "deviceType": "relay"}, {"displayName": "Heater-StartStop", "deviceType": "relay"}, {"displayName": "Cooling Tower StartStop", "deviceType": "relay"}, {"displayName": "Condenser Water Pump StartStop", "deviceType": "relay"}, {"displayName": "Chilled PumpStartStop", "deviceType": "relay"}]}, {"portNumber": 14, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "AHU-StartStop", "deviceType": "relay"}, {"displayName": "Heater-StartStop", "deviceType": "relay"}, {"displayName": "Cooling Tower StartStop", "deviceType": "relay"}, {"displayName": "Condenser Water Pump StartStop", "deviceType": "relay"}, {"displayName": "Chilled PumpStartStop", "deviceType": "relay"}]}, {"portNumber": 15, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "AHU-StartStop", "deviceType": "relay"}, {"displayName": "Heater-StartStop", "deviceType": "relay"}, {"displayName": "Cooling Tower StartStop", "deviceType": "relay"}, {"displayName": "Condenser Water Pump StartStop", "deviceType": "relay"}, {"displayName": "Chilled PumpStartStop", "deviceType": "relay"}]}], "v3": [{"portNumber": -1, "communicationCategory": ["TCP"], "deviceTypes": [{"displayName": "Fire Alarm Gateway", "deviceType": "fireAlarm"}]}, {"portNumber": 1, "communicationCategory": ["AVO", "ACO"], "deviceTypes": [{"displayName": "Actuator Control", "deviceType": "actuatorControl"}, {"displayName": "Damper Control", "deviceType": "damperControl"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "thyristor"}, {"displayName": "EC Fan Unit", "deviceType": "ecfan"}]}, {"portNumber": 2, "communicationCategory": ["AVO", "ACO"], "deviceTypes": [{"displayName": "Actuator Control", "deviceType": "actuatorControl"}, {"displayName": "Damper Control", "deviceType": "damperControl"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "thyristor"}, {"displayName": "EC Fan Unit", "deviceType": "ecfan"}]}, {"portNumber": 3, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "ET Vibration Sensor", "deviceType": "vibrationSensor"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 4, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "ET Vibration Sensor", "deviceType": "vibrationSensor"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 5, "communicationCategory": ["AVI"], "deviceTypes": [{"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}]}, {"portNumber": 6, "communicationCategory": ["AVI"], "deviceTypes": [{"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}]}, {"portNumber": 7, "communicationCategory": ["CHL", "TDS", "EM", "VFD", "SN", "BTU", "HPC", "FM", "ORP", "PH", "CMP", "VRF", "MCU", "THM", "UPS", "DG", "EV", "CSU", "ECFAN", "PLC", "UVL"], "deviceTypes": [{"displayName": "Thermostat", "deviceType": "thermostat"}, {"displayName": "Add Tech Motor Control Unit", "deviceType": "mcu"}, {"displayName": "Motor Control Unit", "deviceType": "mcuController"}, {"displayName": "Temperature Humidity Sensor", "deviceType": "temperatureHumiditySensor"}, {"displayName": "TDS Meter", "deviceType": "tdsMeter"}, {"displayName": "ORP Sensor", "deviceType": "orpSensor"}, {"displayName": "PH Sensor", "deviceType": "phSensor"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "Chiller Controller", "deviceType": "chillerController"}, {"displayName": "Energy Meter", "deviceType": "em"}, {"displayName": "Outside Air/Humidity Sensor", "deviceType": "outsideTmpHumSensor"}, {"displayName": "BTU Meter", "deviceType": "btumeter"}, {"deviceType": "flowMeter", "displayName": "Flow Sensor"}, {"deviceType": "heatPumpController", "displayName": "Heat Pump Controller"}, {"deviceType": "compressorController", "displayName": "Compressor Controller"}, {"deviceType": "vrfController", "displayName": "VRF Controller"}, {"deviceType": "upsController", "displayName": "UPS Controller"}, {"deviceType": "dgController", "displayName": "DG Controller"}, {"displayName": "Energy Valve", "deviceType": "energyvalve"}, {"displayName": "Cassette Unit", "deviceType": "csu"}, {"displayName": "Miztek China EC Fan Unit", "deviceType": "ecfan"}, {"displayName": "Radiant PLC", "deviceType": "plcController"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 8, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "AHU-Feedback", "deviceType": "relayFeedback"}, {"displayName": "<PERSON>er-<PERSON><PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "Cooling Tower Feedback", "deviceType": "relayFeedback"}, {"displayName": "Condenser Water Pump Feedback", "deviceType": "relayFeedback"}, {"displayName": "Chilled WaterPump Feedback", "deviceType": "relayFeedback"}, {"displayName": "DP Switch Feedback", "deviceType": "relayFeedback"}]}, {"portNumber": 9, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "AHU-Feedback", "deviceType": "relayFeedback"}, {"displayName": "<PERSON>er-<PERSON><PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "Cooling Tower Feedback", "deviceType": "relayFeedback"}, {"displayName": "Condenser Water Pump Feedback", "deviceType": "relayFeedback"}, {"displayName": "Chilled WaterPump Feedback", "deviceType": "relayFeedback"}, {"displayName": "DP Switch Feedback", "deviceType": "relayFeedback"}]}, {"portNumber": 10, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "AHU-Feedback", "deviceType": "relayFeedback"}, {"displayName": "<PERSON>er-<PERSON><PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "Cooling Tower Feedback", "deviceType": "relayFeedback"}, {"displayName": "Condenser Water Pump Feedback", "deviceType": "relayFeedback"}, {"displayName": "Chilled WaterPump Feedback", "deviceType": "relayFeedback"}, {"displayName": "DP Switch Feedback", "deviceType": "relayFeedback"}]}, {"portNumber": 11, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "AHU-Feedback", "deviceType": "relayFeedback"}, {"displayName": "<PERSON>er-<PERSON><PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "Cooling Tower Feedback", "deviceType": "relayFeedback"}, {"displayName": "Condenser Water Pump Feedback", "deviceType": "relayFeedback"}, {"displayName": "Chilled Water Pump Fe<PERSON>back", "deviceType": "relayFeedback"}, {"displayName": "DP Switch Feedback", "deviceType": "relayFeedback"}]}, {"portNumber": 12, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "AHU-StartStop", "deviceType": "relay"}, {"displayName": "Heater-StartStop", "deviceType": "relay"}, {"displayName": "Cooling Tower StartStop", "deviceType": "relay"}, {"displayName": "Condenser Water Pump StartStop", "deviceType": "relay"}, {"displayName": "Chilled PumpStartStop", "deviceType": "relay"}]}, {"portNumber": 13, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "AHU-StartStop", "deviceType": "relay"}, {"displayName": "Heater-StartStop", "deviceType": "relay"}, {"displayName": "Cooling Tower StartStop", "deviceType": "relay"}, {"displayName": "Condenser Water Pump StartStop", "deviceType": "relay"}, {"displayName": "Chilled PumpStartStop", "deviceType": "relay"}]}, {"portNumber": 14, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "AHU-StartStop", "deviceType": "relay"}, {"displayName": "Heater-StartStop", "deviceType": "relay"}, {"displayName": "Cooling Tower StartStop", "deviceType": "relay"}, {"displayName": "Condenser Water Pump StartStop", "deviceType": "relay"}, {"displayName": "Chilled PumpStartStop", "deviceType": "relay"}]}, {"portNumber": 15, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "AHU-StartStop", "deviceType": "relay"}, {"displayName": "Heater-StartStop", "deviceType": "relay"}, {"displayName": "Cooling Tower StartStop", "deviceType": "relay"}, {"displayName": "Condenser Water Pump StartStop", "deviceType": "relay"}, {"displayName": "Chilled PumpStartStop", "deviceType": "relay"}]}]}}, {"displayName": "<PERSON><PERSON>", "versions": ["v1", "v3", "v4"], "deviceType": "joulelogger", "boardConfig": {"v1": [{"portNumber": 1, "communicationCategory": ["CHL", "TDS", "VFD", "HPC", "EM", "SN", "BTU", "FM", "ORP", "PH", "CMP", "VRF", "MCU", "THM", "UPS", "DG", "EV", "CSU", "ECFAN", "PLC", "UVL"], "deviceTypes": [{"displayName": "Thermostat", "deviceType": "thermostat"}, {"displayName": "Add Tech Motor Control Unit", "deviceType": "mcu"}, {"displayName": "Motor Control Unit", "deviceType": "mcuController"}, {"displayName": "Temperature Humidity Sensor", "deviceType": "temperatureHumiditySensor"}, {"displayName": "TDS Meter", "deviceType": "tdsMeter"}, {"displayName": "ORP Sensor", "deviceType": "orpSensor"}, {"displayName": "PH Sensor", "deviceType": "phSensor"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "Chiller Controller", "deviceType": "chillerController"}, {"displayName": "Energy Meter", "deviceType": "em"}, {"displayName": "Outside Air/Humidity Sensor", "deviceType": "outsideTmpHumSensor"}, {"displayName": "BTU Meter", "deviceType": "btumeter"}, {"deviceType": "flowMeter", "displayName": "Flow Sensor"}, {"deviceType": "heatPumpController", "displayName": "Heat Pump Controller"}, {"deviceType": "compressorController", "displayName": "Compressor Controller"}, {"deviceType": "vrfController", "displayName": "VRF Controller"}, {"deviceType": "upsController", "displayName": "UPS Controller"}, {"deviceType": "dgController", "displayName": "DG Controller"}, {"displayName": "Energy Valve", "deviceType": "energyvalve"}, {"displayName": "Cassette Unit", "deviceType": "csu"}, {"displayName": "Miztek China EC Fan Unit", "deviceType": "ecfan"}, {"displayName": "Radiant PLC", "deviceType": "plcController"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}], "v3": [{"portNumber": -1, "communicationCategory": ["TCP"], "deviceTypes": [{"displayName": "Fire Alarm Gateway", "deviceType": "fireAlarm"}]}, {"portNumber": 1, "communicationCategory": ["CHL", "TDS", "VFD", "EM", "SN", "BTU", "HPC", "FM", "ORP", "PH", "CMP", "VRF", "MCU", "THM", "UPS", "DG", "EV", "CSU", "ECFAN", "PLC", "UVL"], "deviceTypes": [{"displayName": "Thermostat", "deviceType": "thermostat"}, {"displayName": "Add Tech Motor Control Unit", "deviceType": "mcu"}, {"displayName": "Motor Control Unit", "deviceType": "mcuController"}, {"displayName": "Temperature Humidity Sensor", "deviceType": "temperatureHumiditySensor"}, {"displayName": "TDS Meter", "deviceType": "tdsMeter"}, {"displayName": "ORP Sensor", "deviceType": "orpSensor"}, {"displayName": "PH Sensor", "deviceType": "phSensor"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "Chiller Controller", "deviceType": "chillerController"}, {"displayName": "Energy Meter", "deviceType": "em"}, {"displayName": "Outside Air/Humidity Sensor", "deviceType": "outsideTmpHumSensor"}, {"displayName": "BTU Meter", "deviceType": "btumeter"}, {"deviceType": "flowMeter", "displayName": "Flow Sensor"}, {"deviceType": "heatPumpController", "displayName": "Heat Pump Controller"}, {"deviceType": "compressorController", "displayName": "Compressor Controller"}, {"deviceType": "vrfController", "displayName": "VRF Controller"}, {"deviceType": "upsController", "displayName": "UPS Controller"}, {"deviceType": "dgController", "displayName": "DG Controller"}, {"displayName": "Energy Valve", "deviceType": "energyvalve"}, {"displayName": "Cassette Unit", "deviceType": "csu"}, {"displayName": "Miztek China EC Fan Unit", "deviceType": "ecfan"}, {"displayName": "Radiant PLC", "deviceType": "plcController"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}], "v4": [{"portNumber": -1, "communicationCategory": ["TCP"], "deviceTypes": [{"displayName": "Fire Alarm Gateway", "deviceType": "fireAlarm"}]}, {"portNumber": 1, "communicationCategory": ["CHL", "TDS", "VFD", "EM", "SN", "BTU", "HPC", "FM", "ORP", "PH", "CMP", "VRF", "MCU", "THM", "UPS", "DG", "EV", "CSU", "ECFAN", "PLC", "UVL"], "deviceTypes": [{"displayName": "Thermostat", "deviceType": "thermostat"}, {"displayName": "Add Tech Motor Control Unit", "deviceType": "mcu"}, {"displayName": "Motor Control Unit", "deviceType": "mcuController"}, {"displayName": "Temperature Humidity Sensor", "deviceType": "temperatureHumiditySensor"}, {"displayName": "TDS Meter", "deviceType": "tdsMeter"}, {"displayName": "ORP Sensor", "deviceType": "orpSensor"}, {"displayName": "PH Sensor", "deviceType": "phSensor"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "Chiller Controller", "deviceType": "chillerController"}, {"displayName": "Energy Meter", "deviceType": "em"}, {"displayName": "Outside Air/Humidity Sensor", "deviceType": "outsideTmpHumSensor"}, {"displayName": "BTU Meter", "deviceType": "btumeter"}, {"deviceType": "flowMeter", "displayName": "Flow Sensor"}, {"deviceType": "heatPumpController", "displayName": "Heat Pump Controller"}, {"deviceType": "compressorController", "displayName": "Compressor Controller"}, {"deviceType": "vrfController", "displayName": "VRF Controller"}, {"deviceType": "upsController", "displayName": "UPS Controller"}, {"deviceType": "dgController", "displayName": "DG Controller"}, {"displayName": "Energy Valve", "deviceType": "energyvalve"}, {"displayName": "Cassette Unit", "deviceType": "csu"}, {"displayName": "Miztek China EC Fan Unit", "deviceType": "ecfan"}, {"displayName": "Radiant PLC", "deviceType": "plcController"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}]}}, {"displayName": "Joule Stat", "versions": ["v1", "v2"], "deviceType": "joules<PERSON>", "boardConfig": {"v1": [{"portNumber": 1, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "AHU-Feedback", "deviceType": "relayFeedback"}, {"displayName": "<PERSON>er-<PERSON><PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "Cooling Tower Feedback", "deviceType": "relayFeedback"}, {"displayName": "Condenser Water Pump Feedback", "deviceType": "relayFeedback"}, {"displayName": "Chilled WaterPump Feedback", "deviceType": "relayFeedback"}, {"displayName": "DP Switch Feedback", "deviceType": "relayFeedback"}]}, {"portNumber": 2, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "ET Vibration Sensor", "deviceType": "vibrationSensor"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 3, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "ET Vibration Sensor", "deviceType": "vibrationSensor"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 4, "communicationCategory": ["RTD"], "deviceTypes": [{"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}]}, {"portNumber": 5, "communicationCategory": ["RTD"], "deviceTypes": [{"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}]}, {"portNumber": 6, "communicationCategory": ["RTD"], "deviceTypes": [{"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}]}, {"portNumber": 7, "communicationCategory": ["RTD"], "deviceTypes": [{"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}]}, {"portNumber": 8, "communicationCategory": ["AVI"], "deviceTypes": [{"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Test Device", "deviceType": "testDeviceInput"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}]}, {"portNumber": 9, "communicationCategory": ["AVO"], "deviceTypes": [{"displayName": "Actuator Control", "deviceType": "actuatorControl"}, {"displayName": "Damper Control", "deviceType": "damperControl"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "thyristor"}, {"displayName": "EC Fan Unit", "deviceType": "ecfan"}]}], "v2": [{"portNumber": -1, "communicationCategory": ["TCP"], "deviceTypes": [{"displayName": "Fire Alarm Gateway", "deviceType": "fireAlarm"}]}, {"portNumber": 1, "communicationCategory": ["AVI"], "deviceTypes": [{"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Test Device", "deviceType": "testDeviceInput"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}]}, {"portNumber": 2, "communicationCategory": ["AVI"], "deviceTypes": [{"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Test Device", "deviceType": "testDeviceInput"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}]}, {"portNumber": 3, "communicationCategory": ["AVO"], "deviceTypes": [{"displayName": "Actuator Control", "deviceType": "actuatorControl"}, {"displayName": "Damper Control", "deviceType": "damperControl"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "thyristor"}, {"displayName": "EC Fan Unit", "deviceType": "ecfan"}]}, {"portNumber": 4, "communicationCategory": ["AVO"], "deviceTypes": [{"displayName": "Actuator Control", "deviceType": "actuatorControl"}, {"displayName": "Damper Control", "deviceType": "damperControl"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "thyristor"}, {"displayName": "EC Fan Unit", "deviceType": "ecfan"}]}, {"portNumber": 5, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "AHU-StartStop", "deviceType": "relay"}, {"displayName": "Heater-StartStop", "deviceType": "relay"}, {"displayName": "Cooling Tower StartStop", "deviceType": "relay"}, {"displayName": "Condenser Water Pump StartStop", "deviceType": "relay"}, {"displayName": "Chilled PumpStartStop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}]}, {"portNumber": 6, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "AHU-StartStop", "deviceType": "relay"}, {"displayName": "Heater-StartStop", "deviceType": "relay"}, {"displayName": "Cooling Tower StartStop", "deviceType": "relay"}, {"displayName": "Condenser Water Pump StartStop", "deviceType": "relay"}, {"displayName": "Chilled PumpStartStop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}]}, {"portNumber": 7, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "AHU-StartStop", "deviceType": "relay"}, {"displayName": "Heater-StartStop", "deviceType": "relay"}, {"displayName": "Cooling Tower StartStop", "deviceType": "relay"}, {"displayName": "Condenser Water Pump StartStop", "deviceType": "relay"}, {"displayName": "Chilled PumpStartStop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}]}, {"portNumber": 8, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "AHU-StartStop", "deviceType": "relay"}, {"displayName": "Heater-StartStop", "deviceType": "relay"}, {"displayName": "Cooling Tower StartStop", "deviceType": "relay"}, {"displayName": "Condenser Water Pump StartStop", "deviceType": "relay"}, {"displayName": "Chilled PumpStartStop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}]}, {"portNumber": 9, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "AHU-Feedback", "deviceType": "relayFeedback"}, {"displayName": "<PERSON>er-<PERSON><PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "Cooling Tower Feedback", "deviceType": "relayFeedback"}, {"displayName": "Condenser Water Pump Feedback", "deviceType": "relayFeedback"}, {"displayName": "Chilled Water Pump Fe<PERSON>back", "deviceType": "relayFeedback"}, {"displayName": "DP Switch Feedback", "deviceType": "relayFeedback"}]}, {"portNumber": 10, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "AHU-Feedback", "deviceType": "relayFeedback"}, {"displayName": "<PERSON>er-<PERSON><PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "Cooling Tower Feedback", "deviceType": "relayFeedback"}, {"displayName": "Condenser Water Pump Feedback", "deviceType": "relayFeedback"}, {"displayName": "Chilled Water Pump Fe<PERSON>back", "deviceType": "relayFeedback"}, {"displayName": "DP Switch Feedback", "deviceType": "relayFeedback"}]}, {"portNumber": 11, "communicationCategory": ["CHL", "TDS", "EM", "VFD", "SN", "BTU", "HPC", "FM", "ORP", "PH", "CMP", "VRF", "MCU", "THM", "UPS", "DG", "EV", "CSU", "ECFAN", "PLC", "UVL"], "deviceTypes": [{"displayName": "Thermostat", "deviceType": "thermostat"}, {"displayName": "Add Tech Motor Control Unit", "deviceType": "mcu"}, {"displayName": "Motor Control Unit", "deviceType": "mcuController"}, {"displayName": "Temperature Humidity Sensor", "deviceType": "temperatureHumiditySensor"}, {"displayName": "TDS Meter", "deviceType": "tdsMeter"}, {"displayName": "ORP Sensor", "deviceType": "orpSensor"}, {"displayName": "PH Sensor", "deviceType": "phSensor"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "Chiller Controller", "deviceType": "chillerController"}, {"displayName": "Energy Meter", "deviceType": "em"}, {"displayName": "Outside Air/Humidity Sensor", "deviceType": "outsideTmpHumSensor"}, {"displayName": "BTU Meter", "deviceType": "btumeter"}, {"deviceType": "flowMeter", "displayName": "Flow Sensor"}, {"deviceType": "heatPumpController", "displayName": "Heat Pump Controller"}, {"deviceType": "compressorController", "displayName": "Compressor Controller"}, {"deviceType": "vrfController", "displayName": "VRF Controller"}, {"deviceType": "upsController", "displayName": "UPS Controller"}, {"deviceType": "dgController", "displayName": "DG Controller"}, {"displayName": "Energy Valve", "deviceType": "energyvalve"}, {"displayName": "Cassette Unit", "deviceType": "csu"}, {"displayName": "Miztek China EC Fan Unit", "deviceType": "ecfan"}, {"displayName": "Radiant PLC", "deviceType": "plcController"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 12, "communicationCategory": ["RTD"], "deviceTypes": [{"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}]}, {"portNumber": 13, "communicationCategory": ["RTD"], "deviceTypes": [{"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}]}, {"portNumber": 14, "communicationCategory": ["RTD"], "deviceTypes": [{"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}]}, {"portNumber": 15, "communicationCategory": ["RTD"], "deviceTypes": [{"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}]}, {"portNumber": 16, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "ET Vibration Sensor", "deviceType": "vibrationSensor"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 17, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "ET Vibration Sensor", "deviceType": "vibrationSensor"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 18, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "ET Vibration Sensor", "deviceType": "vibrationSensor"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 19, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "ET Vibration Sensor", "deviceType": "vibrationSensor"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 20, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "ET Vibration Sensor", "deviceType": "vibrationSensor"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 21, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "ET Vibration Sensor", "deviceType": "vibrationSensor"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 22, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "ET Vibration Sensor", "deviceType": "vibrationSensor"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 23, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "ET Vibration Sensor", "deviceType": "vibrationSensor"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}]}}, {"displayName": "<PERSON><PERSON>", "versions": ["v1"], "deviceType": "joulem<PERSON>", "boardConfig": {"v1": [{"portNumber": 1, "communicationCategory": ["CHL", "TDS", "VFD", "EM", "SN", "BTU", "HPC", "FM", "ORP", "PH", "CMP", "VRF", "MCU", "THM", "UPS", "DG", "EV", "CSU", "ECFAN", "PLC", "UVL"], "deviceTypes": [{"displayName": "Thermostat", "deviceType": "thermostat"}, {"displayName": "Add Tech Motor Control Unit", "deviceType": "mcu"}, {"displayName": "Motor Control Unit", "deviceType": "mcuController"}, {"displayName": "Temperature Humidity Sensor", "deviceType": "temperatureHumiditySensor"}, {"displayName": "TDS Meter", "deviceType": "tdsMeter"}, {"displayName": "ORP Sensor", "deviceType": "orpSensor"}, {"displayName": "PH Sensor", "deviceType": "phSensor"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "Chiller Controller", "deviceType": "chillerController"}, {"displayName": "Energy Meter", "deviceType": "em"}, {"displayName": "Outside Air/Humidity Sensor", "deviceType": "outsideTmpHumSensor"}, {"displayName": "BTU Meter", "deviceType": "btumeter"}, {"deviceType": "flowMeter", "displayName": "Flow Sensor"}, {"deviceType": "heatPumpController", "displayName": "Heat Pump Controller"}, {"deviceType": "compressorController", "displayName": "Compressor Controller"}, {"deviceType": "vrfController", "displayName": "VRF Controller"}, {"deviceType": "upsController", "displayName": "UPS Controller"}, {"deviceType": "dgController", "displayName": "DG Controller"}, {"displayName": "Energy Valve", "deviceType": "energyvalve"}, {"displayName": "Cassette Unit", "deviceType": "csu"}, {"displayName": "Miztek China EC Fan Unit", "deviceType": "ecfan"}, {"displayName": "Radiant PLC", "deviceType": "plcController"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 2, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "AHU-Feedback", "deviceType": "relayFeedback"}, {"displayName": "<PERSON>er-<PERSON><PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "Cooling Tower Feedback", "deviceType": "relayFeedback"}, {"displayName": "Condenser Water Pump Feedback", "deviceType": "relayFeedback"}, {"displayName": "Chilled WaterPump Feedback", "deviceType": "relayFeedback"}, {"displayName": "DP Switch Feedback", "deviceType": "relayFeedback"}]}, {"portNumber": 3, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "AHU-Feedback", "deviceType": "relayFeedback"}, {"displayName": "<PERSON>er-<PERSON><PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "Cooling Tower Feedback", "deviceType": "relayFeedback"}, {"displayName": "Condenser Water Pump Feedback", "deviceType": "relayFeedback"}, {"displayName": "Chilled Water Pump Fe<PERSON>back", "deviceType": "relayFeedback"}, {"displayName": "DP Switch Feedback", "deviceType": "relayFeedback"}]}, {"portNumber": 4, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "AHU-StartStop", "deviceType": "relay"}, {"displayName": "Heater-StartStop", "deviceType": "relay"}, {"displayName": "Cooling Tower StartStop", "deviceType": "relay"}, {"displayName": "Condenser Water Pump StartStop", "deviceType": "relay"}, {"displayName": "Chilled PumpStartStop", "deviceType": "relay"}]}]}}, {"displayName": "<PERSON><PERSON>", "versions": ["v1", "v2"], "deviceType": "joulesense", "boardConfig": {"v1": [{"portNumber": 1, "deviceTypes": []}], "v2": [{"portNumber": 1, "deviceTypes": []}]}}, {"displayName": "Joule Box", "versions": ["v1", "v2"], "deviceType": "joulebox", "boardConfig": {"v1": [{"portNumber": 1, "deviceTypes": []}], "v2": [{"portNumber": -1, "communicationCategory": ["TCP"], "deviceTypes": [{"displayName": "Fire Alarm Gateway", "deviceType": "fireAlarm"}]}, {"portNumber": 1, "deviceTypes": []}]}}]}, {"vendorId": "protoconvert", "displayName": "Proto Convert", "controllerType": "parent", "controllers": [{"displayName": "Controller", "deviceType": "protoconvert-controller", "versions": ["v1"], "boardConfig": {"v1": [{"portNumber": 1, "deviceTypes": ["all"]}]}, "infoRequired": [{"key": "url", "type": "string", "displayName": "Controller IP"}, {"key": "MAC", "type": "string", "displayName": "MAC Address"}]}]}, {"vendorId": "renu", "displayName": "<PERSON><PERSON>", "controllerType": "child", "controllers": [{"displayName": "Renu AI-8", "versions": ["v1"], "deviceType": "renuai8", "boardConfig": {"v1": [{"portNumber": 1, "communicationCategory": ["AVI", "ACI", "UVL"], "deviceTypes": [{"displayName": "ET Vibration Sensor", "deviceType": "vibrationSensor"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 2, "communicationCategory": ["AVI", "ACI", "UVL"], "deviceTypes": [{"displayName": "ET Vibration Sensor", "deviceType": "vibrationSensor"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 3, "communicationCategory": ["AVI", "ACI", "UVL"], "deviceTypes": [{"displayName": "ET Vibration Sensor", "deviceType": "vibrationSensor"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 4, "communicationCategory": ["AVI", "ACI", "UVL"], "deviceTypes": [{"displayName": "ET Vibration Sensor", "deviceType": "vibrationSensor"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 5, "communicationCategory": ["AVI", "ACI", "UVL"], "deviceTypes": [{"displayName": "ET Vibration Sensor", "deviceType": "vibrationSensor"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 6, "communicationCategory": ["AVI", "ACI", "UVL"], "deviceTypes": [{"displayName": "ET Vibration Sensor", "deviceType": "vibrationSensor"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 7, "communicationCategory": ["AVI", "ACI", "UVL"], "deviceTypes": [{"displayName": "ET Vibration Sensor", "deviceType": "vibrationSensor"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 8, "communicationCategory": ["AVI", "ACI", "UVL"], "deviceTypes": [{"displayName": "ET Vibration Sensor", "deviceType": "vibrationSensor"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}]}, "communicationCategory": "MB"}, {"displayName": "Renu AI-4 AO-2", "versions": ["v1"], "deviceType": "renuaiao6", "boardConfig": {"v1": [{"portNumber": 1, "communicationCategory": ["AVI", "ACI", "RTD", "UVL"], "deviceTypes": [{"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 2, "communicationCategory": ["AVI", "ACI", "RTD", "UVL"], "deviceTypes": [{"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 3, "communicationCategory": ["AVI", "ACI", "RTD", "UVL"], "deviceTypes": [{"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 4, "communicationCategory": ["AVI", "ACI", "RTD", "UVL"], "deviceTypes": [{"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 5, "communicationCategory": ["AVO", "UVL"], "deviceTypes": [{"displayName": "Actuator Control", "deviceType": "actuatorControl"}, {"displayName": "Damper Control", "deviceType": "damperControl"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "thyristor"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}, {"displayName": "EC Fan Unit", "deviceType": "ecfan"}]}, {"portNumber": 6, "communicationCategory": ["AVO", "UVL"], "deviceTypes": [{"displayName": "Actuator Control", "deviceType": "actuatorControl"}, {"displayName": "Damper Control", "deviceType": "damperControl"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "thyristor"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}, {"displayName": "EC Fan Unit", "deviceType": "ecfan"}]}]}, "communicationCategory": "MB"}, {"displayName": "Renu DIDO-14", "versions": ["v1"], "deviceType": "renudido14", "boardConfig": {"v1": [{"portNumber": 1, "communicationCategory": ["DF", "UVL"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 2, "communicationCategory": ["DF", "UVL"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 3, "communicationCategory": ["DF", "UVL"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 4, "communicationCategory": ["DF", "UVL"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 5, "communicationCategory": ["DF", "UVL"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 6, "communicationCategory": ["DF", "UVL"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 7, "communicationCategory": ["DF", "UVL"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 8, "communicationCategory": ["DF", "UVL"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 9, "communicationCategory": ["DR", "UVL"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 10, "communicationCategory": ["DR", "UVL"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 11, "communicationCategory": ["DR", "UVL"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 12, "communicationCategory": ["DR", "UVL"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 13, "communicationCategory": ["DR", "UVL"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 14, "communicationCategory": ["DR", "UVL"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}]}, "communicationCategory": "MB"}, {"displayName": "Renu FL001 0808R", "versions": ["v1"], "deviceType": "renudido16", "boardConfig": {"v1": [{"portNumber": 1, "communicationCategory": ["DF", "UVL"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 2, "communicationCategory": ["DF", "UVL"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 3, "communicationCategory": ["DF", "UVL"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 4, "communicationCategory": ["DF", "UVL"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 5, "communicationCategory": ["DF", "UVL"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 6, "communicationCategory": ["DF", "UVL"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 7, "communicationCategory": ["DF", "UVL"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 8, "communicationCategory": ["DF", "UVL"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 9, "communicationCategory": ["DR", "UVL"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 10, "communicationCategory": ["DR", "UVL"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 11, "communicationCategory": ["DR", "UVL"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 12, "communicationCategory": ["DR", "UVL"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 13, "communicationCategory": ["DR", "UVL"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 14, "communicationCategory": ["DR", "UVL"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 15, "communicationCategory": ["DR", "UVL"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 16, "communicationCategory": ["DR", "UVL"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}]}, "communicationCategory": "MB"}]}, {"vendorId": "n3uron", "displayName": "N3uron", "controllerType": "child", "controllers": [{"displayName": "N3uron-BACnet-MQTT", "versions": ["v1"], "deviceType": "n3uronbacnetmqtt", "boardConfig": {"v1": []}, "communicationCategory": "bacnet_to_mqtt"}]}, {"vendorId": "n3uronbacnetmqtt", "displayName": "N3uron BACnet mqtt", "controllerType": "parent", "controllers": [{"displayName": "Virtual-N3uron-BACnet-MQTT", "deviceType": "n3uronbacnetmqtt-controller", "versions": ["v1"], "boardConfig": {"v1": [{"portNumber": 1, "communicationCategory": ["AVI", "ACI", "UVL", "VFD", "UPS", "EM"], "deviceTypes": [{"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "UPSCONTROLLER Generic Driver", "deviceType": "upsController"}, {"displayName": "VRFCONTROLLER Generic Driver", "deviceType": "vrfController"}, {"displayName": "VIBRATIONSENSOR Generic Driver", "deviceType": "vibrationSensor"}, {"displayName": "UVLAMP Generic Driver", "deviceType": "uvLamp"}, {"displayName": "EM Generic Driver", "deviceType": "em"}]}]}}]}, {"vendorId": "smartjoules", "displayName": "Smart Joules", "controllerType": "child", "controllers": [{"displayName": "JouleLeafExtension2", "versions": ["v1"], "deviceType": "jouleleafext2", "boardConfig": {"v1": [{"portNumber": 8, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 9, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 10, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 11, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 12, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 13, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 14, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 15, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 16, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 17, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 18, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 19, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 20, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 21, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 22, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 23, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 27, "communicationCategory": ["AVI"], "deviceTypes": [{"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}]}, {"portNumber": 28, "communicationCategory": ["AVI"], "deviceTypes": [{"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}]}, {"portNumber": 29, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 30, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 31, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 32, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 33, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 34, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 35, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 36, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 45, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 46, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 47, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 48, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 49, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 50, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 51, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 52, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}]}, "communicationCategory": "NMB"}, {"displayName": "JouleLeafExtension1", "versions": ["v1"], "deviceType": "jouleleafext1", "boardConfig": {"v1": [{"portNumber": 2, "communicationCategory": ["AVO"], "deviceTypes": [{"displayName": "Actuator Control", "deviceType": "actuatorControl"}, {"displayName": "Damper Control", "deviceType": "damperControl"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "thyristor"}, {"displayName": "EC Fan Unit", "deviceType": "ecfan"}]}, {"portNumber": 3, "communicationCategory": ["AVO"], "deviceTypes": [{"displayName": "Actuator Control", "deviceType": "actuatorControl"}, {"displayName": "Damper Control", "deviceType": "damperControl"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "thyristor"}, {"displayName": "EC Fan Unit", "deviceType": "ecfan"}]}, {"portNumber": 4, "communicationCategory": ["AVO"], "deviceTypes": [{"displayName": "Actuator Control", "deviceType": "actuatorControl"}, {"displayName": "Damper Control", "deviceType": "damperControl"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "thyristor"}, {"displayName": "EC Fan Unit", "deviceType": "ecfan"}]}, {"portNumber": 5, "communicationCategory": ["AVO"], "deviceTypes": [{"displayName": "Actuator Control", "deviceType": "actuatorControl"}, {"displayName": "Damper Control", "deviceType": "damperControl"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "thyristor"}, {"displayName": "EC Fan Unit", "deviceType": "ecfan"}]}, {"portNumber": 6, "communicationCategory": ["AVO"], "deviceTypes": [{"displayName": "Actuator Control", "deviceType": "actuatorControl"}, {"displayName": "Damper Control", "deviceType": "damperControl"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "thyristor"}, {"displayName": "EC Fan Unit", "deviceType": "ecfan"}]}, {"portNumber": 7, "communicationCategory": ["AVO"], "deviceTypes": [{"displayName": "Actuator Control", "deviceType": "actuatorControl"}, {"displayName": "Damper Control", "deviceType": "damperControl"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "thyristor"}, {"displayName": "EC Fan Unit", "deviceType": "ecfan"}]}, {"portNumber": 8, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}]}, {"portNumber": 9, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}]}, {"portNumber": 10, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}]}, {"portNumber": 11, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}]}, {"portNumber": 12, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}]}, {"portNumber": 13, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}]}, {"portNumber": 14, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}]}, {"portNumber": 15, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}]}, {"portNumber": 16, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}]}, {"portNumber": 17, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}]}, {"portNumber": 18, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}]}, {"portNumber": 19, "communicationCategory": ["DR"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}]}, {"portNumber": 27, "communicationCategory": ["AVI"], "deviceTypes": [{"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}]}, {"portNumber": 28, "communicationCategory": ["AVI"], "deviceTypes": [{"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}]}, {"portNumber": 29, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 30, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 31, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 32, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 33, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 34, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 35, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 36, "communicationCategory": ["ACI"], "deviceTypes": [{"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}]}, {"portNumber": 45, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 46, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 47, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 48, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 49, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 50, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 51, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}, {"portNumber": 52, "communicationCategory": ["DF"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}]}]}, "communicationCategory": "NMB"}]}, {"vendorId": "masibus", "displayName": "Masibus", "controllerType": "child", "controllers": [{"displayName": "Masibus DO-16", "versions": ["v1"], "deviceType": "masibusdo16", "boardConfig": {"v1": [{"portNumber": 1, "communicationCategory": ["DR", "UVL"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 2, "communicationCategory": ["DR", "UVL"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 3, "communicationCategory": ["DR", "UVL"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 4, "communicationCategory": ["DR", "UVL"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 5, "communicationCategory": ["DR", "UVL"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 6, "communicationCategory": ["DR", "UVL"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 7, "communicationCategory": ["DR", "UVL"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 8, "communicationCategory": ["DR", "UVL"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 9, "communicationCategory": ["DR", "UVL"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 10, "communicationCategory": ["DR", "UVL"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 11, "communicationCategory": ["DR", "UVL"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 12, "communicationCategory": ["DR", "UVL"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 13, "communicationCategory": ["DR", "UVL"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 14, "communicationCategory": ["DR", "UVL"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 15, "communicationCategory": ["DR", "UVL"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 16, "communicationCategory": ["DR", "UVL"], "deviceTypes": [{"displayName": "Start/Stop", "deviceType": "relay"}, {"displayName": "Pulsating-Start", "deviceType": "pulsating<PERSON><PERSON>lay"}, {"displayName": "Pulsating-Stop", "deviceType": "pulsatingstoprelay"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}]}, "communicationCategory": "MB"}, {"displayName": "Masibus AI-8", "versions": ["v1"], "deviceType": "masibusai8", "boardConfig": {"v1": [{"portNumber": 1, "communicationCategory": ["AVI", "ACI", "RTD", "UVL"], "deviceTypes": [{"displayName": "ET Vibration Sensor", "deviceType": "vibrationSensor"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 2, "communicationCategory": ["AVI", "ACI", "RTD", "UVL"], "deviceTypes": [{"displayName": "ET Vibration Sensor", "deviceType": "vibrationSensor"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 3, "communicationCategory": ["AVI", "ACI", "RTD", "UVL"], "deviceTypes": [{"displayName": "ET Vibration Sensor", "deviceType": "vibrationSensor"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 4, "communicationCategory": ["AVI", "ACI", "RTD", "UVL"], "deviceTypes": [{"displayName": "ET Vibration Sensor", "deviceType": "vibrationSensor"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 5, "communicationCategory": ["AVI", "ACI", "RTD", "UVL"], "deviceTypes": [{"displayName": "ET Vibration Sensor", "deviceType": "vibrationSensor"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 6, "communicationCategory": ["AVI", "ACI", "RTD", "UVL"], "deviceTypes": [{"displayName": "ET Vibration Sensor", "deviceType": "vibrationSensor"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 7, "communicationCategory": ["AVI", "ACI", "RTD", "UVL"], "deviceTypes": [{"displayName": "ET Vibration Sensor", "deviceType": "vibrationSensor"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 8, "communicationCategory": ["AVI", "ACI", "RTD", "UVL"], "deviceTypes": [{"displayName": "ET Vibration Sensor", "deviceType": "vibrationSensor"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Belimo 22DC-13", "deviceType": "ductCO2Sensor"}, {"displayName": "Actuator <PERSON>", "deviceType": "actuatorFeedback"}, {"displayName": "<PERSON><PERSON>", "deviceType": "damperFeedback"}, {"displayName": "Flow Sensor", "deviceType": "flowMeter"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "Pressure Sensor", "deviceType": "pressureSensor"}, {"displayName": "Temperature Sensor", "deviceType": "temperatureSensor"}, {"deviceType": "floatLevelTransmitterSensor", "displayName": "Float Level Transmitter Sensor"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "humiditySensor"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}]}, "communicationCategory": "MB"}, {"displayName": "Masibus AO-8", "versions": ["v1"], "deviceType": "masibusao8", "boardConfig": {"v1": [{"portNumber": 1, "communicationCategory": ["AVO", "UVL"], "deviceTypes": [{"displayName": "Actuator Control", "deviceType": "actuatorControl"}, {"displayName": "Damper Control", "deviceType": "damperControl"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "thyristor"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}, {"displayName": "EC Fan Unit", "deviceType": "ecfan"}]}, {"portNumber": 2, "communicationCategory": ["AVO", "UVL"], "deviceTypes": [{"displayName": "Actuator Control", "deviceType": "actuatorControl"}, {"displayName": "Damper Control", "deviceType": "damperControl"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "thyristor"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}, {"displayName": "EC Fan Unit", "deviceType": "ecfan"}]}, {"portNumber": 3, "communicationCategory": ["AVO", "UVL"], "deviceTypes": [{"displayName": "Actuator Control", "deviceType": "actuatorControl"}, {"displayName": "Damper Control", "deviceType": "damperControl"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "thyristor"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}, {"displayName": "EC Fan Unit", "deviceType": "ecfan"}]}, {"portNumber": 4, "communicationCategory": ["AVO", "UVL"], "deviceTypes": [{"displayName": "Actuator Control", "deviceType": "actuatorControl"}, {"displayName": "Damper Control", "deviceType": "damperControl"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "thyristor"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}, {"displayName": "EC Fan Unit", "deviceType": "ecfan"}]}, {"portNumber": 5, "communicationCategory": ["AVO", "UVL"], "deviceTypes": [{"displayName": "Actuator Control", "deviceType": "actuatorControl"}, {"displayName": "Damper Control", "deviceType": "damperControl"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "thyristor"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}, {"displayName": "EC Fan Unit", "deviceType": "ecfan"}]}, {"portNumber": 6, "communicationCategory": ["AVO", "UVL"], "deviceTypes": [{"displayName": "Actuator Control", "deviceType": "actuatorControl"}, {"displayName": "Damper Control", "deviceType": "damperControl"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "thyristor"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}, {"displayName": "EC Fan Unit", "deviceType": "ecfan"}]}, {"portNumber": 7, "communicationCategory": ["AVO", "UVL"], "deviceTypes": [{"displayName": "Actuator Control", "deviceType": "actuatorControl"}, {"displayName": "Damper Control", "deviceType": "damperControl"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "thyristor"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}, {"displayName": "EC Fan Unit", "deviceType": "ecfan"}]}, {"portNumber": 8, "communicationCategory": ["AVO", "UVL"], "deviceTypes": [{"displayName": "Actuator Control", "deviceType": "actuatorControl"}, {"displayName": "Damper Control", "deviceType": "damperControl"}, {"displayName": "Test Device", "deviceType": "testDeviceOutput"}, {"displayName": "VFD", "deviceType": "vfd"}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "thyristor"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}, {"displayName": "EC Fan Unit", "deviceType": "ecfan"}]}]}, "communicationCategory": "MB"}, {"displayName": "Masibus DI-16", "versions": ["v1"], "deviceType": "masibusdi16", "boardConfig": {"v1": [{"portNumber": 1, "communicationCategory": ["DF", "UVL"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 2, "communicationCategory": ["DF", "UVL"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 3, "communicationCategory": ["DF", "UVL"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 4, "communicationCategory": ["DF", "UVL"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 5, "communicationCategory": ["DF", "UVL"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 6, "communicationCategory": ["DF", "UVL"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 7, "communicationCategory": ["DF", "UVL"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 8, "communicationCategory": ["DF", "UVL"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 9, "communicationCategory": ["DF", "UVL"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 10, "communicationCategory": ["DF", "UVL"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 11, "communicationCategory": ["DF", "UVL"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 12, "communicationCategory": ["DF", "UVL"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 13, "communicationCategory": ["DF", "UVL"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 14, "communicationCategory": ["DF", "UVL"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 15, "communicationCategory": ["DF", "UVL"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}, {"portNumber": 16, "communicationCategory": ["DF", "UVL"], "deviceTypes": [{"displayName": "<PERSON><PERSON>", "deviceType": "relayFeedback"}, {"displayName": "UV Lamp", "deviceType": "uvLamp"}]}]}, "communicationCategory": "MB"}]}]