/**
 * Maintenance.js
 *
 * @description :: A model definition represents a database table/collection.
 * @docs        :: https://sailsjs.com/docs/concepts/models-and-orm/models
 */

module.exports = {

	"attributes": {
		"cid": {
			"type": "string",
			"primaryKey": "hash",
			"required": true
		},
		"status": {
			"type": "string",
		},
		"type": {
			"type": "string",
		},
		"summary": {
			"type": "string",
		},
		"description": {
			"type": "string",
		},
		"creation_time": {
			"type": "string",
		},
		"resolved_time": {
			"type": "string",
		},
		"observed_at": {
			"type": "string"
		},
		"issueid": {
			"type": "string",
		},
		"repoter": {
			"type": "string",
		},
		"accountable": {
			"type": "Array",
		},
		"notify": {
			"type": "Array",
		},
		"priority_site": {
			"type": "string",
		},
		"priority_dev": {
			"type": "string",
		},
		"site": {
			"type": "string",
		},
		"Real": {
			"type": "string",
		},
		"Virtual": {
			"type": "string",
		},
		"due_date": {
			"type": "string",
		},
		"expected_resolve": {
			"type": "string",
		},
	}
};
