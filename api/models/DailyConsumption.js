/**
 * DailyConsumption.js
 *
 * @description :: TODO: You might write a short summary of how this model works and what it represents here.
 * @docs        :: http://sailsjs.org/documentation/concepts/models-and-orm/models
 */

module.exports = {
	"attributes": {
		"siteId": {
			"type": "string",
			"primaryKey": "hash",
		},
		"actual": {
			"type": "integer",
		},
		"actualkwh": {
			"type": "integer",
		},
		"target": {
			"type": "integer",
		},
		"benchmark": {
			"type": "integer",
		},
		"timestamp": {
			"type": "string",
			"primaryKey": "range",
		},
		"production_shiftA": {
			"type": "json",
		},
		"production_shiftB": {
			"type": "json",
		},
		"production_shiftC": {
			"type": "json",
		},
		"actual_units": {
			"type": "string"
		}
	},
};
