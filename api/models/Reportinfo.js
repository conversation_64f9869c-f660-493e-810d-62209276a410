/**
 * Reportinfo.js
 *
 * @description :: A model definition represents a database table/collection.
 * @docs        :: https://sailsjs.com/docs/concepts/models-and-orm/models
 */

module.exports = {
	"attributes": {
		"timestamp": {
			"type": "string",
			"primaryKey": "range"
		},
		"siteId": {
			"type": "string",
			"primaryKey": "hash"
		},
		"analyst": {
			"type": "string",
		},
		"analystComments": {
			"type": "string",
		},
		"analystMail": {
			"type": "string",
		},
		"clientMail": {
			"type": "string",
		},
		"comments": {
			"type": "string",
		},
		"duration": {
			"type": "string",
		},
		"fileName": {
			"type": "string",
		},
		"lastModified": {
			"type": "string",
		},
		"pdfUrl": {
			"type": "string",
		},
		"senton": {
			"type": "string",
		},
		"status": {
			"type": "string",
		}
	},

};

