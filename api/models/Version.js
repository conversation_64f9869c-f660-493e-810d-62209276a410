/**
 * Version.js
 *
 * @description :: A model definition represents a database table/collection.
 * @docs        :: https://sailsjs.com/docs/concepts/models-and-orm/models
 */

module.exports = {

	"attributes": {
		"version": {
			"type": "string",
			"primaryKey": "hash",
			"required": true
		},
		"deviceType": {
			"type": "string",
			"primaryKey": "range",
			"required": true
		},
		"hardwareVer": {
			"type": "string"
		},
		// JouleSense Specific Keys
		"gitTag": {
			"type": "string"
		},
		// Controller Specific Keys
		"gitRepoCICD": {
			"type": "string",
		},
		"gitRepoApplication": {
			"type": "string",
		},
		"gitRepoFirmware": {
			"type": "string",
		},
		"gitRepoHostServices": {
			"type": "string",
		},
		"gitRepoJouleBox": {
			"type": "string",
		},
		"gitRepoRoutingService": {
			"type": "string",
		},
		"gitRepod2rs": {
			"type": "string",
		},
		"dockerApplication": {
			"type": "string",
		},
		"dockerFirmware": {
			"type": "string",
		},
		"dockerJouleBox": {
			"type": "string",
		},
		"repoList": {
			"type": "Array",
		}
	},

};

