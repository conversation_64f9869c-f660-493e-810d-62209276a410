/**
 * Unrepaired.js
 *
 * @description :: TODO: You might write a short summary of how this model works and what it represents here.
 * @docs        :: http://sailsjs.org/documentation/concepts/models-and-orm/models
 */

module.exports = {

	"attributes": {
		"uuid": {
			"type": "string",
			"primaryKey": "hash"
		},
		"resolved": { // userId_ts who resolved and when
			"type": "string",
			"primaryKey": "range"
		},
		"siteId": {
			"index": "secondary",
			"type": "string",
		},
		"parentType": {
			"type": "string",
		},
		"data": {
			"type": "string"
		},
		"deviceMap": {
			"type": "string" // proper parent to child hashmap
		},
		"createdBy": {
			// who created/reopened this issue
			"type": "string"
		},
		"comment": {
			"type": "string"
		},
		"attachments": {
			"type": "string"
		},
		"timeline": {
			"type": "string" // positive negative number showing delay 
		}

	}
};

