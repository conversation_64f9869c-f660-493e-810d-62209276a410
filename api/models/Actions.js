/**
 * Actions.js
 *
 * @description :: TODO: You might write a short summary of how this model works and what it represents here.
 * @docs        :: http://sailsjs.org/documentation/concepts/models-and-orm/models
 */

module.exports = {

	"attributes": {
		"triggerTime": {
			// time of execution
			"type": "string",
			"primaryKey": "hash"
		},
		"sourceId": {  // this is more like commandId

			// uniqCmd from JT = site_2238.cmd
			// uniqCmd from JR = site_alertId/2238.cmd/rId (rId here will be recipe to execute, in sourceInfo the rid will be recipe who asked to run this recipe )

			// in case of schedules scheduling recipe this will be site_scheduleId

			// site_alertId/cmdId in case of recipe sei comming , else site_did.cmd
			"type": "string",
			"primaryKey": "range",
		},
		"source": {
			// recipe/server/pid
			"type": "string",
		},
		"sourceInfo": {
			// in case of server cmds site_userId of user, else site_recipeId, dont know how to handle PID here, maybe
			// only when this action is given by recipe this will be site_recipeId
			// in case of schedule this will be site_userId
			"type": "string",
			"index": "secondary"
		},
		"uniqId": {
			// uuid to uniquely identify command and update it
			"type": "string",
			"index": "uniqIdIndex-hash"
		},
		"type": {
			// (alert/command/recipe=trigger other recipe)
			// on bases of this, info is opened
			"type": "string"
		},
		"response": {
			// gets timestamp when action is done, false not done, -1 no feedback(default -1)
			"type": "string"
		},
		"runOn": {
			// "server/controller",
			"type": "string"
		},
		"isInitiated": {
			// true/false tells if this command is already sent to execution or not, just to avoid normal command and scheduler command collide
			"type": "string"
		},
		"rcCode": {
			// Code recieved from controller about the state
			"type": "string"
		},
		"nest": {
			"type": "string"

		},
		"reachedJb": {
			// 0 or timestamp
			"type": "string"

		},
		"reachedCtrl": {
			//  0 ot ts
			"type": "string"

		},
		"info": {
			// now on bases of tyoe this is json accordingly
			"type": "string"
		},
		"siteId": {
			"type": "string"
		}

	}
};

