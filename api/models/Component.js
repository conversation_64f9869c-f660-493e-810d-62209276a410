/**
 * Component.js
 *
 * @description :: TODO: You might write a short summary of how this model works and what it represents here.
 * @docs        :: http://sailsjs.org/documentation/concepts/models-and-orm/models
 */

module.exports = {

	"attributes": {

		"deviceId": {
			"type": "string", //<site_id+_+no.of commponents> eg ssh_2 ssh_43
			"primaryKey": "hash",
		},
		"siteId": {
			"type": "string",
			"primaryKey": "range"
		},
		"name": {
			"type": "string"
		},
		"data": {
			"type": "json",
		},
		"controls": {
			"type": "json"
		},
		"type": {
			"type": "string"
		},
		"deviceType": {
			"type": "string"
		},
		"mode": {
			"type": "string"
		},
		"regionId": {
			"type": "string"
		},
		"ns1": {
			"type": "string"
		},
		"info": {
			"type": "json"
		},
		"driverType": {
			"type": "string",
		},
		"controllerId": {
			"type": "string"

		},
		"url": {
			"type": "string"
		},
		"pidOn": {
			"type": "string"
		},
		"chemicalsPrize": {
			"type": "string"
		},
		"isVirtualComponent":{
			"type":"string"
		},
		"svgId": {
			"type": "string"
		},
		"deviceParameterList": {
			"type": "array",
		},
    "isInMaintenanceMode":{
      "type": "string",
      "defaultsTo": '0',
    }
	}
};
