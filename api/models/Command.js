/**
 * Controls.js
 *
 * @description :: TODO: You might write a short summary of how this model works and what it represents here.
 * @docs        :: http://sailsjs.org/documentation/concepts/models-and-orm/models
 */

module.exports = {

  attributes: {
    timestamp: {
      type: 'string',
      primaryKey: 'range',
    },
    deviceId: {
      type: 'string',
      primaryKey: 'hash',
    },
    componentId: {
      type: 'string',
    },
    siteId: {
      type: 'string',
      index: 'secondary',
    },
    user: {
      type: 'string',
    },
    value: {
      type: 'string',
    },
    type: {
      type: 'string', // StartStop || SetValue
    },
    param: {
      type: 'string',
    },
    socketID: {
      type: 'string', // 0 for default value and ts for savedTime
    },
    reachedController: {
      type: 'integer',
    },
    executed: {
      type: 'integer',
    },
    mode: {
      type: 'string',
    },
    selectedJB: {
      type: 'string',
    },
    IPAddr: {
      type: 'string',
    },
    commandError: {
      type: 'integer',
    },
    uniqId: {
      type: 'string',
    },
    component_abbr:{
      type: 'string',
    },
  },
};
