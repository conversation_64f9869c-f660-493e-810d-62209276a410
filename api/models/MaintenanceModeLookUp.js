/**
 * MaintenanceLookUp.js
 *
 * @description :: A model definition represents a database table/collection.
 * @docs        :: https://sailsjs.com/docs/concepts/models-and-orm/models
 */

module.exports = {

	"attributes": {

		"Gid": {
			"type": "string",
			"primaryKey": "hash", 
			"required": true
		},
		"status": {
			"type": "string",
			"required": true
		},
		"type": {
			"type": "string",
			"required": true
		},
		"timestamp": {
			"type": "string",
			"primaryKey": "range",
			"required": true
		},
		"cid": {
			"type": "string",
			"required": true
		},
	},

};
