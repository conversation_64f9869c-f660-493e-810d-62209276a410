module.exports = {
	"attributes": {
		"siteId": {
			"type": "string",
			"primaryKey": "hash"
		},
		"deviceId_abbr": {
			"type": "string",
			"primaryKey": "range"
		},
		"abbr": {
			"type": "string"
		},
		"deviceId": {
			"type": "string"
		},
		"index": "string",
		"driver": "string",
		"mulFactor": "string",
		"offset": "string",
		"min": "string",
		"max": "string",
		"displayName": "string",
		"unit": "string",
		"address": "string",
		"inheritedFrom": "string",
		"utilityType": "string",
		"mode": "string",
		"errOffset": "string",
		"paramGroup": "string",
		"filter_existence": "string",
		"filter_oldVal": "string",
		"filter_variance": "string",
		"statePreference": "string",
		"functionCode": "string",
		"lcdLabel": "string",
		"rawUnit": "string",
		"dau": "string",
		"operation": "string",
		"regType": "string",
		"tolerance": "string",
		"bitIndex": "string",
		"secDriver": "string",
		"isBatch": "string",
		"batch": "string",
		"batchDelayInMilliSeconds": "string",
		"componentId": "string",
		'decimalPrecision': 'number',
	}
};
