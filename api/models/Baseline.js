/**
 * Baseline.js
 *
 * @description :: TODO: You might write a short summary of how this model works and what it represents here.
 * @docs        :: http://sailsjs.org/documentation/concepts/models-and-orm/models
 */

module.exports = {


	"attributes": {
		"siteId": {
			"type": "string",
			"primaryKey": "hash",
		},
		"startDate": {
			"required": true,
			"primaryKey": "range",
			"type": "string"
		},
		"endDate": {
			"required": true,
			"type": "string"
		},
		"baselineValue": {
			"type": "float"
		},
		"target": {
			"type": "float",
		},
		"consumptionValue": {
			"type": "float"
		}
	}

};

