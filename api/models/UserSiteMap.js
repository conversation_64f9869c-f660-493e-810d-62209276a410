/**
 * UserSiteMap.js
 *
 * @description :: TODO: You might write a short summary of how this model works and what it represents here.
 * @docs        :: http://sailsjs.org/documentation/concepts/models-and-orm/models
 */

module.exports = {

	"attributes": {
		"userId": {
			"primaryKey": "hash",
			"type": "string"
		},
		"siteId": {
			"primaryKey": "range",
			"type": "string"
		},
		"role": {
			"type": "string"
		},
		"djNotif": {
			"type": "string"
		},
		"mailConfig": {
			"type": "string"
		},
		"msgConfig": {
			"type": "string"
		},
		"dj": {
			"type": "string"
		},
		"unitPreference": {
			"type": "string"
		},
		"phone": {
			"type": "string"
		},
		"consumptionPageEMList": {
			"type": "array"
		},
		"status": {
			"type": "integer",
			"defaultsTo": 1
		}
	}
};

