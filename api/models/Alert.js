/**
 * Alert.js
 *
 * @description :: TODO: You might write a short summary of how this model works and what it represents here.
 * @docs        :: http://sailsjs.org/documentation/concepts/models-and-orm/models
 */

module.exports = {

	"attributes": {
		
		"siteId": {
			"type": "string",
			"primaryKey": "hash"
		},
		"timestamp": {
			"type": "string",
			"primaryKey": "range"
		},
		"eventId": {
			"type": "string",
			"index": "secondary"
		},
		"sub_category": {
			"type": "string",
		},
		"readStatus": {
			"type": "string"
		},
		"body": {
			"type": "string"
		},
		"title": {
			"type": "string"
		},
		"description": {
			"type": "string"
		},
		"alertType": {
			"type": "string"
		},
		"priority": {
			"type": "string"
		},
		"status": {
			"type": "string"
		}
	}
};

