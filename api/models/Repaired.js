/**
 * Repaired.js
 *
 * @description :: TODO: You might write a short summary of how this model works and what it represents here.
 * @docs        :: http://sailsjs.org/documentation/concepts/models-and-orm/models
 */

module.exports = {

	"attributes": {
		"did": {
			"type": "string",
			"primaryKey": "hash"
		},
		"uuid": {
			"type": "string",
			"primaryKey": "range"
		},
		"siteId": {
			"type": "string",
		},
		"data": {
			"type": "string",
		},
		"parent": {
			"type": "string",
		},
		"attachments": {
			"type": "string",
		},
		"comment": {
			"type": "string",
		},
		"deadline": {
			"type": "integer", // ts in unix of deadline
		},
		"createdBy": {
			"type": "string", // who created/reopened this issue
		},
		"reopened": {
			"type": "string",
		}

    
	}
};

