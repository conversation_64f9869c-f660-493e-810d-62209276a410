/**
 * Queriedparameters.js
 *
 * @description :: TODO: You might write a short summary of how this model works and what it represents here.
 * @docs        :: http://sailsjs.org/documentation/concepts/models-and-orm/models
 */

module.exports = {

	"attributes": {
		"siteId": {
			"type": "string",
			"primaryKey": "hash",
		},
		"processId": {
			"type": "string",
			"primaryKey": "range",
		},
		"offsetData": {
			"type": "json"
		}
	},
};

