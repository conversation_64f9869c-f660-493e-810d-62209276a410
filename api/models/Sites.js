/**
 * Sites.js
 *
 * @description :: TODO: You might write a short summary of how this model works and what it represents here.
 * @docs        :: http://sailsjs.org/documentation/concepts/models-and-orm/models
 */

module.exports = {

  attributes: {
    siteId: {
      type: 'string',
      primaryKey: 'hash',
    },
    siteName: {
      type: 'string',
    },
    socketId: {
      type: 'string',
    },

    city: {
      type: 'string',

    },
    latitude: {
      type: 'string',
    },
    longitude: {
      type: 'string',
    },
    networkCount: {
      type: 'integer',
    },
    regionCount: {
      type: 'integer',

    },
    networks: {
      type: 'json',
    },
    network: {
      type: 'json',
    },
    regions: {
      type: 'json',
    },
    map: {
      type: 'json',
    },
    controller: {
      type: 'string',
    },
    isJouleBoxConnected: {
      type: 'integer',
    },
    areas: {
      type: 'json',
    },
    planningDocUrl: {
      type: 'string',
    },
    unitCost: {
      type: 'number',
    },
    status: {
      type: 'integer',
      defaultsTo: 1,
    },
    listPublicData() {
      const siteObj = this.toObject();
      // delete siteObj.network;
      // delete siteObj.regions;
      delete siteObj.createdAt;
      delete siteObj.updatedAt;
      return siteObj;
    },
    consumptionUnit: {
      type: 'string',
    },
    circuitCount: {
      type:'integer'
    },
    industryType: {
      type: 'string',
    },
    timezone: {
      type: 'string',
      required: true,
    },
  },
};
