const adapter = require("../../adapter/index");
const schema = {
	"primaryKey": "processId",
	"tableName": "processes",
	"attributes": {
		"processId": {
			"type": "string",
			"primaryKey": "hash",
			"description": "hash",
		},
		"siteId": {
			"type": "string",
			"description": "global-secondary##type",
			"index": "siteId_type_global_index-hash",
		},
		"type": {
			"type": "string",
			"index": "siteId_type_global_index-range",
		},
		"name": {
			"type": "string",
		},
		"componentType": {
			"type": "string",
		},
		"deviceType": {
			"type": "string",
		},
		"rawParams": {
			"type": "json",
			"columnType": "array",
		},
		"queriedParams": {
			"type": "json",
			"columnType": "array",
		},
		"calcParams": {
			"type": "json",
			"columnType": "array",
		},
		"plantParams": {
			"type": "json",
			"columnType": "array",
		},
		"plantDevices": {
			"type": "json",
			"columnType": "array",
		},
		"components": {
			"type": "json",
			"columnType": "array",
		},
		"controls": {
			"type": "json",
			"columnType": "array",
		},
		"isDisplayed": {
			"type": "boolean",
		},
		"childProcesses": {
			"type": "json",
			"columnType": "array",
		},
		"parentProcess": {
			"type": "string",
		},
		"regionId": {
			"type": "string",
		},
		"controllerId": {
			"type": "string",
		},
		"driverType": {
			"type": "string",
		},
		"plantCategories": {
			"type": "json",
			"columnType": "stringSet",
		},
		"aggregatedParams": {
			"type": "json",
			"columnType": "array",
		}
	},
};
adapter.addSchema(schema);
schema.create = function(query) {
	return adapter.create(schema.tableName, query);
};
schema.find = function(query) {
	return adapter.find(schema.tableName, query);
};
schema.update = function(query, updates) {
	return adapter.update(schema.tableName, query, updates);
};
schema.destroy = function(query) {
	return adapter.destroy(schema.tableName, query);
};
module.exports = schema;
