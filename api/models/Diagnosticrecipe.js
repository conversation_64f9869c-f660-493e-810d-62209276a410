/**
 * Diagnosticrecipe.js
 *
 * @description :: TODO: You might write a short summary of how this model works and what it represents here.
 * @docs        :: http://sailsjs.org/documentation/concepts/models-and-orm/models
 */

module.exports = {

	"attributes": {
		"recipeId": {
			"type": "string",
			"primaryKey": "range"
		},
		"title": {
			"type": "string"
		},
		"description": {
			"type": "string"
		},
		"siteId": {
			"type": "string",
			"primaryKey": "hash"
		},
		"notify": {
			"type": "json"
		},
		"accountable": {
			"type": "json"
		},
		// Alert Type; 0 = Generic, 1 = Specific
		"alertType": {
			"type": "integer"
		},
		// 0 = Off, 1 = ON
		"status": {
			"type": "integer"
		},
		"logic": {
			"type": "string"
		},
		// Parameter mapping - from FrontEnd
		"paramMapping": {
			"type": "json"
		},
		// Generated by backend. Stores all information required to be fetched by the evaluator to execute the recipe. Also has the mapping of the placeholders in the parsedLogic.
		"logicParams": {
			"type": "json"
		},
		// Device List
		"devices": {
			"type": "json"
		},
		// Single deviceId associated with the recipe.
		"deviceId": {
			"type": "string"
		},
		"groupId": {
			"type": "string",
		},
		// Generated by backend. Replaces keywords in the logic with placeholders which can be recognized by the evaluator.
		"parsedLogic": {
			"type": "string"
		},
	}
};
