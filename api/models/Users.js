/**
 * Users.js
 *
 * @description :: TODO: You might write a short summary of how this model works and what it represents here.
 * @docs        :: http://sailsjs.org/documentation/concepts/models-and-orm/models
 */

//this is userTable for user login and his access permission
let bcrypt = require("bcryptjs");
module.exports = {
	"schema": true,
	"attributes": {
		"userId": {
			"primaryKey": "hash",
			"type": "string"
		},

		"userRole": {
			"type": "string",
			"enum": ["admin", "adm1n", "smartjoules employee", "smartjoules executive", "site operator", "executive"]
		},
		"userOrganization": {
			"primaryKey": "range",
			"type": "string"
		},
		"name": {
			"type": "string"
		},
		"address": {
			"type": "string"
		},
		"email": {
			"required": true,
			"type": "string"
		},
		"password": {
			"required": "true",
			"type": "string"
		},
		"phone": {
			"required": "true",
			"type": "string"
		},
		"picture": {
			"type": "string"
		},
		"policiesGroup": {
			"type": "string"
		},
		"defaultSite": {
			"type": "string"
		},
		"designation": {
			"type": "string"
		},
		"accountable": {
			"type": "string"
		},
		"notify": {
			"type": "string"
		},
        
		"toJSON": function() {
			let obj = this.toObject();
			delete obj.password;
			delete obj.createdAt;
			delete obj.updatedAt;
			return obj;
		}

	},
	"beforeCreate": function(values, next) {
		bcrypt.genSalt(10, function(err, salt) {
			if (err) return next(err);
			bcrypt.hash(values.password, salt, function(err, hash) {
				if (err) return next(err);
				values.password = hash;
				next();
			});
		});
	},
	"beforeUpdate": function(values, next) {
		if (!values.password){
			next();
			return;
		}
		bcrypt.genSalt(10, function(err, salt) {
			if (err) return next(err);
			bcrypt.hash(values.password, salt, function(err, hash) {
				if (err) return next(err);
				values.password = hash;
				next();
			});
		});
	},

	"comparePassword": async function(password, user, cb) {
		if (!cb)
			return new Promise((resolve, reject)=>{
				bcrypt.compare(password, user.password, (err, match)=>{
					if (err)
						return reject(err);
					if (match)
						return resolve(true);
					else (!match);
					return resolve(false);
                    
				});

			});
		bcrypt.compare(password, user.password, function(err, match) {

			if (err) cb(err);
			if (match) {
				cb(null, true);
			} else {
				cb(err);
			}
		});
	}

};