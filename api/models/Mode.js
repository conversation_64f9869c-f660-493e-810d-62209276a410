/**
 * Modes.js
 *
 * @description :: TODO: You might write a short summary of how this model works and what it represents here.
 * @docs        :: http://sailsjs.org/documentation/concepts/models-and-orm/models
 */

module.exports = {

	"attributes": {
		"did": {
			"type": "string",
			"primaryKey": "hash",
		},
		"timestamp": {
			"type": "string",
			"primaryKey": "range",
		},
		"endTs": {
			"type": "string",
		},
		"siteId": {
			"type": "string",
		},
		"allModes": {
			"type": "json",
		},
		"changedMode": {
			"type": "string",
		},
		"currMode": {
			"type": "string",
		},
		"createdBy": {
			"type": "string",
		}
	}
};

