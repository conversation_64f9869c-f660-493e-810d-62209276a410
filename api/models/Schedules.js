/**
 * Schedules.js
 *
 * @description :: TODO: You might write a short summary of how this model works and what it represents here.
 * @docs        :: http://sailsjs.org/documentation/concepts/models-and-orm/models
 */

module.exports = {

	"attributes": {
		"sid": {
			"type": "string",
			"primaryKey": "hash"
		},
		"rid": {
			"type": "string",
			"primaryKey": "range"
		},
		"siteId": {
			"type": "string"
		},
		"runOn": {
			"type": "string"
		},
		"ts": {
			"type": "string",
		},
		"schedule": {
			"type": "string",
		},
		"reachedJB": {
			"type": "string",
		},
		"repeat_type": {
			"type": "string",
		},
		"UpdateSch": {
			"type": "string"
		},
		"isDeployed": {
			"type": "string"
		}
	}
};
