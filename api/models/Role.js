/**
 * Role.js
 *
 * @description :: TODO: You might write a short summary of how this model works and what it represents here.
 * @docs        :: http://sailsjs.org/documentation/concepts/models-and-orm/models
 */

module.exports = {

	"attributes": {
      
		"roleName": {
			// roleName :  executive_sjo
			"type": "string",
			"primaryKey": "hash"
		},
		"policies": {
			"type": "string",
		},
		"defpref": {
			"type": "string",
		},
		"isDeleted": {
			"type": "string"
		},
		"policiesBE":{
			"type": "string"
		},
		"toJSON": function() {
			let obj = this.toObject();
			delete obj.isDeleted;
			delete obj.createdAt;
			delete obj.updatedAt;
			return obj;
		}

      
	},

};

