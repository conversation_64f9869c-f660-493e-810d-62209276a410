/**
 * DeviceType.js
 *
 * @description :: TODO: You might write a short summary of how this model works and what it represents here.
 * @docs        :: http://sailsjs.org/documentation/concepts/models-and-orm/models
 */

module.exports = {

	"attributes": {
		"deviceType": {
			"type": "string",
			"primaryKey": "hash"
		},
		"driverType": {
			"type": "string",
			"primaryKey": "range"
		},
		"driverName": {
			"type": "string"
		},
		"params": {
			"type": "json"
		},
		"parameters": {
			"type": "array"
		},
		"communicationCategory": {
			"type": "string"
		},
		"communicationType": {
			"type": "string"
		},
		"class": {
			"type": "string"
		},
		"svgIds": {
			"type": "array"
		},
		"mbBatchReadEnable": {
			"type": "string"
		  },
	}
};