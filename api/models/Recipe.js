/**
 * Recipe.js
 *
 * @description :: TODO: You might write a short summary of how this model works and what it represents here.
 * @docs        :: http://sailsjs.org/documentation/concepts/models-and-orm/models
 */

module.exports = {

	"attributes": {

		"rid": {
			"type": "string",
			"primaryKey": "hash",
		},
		"label": {
			"type": "string"
		},
		"alwaysRun": {
			"type": "string",
		},
		"neo": {
			"type": "string",
		},
		"operator": {
			"type": "string"
		},
		"actionable": {
			"type": "string"
		},
		"everyMinuteTopics": {
			"type": "string"
		},
		"params": {
			"type": "string"
		},
		"siteId": {
			"type": "string",
			"primaryKey": "range",
		},
		"dependentOnOthers": {
			"type": "string",
		},
		"runOn": {
			"type": "string",
		},
		"priority": {
			"type": "string",
		},
		"switchOff": {
			"type": "string",
		},
		"notRun": {
			"type": "string",
		},
		"userId": {
			"type": "string"
		},
		"oldObservable": {
			"type": "string"
		},
		"scheduled": {
			"type": "string"
		},
		"ReachedJb": {
			"type": "string"
		},
		"schedule": {
			"type": "string"
		},
		"isSchedule": {
			"type": "string"
		},
		"rtype": {
			"type": "string"
		},
		"isAbstract": {
			"type": "string"
		},
		"type": {
			"type": "string"
		},
		"UpdateSch": {
			"type": "string"
		},
		"recipelabel": {
			"type": "string"
		},
		"currState": {
			"type": "string"
		},
		"isActive": {
			"type": "string"
		},
		"isStage": {
			"type": "string"
		},
		"maxLogNeeded": {
			"type": "string"
		},
		"maxDataNeeded": {
			"type": "string"
		},
		"formula": {
			"type": "string"
		},
		"user": {
			"type": "string"
		},
		"runInterval": {
			"type": "string"
		},
		"componentsType": {
			"type": "string"
		},
		'appType': {
			'type': 'string'
		},
		'misc': {
			'type': 'string'
		},
		'componentId': {
			'type': 'string'
		},
		'attachments': {
			'type': 'json',
		  },
	}
};

