const moment = require("moment-timezone");
moment.tz.setDefault("Asia/Kolkata");
moment.defaultFormat = "YYYY-MM-DD HH:mm:ss";
const dashboardUtil = require("../utils/dashboardUtil");
/**
@module dashboardController
@description Collection of API Handler functions handling requests to all routes concerned with the Dashboard service.
*/
module.exports = {
	/**
	 * @function dashboardController.consumption
	 * @param {Object} req
	 * @param {string} req.params.siteId
	 * @param {string} req.params.plantId processId for which consumption is to be calculted. For a collection of all the plants use "all"
	 * @param {string} req.query.start time in format YYYY-MM-DD HH:mm:ss
	 * @param {string} req.query.end time tn format YYYY-MM-DD HH:mm:ss
	 * @param {string} req.query.duration [day|week|month]
	 * If duration is provided the start and end will be automatically generated
	 * In any case start or end is provided with duration then start and end have priority and can result in undesirable outputs. So always avoid start and end. Default value of duaration is hour
	 * @param {string} req.query.deviceTypes [em] comma seperated list of deviceTypes to query for consumption, defaults to em
	 * @param {string} req.query.category [electricity|steam] parameter which should be used for calculating consumption depends on this values.
	 * For example For parameter electricity the parameters being queries are kvah and kwh. Default value of category is electricity
	 * @param {string} req.query.display [chart|table] determines the response format, default value is chart. For chart the response is returned in decreasing order of the consumption
	 * @param {number} req.query.limit works only for display type of chart, limits the numbers of records returned
	 * @param {Object} res
	 */
	"consumption": async function(req, res) {
		let { siteId, plantId } = req.params;
		let unitPref = req._userMeta.unitPref;
		if (!siteId) {
			return res.badRequest({
				"err": "Missing Parameters",
			});
		}
		try {
			const util = dashboardUtil.consumption;
			let conumptionPreferredUnit, mainMeters = [];
			const {
				timeObj,
				deviceTypes,
				limit,
				category,
				display,
			} = util.fillQueryElements(req.query);
			const plants = await dashboardService.getPlantInfo(
				siteId,
				plantId,
				["processId", "plantDevices", "plantParams", "rawParams"]
			);
			
			if (category === "steam") {
				conumptionPreferredUnit = "conssteam";
			} else {
				conumptionPreferredUnit = unitPref.cons ? unitPref.cons : "kwh"; // default for consumption is kwh.. IN future use a service to get
				// preferred unit of that site instead of hardcoding it kwh..
			}

			const plantIds = plants.map(plant => plant.processId);
			let deviceIds = util.getAllDeviceIds(plants, deviceTypes);

			if (display === "chart" && limit !== -1) {
				plants.forEach(plant => {
					mainMeters = mainMeters.concat(util.getMainMeterFromProcess(plant));
				});				
				deviceIds = deviceIds
					.filter(deviceId => !mainMeters.includes(deviceId));
			}
			const $deviceConsumptionData = dashboardService.getConsumption(
				deviceIds,
				timeObj,
				category,
				display
			);
			const $plantConsumption = dashboardService.getConsumption(
				plantIds,
				timeObj,
				category,
				display
			);
			const consumptionData = [];
			const consumptionVal = [];
			const $consArr = [$deviceConsumptionData, $plantConsumption];
			for (let index in $consArr) {
				const $cons = $consArr[index];
				const dataArr = await Promise.all($cons);
				const normalizedConsumption = dataArr.map(data =>
					util.normalize(data, category, conumptionPreferredUnit)
				);
				const value = util.sumConsumption(normalizedConsumption);
				consumptionData.push(normalizedConsumption);
				consumptionVal.push(value);
			}
			let responseObj;
			if (display === "table") {
				responseObj = util.prepareTable(consumptionData[0]);
			} else {
				//keys others,unaccounted to be used for cha
				responseObj = util.sortConsumption(
					consumptionData[0],
					limit,
					category
				);
				const [deviceCons, plantCons] = consumptionVal;
				// const otherConsumption = helper.returnFilteredNumber(plantCons - deviceCons);
				// if (
				// 	!isNaN(otherConsumption) &&
				// 	otherConsumption &&
				// 	otherConsumption > 0
				// ) {
				// 	responseObj.push({
				// 		"value": otherConsumption,
				// 		"display": "Unaccounted",
				// 		"deviceId": "unaccounted",
				// 		"unit": dashboardUtil.UNIT_MAPPER[conumptionPreferredUnit]
				// 	});
				// }
				if (limit === -1) {  // send devices if limit is -1.. No slicing needed
					responseObj = responseObj.sort((val1, val2) => val2.value - val1.value);
				} else {
					responseObj = responseObj.sort((val1, val2) => val2.value - val1.value).slice(0, limit);
				}
			}
			return res.ok({"data": responseObj});
		} catch (err) {
			sails.log.error(err);
			return res.serverError();
		}
	},
	/**
	 * @function dashboardController.consumptionExcel
	 * @description Generates an excel sheet for consumption data, specifically for RAMA.
	 * @param {object} req  
	 */
	"consumptionExcel": async function(req, res){
		let { siteId, plantId } = req.params;
		if (!siteId || !plantId) {
			return res.badRequest({
				"err": "Missing Parameters",
			});
		}
		
		try {
			const util = dashboardUtil.consumptionExcel;
			const { start, end } = req.query;
			if (!start || !end){
				return res.badRequest("Either 'start' or 'end' missing from query parameters");
			}
			const timeObj = util.generateTimeObj(req.query);

			let $devices = Devices.find({ siteId });
			let $process = processService.find({ "processId": plantId });
			let  process = await $process;
			if (process.length === 0){
				return res.badRequest({ "error": "plantId not found in processes" });
			}
			process = process[0];
			const emList = process.plantDevices["em"];
			const devices = await $devices;
			const emObjects = devices.filter(device => emList.includes(device.deviceId));

			// Getting Consumption data based on time range and device list.
			let $consumptionData =  util.getConsumption(emList, timeObj);

			// Generating device tree
			let deviceTree = util.generateDeviceTree(emObjects);
			let consumptionData = await $consumptionData;
			let simplifiedConsumptionData = util.simplify(consumptionData, deviceTree);
			const responseData = await util.generateExcel(deviceTree, simplifiedConsumptionData);
			res.setHeader("Content-disposition", "attachment;"); 
			res.contentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
			// helper.saveMockData(consumptionData, "consumptionData");
			return res.send(responseData);
		} catch (error) {
			sails.log.error("[dashboard >> consumptionExcel] Error!", error);
			return res.serverError();
		}
	},
};
