/**
 * DiagnosticsController
 *
 * @description :: Server-side logic for managing diagnostics module
 * @help        :: See http://sailsjs.org/#!/documentation/concepts/Controllers
 */

const axios = require("axios");
const moment = require("moment-timezone");
moment.tz.setDefault("Asia/Kolkata");
const uuidv1 = require("uuid/v1");
const uuid = require("uuid");
const AWS = require("aws-sdk");
AWS.config.update({ region: process.env?.REGION, logger: console });
const elasticsearch = require("elasticsearch");
const awsHttpClient = require("http-aws-es");
const userService = require("../services/userService");
let client = elasticsearch.Client({
  "host": "search-sj-elasticsearchcluster-w3v6acfblmbzq47k2k5x2aukqy.us-west-2.es.amazonaws.com",
  "connectionClass": awsHttpClient,
});

module.exports = {
  /**
   * @function CICDFeedback
   * @summary JouleSense CICD script on Joulebox used to call this route. To be deleted. Temporarily changing the name of the API. Delete if you are reading this after November.
   * <AUTHOR> <PERSON> <<EMAIL>>
   */
  "_CICDFeedback": function(req, res) {
    if (req.body.code == "37" || req.body.code == "38")
      return this.diagnosticFeedback(req, res);
    if (!req.body.siteId) {
      sails.log.error("[CICDFeedback] siteId not mentioned!");
      return res.badRequest("SiteId not mentioned!");
    }
    let siteId = req.body.siteId;
    sails.sockets.broadcast(siteId, "diagnostic", req.body);
    res.ok();
  },

  /**
   * <AUTHOR> <<EMAIL>>
   * @name updateToNewVersion
   * @returns object list of Devices which started updated
   * @summary Used to Update Multiple firmware using CICD v3 of Controllers and Joule Sense
   * @description This API helps to filter devices on the basis of device type selected and to generate the corresponding payload to publish it. So, that CICD v3 can perform the upgradation process.
   */
  "updateToNewVersion": async (req, res) => {

    if (!req.body.arr || !req.body.version || !req.body.deviceType)
      res.badRequest("Either deviceType OR version OR arr not mentioned.");

    let { arr, version, deviceType, siteId, hardReboot } = req.body;
    let socketId = req._userMeta._h_;
    let deviceAllData = arr;
    let jouleSenseList = [];
    let deviceList = [];
    let nonJouleSenseList = [];
    let responseArray = [];
    let deviceAlreadyUpdating = [];
    let hardwareVer;
    // Filtering devices
    for (let i in deviceAllData) {
      switch (deviceAllData[i].deviceType) {
        case "joulestat":
        case "joulelogger":
        case "jouleiocontrol":
        case "joulemomo":
        case "joulebox":
          nonJouleSenseList.push(deviceAllData[i]);
          break;
        case "joulesense":
          jouleSenseList.push(deviceAllData[i]);
          break;
        default:
          sails.log(`This deviceType is not handeled: ${deviceAllData[i].deviceType} of deviceId: ${deviceAllData[i].deviceId}`);
          break;
      }
    }
    // Generating Payloads on the basis of their deviceType
    switch (deviceType) {
      case "controller":
        for (let i in nonJouleSenseList) {
          await Devices.update({
            "deviceId": nonJouleSenseList[i].deviceId,
            "siteId": siteId,
          }).set({
            "expectedVersion": version,
          });
          await diagnosticService.updateAPICallBack(nonJouleSenseList[i],
            version,
            socketId,
            hardReboot,
            responseArray,
            deviceAlreadyUpdating);
        }
        break;
      case "joulesense":
        // TODOI: Setting hardware version as the first element found in the selected array. To confirm.
        if (jouleSenseList.length == 0) break;
        else hardwareVer = jouleSenseList[0].hardwareVer;

        switch (hardwareVer) {

          // For hardware version v1
          case undefined:
          case "v1":
            if (req.body.toSkip === undefined || !req.body.commandControllerId) {
              return res.badRequest("toSkip flag OR commandControllerId not passed.");
            }
            for (let i in deviceAllData) {
              if (deviceAllData[i].deviceType == deviceType) {
                deviceList.push(deviceAllData[i].deviceId);
                responseArray.push(deviceAllData[i]);
              }
            }
            deviceList = deviceList.join(",");
            diagnosticService.generatePayloadForJouleSense(req.body.commandControllerId, version, deviceList, req.body.toSkip, socketId);
            break;

          case "v2": {
            let result = await diagnosticService.fetchJouleSenseVersion(version, deviceType), gitTag;
            if (!result.status) {
              return res.serverError(result);
            } else gitTag = result.version;
            jouleSenseList.forEach(device => {
              diagnosticService.setExpectedVersion(device, version);
              diagnosticService.publishJouleSenseV2Payload(device, gitTag, socketId);
              responseArray.push(device);
            });
            break;
          }
        }
        break;
    }
    if (responseArray.length == 0) {
      res.ok({
        "message": "Already Upto date",
        "arr": responseArray,
        "inUpdate": deviceAlreadyUpdating,
      });
    } else {
      res.ok({
        "message": "Update request sent to controller",
        "arr": responseArray,
        "inUpdate": deviceAlreadyUpdating,
      });
    }

  },

  "updateFirmware": function(req, res) {
    let deviceType, arrSelected, socketId, deviceList = [], payload = {}, topic = "", optionString = "", responseArray = [];
    // Validating required parameters
    if (!req.body.siteId || !req.body.deviceType || !req.body.arr) {
      return res.badRequest({ "error": "Either siteId OR deviceType OR arr(selected elements) not mentioned." });
    } else {
      // siteId = req.body.siteId;
      deviceType = req.body.deviceType;
      arrSelected = req.body.arr;
      socketId = req._userMeta._h_;
    }

    switch (deviceType) {
      case "joulesense":
        // Checking for joulesense specific parameters
        if (!req.body.version || req.body.toSkip == undefined || !req.body.commandControllerId) {
          return res.badRequest({ "error": "version OR toSkip flag not passed." });
        }
        for (let i in arrSelected) {
          if (arrSelected[i].deviceType == deviceType) {
            deviceList.push(arrSelected[i].deviceId);
            responseArray.push(arrSelected[i]);
          }
        }
        deviceList = deviceList.join(",");

        topic = "command/" + req.body.commandControllerId + "/cicd";
        // Preparing payload packet
        payload = {
          "param": "firmware",
          "value": {
            "version": req.body.version,
            "deviceList": deviceList,
            "toSkip": req.body.toSkip,
          },
          "key": socketId,
          "operation": "js_update",
        };
        payload = JSON.stringify(payload);
        eventService.publish(topic, payload);
        break;
      default:
        // Checking controller specific parameters.
        if (!req.body.version || !req.body.repo || req.body.skipGitRepo == undefined) {
          return res.badRequest({ "error": "Either verion OR repo OR option not mentioned." });
        }
        optionString = req.body.skipGitRepo ? "skip" : "clone";
        for (let i in arrSelected) {
          if (arrSelected[i].deviceType == deviceType) {
            deviceList.push(arrSelected[i].deviceId);
            responseArray.push(arrSelected[i]);
          }
        }
        // Preparing payload packet
        payload = {
          "param": "firmware",
          "value": {
            "repo": req.body.repo,
            "version": req.body.version,
            "option": optionString,
            "softReboot": req.body.softReboot,
            "hardReboot": req.body.hardReboot,
            "dockerPull": req.body.dockerPull,
            "dockerParams": req.body.dockerParams,
          },
          "key": socketId,
          "operation": "jioc_update",
        };
        payload = JSON.stringify(payload);
        for (let i in deviceList) {
          topic = "command/" + deviceList[i] + "/cicd";
          eventService.publish(topic, payload);
        }
        break;
    }
    res.ok({
      "message": "Update request sent to controller",
      "arr": responseArray,
    });
  },

  "updateManyFirmware": function(req, res) {
    try {
      let deviceType, arrSelected, socketId, deviceList = [], payload = {}, topic = "", optionString = "", responseArray = [];
      // Validating required parameters
      if (!req.body.siteId || !req.body.deviceType || !req.body.arr) {
        return res.badRequest({ "error": "Either siteId OR deviceType OR arr(selected elements) not mentioned." });
      } else {
        // siteId = req.body.siteId;
        deviceType = req.body.deviceType;
        arrSelected = req.body.arr;
        socketId = req._userMeta._h_;
      }
      let values = req.body.values;
      switch (deviceType) {
        case "joulesense":
          // Checking for joulesense specific parameters
          if (!req.body.version || req.body.toSkip == undefined || !req.body.commandControllerId) {
            return res.badRequest({ "error": "version OR toSkip flag not passed." });
          }
          for (let i in arrSelected) {
            if (arrSelected[i].deviceType == deviceType) {
              deviceList.push(arrSelected[i].deviceId);
              responseArray.push(arrSelected[i]);
            }
          }
          deviceList = deviceList.join(",");

          topic = "command/" + req.body.commandControllerId + "/cicd";
          // Preparing payload packet
          payload = {
            "param": "firmware",
            "value": {
              "version": req.body.version,
              "deviceList": deviceList,
              "toSkip": req.body.toSkip,
            },
            "key": socketId,
            "operation": "js_update",
          };
          payload = JSON.stringify(payload);
          eventService.publish(topic, payload);
          break;
        default:
          // Checking controller specific parameters.
          if (!req.body.values || req.body.hardReboot == undefined) {
            return res.badRequest({ "error": "hardReboot OR Value packet for all Repos missing." });
          }
          optionString = req.body.skipGitRepo ? "skip" : "clone";
          for (let i in arrSelected) {
            if (arrSelected[i].deviceType == deviceType) {
              deviceList.push(arrSelected[i].deviceId);
              responseArray.push(arrSelected[i]);
            }
          }
          // Preparing payload packet

          payload = {
            "param": "firmware",
            "values": [],
            "hardReboot": req.body.hardReboot,
            "key": socketId,
            "operation": "jioc_update",
          };

          for (let i in values) {
            if (!values[i].version || !values[i].repo || values[i].skipGitRepo == undefined || values[i].dockerPull == undefined || values[i].softReboot == undefined) {
              return res.badRequest({ "error": "Ether version OR repo OR skipGitRepo OR dockerPull OR softReboot missing in value packets." });
            }
            optionString = values[i].skipGitRepo ? "skip" : "clone";
            let valueObject = {
              "repo": values[i].repo,
              "version": values[i].version,
              "option": optionString,
              "softReboot": values[i].softReboot,
              "dockerPull": values[i].dockerPull,
              "dockerParams": values[i].dockerParams,
            };
            payload.values.push(valueObject);
          }

          payload = JSON.stringify(payload);
          for (let i in deviceList) {
            topic = "command/" + deviceList[i] + "/cicd";
            eventService.publish(topic, payload);
          }
          break;
      }
      res.ok({
        "message": "Update request sent to controller",
        "arr": responseArray,
      });
    } catch (error) {
      sails.log.error("[updateManyFirmware]: ", error);
      return res.serverError({ "error": "Server Error!" });
    }
  },
  // Populate diagnostics page. V2.
  "populateDiagnostics": async function(req, res) {
    let { siteId } = req.body;
    const userId = req._userMeta.id;

    try {
      // Checking for request parameters.
      if (!siteId) {
        sails.log.error("SiteId not passed.");
        return res.badRequest("No SiteId mentioned!");
      } else {
        siteId = req.body.siteId;
      }

      if (!await userService.userHasSiteAccess(userId, siteId)) {
        return res.badRequest("User does not have access to this site.");
      }

      let dictionary = {},
        countArr = {
          "areas": [],
          "regions": [],
          "networks": [],
          "controllers": [],
          "slaves": [],
          "components": [],
        },
        map = {
          "controllerToSlave": {},
          "componentToSlave": {},
          "componentToController": {},
          "noComponentToController": [],
          "networkToController": {},
          "noNetworkToController": [],
          "regionToController": {},
          "regionToComponent": {},
        },
        debug = {
          "noComponentToController": map.noComponentToController,
          "noNetworkToController": map.noNetworkToController,
          "noNetworkInDevice": [],
          "deviceTypeDefault": [],
        };
      // Fetching all devices.
      let devices = Devices.find({ "siteId": siteId });
      // Fetching all components
      let components = Component.find({ "siteId": siteId });

      // Fetching all sites.
      let siteInfo = Sites.find({ "siteId": siteId }).then(site => {
        site = site[0];
        // Populating all Areas.
        let areas = site.areas;
        // Saving all areas
        if (!areas) {
          areas = {};
        }
        countArr.areas = Object.keys(areas);

        for (let area in areas) {
          let areaObj = areas[area];
          let newObj = {
            "id": area,
            "childNodes": {
              "geo": areaObj.regions,
              "component": areaObj.regions,
              "network": -1,
            },
            "toShow": false,
            "layer": {
              "geo": 0,
              "component": 0,
              "network": -1,
            },
            "objectType": 4,
          };
          dictionary[area] = { ...areaObj, ...newObj };
        }
        // Populating all regions.
        let regions = site.regions;
        if (!regions)
          regions = {};
        // Saving all regions
        countArr.regions = Object.keys(regions);
        for (let region in regions) {
          let regionObj = regions[region];
          let newObj = {
            "id": region,
            "childNodes": {
              "geo": regionObj.controller,
              "component": -1,
              "network": -1,
            },
            "toShow": false,
            "layer": {
              "geo": 1,
              "component": 1,
              "network": -1,
            },
            "objectType": 3,

          };
          dictionary[region] = { ...regionObj, ...newObj };
        }
        return site;
      });

      // Fetching current JouleBox Location
      let jouleboxLocationPromise = DyanmoKeyStore.find({ "key": `${siteId}_jb` });

      components.then(components => {
        for (let i in components) {
          // Saving all components
          countArr.components.push(components[i].deviceId);
          // Mapping components to their regions
          if (!map.regionToComponent[components[i].regionId]) {
            map.regionToComponent[components[i].regionId] = [components[i].deviceId];
          } else {
            map.regionToComponent[components[i].regionId].push(components[i].deviceId);
          }

          // Adding all components to dictionary
          dictionary[components[i].deviceId] = components[i];
        }

      });

      devices.then(devices => {
        let tempNetworkObject = {};

        for (let i in devices) {
          // Saving all networks
          if (devices[i].networkId == undefined) {
            debug.noNetworkInDevice.push(devices[i]);
          } else {
            tempNetworkObject[devices[i].networkId] = true;
          }

          switch (devices[i].deviceType) {
            // Cases for controllers
            case "joulestat":
            case "joulelogger":
            case "jouleiocontrol":
            case "joulemomo":
            case "joulesense":
            case "joulebox":
              // Adding all controllers to countArr
              countArr.controllers.push(devices[i].deviceId);
              // Mapping controllers to their components
              if (devices[i].componentId == undefined) {
                map.noComponentToController.push(devices[i]);
              } else {
                if (!map.componentToController[devices[i].componentId]) {
                  map.componentToController[devices[i].componentId] = [devices[i].deviceId];
                } else {
                  map.componentToController[devices[i].componentId].push(devices[i].deviceId);
                }
              }
              // Mapping controllers to their networks
              if (!devices[i].networkId == undefined) {
                map.noNetworkToController.push(devices[i]);
              } else {
                if (!map.networkToController[devices[i].networkId]) {
                  map.networkToController[devices[i].networkId] = [devices[i].deviceId];
                } else {
                  map.networkToController[devices[i].networkId].push(devices[i].deviceId);
                }
              }
              // Mapping controllers to their regions
              if (!map.regionToController[devices[i].regionId]) {
                map.regionToController[devices[i].regionId] = [devices[i].deviceId];
              } else {
                map.regionToController[devices[i].regionId].push(devices[i].deviceId);
              }
              // Populating controllers in dictionary
              dictionary[devices[i].deviceId] = devices[i];
              break;
            // Cases for slaves
            case "actuatorControl":
            case "actuatorFeedback":
            case "em":
            case "relay":
            case "relayFeedback":
            case "temperatureSensor":
            case "vfd":
            case "humiditySensor":
            case "damperFeedback":
            case "pressureSensor":
            case "chillerController":
            case "outsideTmpHumSensor":
            case "flowMeter":
            case "temperatureHumiditySensor":
            case "ff":
            case "damperControl":
            case "btumeter":
            case "tdsMeter":
              // Adding all slaves to countArr
              countArr.slaves.push(devices[i].deviceId);
              // Mapping slaves to their controllers.
              if (!map.controllerToSlave[devices[i].controllerId]) {
                map.controllerToSlave[devices[i].controllerId] = [devices[i].deviceId];
              } else {
                map.controllerToSlave[devices[i].controllerId].push(devices[i].deviceId);
              }
              // Mapping slaves to their components.
              if (!map.componentToSlave[devices[i].componentId]) {
                map.componentToSlave[devices[i].componentId] = [devices[i].deviceId];
              } else {
                map.componentToSlave[devices[i].componentId].push(devices[i].deviceId);
              }
              // Populating slaves in the dictionary
              dictionary[devices[i].deviceId] = devices[i];
              break;
            default:
              debug.deviceTypeDefault.push(devices[i]);
              sails.log.error(`[populateDiagnostics] Error!: "${devices[i].deviceType}" not being filtered! Someone look into this. DebugInfo: ${JSON.stringify(devices[i])}`);
              break;
          }
        }
        // Saving all networks
        countArr.networks = Object.keys(tempNetworkObject);

      });

      siteInfo = await siteInfo;
      devices = await devices;
      components = await components;

      // Populating slaves properties.
      for (let i in countArr.slaves) {
        let slave = dictionary[countArr.slaves[i]];
        let newObj = {
          "id": slave.deviceId,
          "childNodes": {
            "geo": -1,
            "component": -1,
            "network": -1,
          },
          "toShow": false,
          "layer": {
            "geo": 3,
            "component": 4,
            "network": 2,
          },
          "objectType": 0,
        };
        dictionary[countArr.slaves[i]] = { ...slave, ...newObj };
      }
      // Populating controllers properties
      for (let i in countArr.controllers) {
        let controller = dictionary[countArr.controllers[i]];
        let newObj = {
          "id": controller.deviceId,
          "childNodes": {
            "geo": map.controllerToSlave.hasOwnProperty(controller.deviceId) ? map.controllerToSlave[controller.deviceId] : -1,
            "component": map.controllerToSlave.hasOwnProperty(controller.deviceId) ? map.controllerToSlave[controller.deviceId] : -1,
            "network": map.controllerToSlave.hasOwnProperty(controller.deviceId) ? map.controllerToSlave[controller.deviceId] : -1,
          },
          "toShow": false,
          "layer": {
            "geo": 2,
            "component": 3,
            "network": 1,
          },
          "objectType": 1,
        };
        // Adding remoteAccess keys
        if (!controller.hasOwnProperty("remoteAccess")) {
          newObj["remoteAccess"] = false;
          newObj["remoteAccessPort"] = diagnosticService.sshCommands(null);
        } else {
          if (controller.hasOwnProperty("remoteAccessPort")) {
            newObj["remoteAccessPort"] = diagnosticService.sshCommands(controller.remoteAccessPort);
          } else {
            newObj["remoteAccess"] = false;
          }
        }
        dictionary[controller.deviceId] = { ...controller, ...newObj };
      }
      // Populating components
      for (let i in countArr.components) {
        let component = dictionary[countArr.components[i]];
        let newObj = {
          "id": component.deviceId,
          "childNodes": {
            "geo": -1,
            "component": map.componentToSlave.hasOwnProperty(component.deviceId) ? map.componentToSlave[component.deviceId] : -1,
            "network": -1,
          },
          "toShow": false,
          "layer": {
            "geo": -1,
            "component": 2,
            "network": -1,
          },
          "objectType": 2,
        };
        dictionary[component.deviceId] = { ...component, ...newObj };
      }

      // Populating networks
      for (let i in countArr.networks) {
        let network = countArr.networks[i];
        let newObj = {
          "id": network,
          "name": network,
          "childNodes": {
            "geo": -1,
            "component": -1,
            "network": map.networkToController.hasOwnProperty(network) ? map.networkToController[network] : -1,
          },
          "toShow": false,
          "layer": {
            "geo": 0,
            "component": -1,
            "network": -1,
          },
          "objectType": 5,
        };
        dictionary[network] = newObj;
      }

      // Populating regions
      let regions = siteInfo.regions;
      for (let region in regions) {
        let regionObj = regions[region];
        let newObj = {
          "id": region,
          // "childNodes": regionObj.controller,
          "childNodes": {
            "geo": regionObj.controller,
            "component": map.regionToComponent.hasOwnProperty(region) ? map.regionToComponent[region] : -1,
            "network": -1,
          },
          "toShow": false,
          "layer": {
            "geo": 1,
            "component": 1,
            "network": -1,
          },
          "objectType": 3,
        };
        dictionary[region] = { ...regionObj, ...newObj };
      }


      // Parsing JouleBox Location from DyanmoKeyStore
      let retObjJouleBox = {};
      try {
        jouleboxLocationPromise = await jouleboxLocationPromise;
        let value;
        if (jouleboxLocationPromise.length != 0) value = helper.toJson(jouleboxLocationPromise[0].value);
        // Example of value
        // value { 'smartjoules-network-0': '100010,automatic' }
        Object.keys(value).forEach(key => {
          // Getting deviceId of the JouleBox Location
          let jouleBoxLocation = value[key].split(",")[0];
          retObjJouleBox[key] = jouleBoxLocation;
          dictionary[jouleBoxLocation]["isJouleBox"] = true;
        });
      } catch (error) {
        sails.log.error("[populateDiagnostics] Error finding joulebox location!! Error: \n");
        sails.log.error(error);
      }

      // Fixing response object
      let responseObject = {
        "dictionary": dictionary,
        "parentNodes": {
          "geo": countArr.areas,
          "component": countArr.areas,
          "network": countArr.networks,
        },
        "countArr": countArr,
        "jouleBoxLocation": retObjJouleBox,
      };

      res.ok(responseObject);
    } catch (error) {
      sails.log.error("error: ", error);
      sails.log.error("stack!:", error.stack);
    }
  },

  "_populateDiagnostics": function(req, res) {
    let siteId;
    if (!req.body.siteId) {
      sails.log.error("Site ID not posted.");
      return res.badRequest("No siteId mentioned.");

    } else {
      siteId = req.body.siteId;
    }
    let debugArr = [];
    let map = {
      "networks": {},
      "regions": {},
      "ns1": {},
      "ns2": {},
    };
    let retObj = {};
    let parentNodes = [], ns2List = [], ns1Dict = {};
    Devices.find({ "siteId": siteId })
      .then(devices => {
        try {
          sails.log.info(`Devices table returned ${devices.length} records for [populateDiagnostics].`);
          for (let i in devices) {
            switch (devices[i].deviceType) {
              // Grouping ns2s by ns1s
              case "actuator":
              case "relay":
              case "thermostat":
              case "chillerController":
              case "temperatureSensor":
              case "tempratureSensor":
              case "temperatureHumiditySensor":
              case "temprature_humiditySensor":
              case "tempratureHumidityLogger":
              case "em":
              case "vfd":
                if (!map.ns2[devices[i].networkSlave1]) {
                  map.ns2[devices[i].networkSlave1] = [devices[i]];
                } else {
                  map.ns2[devices[i].networkSlave1].push(devices[i]);
                }
                break;
              // Grouping ns1s by regions
              case "jouleio":
              case "joulesense":
              case "joulelogger":
                if (!map.ns1[devices[i].regionId])
                  map.ns1[devices[i].regionId] = [devices[i]];
                else
                  map.ns1[devices[i].regionId].push(devices[i]);
                // Adding Unique Regions
                if (!map.regions[devices[i].networkId]) {
                  map.regions[devices[i].networkId] = {};
                  map.regions[devices[i].networkId][devices[i].regionId] = true;
                } else
                  map.regions[devices[i].networkId][devices[i].regionId] = true;

                break;
              case "joulebox":
                if (!map.networks[devices[i].siteId])
                  map.networks[devices[i].siteId] = [devices[i]];
                else
                  map.networks[devices[i].siteId].push(devices[i]);
                break;
              default:
                debugArr.push(devices[i]);
                break;
            }
          }


          // Populating JouleBoxes:
          for (let i in map.networks[siteId]) {
            let joulebox = map.networks[siteId][i];
            let newObj = {
              "name": joulebox.name,
              "deviceType": joulebox.deviceType,
              "sv": "N/A",
              "hv": "N/A",
              "dqi": "N/A",
              "networkId": joulebox.networkId,
              "layer": 0,
              "deviceId": joulebox.deviceId,
              "toShow": false,
              "childNodes": [],
              "parentNode": null,
            };
            // maintenance flag
            if (joulebox.maintenanceMode === undefined)
              newObj.maintenanceMode = false;
            else
              newObj.maintenanceMode = joulebox.maintenanceMode;
            // child nodes
            for (let regionId in map.regions[joulebox.networkId]) {
              newObj.childNodes.push(regionId);
            }
            // Remote Access Key
            if (joulebox.hasOwnProperty("remoteAccess")) {
              newObj.remoteAccess = joulebox.remoteAccess;
              if (joulebox.remoteAccess == true) {
                if (joulebox.hasOwnProperty("remoteAccessPort")) {
                  let sshKeys = diagnosticService.sshCommands(joulebox.remoteAccessPort);
                  newObj["remoteAccessPort"] = { ...sshKeys };
                } else
                  newObj.remoteAccess = false;
              }
            } else {
              newObj.remoteAccess = false;
            }
            // Setting up return object
            retObj[joulebox.deviceId] = newObj;
            parentNodes.push(joulebox.deviceId);

          }
          // Populating regions
          for (let i in map.regions) {
            let regions = map.regions[i];
            for (let region in regions) {
              let newObj = {
                "name": region,
                "dqi": "N/A",
                "networkId": i,
                "deviceType": "region",
                "toShow": false,
                "layer": 1,
                "maintenanceMode": false,
                "deviceId": region,
                "childNodes": [],
                "parentNode": i,
              };
              let ns1Arr = map.ns1[region];
              for (let k in ns1Arr) {
                newObj.childNodes.push(ns1Arr[k].deviceId);
              }
              retObj[region] = newObj;
            }
          }
          // Populating NS1s
          for (let i in map.ns1) {
            for (let j in map.ns1[i]) {
              let ns1Obj = map.ns1[i][j];
              let newObj = {
                "name": ns1Obj.name,
                "dqi": "N/A",
                "sv": "N/A",
                "hv": "N/A",
                "networkId": ns1Obj.networkId,
                "networkSlave1": ns1Obj.networkSlave1,
                "deviceType": ns1Obj.deviceType,
                "regionId": ns1Obj.regionId,
                "layer": 2,
                "deviceId": ns1Obj.deviceId,
                "toShow": false,
                "childNodes": [],
                "parentNode": ns1Obj.regionId,
              };
              // maintenance flag
              if (ns1Obj.maintenanceMode === undefined)
                newObj.maintenanceMode = false;
              else
                newObj.maintenanceMode = ns1Obj.maintenanceMode;
              // adding joulesense specific keys
              if (ns1Obj.deviceType == "joulesense") {
                newObj.communicationCategory = ns1Obj.communicationCategory;
                newObj.driverType = ns1Obj.driverType;
                newObj.networkSlave2 = ns1Obj.networkSlave2;
              }
              // Adding JouleIOControl specific keys
              // Remote Access Key
              if (ns1Obj.deviceType == "jouleio") {
                if (ns1Obj.hasOwnProperty("remoteAccess")) {
                  newObj.remoteAccess = ns1Obj.remoteAccess;
                  if (ns1Obj.remoteAccess == true) {
                    if (ns1Obj.hasOwnProperty("remoteAccessPort")) {
                      let sshKeys = diagnosticService.sshCommands(ns1Obj.remoteAccessPort);
                      newObj["remoteAccessPort"] = { ...sshKeys };
                    } else
                      newObj.remoteAccess = false;
                  }
                } else {
                  newObj.remoteAccess = false;
                }
              }
              // Setting up child nodes
              let ns2Arr = map.ns2[ns1Obj.networkSlave1];
              for (let k in ns2Arr) {
                newObj.childNodes.push(ns2Arr[k].deviceId);
              }
              retObj[ns1Obj.deviceId] = newObj;
              if (!ns1Dict[ns1Obj.networkId])
                ns1Dict[ns1Obj.networkId] = {};
              ns1Dict[ns1Obj.networkId][ns1Obj.networkSlave1] = ns1Obj.deviceId;
            }
          }
          // Populating NS2s
          for (let i in map.ns2) {
            for (let j in map.ns2[i]) {
              try {
                let ns2Obj = map.ns2[i][j];
                let newObj = {
                  "name": ns2Obj.name,
                  "dqi": "N/A",
                  "networkId": ns2Obj.networkId,
                  "networkSlave1": ns2Obj.networkSlave1,
                  "networkSlave2": ns2Obj.networkSlave2,
                  "deviceType": ns2Obj.deviceType,
                  "regionId": ns2Obj.regionId,
                  "layer": 3,
                  "deviceId": ns2Obj.deviceId,
                  "communicationCategory": ns2Obj.communicationCategory,
                  "driverType": ns2Obj.driverType,
                  "childNodes": "-1",
                  "parentNode": ns1Dict[ns2Obj.networkId][ns2Obj.networkSlave1],
                  "toShow": false,
                };
                // Which mode the device is in
                if (ns2Obj.mode) {
                  try {
                    let mode, arr = [];
                    mode = JSON.parse(ns2Obj.mode);
                    for (let key in mode) {
                      let obj = {};
                      obj[key] = mode[key];
                      arr.push(obj);
                    }
                    newObj["mode"] = arr;
                  } catch (err) {
                    sails.log.error(err);
                    sails.log.error("Could not parse mode object from existing devices data.");
                    let mode = { "error": "Could not parse from existing back end data." };
                    newObj["mode"] = mode;
                  }
                } else newObj["mode"] = null;
                // maintenance flag
                if (ns2Obj.maintenanceMode === undefined)
                  newObj.maintenanceMode = false;
                else
                  newObj.maintenanceMode = ns2Obj.maintenanceMode;
                retObj[ns2Obj.deviceId] = newObj;
                ns2List.push(ns2Obj.deviceId);
              } catch (error) {
                sails.log.error(error);
              }
            }
          }
        } catch (error) {
          sails.log.error("[populateDiagnostics] Error!");
          sails.log.error(error);
        }
        let retPacket = {
          "dictionary": retObj,
          "parentNodes": parentNodes,
          "ns1Dict": ns1Dict,
          "ns2List": ns2List,
          "devices": devices,
        };
        res.ok(retPacket);
      });
  },

  "reqInfo": async function(req, res) {
    let siteId, arrSelected, socketId;
    // Checking request parameters:
    if (!req.body.arr || !req.body.siteId) {
      return res.badRequest({ "error": "No SiteID or Attay of selected elements mentioned." });
    } else {
      siteId = req.body.siteId;
      arrSelected = req.body.arr;
    }
    socketId = req._userMeta._h_;

    // Helper function
    function reqInfoRaspi(deviceId) {
      let topic = siteId + "/command/" + deviceId + "/diagnostic";
      let payload = {
        "param": "info",
        "value": null,
        "operation": "info",
        "ts": new Date(),
        "key": socketId,
        "socketId": socketId,
      };
      payload = JSON.stringify(payload);
      return eventService.publish(topic, payload);
    }

    function reqInfoJouleSense(deviceId, hardwareVer) {
      let topic = siteId + "/command/" + deviceId + "/diagnostic", payload;

      if (hardwareVer == undefined || hardwareVer == "v1") {
        payload = {
          "param": "info",
          "key": socketId,
        };
      } else if (hardwareVer == "v2") {
        payload = {
          "param": "info",
          "key": socketId,
          "socketId": socketId,
        };
      }

      payload = JSON.stringify(payload);
      return eventService.publish(topic, payload);
    }

    arrSelected.forEach(device => {
      switch (device.deviceType) {
        // Incase its a joulesense
        case "joulesense":
          reqInfoJouleSense(device.deviceId, device.hardwareVer);
          break;
        // Assuming it to be a raspi in every other case.
        default:
          reqInfoRaspi(device.deviceId);
          break;
      }
    });

    res.ok({ "message": "Info request sent." });
  },

  "restartDevice": function(req, res) {
    let siteId, arrSelected, socketId;
    if (!req.body.siteId || !req.body.arr) {
      return res.badRequest({ "error": "No siteId or selected elements array mentioned." });
    } else {
      siteId = req.body.siteId;
      arrSelected = req.body.arr;
    }
    socketId = req._userMeta._h_;

    // Helper functions
    function restartRasPi(deviceId) {
      let topic = siteId + "/command/" + deviceId + "/diagnostic";
      let payload = {
        "param": "hardReboot",
        "value": "null",
        "operation": "hardReboot",
        "ts": new Date(),
        "key": socketId,
      };
      payload = JSON.stringify(payload);
      return eventService.publish(topic, payload);
    }

    function restartJouleSense(deviceId, hardwareVer) {
      let topic = siteId + "/command/" + deviceId + "/diagnostic", payload;

      if (hardwareVer == undefined || hardwareVer == "v1") {
        payload = {
          "param": "restart",
          "key": socketId,
        };
      } else if (hardwareVer == "v2") {
        payload = {
          "param": "restart",
          "key": socketId,
          "socketId": socketId,
        };
      }
      payload = JSON.stringify(payload);
      return eventService.publish(topic, payload);
    }

    arrSelected.forEach(device => {
      switch (device.deviceType) {
        case "joulesense":
          restartJouleSense(device.deviceId, device.hardwareVer);
          break;
        default:
          restartRasPi(device.deviceId);
          break;
      }
    });

    res.ok({ "message": "Restart request sent." });
  },

  "recalibrateSensor": function(req, res) {
    let siteId, arrSelected, socketId;
    if (!req.body.siteId || !req.body.arr) {
      return res.badRequest({ "error": "No siteId or selected elements array mentioned." });
    } else {
      siteId = req.body.siteId;
      arrSelected = req.body.arr;
    }
    socketId = req._userMeta._h_;
    for (let i in arrSelected) {
      let topic = siteId + "/command/" + arrSelected[i].deviceId + "/diagnostic", payload = "", hardwareVer = arrSelected[i].hardwareVer;
      if (hardwareVer == undefined || hardwareVer == "v1") {
        payload = {
          "param": "recab",
          "key": socketId,
        };
      } else if (hardwareVer == "v2") {
        payload = {
          "param": "recab",
          "key": socketId,
          "socketId": socketId,
        };
      }
      payload = JSON.stringify(payload);
      return eventService.publish(topic, payload);
    }
  },

  "testQueryAthena": async function(req, res) {
    let data;
    try {
      let queryResponse = await athenaService.startQuery("SELECT * FROM \"joulestest\".\"datadevicestemp\" limit 4000;");
      let queryId = queryResponse.QueryExecutionId;
      sails.log.info("queryResponse: ", queryResponse);
      sails.log.info("Waiting for Query to finish executing:");
      await athenaService.waitForQueryToComplete(queryId);
      sails.log.info("Query finished executing! Fetching results.");
      data = await athenaService.getQueryResults(queryId);
      sails.log.info("Results!:");
      sails.log.info(data);

    } catch (error) {
      sails.log.info(error);
    }
    res.ok(data);
  },

  "testDiagnostics2": async function(req, res) {
    sails.log("testDiagnostics2 was called!");
    let data = await MaintenanceMode.find({
      "cid": "281445a0-fb0b-11e9-9699-035e83eab406",
    });
    let response = await diagnosticService.sendMaintenanceAlert(data[0], true);

    sails.log("[testDiagnostics2] data: ", response);
    res.ok(response);
  },

  "testDiagnostics": async function(req, res) {
    sails.log("testDiagnostics was called!");
    try {

      res.ok();
    } catch (err) {
      sails.log.error(err);
      res.serverError({
        "status": false,
        "message": "Server Error!",
      });
    }
  },

  "diagnosticFeedback": function(req, res) {
    let obj = req.body, siteId, i, j, postObj = {};

    if (!obj.siteId) {
      sails.log.error("ERROR! diagnosticFeedback called without mentioning siteId! Not broadcasting socket.");
      return res.ok();
    } else {
      siteId = obj.siteId;
    }

    if (!obj.code || !obj.networkId) {
      postObj.code = 0;
      postObj.feedback = "API called without code or networkId.";
      sails.log.error("API called without code or networkId.");
      sails.sockets.broadcast(obj.siteId, "diagnostic", postObj);
      return res.ok();
    } else {
      postObj["code"] = obj.code;
      postObj["networkId"] = obj.networkId;
    }

    switch (obj.code) {
      case "36":
        // Reboot successfully after updating.
        break;
      case "37":
        // Reboot randomly.
        break;
      case "38":
        // JB_onReboot Script error
        // Removing the remoteAccess port if it exists.
        Devices.update({
            "deviceId": obj.deviceId,
            "siteId": obj.siteId,
          },
          {
            "remoteAccess": false,
            "remoteAccessPort": null,
          })
          .then(data => {
          })
          .catch(err => {
            sails.log("Error while updating Device info after JB restarted: ", err);
          });
        sails.sockets.broadcast(siteId, "diagnostic", req.body);
        break;
      case "41":
        // getInfo
        for (i in obj.obj) {
          switch (i) {
            case "JIOC":
            case "JIOS":
              if (!postObj.jouleio)
                postObj["jouleio"] = [];
              for (j in obj.obj[i]) {
                let tempObj = {
                  "deviceType": i,
                  "networkSlave1": j,
                  "ip": obj.obj[i][j].ip,
                  "networkStrength": obj.obj[i][j].networkstrength,
                };
                postObj["jouleio"].push(tempObj);
              }
              break;
            case "JS":
              if (!postObj.joulesense)
                postObj["joulesense"] = [];
              for (j in obj.obj[i]) {
                let tempObj = {
                  "deviceType": "joulesense",
                  "networkSlave1": j,
                  "ip": obj.obj[i][j].ip,
                  "networkStrength": obj.obj[i][j].networkstrength,
                };
                postObj["joulesense"].push(tempObj);
              }
              break;
          }
        }
        sails.sockets.broadcast(siteId, "diagnostic", postObj);
        break;
      case "42":
      case "43":
        // restartDevice
        sails.sockets.broadcast(siteId, "diagnostic", obj);
        break;
      case "44":
        break;
      case "45": {
        // Reverse SSH start feedback socket broadcast
        let cmdObj = diagnosticService.sshCommands(obj.obj.port);
        obj = { ...obj, ...cmdObj };
        sails.sockets.broadcast(siteId, "diagnostic", obj);
        // Updating Dynamo entry in Devices, adding "remoteAccess : true"
        Devices.update(
          { "deviceId": obj.obj.deviceId, "siteId": obj.siteId },
          { "remoteAccess": true, "remoteAccessPort": obj.obj.port })
          .then(data => {
            // sails.log('Data after updating:',data);
          })
          .catch(err => {
            // sails.log('Error!!:', err)
          });
        break;
      }
      case "46":
        // Reverse SSH stop feedback socket broadcast
        // sails.log('No feedback socket broadcast! obj: ', obj)
        sails.sockets.broadcast(siteId, "diagnostic", obj);
        // Updating Dynamo entry in Devices, adding "remoteAccess : false"
        Devices.update(
          { "deviceId": obj.obj.deviceId, "siteId": obj.siteId },
          { "remoteAccess": false, "remoteAccessPort": null })
          .then(data => {
            // sails.log('Data after updating:',data);
          })
          .catch(err => {
            // sails.log('Error!!:', err)
          });
        break;
      case "47":
        // Reverse SSH start failed
        // sails.log('Reverse SSH start failed. obj:', obj);
        sails.sockets.broadcast(siteId, "diagnostic", obj);
        break;
      default:
        break;
    }
    res.ok();
  },

  // Required parameters

  "maintenanceMode": function(req, res) {
    let i, promiseArr = [], deviceList, siteId, maintenanceMode, timestamp;
    timestamp = moment();

    // Checking for FrontEnd Input
    if (req.body.maintenanceMode === undefined || !req.body.deviceList || !req.body.siteId) {
      res.badRequest({
        "err": "No maintenance flag OR deviceList OR siteId mentioned.",
      });
    } else {
      deviceList = req.body.deviceList;
      maintenanceMode = req.body.maintenanceMode;
      siteId = req.body.siteId;
    }
    // Fetching devices
    for (i in deviceList) {
      let tempPromise = Devices.find({
        "deviceId": deviceList[i],
        "siteId": siteId,
      })
        .then(async device => {
          device = device[0];
          try {
            if (device.maintenanceStart === undefined)
              device.maintenanceStart = [];
            else
              device.maintenanceStart = JSON.parse(device.maintenanceStart);
            if (device.maintenanceEnd === undefined)
              device.maintenanceEnd = [];
            else
              device.maintenanceEnd = JSON.parse(device.maintenanceEnd);
          } catch (err) {
            sails.log.error("[maintenanceMode] Error parsing maintenanceStart or maintenanceEnd");
            sails.log.error(err);
            return Promise.reject({
              "deviceId": device.deviceId,
              "status": false,
              "message": "Error parsing maintenanceStart or maintenanceEnd",
            });
          }
          if (device.maintenanceMode == maintenanceMode) {
            return {
              "deviceId": device.deviceId,
              "status": true,
            };
          } else {
            if (maintenanceMode == true) {
              device.maintenanceMode = maintenanceMode;
              if (device.maintenanceStart.length != device.maintenanceEnd.length) {
                sails.log.error("Inconsistent maintenanceStart and maintenanceEnd lengths while trying to enter start timestamp.");
                return {
                  "deviceId": device.deviceId,
                  "status": false,
                  "message": "Inconsistent maintenanceStart and maintenanceEnd lengths while trying to enter start timestamp.",
                };
              }
              device.maintenanceStart.push(timestamp.format("YYYY-MM-DD HH:mm:ss"));
            } else {
              // If maintenanceMode flag doesn't exist
              if (device.maintenanceMode === undefined) {
                device.maintenanceMode = false;
                device.maintenanceStart = JSON.stringify(device.maintenanceStart);
                device.maintenanceEnd = JSON.stringify(device.maintenanceEnd);
                await Devices.update({
                  "deviceId": device.deviceId,
                  "siteId": siteId,
                }, device);
                return {
                  "deviceId": device.deviceId,
                  "status": true,
                };
              }
              device.maintenanceMode = maintenanceMode;
              if (device.maintenanceStart.length - device.maintenanceEnd.length != 1) {
                sails.log.error("Inconsistent maintenanceStart and maintenanceEnd lengths while trying to enter end timestamp.");
                return {
                  "deviceId": device.deviceId,
                  "status": false,
                  "message": "Inconsistent maintenanceStart and maintenanceEnd lengths while trying to enter end timestamp.",
                };
              }
              device.maintenanceEnd.push(timestamp.format("YYYY-MM-DD HH:mm:ss"));
            }
            device.maintenanceStart = JSON.stringify(device.maintenanceStart);
            device.maintenanceEnd = JSON.stringify(device.maintenanceEnd);
            await Devices.update({
              "deviceId": device.deviceId,
              "siteId": siteId,
            }, device);
            return {
              "deviceId": device.deviceId,
              "status": true,
            };
          }
        })
        .catch(err => {
          sails.log.error("[maintenanceMode] Error in base promise!");
          sails.log.error(err);
        });
      promiseArr.push(tempPromise);
    }
    Promise.all(promiseArr).then(data => {
      sails.log.info("Promise Array after all have ended.");
      sails.log.info(data);
      return res.ok({
        "message": "All records updated or returned error.",
        "data": data,
      });
    });
  },

  "remoteAccess": function(req, res) {
    // sails.log('remoteAccess Called!');
    // sails.log(req.body);
    // Checking for parameters
    let deviceId, option, socketId;
    if (!req.body.siteId || !req.body.deviceId || req.body.option == undefined) {
      return res.badRequest({ "err": "Either siteId OR deviceId OR option not mentioned." });
    } else {
      // siteId = req.body.siteId;
      deviceId = req.body.deviceId;
      option = req.body.option ? true : false;
    }
    socketId = req._userMeta._h_;
    let topicOld = "command/" + deviceId + "/cicd";
    let topicNew = "global/command/" + deviceId + "/cicd";
    let payload = {
      "param": "reverse_ssh",
      "value": option,
      "key": socketId,
      "ts": new Date().getTime(),
      "operation": "reverse_ssh",
    };
    payload = JSON.stringify(payload);
    // eventService.publish(topic, payload);
    eventService.publish(topicOld, payload);
    eventService.publish(topicNew, payload);
    res.ok({ "message": "Remote Access Command sent to controller." });
  },

  "gitFetchTags": function(req, res) {

    // Checking for arguments
    if (!req.body.siteId || !req.body.repository) {
      sails.log.error("[gitFetchTags] siteId OR repository not mentioned!");
      return res.badRequest("siteId OR repository not mentioned!");
    }

    let token, requestConfig, url, payload = {};
    // let siteId = req.body.siteId;
    token = sails.config.credentials.gitFetchTagsToken;
    requestConfig = {
      "headers": {
        "Authorization": "token " + token,
      },
    };
    url = "https://api.github.com/repos/SmartJoules/" + req.body.repository + "/tags";
    axios.get(url, requestConfig)
      .then(response => {
        // sails.log('Response from request:', response.data);
        let tags = [];
        for (let i in response.data) {
          tags.push(response.data[i].name);
        }
        payload["tags"] = tags;
        res.ok(payload);
      })
      .catch(err => {
        sails.log.error("Error while calling github API:");
        sails.log(err);
      });
  },

  //New Diagnostics APIs

  /**
   * <AUTHOR> <<EMAIL>>
   * @name setJouleBox
   * @param (string) controllerId
   * @summary Helps to manually set the Joule Box.
   * @returns { "status": "done" }
   */
  "setJouleBox": async (req, res) => {
    try {
      let inputCheck = checkInput(req.body);
      if (!inputCheck.status) return res.badRequest(inputCheck);
      let { automatic, id } = req.body;
      let { mode, controllerId } = initVariables(automatic, id);
      let { "_site": siteId, "_site": socketId } = req._userMeta;

      let payload = generatePayload(mode, controllerId, socketId);
      let site = await Sites.findOne({ siteId });
      publishPayloadToAllControllers(site, payload);
      return res.ok({ "status": true, "message": "Payloads Published." });
    } catch (error) {
      sails.log.error(error);
      return res.serverError({ "status": false, "err": "[setJoulebox] Server error!" });
    }

    // Helper function
    function checkInput(body) {
      let result = {
        "status": false,
      };
      let { id, automatic } = body;
      if (automatic === undefined || typeof automatic !== "boolean") {
        result["message"] = "'autmatic' flag not present or type not boolean in payload.";
        return result;
      } else if (automatic === false && id === undefined) {
        result["message"] = "'id' not present in payload while 'automatic' flag set to 'false'.";
        return result;
      } else return {
        "status": true,
      };
    }

    function generatePayload(mode, controllerId, socketId) {
      return {
        "param": "setJouleBox",
        "value": {
          "timestamp": moment().unix(),
          "mode": mode,
          "controllerId": controllerId,
        },
        "key": socketId,
        "operation": "setJouleBox",
      };
    }

    function publishPayloadToAllControllers(site, payload) {
      let controllerIdList = [];
      Object.keys(site.regions).forEach(regionId => {
        let region = site.regions[regionId];
        controllerIdList = controllerIdList.concat(region.controller);
      });
      controllerIdList.forEach(controllerId => {
        let topic = "global/command/" + controllerId + "/cicd";
        // sails.log(payload, topic);
        //publishing the payload on mentioned topic
        eventService.publish(topic, JSON.stringify(payload));
      });
    }

    function initVariables(automatic, id) {
      if (automatic === true) {
        return {
          "controllerId": 0,
          "mode": "automatic",
        };
      } else {
        return {
          "controllerId": id,
          "mode": "manual",
        };
      }
    }
  },

  /**
   * <AUTHOR> <<EMAIL>>
   * @name needsMaintenanceCircle
   * @param (String) siteId
   * @summary simply collect data from sageMaker and throw it to FE to color the circles who needs maintenance
   * @returns (string)
   */
  "needsMaintenanceCircle": async (req, res) => {
    let { siteId } = req.body;
    try {
      if (!siteId) return res.badRequest("Params not fulfilled");
      let param = {
        "siteId": siteId,
      };
      let response = await axios.post("http://54.70.55.11:8000/api/deviceRanking", param, {
        "headers": {
          "Content-Type": "application/json",
        },
      });
      return res.ok(response.data);
    } catch (error) {
      sails.log.error("[needsMaintenanceCircle] Error!");
      sails.log.error(error);
      res.serverError();
    }
  },

  /**
   * <AUTHOR> <<EMAIL>>
   * @name calculateDQI
   * @param (object[]) deviceObj contains all the info. of devices
   * @param (string) siteId
   * @summary Fetches data from index *_data_* in ElasticSearch, and calculate DQI for corresponding devices.
   * @returns [[deviceId, DQI]]
   */
  "calculateDQI": async (req, res) => {
    let { deviceObj, siteId, dictionary } = req.body;
    let date = moment().format("YYYY-MM-DD");
    let endTime = moment().format("YYYY-MM-DD HH:mm:ss");
    let startTime = moment(endTime).subtract(1, "hours").format("YYYY-MM-DD HH:mm:ss");
    //Creating index to fetch count from ElasticSearch
    let index = siteId + "_data_" + date;
    let resArr = [];
    for (let i in deviceObj) {
      let arr = [];
      let dataArr = [];
      if (deviceObj[i].hasOwnProperty("childNodes")) {
        if (deviceObj[i].objectType == 0) {
          //In the case of devices
          arr = [deviceObj[i].deviceId];
        } else if (deviceObj[i].objectType == 1) {
          //In the case of Controllers
          if (deviceObj[i].childNodes.geo != -1) arr = deviceObj[i].childNodes.geo;
          else arr = [deviceObj[i].id];
        } else if (deviceObj[i].objectType == 2) {
          //In the case of Components
          if (deviceObj[i].childNodes.component != -1) arr = deviceObj[i].childNodes.component;
          else arr = [deviceObj[i].id];
        } else if (deviceObj[i].objectType == 3) {
          //In the case of Region
          if (deviceObj[i].childNodes.geo != -1) {
            let controllers = deviceObj[i].childNodes.geo;
            let devices = "";
            for (let j in controllers) {
              let cId = controllers[j];
              // arr.push(cId);
              if (dictionary[cId].childNodes.geo != -1 && dictionary[cId].childNodes.geo != undefined) {
                let dId = dictionary[cId].childNodes.geo;
                // arr = arr.concat(dId);
                devices += dId.toString();
              }
            }
            arr = devices.split(",");
          } else {
            arr = [deviceObj[i].id];
          }
        }
        //Fetching parameters for each set of deviceIds
        let length = arr.length;
        let id = deviceObj[i].id;
        // sails.log(id, arr, length);
        //Quering in Elastic Search
        let response = await client.count({
          "index": index,
          "type": "_doc",
          "body": {
            "query": {
              "bool": {
                "filter": {
                  "terms": {
                    "deviceId": arr,
                  },
                },
                "must": [{
                  "range": {
                    "timestamp": {
                      "gte": startTime,
                      "lte": endTime,
                    },
                  },
                }],
              },
            },
          },
        });

        dataArr = [id, parseFloat(response.count / length).toFixed(2)];
        sails.log(dataArr);
        //pushing data for each selected in array
        resArr.push(dataArr);
      }
    }

    res.ok(resArr);
  },

  /**
   * <AUTHOR> <<EMAIL>>
   * @name updateAlertsAccountables
   * @param (string) siteId
   * @param (Array) arr containing list of accountables for the sites
   * @summary Update accountables list in dynamoKeyStore table in DynamoDB for each site.
   * @returns (Object) {"status": "Data Updated"}
   */
  "storeAlertsAccountables": async (req, res) => {
    let { siteId, arr } = req.body;
    let dataObj = {
      "key": `controllerInternetStatusAlert-${siteId}`,
      "list": arr,
    };

    let exists = await DyanmoKeyStore.destroy({ "key": dataObj.key });
    // console.log(exists);
    if (exists) {
      await DyanmoKeyStore.create(dataObj).exec((err, data) => {
        if (err) {
          return res.serverError("error occurred");
        }
        return res.ok({ "status": "Data Updated" });
      });
    } else {
      await DyanmoKeyStore.update({ "key": dataObj.key }).set({
        "list": arr,
      });
      return res.ok({ "status": "Data Updated" });
    }
  },

  /**
   * <AUTHOR> <<EMAIL>>
   * @name giveAccountables
   * @param (string) siteId
   * @summary Give accountables list from dynamoKeyStore table in DynamoDB for each site.
   * @returns (object) {"data" : response}
   */
  "giveAccountables": async (req, res) => {
    let { siteId } = req.body;

    try {
      //Fetching data from table
      let data = await DyanmoKeyStore.find({
        "key": `controllerInternetStatusAlert-${siteId}`,
      });

      if (data.length == 0) return res.ok({ "status": "No data for site" });
      //Getting the array of Accountables
      let response = data[0].list;

      res.ok({ "data": response });
    } catch (err) {
      // console.log(err);
      res.badRequest({ "err": "Some error occured" });
    }
  },

  /**
   * @function getDQIGraph
   * @summary Returns percentage DQI of a particular deviceId grouped either on days or hours.
   * @param {array} req.body.deviceObj Containing device Objects.
   * @param {string} req.body.startTime Start time
   * @param {string} req.body.endTime end time
   * @param {string} req.body.siteId SiteID
   * @param {string} req.body.groupBy Either "Hours" or "Days".
   * <AUTHOR> Gupta
   */
  "getDQIGraph": async function(req, res) {
    try {
      let util = diagnosticUtil.getDQIGraph;
      // Check Input
      let inputCheck = util.checkInput(req.body);
      if (!inputCheck.status) return res.badRequest(inputCheck);
      let { deviceList, groupBy, startTime, endTime } = req.body;
      // Generate Query
      let query = util.generateElasticsearchQuery(req.body);
      // Fetch data from elasticsearch.
      let data = await getDataFromElastic(query);
      // Parse response object
      util.calcDQIPercentage(data, groupBy, startTime, endTime, deviceList.length);
      res.ok([{
        "dqi": data,
      }]);
    } catch (error) {
      sails.log.error("[getDQIGraph] Error!");
      sails.log.error(error);
      return res.serverError({
        "status": false,
        "message": "Server Error",
      });
    }

    // Helper functions
    async function getDataFromElastic(query) {
      let response = await client.search(query);
      return response.aggregations.dqi_over_time.buckets;
    }
  },

  //New Diagnostics -> Alert Service
  /**
   * <AUTHOR> Gupta
   * @function saveAlertRecipe
   * @summary Saves a Benchmarking Alert Recipe to Diagnosticrecipe table with after parsing recipe "logic" to "logicParams" to a format that can be read by the BenchmarkingAlert service.
   * @param {string} title
   * @param {string} description
   * @param {string} siteId
   * @param {array} notify Array of users who to email.
   * @param {array} accountable Array of users who are accountable.
   * @param {string} logic Recipe logic as taken from the Front End without any parsing.
   * @param {object} paramMapping Object containing different parameters present in the logic to help replace and parse later.
   * @param {object} paramMapping.recipeString Where the recipeString is the Text added by FrontEnd as a device or a parameter.
   * @param {string} paramMapping.recipeString.param Key of the parameter as saved in the databases.
   * @param {string} paramMapping.recipeString.paramType Type of parameter, ie, "benchmarking" / "strictness"
   * @param {integer} alertType Saved as 0 for generic or 1 for specific recipes.
   * @param {array} devices Array of devices sent ONLY if alert type is 0(Generic).
   */
  "saveAlertRecipe": async (req, res) => {
    // Init variables
    let saveObj = {};
    try {
      // End API if input params are incorrect.
      if (!init(req.body)) return;

      // Parsing logic
      parseLogic(req.body);

      // Saving recipes
      await saveRecipes(req.body);
      return res.ok({
        "status": true,
      });
    } catch (error) {
      // console.log("[saveAlertRecipe] Error!", error);
      return res.serverError("[saveAlertRecipe] Server Error!");
    }

    // Helper functions ============
    function init(body) {
      if (body.title == undefined || body.description == undefined || body.siteId == undefined) {
        res.badRequest({ "err": "Either title OR description OR siteId not mentioned" });
        return false;
      }
      if (body.notify == undefined || body.accountable == undefined) {
        res.badRequest({ "err": "Either notify OR accountable not mentioned" });
        return false;
      } else {
        body.notify = helper.toJson(body.notify);
        if (body.notify == undefined) {
          res.badRequest({ "err": "Could not parse 'notify'." });
          return false;
        }
      }
      if (body.logic == undefined || body.paramMapping == undefined) {
        res.badRequest({ "err": "Either logic OR query OR paramMapping not mentioned" });
        return false;
      } else {
        if (typeof (body.paramMapping) == "string") body.paramMapping = helper.toJson(body.paramMapping);
        if (body.paramMapping == undefined) {
          res.badRequest({ "err": "Could not parse paramMapping." });
        }
      }
      if (body.alertType == undefined) {
        res.badRequest({ "err": "alertType not mentioned" });
        return false;
      } else {
        if (body.alertType == 0) { // generic
          if (body.devices == undefined) {
            res.badRequest({ "err": "devices not mentioned" });
            return false;
          } else {
            body.devices = helper.toJson(body.devices);
            if (body.devices == undefined) {
              res.badRequest({ "err": "Could not parse 'devices'." });
              return false;
            }
          }
        } else {
          delete body.devices;
        }
      }
      // Initializing save object
      saveObj = { ...body };

      return true;
    }

    function parseLogic(body) {
      let { logic, paramMapping, alertType } = body;
      let logicParams = {}, newLogic = logic; // parameters to be saved in the recipe.

      // Replacing benchmarking params with placeholders
      Object.keys(paramMapping).forEach((param, i) => {
        let placeholder = `$${i}`;
        newLogic = newLogic.replace(new RegExp(param), placeholder);

        // Saving placeholder mappings
        if (alertType == 0) { // If alert is generic
          logicParams[placeholder] = {
            "param": paramMapping[param]["param"],
            "paramType": paramMapping[param]["paramType"],
          };
        } else { // If alert is specific
          logicParams[placeholder] = {
            "param": paramMapping[param]["param"],
            "deviceId": paramMapping[param]["deviceId"],
            "paramType": paramMapping[param]["paramType"],
          };
        }
      });

      // Replacing ANDs and ORs with && and ||
      newLogic = newLogic.replace(/and/ig, "&&");
      newLogic = newLogic.replace(/or/ig, "||");
      newLogic = newLogic.replace(/ /g, "");

      // Saving parsed keys to saveObj
      saveObj.parsedLogic = newLogic;
      saveObj.logicParams = logicParams;
      saveObj.paramMapping = paramMapping;
    }

    async function saveRecipes(body) {
      let { devices, alertType } = body;
      // Adding status as active.
      saveObj.status = "1";

      // Saving recipes
      if (alertType == 1) { // Specific recipe
        saveObj.recipeId = uuid();
        // Saving to dynamo
        await Diagnosticrecipe.create(saveObj);
      } else { // Generic recipe.
        // Saving individual recipes for each device.
        let groupId = uuid();
        devices.forEach(async device => {
          saveObj.recipeId = uuid();
          saveObj.groupId = groupId;
          saveObj["deviceId"] = device;
          // Adding deviceId to each benchmarking param mapping
          for (let i in saveObj.logicParams) {
            saveObj.logicParams[i]["deviceId"] = device;
          }
          // Saving each recipe
          // console.log("saveObj: ", saveObj);
          await Diagnosticrecipe.create(saveObj);
        });
      }
    }
  },

  "getBenchmarkingRecipes": async (req, res) => {
    // Init Variables
    let recipes, responseObject;
    try {

      // End API if input params are incorrect.
      if (!init()) return;

      // Fetching Recipes
      recipes = await fetchRecipes();

      // Grouping Abstract recipes
      groupRecipes();

      // Sending return object.
      return res.ok(responseObject);
    } catch (error) {
      // console.log("[getBenchmarkingRecipes] Error!", error);
      return res.serverError("[getBenchmarkingRecipes] Server Error!");
    }

    // Helper functions ==============
    function init() {
      let body = req.body;
      // console.log('Checking init variables.');
      if (body.siteId == undefined) {
        res.badRequest({ "err": " siteId not mentioned" });
        return false;
      }
      return true;
    }

    async function fetchRecipes() {
      let { siteId } = req.body;
      return await Diagnosticrecipe.find({
        "siteId": siteId,
      });
    }

    function groupRecipes() {
      let groupMap = {}, specificRecipe = [];
      recipes.forEach(recipe => {
        if (recipe.hasOwnProperty("groupId")) {
          if (groupMap.hasOwnProperty(recipe.groupId)) {
            groupMap[recipe.groupId].push(recipe);
          } else {
            groupMap[recipe.groupId] = [recipe];
          }
        } else {
          specificRecipe.push(recipe);
        }
      });
      responseObject = {
        "abstractRecipes": groupMap,
        "specificRecipes": specificRecipe,
      };
    }
  },

  /**
   * <AUTHOR> <<EMAIL>>
   * @name editAlerts
   * @param (Object[]) arr contains all the Alerts details which needs to be update
   * @summary Update the Alerts data in Diagnostics Recipe tables
   * @returns (Object) {"status": "Data modified"}
   */
  "editAlerts": async (req, res) => {
    try {
      let { arr } = req.body;

      if (!arr || arr.length == 0) return res.badRequest({ "err": "No data in arr" });

      for (let i in arr) {
        let data = arr[i];
        // console.log(data);
        let siteId = data.siteId;
        let recipeId = data.recipeId;
        delete data.siteId;
        delete data.recipeId;
        await Diagnosticrecipe.update({
          "siteId": siteId,
          "recipeId": recipeId,
        }).set(data);
      }
      res.ok({ "status": "Data modified" });
    } catch (err) {
      sails.log(err);
      res.serverError({ "err": "Some Error Occured" });
    }
  },

  /**
   * @function deleteAlertRecipes
   * @summary Deletes all recipes that were passed in the request parameter.
   * @param {array} req.body.recipeList Array of objects. Each object contains the 'siteId' and 'recipeId' of the recipe.
   * @param {string} req.body.recipeList[0].siteId Site ID.
   * @param {string} req.body.recipeList[0].recipeId Recipe ID.
   */
  "deleteAlertRecipes": async (req, res) => {
    try {
      // sails.log.info("[deleteAlertRecipes] Called!");
      let util = diagnosticUtil.deleteAlertRecipes;

      let inputCheck = util.checkInput(req.body);
      if (!inputCheck.status) return res.badRequest(inputCheck);

      let { recipeList } = req.body, promiseArr = [];
      recipeList.forEach(recipe => {
        let deletePromise = Diagnosticrecipe.destroy({
          "siteId": recipe.siteId,
          "recipeId": recipe.recipeId,
        });
        promiseArr.push(deletePromise);
      });
      promiseArr = await Promise.all(promiseArr);
      return res.ok({
        "status": true,
        "message": "All supplied records deleted.",
      });
    } catch (error) {
      sails.log.error("[deleteAlertRecipes] Error!", error);
      return res.serverError({
        "status": false,
        "message": "Server Error! Check Logs for info.",
      });
    }
  },

  //New Diagnostics -> MaintenanceMode APIs

  /**
   * <AUTHOR> <<EMAIL>>
   * @name giveMaintenanceList
   * @param (string) siteId
   * @param (string) id -Nothing but a deviceId
   * @summary Fetches Maintenance data from ElasticSearch, corresponding to a siteId which don't have status "Release". Lists all mainteance cards that were open between start time and end time.
   * @returns (Object) {data: arr}
   */
  "giveMaintenanceList": async (req, res) => {
    // console.log('giveMaintenanceList: ', moment());
    let { siteId, startTime, id, endTime } = req.body;
    let responce;
    try {
      if (!siteId || !startTime) return res.badRequest({ "err": "Either SiteID or Timestamps is not defined" });

      endTime = moment(endTime).format("YYYY-MM-DD HH:mm:ss");
      startTime = moment(startTime).format("YYYY-MM-DD HH:mm:ss");
      //Fetching data from Elastic Search
      let response = await client.search({
        "index": "test_maintenancemode_2",
        "body": {
          "size": 10000,
          "query": {
            "bool": {
              "must": [
                {
                  "range": {
                    "observed_at": {
                      "lte": endTime,
                    },
                  },
                },
                {
                  "match": {
                    "site": siteId,
                  },
                },
                {
                  "bool": {
                    "should": [
                      {
                        "range": {
                          "resolved_time": {
                            "gte": startTime,
                          },
                        },
                      },
                      {
                        "bool": {
                          "must_not": [{
                            "exists": { "field": "resolved_time" },
                          }],
                        },
                      },
                    ],
                    "minimum_should_match": 1,
                  },
                }],
            },
          },
        },
      });
      //When data not found
      if (response.hits.total == 0)
        return res.ok({ "status": "No Maintenance data for corresponding site" });

      let data = response.hits.hits, UsersPromiseArr = [];
      for (let i in data) {
        let emailId = data[i]._source.repoter;
        //Getting the Username on the basis of emailId
        let user = Users.find({
          "userId": emailId,
        })
          .then(val => {
            let userName = null;
            if (val.length != 0) userName = val[0].name;
            // console.log(val, emailId);
            //Adding key username in original data
            data[i]["username"] = userName;
          });
        UsersPromiseArr.push(user);
      }
      await Promise.all(UsersPromiseArr);

      if (!id) {
        //returning data as fetched when id is undefined
        res.ok({
          "data": data,
        });
      } else {
        //getting all the cards containing the particular id
        responce = diagnosticService.getAllCards(data, id);
        res.ok({
          "data": responce,
        });
      }
    } catch (err) {
      sails.log.error(`[giveMaintenanceList] Error!: ${err}`);
      res.badRequest({ "status": "Some error occcured" });
    }
  },

  /**
   * <AUTHOR> Gupta
   * @function updateMaintenanceInfo
   * @summary Updates Maintenance card info in elasticsearch index.
   * @param {string} id _id of the elasticsearch document to be updated.
   * @param {object} maintenanceInfo All maintenance fields that need to be updated.
   * @returns {object} Returns true or false status.
   */
  "updateMaintenanceInfo": async (req, res) => {
    try {
      // sails.log("[updateMaintenanceInfo] Called!");
      let util = diagnosticUtil.updateMaintenanceInfo;
      // Checking for input parameters
      let inputCheck = util.checkInput(req.body);
      if (!inputCheck.status) return res.badRequest(inputCheck);
      let { id, maintenanceInfo } = req.body;

      // Updating maintenance card
      if (!await updateMaintenanceCard(id, maintenanceInfo)) return;

      return res.ok({
        "status": true,
        "message": "Maintenance card updated.",
      });
    } catch (error) {
      sails.log.error("[updateMaintenanceInfo] Error!");
      sails.log.error(error);
      return res.serverError({
        "status": false,
        "message": "Server Error",
      });
    }

    async function updateMaintenanceCard(id, maintenanceInfo) {
      try {
        await client.update({
          "id": id,
          "index": "test_maintenancemode_2", // Hardcoded maintenance index.
          "type": "parameters", // Hardcoded maintenance type.
          "body": { "doc": maintenanceInfo },
        });
        return true;
      } catch (error) {
        sails.log.error("[updateMaintenanceInfo] Error in updateMaintenanceCard!");
        sails.log.error(error);
        res.serverError({
          "status": false,
          "message": "Failed to update maintenance card",
          "elasticMessage": error.message,
        });
        return false;
      }
    }
  },

  /**
   * <AUTHOR> Gupta
   * @function deleteMaintenanceInfo
   * @summary Deletes Maintenance card in elasticsearch index.
   * @param {string} id _id of the elasticsearch document to be updated.
   * @returns {object} Returns true or false status of the operation wrapped inside an object.
   */
  "deleteMaintenanceInfo": async (req, res) => {
    try {
      // sails.log("[deleteMaintenanceInfo] Called!");
      let util = diagnosticUtil.deleteMaintenanceInfo;
      // Checking input paramters
      let inputCheck = util.checkInput(req.body);
      if (!inputCheck.status) return res.badRequest(inputCheck);
      let { id } = req.body;
      // Checking if user is tech support.
      let user = req._userMeta.id;
      const hardcodedUserList = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"];
      if (!hardcodedUserList.includes(user)) {
        return res.ok({
          "status": false,
          "message": "User not authorized to delete maintenance cards.",
        });
      }

      // Deleting elasticsearch document.
      let deleteMaintenanceResult = await deleteMaintenanceCard(id);
      if (!deleteMaintenanceResult) return;
      return res.ok({
        "status": true,
        "message": "Maintenance card deleted.",
      });
    } catch (error) {
      sails.log.error("[deleteMaintenanceInfo] Error!");
      sails.log.error(error);
      return res.serverError({
        "status": false,
        "message": "Server Error",
      });
    }

    async function deleteMaintenanceCard(id) {
      try {
        await client.delete({
          "id": id,
          "index": "test_maintenancemode_2", // Hardcoded maintenance index.
          "type": "parameters", // Hardcoded maintenance type.
        });
        return true;
      } catch (error) {
        sails.log.error("[deleteMaintenanceInfo]: Failed to delete maintenance card.", error);
        res.serverError({
          "status": false,
          "message": "Failed to delete maintenance card.",
          "elasticMessage": error.message,
        });
        return false;
      }
    }
  },

  /**
   * <AUTHOR> <<EMAIL>>
   * @name needsMaintenanceGraph
   * @param (string) siteId
   * @param (string) startTime
   * @param (string) endTime
   * @summary Fetches Events from ElasticSearch, corresponding to a siteId on particular time range
   * @returns (Object) {data: arr}
   */
  "needsMaintenanceGraph": async (req, res) => {
    let { startTime, endTime, siteId } = req.body;
    startTime = moment(startTime).format("YYYY-MM-DD HH:mm:ss");
    endTime = moment(endTime).format("YYYY-MM-DD HH:mm:ss");
    //Extracting data from Elastic Search
    let response = await client.search({
      "index": "event-history",
      "body": {
        "size": 10000,
        "query": {
          "bool": {
            "filter": {
              "terms": {
                "siteId": [siteId],
              },
            },
            "must": [
              {
                "range": {
                  "timestamp": {
                    "lte": endTime,
                    "gte": startTime,
                  },
                },
              },
            ],
          },
        },
      },
    });
    let data = response.hits.hits;
    for (let i in data) {
      data[i]._source.timestamp = moment(data[i]._source.timestamp).unix() * 1000;
    }
    return res.ok(data);
  },

  /**
   * <AUTHOR> <<EMAIL>>
   * @name showMaintenanceInfo
   * @param (string) siteId
   * @param (string) deviceId
   * @param (string) startTime starting time of range
   * @param (string) endTime ending time of range
   * @summary Fetches Maintenance data from ElasticSearch, corresponding to a siteId of particular deviceId, API is called when device is selected to see maintenance infoAPI
   * @returns (object) {"data": resArr}
   */
  "showMaintenanceInfo": async (req, res) => {
    let { siteId, deviceId, startTime, endTime } = req.body;
    try {
      if (!siteId || !deviceId || !startTime || !endTime) return res.badRequest({ "err": "Either siteId, deviceId or timestamps isn't passed" });

      startTime = moment(startTime).format("YYYY-MM-DD HH:mm:ss");
      endTime = moment(endTime).format("YYYY-MM-DD HH:mm:ss");
      //Fetching data from Elastic Search
      let response = await client.search({
        "index": "test_maintenancemode_2",
        "body": {
          "size": 10000,
          "query": {
            "bool": {
              "must": [
                { "match": { "site": siteId } },
                {
                  "range": {
                    "creation_time": {
                      "gte": startTime,
                      "lte": endTime,
                    },
                  },
                },
              ],
              "must_not": [
                { "match": { "status": "Release" } },
              ],
            },
          },
        },
      });
      //When data not found
      if (response.hits.total == 0) return res.ok({ "status": "No Maintenance data for corresponding site" });

      let resArr = [];
      let data = response.hits.hits;
      //Add card data in array iff device exists in Real
      for (let i in data) {
        let realData = data[i]._source.Real;
        for (let j in realData) {
          if (realData[j].includes(deviceId))
            resArr.push(data[i]);
        }
      }
      return res.ok({
        "data": resArr,
      });
    } catch (err) {
      res.badRequest({ "status": "Some error occcured" });
    }
  },

  /**
   * <AUTHOR> <<EMAIL>>
   * @name resolveFromMaintenance
   * @param (string) id - ID of the maintenance card
   * @summary Simply change the status of the card to "Release" which get Resolved
   * @returns (string) {"status": "Resolved(Status Updated)"}
   */
  "resolveFromMaintenance": async (req, res) => {
    let maintenanceInfo = req.body;

    if (!maintenanceInfo.cid) return res.badRequest({ "err": "Please check the Maintenance Card ID" });

    let currTimeStamp = moment().format("YYYY-MM-DD HH:mm:ss");
    try {
      let promiseArr = [];
      //Updating Status in ElasticSearch
      let $updateQuery = client.updateByQuery({
        "index": "test_maintenancemode_2",
        "body": {
          "query": {
            "bool": {
              "must": [
                { "match": { "_id": maintenanceInfo.cid } },
              ],
            },
          },
          "script": {
            "inline": "ctx._source.status = params.value1; ctx._source.resolved_time = params.value2;",
            "lang": "painless",
            "params": {
              "value1": "Release",
              "value2": currTimeStamp,
            },
          },
        },
      });
      promiseArr.push($updateQuery);

      // Send maintenance Alert
      let $maintenanceAlert = diagnosticService.sendMaintenanceAlert(maintenanceInfo, true);
      promiseArr.push($maintenanceAlert);

      promiseArr = await Promise.all(promiseArr);
      let responseObject = {};
      if (promiseArr[1].status === false) {
        responseObject = {
          "status": false,
          "error": promiseArr[1].error.message,
        };
      } else {
        responseObject = {
          "status": true,
          "message": "Resolved(Status Updated)",
        };
      }

      return res.ok(responseObject);
    } catch (err) {
      sails.log.error("[resolveFromMaintenance] Error!: ", err);
      res.serverError({ "err": "Some error occured while resolving card" });
    }
  },

  /**
   * <AUTHOR> <<EMAIL>>
   * @name checkRealOrVirtualMaintenance
   * @param (string) siteId
   * @param (string) startTime
   * @param (string) endTime
   * @summary Gives list of all the devices which are in Real/Virtual Maintenance
   * @returns (object) {real: [], virtual: []}
   */
  "checkRealOrVirtualMaintenance": async (req, res) => {
    let { siteId, endTime, startTime } = req.body;

    try {
      if (!siteId || !startTime || !endTime) return res.badRequest({ "err": "SiteId or timestamps isn't passed" });

      startTime = moment(startTime).format("YYYY-MM-DD HH:mm:ss");
      endTime = moment(endTime).format("YYYY-MM-DD HH:mm:ss");
      //Querying in Elastic Search
      let response = await client.search({
        "index": "test_maintenancemode_2",
        "body": {
          "size": 10000,
          "query": {
            "bool": {
              "must": [
                {
                  "range": {
                    "observed_at": {
                      "lte": endTime,
                    },
                  },
                },
                {
                  "match": {
                    "site": siteId,
                  },
                },
                {
                  "bool": {
                    "should": [
                      {
                        "range": {
                          "resolved_time": {
                            "gte": startTime,
                          },
                        },
                      },
                      {
                        "bool": {
                          "must_not": [{
                            "exists": { "field": "resolved_time" },
                          }],
                        },
                      },
                    ],
                    "minimum_should_match": 1,
                  },
                }],
            },
          },
        },
      });
      // console.log(response);
      //When No Data found
      if (response.hits.total == 0)
        return res.ok({ "status": "No Maintenance data for corresponding site" });

      let data = response.hits.hits;
      //getting list of Real/Virtual Ids
      let responce = await diagnosticService.sortingRealVirtualIDs(data);

      res.ok(responce);
    } catch (error) {
      sails.log(error);
      res.badRequest({ "err": "Some Error Occurred" });
    }
  },

  /**
   * <AUTHOR> <<EMAIL>>
   * @name storeMaintenanceInfo
   * @param {string} modeType
   * @param {string} description
   * @param {string} reporter
   * @param {Array} accountable
   * @param {string} expectedResolve
   * @param {string} issueObservedAt
   * @param {string} siteId
   * @param {string} typeOfIssue
   * @param {Array} notify
   * @param {string} priority
   * @param {string} summary
   * @param {string} deadline
   * @param {string} attachments
   * @param {object} rd Real Devices
   * @summary Collect all Data in specific Data Structure to create new Maintenance Card.
   * @returns {object} As a new card is created it returns the list of new real /virtual devices.
   */
  "storeMaintenanceInfo": async (req, res) => {
    try {
      let { modeType, description, reporter, accountable, expectedresolve, issueObservedAt, siteId, typeOfIssue, notify, priority, summary, rd, deadline, attachments } = req.body;
      let data;
      let attach = [];
      for (let i in attachments) {
        if (attachments[i].fileUrl != "") attach.push(attachments[i].fileUrl);
      }
      let params = {
        "modeType": modeType,
        "description": description,
        "reporter": reporter,
        "accountable": accountable,
        "expectedResolve": expectedresolve,
        "issueObservedAt": issueObservedAt,
        "siteId": siteId,
        "notify": notify,
        "typeOfIssue": typeOfIssue,
        "priority": priority,
        "summary": summary,
        "rd": rd,
        "deadline": deadline,
        "attachments": attach,
      };
      // sails.log(params);
      // if(!modeType || !description || !reporter || !accountable || !expectedresolve || !issueObservedAt || !siteId ||!typeOfIssue || !notify || !priority || !summary || !rd || !deadline) return res.badRequest({"err": "Params not fulfilled"});

      let uuid = uuidv1();
      if (typeOfIssue == "DJ") {
        //Collecting data and creating task on JIRA
        data = await diagnosticService.getJiraTaskID(params, uuid);
      } else {
        //Collecting data to Create card on Elastic Search and Dynamo
        data = await diagnosticService.collectMaintenanceData(params, uuid);
      }

      let promiseArr = [];
      // TODOI: Temp fix. Directly putting data to elasticsearch. No data being entered in Dynamo. Maintenance Type DJ Not being handled.
      promiseArr.push(tempDirectToElastic(data[0], siteId));
      // Sending alerts to notifiable users.
      promiseArr.push(diagnosticService.sendMaintenanceAlert(data[0], false));
      let returnObj = {
        "real": data[1],
        "virtual": data[2],
      };
      let promiseResults = await Promise.all(promiseArr);
      if (promiseResults[1].status == false) returnObj["error"] = promiseResults[1].error.message;
      return res.ok(returnObj);
    } catch (e) {
      sails.log(e);
      return res.serverError();
    }

    async function tempDirectToElastic(data, site) {
      // sails.log("data, site: ", data, site);
      let response = await client.index({
        "index": "test_maintenancemode_2",
        "type": "parameters",
        "body": data,
        "id": data.cid,
      });
      return response;
    }
  },

  /**
   * <AUTHOR> <<EMAIL>>
   * @name underMaintenanceGraph
   * @param {string} siteId
   * @param {Array} arr (Contains list of devices info)
   * @param {string} startTime
   * @param {string} endTime
   * @summary Provide data to construct underMaintenance Graph.
   * @returns {object} {"data": [ { timestamp: [ 1554316200, 1556562600 ], deviceId: '2215' }]}
   */
  "underMaintenanceGraph": async (req, res) => {
    let { siteId, arr, startTime, endTime } = req.body;

    try {
      if (!siteId || arr.length == 0 || !startTime || !endTime) return res.badRequest({ "err": "Either siteId or timestamps is not passed or arr has no data" });

      startTime = moment(startTime).format("YYYY-MM-DD HH:mm:ss");
      endTime = moment(endTime).format("YYYY-MM-DD HH:mm:ss");
      let response = await client.search({
        "index": "test_maintenancemode_2",
        "body": {
          "size": 10000,
          "query": {
            "bool": {
              "must": [
                {
                  "range": {
                    "observed_at": {
                      "lte": startTime,
                    },
                  },
                },
                {
                  "match": {
                    "site": siteId,
                  },
                },
                {
                  "bool": {
                    "should": [{
                      "bool": {
                        "must": [{
                          "range": {
                            "resolved_time": {
                              "gte": startTime,
                            },
                          },
                        }],
                      },
                    },
                      {
                        "bool": {
                          "must_not": [{
                            "exists": { "field": "resolved_time" },
                          }],
                        },
                      }],
                    "minimum_should_match": 1,
                  },
                }],
            },
          },
        },
      });

      if (response.hits.total == 0) return res.ok({ "status": "No Maintenance data for corresponding site" });

      let data = response.hits.hits;
      let completeResponse = await diagnosticService.createUnderMaintenanceGraphData(arr, data);
      res.ok({ "data": completeResponse });
    } catch (error) {
      res.badRequest({ "err": "Some Error Occured" });
    }
  },

  /**
   * <AUTHOR> <<EMAIL>>
   * @name maintenanceHistory
   * @param {string} siteId
   * @param {Array} arr (Contains list of devices data)
   * @summary Provide data to construct maintenanceHistory Graph.
   * @returns {Object} {"data": [ { deviceId: '2215', timestamp: [ [ 1554316200, 1556562600 ] ] } ]}
   */
  "maintenanceHistory": async (req, res) => {
    let { siteId, arr } = req.body;

    try {
      if (!siteId || !arr) return res.badRequest({ "err": "siteId is not valid or arr has no data" });
      //Fetching data from Elastic Search
      let response = await client.search({
        "index": "test_maintenancemode_2",
        "body": {
          "size": 10000,
          "query": {
            "bool": {
              "must": [
                { "match": { "site": siteId } },
              ],
            },
          },
        },
      });
      //When No Data found
      if (response.hits.total == 0) return res.ok({ "status": "No Maintenance data for corresponding site" });

      let data = response.hits.hits;
      let completeResponse = await diagnosticService.createUnderMaintenanceGraphData(arr, data);
      let ultimateResponse = [];
      //Creating data in such a format which is needed to draw graph
      for (let l in arr) {
        let finalArray = [];
        let ts = [];
        let finalResponse = {};
        let deviceId = arr[l].id;
        for (let m in completeResponse) {
          if (completeResponse[m].deviceId == deviceId) {
            ts.push(completeResponse[m].timestamp);
          }
        }
        finalArray.push(ts);
        finalResponse["deviceId"] = deviceId;
        finalResponse["timestamp"] = ts;
        ultimateResponse.push(finalResponse);
        finalResponse = {};
      }
      res.ok({ "data": ultimateResponse });
    } catch (error) {
      sails.log(error);
      res.badRequest({ "err": "Some Error Occured" });
    }
  },

  //New Diagnostics -> Benchmarking APIs

  /**
   * <AUTHOR> Gupta
   * @function diagnosticBenchmarkingGraph
   * @summary Fetches benchmarking data from the elasticsearch index. Aggregates it based on a timestamp for all different devices. Adds null values for missing data to represent breaks in the graph. Replicates data forward incase of a few parameters. Fetches strictness data for each parameter.
   * @param {string} startTime
   * @param {string} endTime
   * @param {string} siteId
   * @param {array} deviceObject Array of objects from which deviceList (Array of deviceIds) is obtained.
   * @returns {object} {"parameters": {"name": "<paramName>", "data": [Graph Data. 2d array of timeslot and valyes], "strictness": integer}
   */
  "diagnosticBenchmarkingGraph": async (req, res) => {
    try {
      // console.log("[diagnosticBenchmarkingGraph] Called!");
      let util = diagnosticUtil.diagnosticBenchmarkingGraph;
      // Checking for input parameters and initializing variables
      // if (!util.checkInput(req.body)) return;
      let inputCheck = util.checkInput(req.body);
      if (!inputCheck.status) return res.badRequest({ inputCheck });

      let { startTime, endTime, deviceList, siteId } = req.body;
      const benchmarkingParamList = ["alpha", "beta", "n", "l", "alphajr", "alphajt", "alphath", "ti", "m", "dp", "mp", "cp", "quality"];

      // Get strictness data
      let strictnessData = util.getStrictness(benchmarkingParamList, siteId);

      // Fetching benchmarking data from elasticsearch
      let benchmarkingData = await fetchBenchmarkingData(startTime, endTime, deviceList, siteId);

      // Aggregate benchmarking data
      let aggregatedData = util.aggregateBenchmarkingData(benchmarkingData, benchmarkingParamList);

      // Replicate data forward for actionables
      util.extendDataSelectParameters(aggregatedData);

      // SKIP: Get events ,ie, alerts history, cicd update, etc
      // Awaiting promises.
      strictnessData = await strictnessData;
      // Send response
      let response = util.parseResponseData(benchmarkingParamList, aggregatedData, strictnessData);
      return res.ok(response);

    } catch (error) {
      sails.log.error("[diagnosticBenchmarkingGraph] Error!");
      sails.log.error(error);
      return res.serverError({
        "status": false,
        "message": "Server Error",
        "error": error,
      });
    }

    // Helper functions
    async function fetchBenchmarkingData(startTime, endTime, deviceList, siteId) {
      // Reducting endTime by 15 minutes as safety buffer of Benchmarking parameters to be calculated.
      let queriableEndTime = moment(endTime).subtract(15, "minutes").format("YYYY-MM-DD HH:mm:ss"), index = `${siteId}_dj_benchmarking_parameters`;
      index = diagnosticService.generateElasticIndex(startTime, endTime, index, "months");
      //Quering data from Elastic search
      let response = await client.search({
        "index": index,
        "ignore_unavailable": true,
        "body": {
          "size": 10000,
          "query": {
            "bool": {
              "must": [
                {
                  "range": { "start_time": { "gte": startTime, "lte": queriableEndTime } },
                },
              ],
              "filter": {
                "terms": {
                  "deviceId": deviceList,
                },
              },
            },
          },
          "sort": {
            "start_time": {
              "order": "asc",
            },
          },
        },
      });
      return response.hits.hits;
    }

  },
  /**
   * <AUTHOR> <<EMAIL>>
   * @name storeStrictness
   * @param {string} siteId
   * @param {string} param (Benchmarking Parameter)
   * @param {string} value (Value of strictness)
   * @summary Store strictness for benchmarking parameter per site.
   * @returns {Object} {"status": "Strictness Saved"}
   */
  "storeStrictness": async (req, res) => {
    let { param, value, siteId } = req.body;
    let dataObj = {
      "key": `diagnosticStrictness-${param}-${siteId}`,
    };
    try {
      //Fetching currently stored Strinctness
      let data = await DyanmoKeyStore.find(dataObj);
      let resObj = {};
      // console.log(data);
      if (data.length == 0) {
        resObj[param] = value;
        resObj = JSON.stringify(resObj);
        //storing data
        await DyanmoKeyStore.create({
          "key": dataObj.key,
          "value": resObj,
        });
      } else {
        resObj = helper.toJson(data[0].value);
        resObj[param] = value;
        //Updating data
        await DyanmoKeyStore.update(dataObj)
          .set({
            "value": JSON.stringify(resObj),
          });
      }
      res.ok({ "status": "Strictness Saved" });
    } catch (err) {
      sails.log(err);
      res.badRequest({ "err": "Some Error occured" });
    }
  },
  /**
   * @function setDefaultStrictness
   * @summary Sets strictness for all hardcoded parameters of a site if not already present. Not being called by JouleTRACK front end. Only for development purposes.
   * @param {array} req.body.sites Array of sites.
   * <AUTHOR> Gupta <<EMAIL>>
   */
  "setDefaultStrictness": async (req, res) => {
    sails.log("[setDefaultStrictness] Called!");
    let { sites } = req.body;
    const benchmarkingParamList = ["alpha", "beta", "nc", "S", "Sjr", "Sjt", "Sth", "ti", "o", "a", "di", "mm"];
    const defaultValue = 80;

    if (sites == undefined || !Array.isArray(sites)) {
      return res.badRequest({
        "status": false,
        "message": "'sites' not present or not an array",
      });
    }
    let promiseArr = [];
    sites.forEach(siteId => {
      benchmarkingParamList.forEach(param => {
        let searchResult = DyanmoKeyStore.find({
          "key": `diagnosticStrictness-${param}-${siteId}`,
        }).then(result => {
          if (result.length == 0) {
            let valueObj = {};
            valueObj[param] = defaultValue;
            return DyanmoKeyStore.create({
              "key": `diagnosticStrictness-${param}-${siteId}`,
              "value": JSON.stringify(valueObj),
            });
          }
        });
        promiseArr.push(searchResult);
      });
    });
    sails.log("[setDefaultStrictness] Waiting for all promises.");
    await Promise.all(promiseArr);
    sails.log("[setDefaultStrictness] All promises complete.");
    return res.ok({
      "status": true,
    });
  },
};
