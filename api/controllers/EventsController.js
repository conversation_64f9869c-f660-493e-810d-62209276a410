const moment = require("moment-timezone");
const cacheService = require("../services/cacheService");
moment.tz.setDefault("Asia/Kolkata");

module.exports={
	"recieve": (req, res)=>{
		// sails.log("Recived Event");
		let {topic, dataObj} = req.allParams();
		// sails.log(topic, dataObj, ">>>>>>>>");
		//write your switch cases or event emittor to react on any event

		// Diagnostics topics:
		// Info response from JouleSense and other RasPis.
		// let InfoTtopicRegEx = /.*\/response\/\d+\/diagnostic$/;
		if (/.*\/response\/\d+\/diagnostic$/.test(topic)) {
			diagnosticService.diagnosticsResponseFeedback(topic, dataObj);
		} else if (/^local\/feedback\/\d+\/cicd$/.test(topic)){
			let deviceId = topic.split("/");
			deviceId = deviceId[2];
			diagnosticService.cicdFeedback(topic, dataObj, deviceId);
		} else if (/.*\/feedback\/\d+\/cicd$/.test(topic)){
			// For JouleSense V2 specifically.
			let deviceId = topic.split("/");
			deviceId = deviceId[2];
			diagnosticService.cicdFeedback(topic, dataObj, deviceId);
		} else if (/^feedback\/\d+\/cicd$/.test(topic)) {
			let deviceId = topic.split("/");
			deviceId = deviceId[1];
			diagnosticService.cicdFeedback(topic, dataObj, deviceId);
		} else if (/.*\/feedback\/\d+\/mode$/.test(topic)) {
			modeService.setModeRep(helper.toJson(dataObj));
		} else if (/.*\/feedback\/\d+\/pidConfig$/.test(topic)) {
			modeService.pidSave(helper.toJson(dataObj));
		} else if (/.*\/feedback\/\d+\/joulerecipe$/.test(topic)) {
			// in case of recipe from controller executed a command(send full action packet)
			feedbackService.wrapperFeedbackFromRecipe(topic, helper.toJson(dataObj));
		} else if (/.*\/alerts\/\d+\/.*$/.test(topic)){
			// in case of recipe from controller executed a alert(we get full action packet)
			//  alert originated from controller "with" recipe 2.0
			// feedbackService.wrapperFeedbackForAlertFromRecipe(topic, helper.toJson(dataObj));
		} else if (/.*\/sendalerts\/\d+\/.*$/.test(topic)){
			// Without thinking, this endpoint will just get info whom to mail/sms/dejoule aletr and it will send it.
			alertService.sendAlert(topic, dataObj, "JouleRecipe");
		} else if (/.*\/feedback\/\d+\/recipelogicconfig$/.test(topic)){
			// RecipeService.unstage(topic, dataObj);
		} else if (/.*\/feedback\/\d+\/recipeControl$/.test(topic)){
			// feedback for config related actions on recipe
			// RecipeService.feedback(topic, dataObj);
		} else if (/.*\/alert\/\d+\/joulerecipe$/.test(topic)){
			//  alert originated from controller without !!!!recipe 2.0 !!!!
			RecipeService.alert(topic, dataObj);
		} else if (/.*\/feedback\/\d+\/jouletrack$/.test(topic)){
			// This is feedback for "command" given from jouletrack and it have the uniqId and status mainly and thats all we need, Action.get(id) get us everything we need...
			feedbackService.wrapperFeedbackFromJTCommand(topic, helper.toJson(dataObj));
		} else if (/.*\/feedback\/\d+\/cicd$/.test(topic)) {
			diagnosticService.jouleSenseFeedback(topic, dataObj);
		}
		else if (/^jouletrack\/data\/(\w+-\w+|\w+|\d+)\/calculated$/.test(topic)) {
			/**
			 * Please note that siteId can contain a '-' charachter
			 * write the regex keeping that in mind.
			 * Here is the regex group that will match siteId for your reference.
			 * (\w+-\w+|\w+)
			 */
			let data = helper.toJson(dataObj);
			data = addTsToPayload(data);
			let { siteId, deviceId } = dataObj;
			let event = {
				"event": "update",
				"data": data
			};
      sails.log.info('[Event-Controller]-[Receive]-->' + JSON.stringify(event));
      eventService.notifyJouleTrack(siteId, "public", "recentData", event);
			const cacheKey = `livedata:${siteId}:${deviceId}`;
			cacheService.setKeyWithTTL(cacheKey, JSON.stringify(dataObj), 180);
		} else if (/.*\/componentdata\/all\/recent$/.test(topic)){
			let dataPackets = helper.toJson(dataObj);
			console.log(`txnId=${req.headers['x-request-id']} Packet receive for socket broadcast: ${topic} payload size=${(Buffer.byteLength(JSON.stringify(dataObj), 'utf8') / 1024).toFixed(2)} KB`);
			dataPackets.forEach(data => broadcastSocketForPacket(data));
		} else if (/^jouletrack\/data\/update\/public$/.test(topic)){
			//expected obj =>
			// data in event data format and channel to publish on
			let dataJ = helper.toJson(dataObj);
			let {channel, data, siteId} = dataJ;
			if (!channel || !data || !siteId){
				sails.log("insufficient parameters");
				return res.badRequest({"err": "insufficient parameters"});
			}
			eventService.notifyJouleTrack(siteId, "public", "devices", data);

		} else if (/^local\/req\/\d+\/cicd$/.test(topic)){
			diagnosticService.checkUpdate(topic, dataObj);
		} else if (/^local\/feedback\/\d+\/status$/.test(topic)){
			diagnosticService.updateDeviceInternetStatus(topic, dataObj);
		} else if (/.*\/request\/\d+\/queriedparameter$/.test(topic)){
			deviceService.getQueriedParameterConfiguration(topic, dataObj);
		} else if (/.*\/queriedParamData\/.*\/recent$/.test(topic)){
			deviceService.saveOffSetData(topic, dataObj);

		}
		return res.ok();
	}

};

// Helper function

const addTsToPayload = function(payload){
	let backendReceiveUnix = Date.now();
	let { sourceUnix, componentCalcUploadUnix } = payload;
	if (!sourceUnix || !componentCalcUploadUnix) return payload;
	let newPayload = {
		...payload,
		backendReceiveUnix,
		backendDelay: backendReceiveUnix - sourceUnix,
		backendDelayComponentCalcUploadUnix: backendReceiveUnix - componentCalcUploadUnix
	};
	return newPayload;
}

const broadcastSocketForPacket = function(dataObj){
	data = addTsToPayload(dataObj);
	for(let key in dataObj.data){
		let value = dataObj.data[key];
		if(value == "null")
			delete dataObj.data[key];
	}
	let { siteId, deviceId } = dataObj;
	let event = {
		"event": "update",
		"data": data
	};
	// const cacheKey = `livedata:${siteId}:${deviceId}`;
	// cacheService.setKeyWithTTL(cacheKey, JSON.stringify(dataObj), 180);
  sails.log.info('[Event-Controller]-[Receive]-[broadcastSocketForPacket]-->' + JSON.stringify(event));
	eventService.notifyJouleTrack(siteId, "public", "recentData", event);
}
//+/qos/trigger/test
