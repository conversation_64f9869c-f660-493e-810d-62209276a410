/**
 * RepairedController
 *
 * @description :: Server-side logic for managing repaireds
 * @help        :: See http://sailsjs.org/#!/documentation/concepts/Controllers
 */

const uuid = require("uuid");
const moment = require("moment-timezone");
moment.tz.add("Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6");
moment.tz.setDefault("Asia/Kolkata");


// optimization

// siteId no need to be 2nddary index in repaired


// function Nod(id,myinfo=''){

//     this._myId = id;
//     this._parent = '0';
//     this._child = [];
//     this.visited = false;
//     this._myinfo = myinfo;
//     this._disarry = [];
//     this.add = function(nod){
//         this._child.push(nod._myId)
//         nod._parent = this._myId;
//     }
//     this.rootadd = function(nod){
//         this._child.push(nod._myId)
//     }

// }

let mlabel = {
	"critical": "maintainancecritical",
	"high": "maintainancehigh",
	"low": "maintainancelow",
	"medium": "maintainancemedium"
};


module.exports = {
	"addToMaintainance": async function (req, res) {
		// // add to maintainance
		// req._userMeta = {id : "<EMAIL>",_role : 'admin'}
		// req.body ={

		//     // dids :{
		//     //     // type  : 'device',
		//     //     // id : ["97","103"]
		//     //     // or
		//     //     type  : 'controller',
		//     //     id : ["96"]
		//     //     // or
		//     //     // type  : 'region',
		//     //     // id : ["aieb"]


		//     // },
		//     dids : ['100','101','102','103','104','105','106','107'],
		//     siteId : 'smt-del',
		//     title : "test",
		//     comment : "comment",
		//     issueObservedAt : "2018-10-10 12:30",
		//     expectedresolve : "2018-10-14 12:30",
		//     deadline : "2018-10-15 12:30",
		//     attachments : [{"sdf":"https://xxx.com"}],
		//     priority : "high", //   -> drop down (critical,high , low med)
		//     accountable : [],
		//     notify : ["<EMAIL>"],
		//     type : "deployment" //  -> drop down(deployment, dejoule,operation,hvac, other)

		// }
		try {
			let { dids, siteId, title, comment, accountable, notify, issueObservedAt, expectedresolve, deadline, attachments, priority, type } = req.body;

			let userRole = req._userMeta["_role"];
			dids = helper.toArray(dids);

			if (!dids || !siteId || !title || !issueObservedAt || !expectedresolve || !deadline || !priority || !type || !accountable || !notify) {
				return res.badRequest("Incomplete params");
			}

			// if(!dids['id'] || !dids['type']){
			//     return res.badRequest("Incomplete paramss");
			// }

			let isGoingNext = await repairedService.isAccessAllowed(userRole);
			if (!isGoingNext) {
				throw "Unauthorized";
			}

			let prepareObj = [];
			let id = uuid();
			let tid = siteId + "_" + id; // range key
			let ts = moment().unix() * 1000;
			let data = JSON.stringify({
				title, issueObservedAt, expectedresolve, deadline, priority, type, "accountable": accountable, "notify": notify, "parentType": dids["type"]
			});
			deadline = parseInt(moment(deadline).unix());
			let cpriority = [mlabel[priority]];
			cpriority = cpriority.filter(Boolean); // removinf nulls, undefined etc

			// getChild(dids['id'],dids['type'],siteId).then(z=>{

			// prepareObj =  addListToModeAndRepair(dids['type'],z,tid,data,siteId,deadline,req._userMeta['id'],ts)
			// up until now we have created objec to add to repairedTable and ModesTable

			// gonagetemall(prepareObj,accountable,notify,id).then(d=>{
			//     res.send(d);
			// }).catch(e=>{
			//     sails.log(e);
			//     res.serverError()
			// })
			// }).catch(e=>{
			//     sails.log(e);
			// })
			attachments = JSON.stringify(attachments);
			let userId = req._userMeta.id;
			prepareObj = repairedService.gotoMaintainance({ dids, tid, data, siteId, deadline, userId, ts, attachments, comment });
			let sendObj;
			repairedService.gonagetemall(prepareObj, accountable, notify, id).then(d => {
				sendObj = d;
				let cacheUser = [];
				sails.log("!23");
				notify.forEach(u => {
					sails.log(u, siteId, cpriority);
					cacheUser.push(alertService.addWrapper(u, siteId, cpriority));
				});
				return Promise.all(cacheUser);
			}).then(d => {
				res.send(sendObj);
			}).catch(e => {
				sails.log(e);
				res.serverError();
			});

		} catch (e) {
			sails.log(e);
			return res.serverError("Error");
		}
	},
	"updateMode": function (req, res) {


		// req._userMeta = {"id":"ujjal"}

		// req.body = {
		//     id : "9accdcaf-bc22-476b-a2d6-36cdd8557405",
		//     siteId : 'smt-del',
		//     title : "test",
		//     comment : "comment1",
		//     issueObservedAt : "2018-10-10 12:30",
		//     expectedresolve : "2018-10-14 12:30",
		//     deadline : "2018-10-15 12:30",
		//     attachments : [{"sdf":"https://xxx.com"}],
		//     priority : "high", //   -> drop down (critical,high , low med)
		//     accountable : ["ujjal"],
		//     notify : [],
		//     type : "deployment" 
		// }

		try {

			let { id, siteId, title, comment, expectedresolve, deadline, attachments, priority, accountable, notify, type } = req.body;

			let isError = false;
			let userId = req._userMeta["id"];
			let userRole = req._userMeta["_role"];


			if (!id || !userId || !siteId) {
				return res.badRequest("Params pls");
			}
			let getId = `${siteId}_${id}`;
			let olddata = {};
			let changeObj = {};
			let fullOldObj = {};
			let updateInto = [];
			let haveAccess = false;

			repairedService.isAccessAllowed(userRole).then(d => {

				if (d) {
					haveAccess = true;
				}

				return Repaired.find({ "uuid": getId });

			}).then(minfo => {

				if (!minfo) {
					isError = true;
					throw "Invalid Id ";
				}

				updateInto = minfo.map(m => {
					return { "did": m["did"], "uuid": m["uuid"] };
				});
				fullOldObj = minfo[0];
				olddata = helper.toJson(fullOldObj.data);

				let realAcc = helper.toArray(olddata["accountable"]);

				if (!haveAccess) {
					if (realAcc.includes(userId)) {
						return true;
					} else {
						return false;
					}
				} else {
					return true;
				}

			}).then(isAcces => {

				if (!isAcces) {
					isError = true;
					throw "Un authorized";
				} else {
					olddata["title"] = title ? title : olddata["title"];
					olddata["expectedresolve"] = expectedresolve ? expectedresolve : olddata["expectedresolve"];
					olddata["deadline"] = deadline ? deadline : olddata["deadline"];
					olddata["priority"] = priority ? priority : olddata["priority"];
					olddata["type"] = type ? type : olddata["type"];
					olddata["accountable"] = accountable ? (accountable) : olddata["accountable"];
					olddata["notify"] = notify ? (notify) : olddata["notify"];

					changeObj["data"] = JSON.stringify(olddata);
					changeObj["comment"] = comment ? comment : fullOldObj["comment"];
					changeObj["attachments"] = attachments ? JSON.stringify(attachments) : fullOldObj["attachments"];

					let promArr = updateInto.map(ui => Repaired.update(ui, changeObj));
					return repairedService.gonagetemall(promArr, accountable, notify, id);
				}


			}).then(doneOnRepair => {

				res.send(doneOnRepair);

			}).catch(e => {
				sails.log(e);
				if (isError) {
					return res.badRequest(e);
				} else {
					return res.badRequest();
				}

			});

		} catch (e) {
			sails.log(e);
			res.serverError();
		}


	},
	"removeFromMaintainance": function (req, res) {

		// issue ressolved , maintainance done

		// req._userMeta = {id : "test"}
		// req.body = {
		//     siteId : "smt-del",
		//     id : "9accdcaf-bc22-476b-a2d6-36cdd8557405"
		// }

		try {
			let { siteId, id } = req.body;
			let accountable, notify;

			if (!siteId || !id) {
				res.badRequest("Param error");
				return res.end();
			}

			let userId = req._userMeta["id"];
			let userRole = req._userMeta["_role"];

			let ts = moment().unix() * 1000;
			let rkey = siteId + "_" + id;
			let resKey = userId + "_" + ts;
			// let rootElem = new Nod('0');
			// let nodeMap = {
			// '0' : rootElem
			// };

			let isError = false;
			let allrepairs;
			let final = {};
			let haveAccess = false;

			repairedService.isAccessAllowed(userRole).then(d => {

				if (d) {
					haveAccess = true;
				}

				return Repaired.find({ "uuid": rkey });

			}).then(repairs => {

				allrepairs = repairs;

				if (repairs.length == 0) {
					isError = true;
					throw ("no legit id");
				}


				let unrepairObj = {
					"uuid": id,
					"resolved": resKey,
					"siteId": siteId,
					"data": "",
					"deviceMap": "[]",
					"createdBy": "",
					"timeline": ""
				};

				unrepairObj["createdBy"] = repairs[0]["createdBy"];
				unrepairObj["timeline"] = moment.duration(moment(repairs[0]["deadline"] * 1000) - moment()).asHours();
				let data = helper.toJson(repairs[0]["data"]);
				// unrepairObj['parentType'] = data['parentType'];
				unrepairObj["data"] = JSON.stringify(data);
				unrepairObj["comment"] = repairs[0]["comment"];
				unrepairObj["attachments"] = repairs[0]["attachments"];

				// repairs.forEach(repair=>{
				//     nodeMap[repair['did']] = new Nod(repair['did'],repair);
				// })
				// repairs.forEach(repair=>{
				//     parent = repair['parent'];
				//     if(parent && parent!='' ){
				//         nodeMap[parent].add(nodeMap[repair['did']])
				//     }else{
				//         rootElem.rootadd(nodeMap[repair['did']])
				//     }
				// })
				unrepairObj["deviceMap"] = JSON.stringify(repairs.map(repair => repair["did"]));
				// unrepairObj['deviceMap'] = JSON.stringify(getGraph(nodeMap));

				if (!unrepairObj["deviceMap"]) {
					isError = true;
					throw "No Device";
				}

				accountable = data["accountable"];
				if (!accountable.includes(req._userMeta["id"]) && !haveAccess) {
					isError = true;
					throw "Unauthorized";
				}
				notify = data["notify"];
				return Unrepaired.create(unrepairObj);
				// sails.log(unrepairObj);
			}).then(added => {

				// flushing from repaired table
				// adding maintainance done in mode table
				let fullArry = [];
				final = added;

				for (let i = 0; i < allrepairs.length; i++) {
					let yid = allrepairs[i]["did"];
					fullArry.push(Repaired.destroy({ "did": yid, "uuid": rkey }));
					fullArry.push(Mode.create({ "did": yid + "_maintainance", "timestamp": ts, "siteId": siteId, "changedMode": "maintainanceOFF", "currMode": "dj" }));
				}

				return Promise.all(fullArry);

			}).then(del => {

				// deleting accountable + motify 
				let adder = {};
				let ulist = [];

				for (let i = 0; i < accountable.length; i++) {
					adder[accountable[i]] = { "accountable": id };
				}
				for (let i = 0; i < notify.length; i++) {
					if (adder[notify[i]]) {
						adder[notify[i]]["notify"] = id;
					} else {
						adder[notify[i]] = { "notify": id };
					}
				}
				for (let us in adder) {
					ulist.push(repairedService.updateIntoUser([us], adder[us], repairedService.updateNUsers));
				}
				return Promise.all(ulist);

			}).then(d2 => {
				res.send(final);
			}).catch(e => {
				sails.log(e);
				if (isError) {
					return res.badRequest(e);
				} else {
					return res.serverError();
				}
			});
		} catch (e) {
			sails.log(e);
			res.serverError();
		}
	},
	"showAllInSite": function (req, res) {

		// info of all in one site maintainance

		// req.body = {
		//     siteId : "smt-del"
		// }

		try {

			let siteId = req.body.siteId;

			if (!siteId) {
				res.badRequest("No proper params");
				return res.end();
			}

			let srcIt = siteId + "_";

			Repaired.find({
				"where": {
					"uuid": {
						"beginsWith": srcIt
					}
				}

			}).then(list => {
				let resHash = {};
				for (let index in list) {
					let temp = list[index]["uuid"].split("_")[1];
					if (!resHash[temp]) {
						resHash[temp] = list[index];
					}
				}
				res.send(resHash);

			}).catch(e => {
				sails.log(e);
				res.serverError();
			});


		} catch (e) {
			sails.log(e);
			res.serverError();
		}


	},
	"showAllInSite2": function (req, res) {

		// info of all in one site maintainance

		// req.body = {
		//     siteId : "smt-del"
		// }

		try {

			let siteId = req.body.siteId;

			if (!siteId) {
				res.badRequest("No proper params");
				return res.end();
			}

			let srcIt = siteId + "_";
			let resHash = {};
			Repaired.find({
				"where": {
					"uuid": {
						"beginsWith": srcIt
					}
				}

			}).then(list => {

				for (let index in list) {
					let temp = list[index]["did"];
					if (!resHash[temp]) {
						resHash[temp] = [];
					}
					resHash[temp].push({ ...list[index], "resolved": false });
				}

				return Unrepaired.find({ "siteId": siteId });

			}).then(unrepaires => {
				if (!unrepaires || unrepaires.length == 0) {
					res.send(resHash);
				} else {
					for (let i in unrepaires) {
						let unrep = unrepaires[i];
						let dmap = helper.toArray(unrep["deviceMap"]);
						let dhsh = helper.toJson(unrep["data"]);
						let ddline = moment(dhsh["deadline"]).unix() * 1000;

						let tempObj = {
							"attachments": unrep["attachments"],
							"comment": unrep["comment"],
							"createdAt": unrep["createdAt"],
							"createdBy": unrep["createdBy"],
							"resolvedBy": unrep["resolved"],
							"siteId": unrep["siteId"],
							"data": unrep["data"],
							"uuid": unrep["siteId"] + "_" + unrep["uuid"],
							"deadline": ddline,
							"resolved": true
						};
						for (let d in dmap) {
							let device = dmap[d];
							sails.log(device);
							if (!resHash[device]) {
								resHash[device] = [];
							}
							resHash[device].push(tempObj);
						}
					}
					res.send(resHash);
				}

			}).catch(e => {
				sails.log(e);
				res.serverError();
			});


		} catch (e) {
			sails.log(e);
			res.serverError();
		}
	},

	"getTick": function (req, res) {
		// deadline cross , expected crossed and resolved, live
		// req.body = {
		//     siteId : "smt-del"
		// }
		let errtype = false;
		try {
			let siteId = req.body.siteId;
			if (!siteId) {
				res.badRequest("Param error");
				return res.end();
			}
			let totDict = {};
			let countDeadCross = 0, countExpCross = 0, live = 0, resolved = 0;
			let findSite = `${siteId}_`;

			Repaired.find({
				"uuid": {
					"beginsWith": findSite
				}
			}).then(repairs => {


				repairs.forEach(repair => {
					totDict[repair["uuid"]] = repair;
				});
				let currts = moment();
				live = Object.keys(totDict).length;

				for (let i in totDict) {
					let tempdead = moment(totDict[i]["deadline"]);
					let tempexpt = moment(helper.toJson(totDict[i]["data"])["expectedresolve"]);
					if (tempdead > currts) {
						countDeadCross++;
					}
					if (tempexpt > currts) {
						countExpCross++;
					}
				}
				return Unrepaired.find({ "siteId": siteId });
			}).then(d2 => {
				if (d2) {
					resolved = d2.length;
				}
				return res.send({
					resolved, live, countDeadCross, countExpCross
				});
			});

		} catch (e) {
			sails.log(e);
			if (errtype) {
				res.badRequest(e);
			} else {
				res.badRequest("Server Error");
			}
		}

	},
	"showOne": function (req, res) {
		// info of 1 in maintainance

		// req.body = {
		//     siteId : "smt-del",
		//     uid : "3786cb38-cdb6-4311-b5c1-b0731c86462a"
		// }
		try {

			let { siteId, uid } = req.body;
			let type = 0;
			if (!siteId || !uid) {
				return res.badRequest("Params error");
			}
			let finder = siteId + "_" + uid;
			Repaired.findOne({
				"uuid": finder
			}).then(d => {
				if (d) {
					return { ...d, "resolved": false };
				} else {
					type = 1;
					return Unrepaired.findOne({ "uuid": uid });
				}

			}).then(d => {
				if (type === 1 && d) {
					let unrep = d;
					let dhsh = helper.toJson(unrep["data"]);
					let ddline = moment(dhsh["deadline"]).unix() * 1000;
					d = {
						"attachments": unrep["attachments"],
						"comment": unrep["comment"],
						"createdAt": unrep["createdAt"],
						"createdBy": unrep["createdBy"],
						"resolvedBy": unrep["resolved"],
						"siteId": unrep["siteId"],
						"data": unrep["data"],
						"uuid": unrep["siteId"] + "_" + unrep["uuid"],
						"deadline": ddline,
						"resolved": true

					};
				}
				res.send(d);
			}).catch(e => {
				sails.log(e);
				res.serverError();
			});

		} catch (e) {
			sails.log.error(e);
			return res.serverError();
		}

	},
	"getrepaired": function (req, res) {

		// list of maintainance that has been resolved on this site
		// req.body = {
		//     siteId : 'smt-del'
		// }
		try {

			let siteId = req.body.siteId;
			if (!siteId) {
				res.badRequest("PArams");
			}

			Unrepaired.find({ "siteId": siteId }).then(d => {
				res.send(d);
			}).catch(e => {
				sails.log(e);
				res.serverError();
			});


		} catch (e) {
			sails.log(e);
			res.serverError();

		}


	},
	"revive": function (req, res) {

		// req._userMeta = {id : "test"}

		// req.body = {
		//     "uuid" : "9accdcaf-bc22-476b-a2d6-36cdd8557405",
		//     "siteId" : "smt-del"
		// }
		let errType = false;
		let fullData;
		try {

			let { uuid, siteId } = req.body;
			if (!uuid || !siteId) {
				res.badRequest("Params pl");
			}
			// get last added 
			// let userId = req._userMeta["id"];
			let userRole = req._userMeta["_role"];
			let id = siteId + "_" + uuid;
			let accountable, notify, attachments, comment;

			let haveAccess = false;

			repairedService.isAccessAllowed(userRole).then(d => {
				if (d) {
					haveAccess = true;
				}
				return Repaired.findOne({ "uuid": id });

			}).then(d1 => {
				if (d1) {
					errType = true;
					throw "Already in maintainance mode";
				} else {
					return Unrepaired.findOne({ "where": { "uuid": uuid, "sort": "-1", "limit": 1 } });
				}

			}).then((d) => {
				if (!d) {
					errType = true;
					throw "Hogya bass tera";
				}
				fullData = helper.toJson(d["data"]);

				// parentType = d['parentType'];
				accountable = fullData["accountable"];
				notify = fullData["notify"];
				// rkey = d["siteId"]+"_"+d["uuid"];
				// reopened = "1";
				comment = d["comment"];
				attachments = d["attachments"];
				// if(parentType!="device"){
				//     parentDevice = [parseInt(helper.toArray(d['deviceMap'])[0])];
				// }else{
				//     parentDevice = helper.toArray(d['deviceMap']);
				// }

				if (!haveAccess && !accountable.includes(req._userMeta["id"])) {
					errType = true;
					throw "UnAuthorized";
				}

				let deviceMap = helper.toArray(d["deviceMap"]);

				if (!deviceMap) {
					res.send("This cannot be valid card");
					return res.end();
				}
				// return getChild(parentDevice,parentType,siteId);

				return deviceMap;
			}).then(z => {

				if (!z) {
					errType = true;
					throw "Id error";

				}
				let tid = siteId + "_" + uuid;
				let ts = moment().unix() * 1000;
				let deadline = fullData["deadline"];
				// prepareObj =  addListToModeAndRepair(parentType,z,tid,JSON.stringify(fullData),siteId,deadline,req._userMeta['id'],ts)
				let dids = z, data = JSON.stringify(fullData), userId = req._userMeta;
				let prepareObj = repairedService.gotoMaintainance({ dids, tid, data, siteId, deadline, userId, ts, attachments, comment });
				return repairedService.gonagetemall(prepareObj, accountable, notify, uuid);

			}).then(d => {
				res.send(d);
			}).catch(e => {
				if (errType) {
					return res.send(e);
				} else {
					sails.log(e);
					return res.serverError();
				}


			});


		} catch (e) {
			sails.log(e);
			return res.end();
		}


	}
};
// function addListToModeAndRepair(idType,z,tid,data,siteId,deadline,userId,ts){

//     try{
//         var prepareObj = [];
//         if(idType=="region"){
//             for(let i in z){
//                 prepareObj.push(Repaired.create({did:i,uuid:tid,data : data,siteId:siteId,deadline:deadline,createdBy:userId}))
//                 nparent = i;
//                 for(let j in z[i]){
//                     nnparent = j;
//                     prepareObj.push(Repaired.create({did:j,parent : nparent,uuid:tid,data : data,siteId:siteId,deadline:deadline,createdBy:userId}))
//                     prepareObj.push(Mode.create({did:j+"_maintainance",timestamp : ts,siteId:siteId,changedMode:"maintainanceOn",currMode:"maintainance",createdBy:userId}))
//                     for(let k in z[i][j]){
//                         prepareObj.push(Repaired.create({did:z[i][j][k],parent : nnparent,uuid:tid,data : data,siteId:siteId,deadline:deadline,createdBy:userId}))
//                         prepareObj.push(Mode.create({did:z[i][j][k]+"_maintainance",timestamp : ts,siteId:siteId,changedMode:"maintainanceOn",currMode:"maintainance",createdBy:userId}))
//                     }
//                 }
//             }
//         }else if(idType=="controller"){
//             for(let j in z){
//                 nnparent = j;
//                 prepareObj.push(Repaired.create({did:j,uuid:tid,data : data,siteId:siteId,deadline:deadline,createdBy:userId}))
//                 prepareObj.push(Mode.create({did:j+"_maintainance",timestamp : ts,siteId:siteId,changedMode:"maintainanceOn",currMode:"maintainance",createdBy:userId}))
//                 for(let k in z[j]){
//                     prepareObj.push(Repaired.create({did:z[j][k],parent : nnparent,uuid:tid,data : data,siteId:siteId,deadline:deadline,createdBy:userId}))
//                     prepareObj.push(Mode.create({did:z[j][k]+"_maintainance",timestamp : ts,siteId:siteId,changedMode:"maintainanceOn",currMode:"maintainance",createdBy:userId}))
//                 }
//             }
//         }else if(idType=="device"){
//             for(let k in z){
//                 prepareObj.push(Repaired.create({did:z[k],uuid:tid,data : data,siteId:siteId,deadline:deadline,createdBy:userId}))
//                 prepareObj.push(Mode.create({did:z[k]+"_maintainance",timestamp : ts,siteId:siteId,changedMode:"maintainanceOn",currMode:"maintainance",createdBy:userId}))
//             }
//         }
//         return (prepareObj);
//     }catch(e){
//         sails.log(e);
//         return []
//     }
// }


// function getGraph(nodeMap,graph=[]){
//     visit = ['0']
//     while(visit.length!=0){
//         nowOn = visit.splice(0,1)[0];
//         useNode = nodeMap[nowOn];
//         if(nowOn!='0'){
//             graph.push(nowOn)

//         }

//         nodeMap[nowOn].visited = true;

//         visiterNode = nodeMap[nowOn]._child;
//         for(let i=0;i<visiterNode.length;i++){
//             nnode = nodeMap[visiterNode[i]];
//             if(!nnode.visited){
//                 visit.push(nnode._myId)
//             }
//         }
//     }
//     return graph
// }


