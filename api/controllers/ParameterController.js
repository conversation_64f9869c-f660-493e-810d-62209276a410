// const AWS = require("aws-sdk");
const moment = require("moment-timezone");
moment.tz.add("Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6");
moment.tz.setDefault("Asia/Kolkata");


module.exports = {
	// curl -X GET http://127.0.0.1:1337/test/getParams/ahu
	// // curl -X POST localhost:1337/test/modular --data "table=test&primaryName=deviceId&primaryVal=ssh_1&secondaryName=timestamp&secondary_1=1517814339264&secondary_2=1517814339267&isTs=1"
	// // curl -X POST localhost:1337/test/modular --data "table=test&primaryName=deviceId&primaryVal=ssh_1&secondaryName=timestamp&secondary_1=1517814339264&isTs=1"
	// // curl -X POST localhost:1337/test/modular --data "table=test&primaryName=deviceId&primaryVal=ssh_1&isTs=1"
	// modular: function(req, res) {
	//     let tableName, primaryName, primaryVal, secondaryName, secondary_1, secondary_2, isTs;
	//     try {
	//         tableName = (req.body.table != undefined) ? req.body.table : undefined;
	//         primaryName = (req.body.primaryName != undefined) ? req.body.primaryName : undefined;
	//         primaryVal = (req.body.primaryVal != undefined) ? req.body.primaryVal : undefined;
	//         secondaryName = (req.body.secondaryName != undefined) ? req.body.secondaryName : undefined;
	//         secondary_1 = (req.body.secondary_1 != undefined) ? req.body.secondary_1 : undefined;
	//         secondary_2 = (req.body.secondary_2 != undefined) ? req.body.secondary_2 : undefined;
	//         isTs = (req.body.isTs != undefined) ? req.body.isTs : 0;

	//     } catch (e) {
	//         sails.log(e);
	//         return res.notFound("Paramaters are not complete");
	//     }

	//     if (dbHelper.checkExist([primaryName, primaryVal, tableName])) {
	//         return res.notFound("Wrong Parameter value tableName and primary key required");
	//     }

	//     if (isTs) {
	//         secondary_1 = moment(secondary_1).format('YYYY-MM-DD HH:mm');
	//         secondary_2 = moment(secondary_2).format('YYYY-MM-DD HH:mm');
	//     }
	//     let name = {
	//         primaryName
	//     };
	//     let vals = {
	//         primaryVal
	//     }
	//     if (secondaryName) {
	//         name["secondaryName"] = secondaryName;
	//         if (secondary_1) {
	//             vals["secondaryVal"] = [secondary_1];
	//             if (secondary_2) {
	//                 vals["secondaryVal"].push(secondary_2)
	//             } else {

	//             }
	//         } else {
	//             return res.notFound("Wrong Parameter value Secondary name provided by it need atleast 1 parameter");

	//         }
	//     }
	//     let real_val = 0;

	//     function ahuFiltering(alldata) {
	//         data = JSON.parse(alldata["data"]);
	//         let KW = data["KW"];
	//         if (KW >= 0.2) {
	//             real_val += 1;
	//             data["runningMinutes"] = real_val;
	//         } else {
	//             data["runningMinutes"] = real_val;
	//         }
	//         alldata["data"] = JSON.stringify(data);

	//     }

	//     function filterCallback(partial_data, cbb) {

	//         try {
	//             for (let i = 0; i < partial_data.length; i++) {
	//                 let i_val = (partial_data[i]);
	//                 ahuFiltering(i_val);
	//             }
	//         } catch (e) {
	//             sails.log(e);
	//             sails.log("error retrieving data");
	//         }
	//         cbb(partial_data); // just put it when u want to save]);
	//     }
	//     modular.getData(tableName, name, vals, filterCallback);

	// },
	// curl -X POST --data "siteId=ssh&param=SA&param_1=22&param_2=24&from=2017-12-06&to=2018-12-06" localhost:1337/test/thermalindex
	"thermalComfortIndex": function (req, res) {
		console.error(`[DynamoDB DataDevices] function=thermalComfortIndex method=${req.method} route=${req.route ? req.route.path : req.url}, statusCode=${res.statusCode}`);
		let param, param_1, param_2, from, to, obj;
		let siteId = req.body.siteId != undefined ? req.body.siteId : "";
		try {
			param = req.body.param != undefined ? req.body.param : "";
			param_1 = req.body.param_1 != undefined ? req.body.param_1 : "";
			param_2 = req.body.param_2 != undefined ? req.body.param_2 : "";
			from = req.body.from != undefined ? req.body.from : "";
			to = req.body.to != undefined ? req.body.to : "";
		} catch (e) {
			sails.log(e);
			res.send("Parameter value undetermined");
		}
		if (dbHelper.checkExist([siteId, param, param_1, from, to])) {
			return res.notFound("Wrong Parameter value tableName and primary key required");
		}

		let deviceId = [];
		this.getAllComp(siteId, "ahu").then((data) => {
			data.forEach((d) => {
				if (!d.id) {
					return res.badRequest("Error parsing data");
				}
				deviceId.push(d.id);

			});
			let parameters = (param).split(",");
			let total = deviceId.length; //number of devices
			/*
            Data fetched from db of particular all AHUs
             retu_result={
                 ssh_1:{[
                     timestamp:<>,
                     data:{
                         paramName:value
                     },{
                        ...
                    }
                ]},
                ssh_2:{[...]}
             }
            */

			let retu_result = {};
			obj = {
				"startTime": from,
				"endTime": to,
				"reqst": parameters,
				"groupBy": "day",
				"type": "thermalIndex"
			};

			function dataCompleter() {
				return new Promise(function (resolve, reject) {
					dbHelper.getData(obj).then(function (data) {
						resolve(data);
					}).catch((reason) => {
						reject("Handle rejected promise (" + reason + ") here.");
					});
				});
			}
			async function dataWrapper() {
				while (total > 0) {
					total -= 1;
					obj["deviceId"] = deviceId[total];
					try {
						let r = await dataCompleter();
						retu_result[deviceId[total]] = r[0];
					} catch (e) {
						retu_result = { "Error": e };
					}

				}
				//  idhar sara data aagya
				// retu_result{
				//  "cwit":[[time,val],[time,val]]
				// }
				// let collabData = [];
				// Object.keys(retu_result).forEach((d)=>{
				//     let temp=retu_result[d];
				//     collabData=[...collabData,...temp];
				// })

				let real_boss = {};

				Object.keys(retu_result).forEach((d) => {
					let collabData = retu_result[d];
					let trivialData = {};
					collabData.forEach((d) => {
						let timeS = d.timestamp;
						let d_data = JSON.parse(d.data);
						let ts = moment(timeS).startOf("day").format("YYYY-MM-DD HH:mm");
						if (d_data[param]) {
							if (trivialData[ts]) {
								trivialData[ts].push(d_data[param]);
							} else {
								trivialData[ts] = [d_data[param]];
							}
						}
					});
					let main_result = {};

					function findAvg(data, from, to) {
						let avg = 0,
							count = 0;
						for (let i = 0; i < data.length; i++) {
							if (data[i] >= from && data[i] <= to) {
								avg += data[i];
								count += 1;
							}
						}
						return avg / count;
					}
					Object.keys(trivialData).map((d1) => {
						let data = trivialData[d1];
						data = (data.filter(Boolean));
						main_result[d1] = findAvg(data, param_1, param_2) / data.length;
					});
					real_boss[d] = main_result;
				});
				res.send(real_boss);
			}

			dataWrapper();

		}).catch((err) => {
			return res.badRequest(err);
		});
	},
	"getAllComp": function (siteId, name) {
		return new Promise((res, rej) => {
			Component.find({ siteId }).exec(function (err, data) {
				if (err) {
					sails.log.error(err);
					rej({ "err": "Unable to fetch data at present", "data": null });
				}
				if (data.length == 0) {
					rej({ "err": "No components for this site", "data": null });
				}
				let returner = data.filter((d) => {
					return d.deviceType == name;
				});
				res(returner);
			});
		});
	},
	// curl -X POST --data "groupBy=[day week month]&siteId=ssh" localhost:1337/test/CWSP
	"chilledWaterSP": function (req, res) {
		console.error(`[DynamoDB DataDevices] function=chilledWaterSP method=${req.method} route=${req.route ? req.route.path : req.url}, statusCode=${res.statusCode}`);
		try {
			let groupBy = req.body.groupBy ? req.body.groupBy : "";
			let siteId = req.body.siteId ? req.body.siteId : "";
			let from = moment().subtract("1", "day").startOf(groupBy).subtract("1", groupBy).format("YYYY-MM-DD HH:mm");
			let to = moment().subtract("1", "day").startOf(groupBy).format("YYYY-MM-DD HH:mm");
			if (!groupBy || !siteId) {
				res.send("Error");
				return res.end();
			}
			let deviceId = [];
			this.getAllComp(siteId, "chiller").then((data) => {

				data.forEach((d) => {
					if (!d.id) { return res.badRequest("Error parsing data"); }
					deviceId.push(d.id);
				});
				let parameters = ["SetPoint"];
				let total = deviceId.length;
				let retu_result = {};
				let obj = {
					"startTime": from,
					"endTime": to,
					"reqst": parameters,
					"groupBy": groupBy
				};

				function dataCompleter() {
					return new Promise(function (resolve, reject) {
						dbHelper.getData(obj).then(function (data) {
							resolve(data);
						}).catch((reason) => {
							reject("Handle rejected promise (" + reason + ") here.");
						});
					});
				}
				async function dataWrapper() {
					while (total > 0) {
						total -= 1;
						obj["deviceId"] = deviceId[total];
						try {
							let r = await dataCompleter();
							retu_result[deviceId[total]] = r[0];
						} catch (e) {
							retu_result = { "Error": e };
						}
					}
					res.send(retu_result);
				}

				dataWrapper();


			}).catch((e) => {
				sails.log.error(e);
			});


		} catch (e) {
			sails.log.error(e);
			res.send("Request Error");
		}
	},
	// curl -X POST --data "siteId=ssh&week=1,2&days=3,4" localhost:1337/test/buildingLoad
	"buildingLoad": function (req, res) {
		console.error(`[DynamoDB DataDevices] function=buildingLoad method=${req.method} route=${req.route ? req.route.path : req.url}, statusCode=${res.statusCode}`);
		try {
			let { days, siteId, week } = req.body;
			if (!siteId) {
				res.send("Error");
				return res.end();
			}


			let key = siteId + "_mainMeter";

			let srcObjects = [];


			if (!week && !days) {
				let temp = [];
				temp[0] = moment().subtract(1, "day").startOf("day").format("YYYY-MM-DD HH:mm");
				temp[1] = moment().subtract(2, "day").startOf("day").format("YYYY-MM-DD HH:mm");
				temp[2] = moment().subtract(1, "day").subtract(1, "week").startOf("day").format("YYYY-MM-DD HH:mm");
				for (let i = 0; i < 3; i++) {
					let startTime = temp[i];
					let endTime = moment(startTime).add(1, "day").format("YYYY-MM-DD HH:mm");
					srcObjects.push({ startTime, endTime, "reqst": ["kw", "timestamp"] });
				}


			} else {
				days = days.split(",");
				week = week.split(",");
				srcObjects = this.formSrcTime(srcObjects, days, week, siteId);
			}
			DyanmoKeyStore.findOne({ key }).then((data) => {
				if (typeof data === "undefined")
					return res.ok({ "err": "No Main Meter Configured", "data": [] });
				data = JSON.parse(JSON.stringify(data));
				let vals = (data.value).split(",");
				let promises = [];
				let temp = {};
				vals.forEach(val => {
					srcObjects.forEach((obj) => {
						temp = { ...obj, "deviceId": val };
						promises.push(temp);
					});
				});
				let toDoPromise = [];
				promises.forEach(promise => {
					toDoPromise.push(dbHelper.getData(promise));
				});
				Promise.all(toDoPromise).then(data => {
					let realData = [];
					data.forEach(d => {
						realData.push(dbHelper.basicFilter(d[0]));
					});
					let FilterData = dbHelper.filterBuilding(realData, vals.length);
					res.send(FilterData);

				}).catch(e => {
					sails.log.error(e);
					res.send("Error in finding data");
					return res.send();
				});
			}).catch(e => {
				sails.log.error(e);
				res.send("Error ! no key ");
				return res.end();
			});


		} catch (e) {
			sails.log.error(e);
			res.send("Error");
			return res.end();

		}
	},
	"formSrcTime": function (obj, days, weeks, siteId) {
		try {
			weeks = weeks.map((d) => parseInt(d));
			for (let weekIndex =0;weekIndex <weeks.length; weekIndex++){
				let week = weeks[weekIndex];
				for (let i = 0; i < days.length; i++) {
					let checker = moment().subtract(1, "month").startOf("month").day(); // day of week in previous month 6
					let day = parseInt(days[i]); // 1
					if (isNaN(day)) { continue; }
					if (((week) * 7 + day) - checker < 0) {
						// let lastWeek = parseInt(weeks.slice(-1)) + 1;
						// weeks.push(lastWeek);
						continue;
					}
					day = day - checker;
					let startTime = moment().subtract(1, "month").startOf("month").add(week, "week").add(day, "day").format("YYYY-MM-DD HH:mm");
					let endTime = moment(startTime).add(1, "day").format("YYYY-MM-DD HH:mm");
					obj.push({ startTime, endTime, "deviceId": siteId, "reqst": ["KW", "timestamp"] });
				}
			}

			return obj;
		} catch (e) {
			sails.log.error(e);
			return [{}];
		}

	},
	// curl -X POST --data "siteId=ssh&&component=[ahu,actuator]&groupBy=[day:week:month]&use=[OutputFrequency,Actuator]" localhost:1337/test/best
	"bestComponent": function (req, res) {
		console.error(`[DynamoDB DataDevices] function=bestComponent method=${req.method} route=${req.route ? req.route.path : req.url} statusCode=${res.statusCode}`);
		try {
			let { siteId, component, groupBy, use } = req.body;
			if (!siteId || !component || !groupBy || !use) {
				res.send("Error ! Incomplete data");
				return res.badRequest();
			}
			if (use != "OutputFrequency" && use != "Actuator") {
				res.send("Error ! Incorrect to use");
				return res.badRequest();
			}
			use = use.toLowerCase();

			let deviceIds = [];
			this.getAllComp(siteId, component).then((data) => {

				data.forEach((d) => {
					if (!d.deviceId) {
						return res.badRequest("Error parsing data");
					}
					deviceIds.push(d.deviceId);

				});
				let total = deviceIds.length;
				let globalAll = {};
				let startTime = moment().startOf("day").subtract(1, "day").subtract(1, groupBy).format("YYYY-MM-DD HH:mm");
				let endTime = moment().startOf("day").subtract(1, "day").format("YYYY-MM-DD HH:mm");
				let reqst = ["kw", use];

				let obj = {
					startTime,
					endTime,
					reqst
				};

				function completeData() {
					return new Promise(function (resolve, reject) {
						dbHelper.getData(obj).then(function (data) {
							resolve(data);
						}).catch((reason) => {
							reject("Handle rejected promise (" + reason + ") here.");
						});
					});
				}

				function standardDeviation(values) {
					values = values.filter(Boolean);
					if (values.length == 0) {
						return "NA";
					}
					let avg = average(values);

					let squareDiffs = values.map(function (value) {
						let diff = value - avg;
						let sqrDiff = diff * diff;
						return sqrDiff;
					});

					let avgSquareDiff = average(squareDiffs);

					let stdDev = Math.sqrt(avgSquareDiff);
					return stdDev;
				}

				function average(data) {
					let sum = data.reduce(function (sum, value) {
						return sum + value;
					}, 0);
					let avg = sum / data.length;
					return avg;
				}

				function Myfilter(data, use, id) {
					// let returner = {};
					let paramArr = {};
					paramArr[use] = [];
					let temp2 = [];
					data.forEach((indata) => {
						let paramData = helper.toJson(indata.data);
						let status = paramData["status"];
						let defaultUse = use == "outputfrequency" ? 50 : 100;
						if (status == 1) {
							let using = paramData[use] ? paramData[use] : defaultUse;
							let ts = indata["timestamp"];
							ts = (moment(ts).unix() * 1000);
							temp2.push([ts, using]);
							paramArr[use].push(using);
						}
					});
					globalAll[id] = temp2;
					return standardDeviation(paramArr[use]);
				}

				function nDifferent(obj, n, start, type) {
					if (n == 0) return [];
					let counter = 0;
					let ret = [];
					let prev = "";
					let d;
					for (let i = start; i < obj.length; i++) {
						d = obj[i];
						if (d.val == 0 && type == "best") break;
						if (prev != d.val) {
							counter++;
							prev = d.val;
							ret.push(d);
						} else {
							ret.push(d);
						}
						if (counter == n) break;
					}
					return ret;
				}
				let retu_result = {};
				async function dataWrapper() {
					while (total > 0) {
						total -= 1;
						obj["deviceId"] = deviceIds[total];
						try {
							let r = await completeData();
							retu_result[deviceIds[total]] = Myfilter(r[0], use, deviceIds[total]);
						} catch (e) {
							retu_result = { "Error": e };
						}
					}
					//  done
					let realResult = [];
					let leftOver = [];
					Object.keys(retu_result).forEach((val) => {
						let temp = {};
						if (retu_result[val] == "NA") {
							temp[val] = "NA";
							realResult.push(temp);
						} else {
							temp[val] = retu_result[val];
							temp["val"] = retu_result[val];
							leftOver.push(temp);
						}
					});
					leftOver = leftOver.sort((x, y) => y.val - x.val);
					// small to large
					let all = leftOver.length;
					let best, bad;
					let toSend = { "best": [], "worst": [] };
					if (all < 10) {
						let Good = Math.ceil(all / 2);
						best = nDifferent(leftOver, Good, 0, "best");
						let noGood = all - best.length;
						bad = nDifferent(leftOver, noGood, best.length, "bad");
					} else {
						best = leftOver.slice(all - 5, all);
						bad = leftOver.slice(0, 5);
					}
					best.forEach((d) => {
						let did = Object.keys(d)[0];
						let temp = {};
						temp[did] = globalAll[did];
						toSend["best"].push(temp);
					});
					bad.forEach((d) => {
						let did = Object.keys(d)[0];
						let temp = {};
						if (retu_result[did] != "0") {
							temp[did] = globalAll[did];
							toSend["worst"].push(temp);
						}

					});
					toSend["notWorking"] = realResult;

					res.send(toSend);
				}
				dataWrapper();


			}).catch(e => {
				sails.log.error(e);
				res.send("Error ! No data for this component");
			});

		} catch (e) {
			sails.log.error(e);
			res.send("Error");
		}
	},

	// curl -X POST localhost:1337/pid/getGraph
	"getPidGraph": function (req, res) {
		// req.body={
		//     siteId : 'ssh',
		//     deviceId : '0',   // 0_pid 0_ifttt 0_iftttpid
		//     start_ts : '1534160907000',
		//     end_ts : '1534247290000', // ssh_1531810330643
		// }
		try {
			let { siteId, deviceId, start_ts, end_ts } = req.body;
			if (!siteId || !deviceId || !start_ts || !end_ts) {
				res.badRequest();
				return res.end();
			}
			let topLevelObj = { "sort": "-1", "timestamp": { "between": [siteId + "_" + start_ts, siteId + "_" + end_ts] } };

			let promiseArr = [];
			promiseArr.push(
				Command.find({ ...topLevelObj, "deviceId": deviceId + "_pid" }),
				Command.find({ ...topLevelObj, "deviceId": deviceId + "_iftttpid" }),
				Command.find({ "sort": "-1", "timestamp": { "lte": siteId + "_" + start_ts }, "deviceId": deviceId + "_iftttpid", "limit": 1 }),
				Command.find({ "sort": "-1", "timestamp": { "lte": siteId + "_" + start_ts }, "deviceId": deviceId + "_pid", "limit": 1 })

			);

			Promise.all(promiseArr).then(specifiedTop => {

				specifiedTop = specifiedTop.map(elem => { if (elem) return elem; else return []; });
				let rangedArr = [...specifiedTop[0], ...specifiedTop[1]];
				rangedArr.sort((a, b) => b.timestamp.split("_")[1] - a.timestamp.split("_")[1]);

				let justBeforeTimeStamp = [...specifiedTop[2], ...specifiedTop[3]];
				justBeforeTimeStamp.sort((a, b) => b.timestamp.split("_")[1] - a.timestamp.split("_")[1]);
				justBeforeTimeStamp[0].executed = start_ts;
				let totalArr = [...rangedArr, justBeforeTimeStamp[0]];
				let finalObj = {
					"real": [],
					"prev": [],
					"after": []
				};
				totalArr.forEach(point => {
					let tempExec = point.executed;
					let val = point.value;
					if (val != "off_off") {
						if (tempExec != "0") {
							let [start, offset] = (val.split("_")).map(parseFloat);
							finalObj.real.push([tempExec, start]);
							finalObj.after.push([tempExec, start + offset]);
							finalObj.prev.push([tempExec, start - offset]);
						}

					}
				});
				res.send(finalObj);

			}).catch(e => {
				sails.log.error(e);
				return res.end();
			});


		} catch (e) {
			sails.log.error(e);
			res.badRequest();
		}
	},
	// curl -X POST --data "" localhost:1337/feedbackGateWay/sjo

	// curl localhost:1337/lastcommand2/ssh/ssh_12
	"lastCommandForSameId": function (req, res) {
		try {
			let id = req.params.id;
			let siteId = req.params.siteId;
			let ret_obj = {};
			let ts = String(moment().subtract(3, "days").unix() * 1000);
			if (!id || !siteId) {
				res.badRequest("Incomplete params");
				return res.end();
			}
			let src_obj = { "sort": "-1", "timestamp": { "gte": siteId + "_" + ts } };

			Component.findOne({ "id": id }).then(async (d) => {
				let ctrl = d.controls;
				sails.log(ctrl);
				if (!ctrl) {
					res.badRequest();
					return res.end();
				}
				for (let c in ctrl) {
					let cid = ctrl[c];
					if (cid) {
						let norm = cid;
						let dj_find = cid + "_jt";
						let iftt_find = cid + "_jr";

						let promise_arr = [
							Command.find({ ...src_obj, "deviceId": dj_find }),
							Command.find({ ...src_obj, "deviceId": norm }),
							Command.find({ ...src_obj, "deviceId": iftt_find })
						];
						try {
							ret_obj[c] = await getLastOfAll2(promise_arr, id, c);
						} catch (e) {
							sails.log.error(e);
						}
					}
				}
				res.send(ret_obj);
			}).catch(e => {
				sails.log(e);
				return res.end();
			});

		} catch (e) {
			sails.log(e);
			return res.badRequest();
		}
	},
	"lastCommand": function (req, res) {
		try {
			let id = req.params.id;
			let siteId = req.params.siteId;
			let ret_obj = {};

			let src_obj = { "sort": "-1", "timestamp": { "beginsWith": siteId + "_" }, "limit": 1 };

			if (!id || !siteId) {
				res.badRequest("Incomplete params");
				return res.end();
			}

			Component.findOne({ "id": id }).then(async (d) => {
				let ctrl = d.controls;
				let ns1 = d.ns1;
				let dj_pid = ns1 + "_pid";        // PID command came manually via dj
				let ifttt_pid = ns1 + "_iftttpid"; // PID command via ifttt
				let promise_arr = [
					Command.findOne({ ...src_obj, "deviceId": dj_pid }),
					Command.findOne({ ...src_obj, "deviceId": ifttt_pid })
				];
				try {
					ret_obj["pid"] = await getLastOfAll(promise_arr, id);
				} catch (e) {
					sails.log(e);
					return res.end();
				}


				if (!ctrl) {
					res.badRequest();
					return res.end();
				}
				for (let c in ctrl) {
					let cid = ctrl[c];
					if (cid) {
						let norm = cid;
						let dj_find = cid + "_dj";
						let iftt_find = cid + "_ifttt";

						promise_arr = [
							Command.findOne({ ...src_obj, "deviceId": dj_find }),
							Command.findOne({ ...src_obj, "deviceId": norm }),
							Command.findOne({ ...src_obj, "deviceId": iftt_find })
						];
						try {
							ret_obj[c] = await getLastOfAll(promise_arr, id);
						} catch (e) {
							sails.log.error(e);
						}
					}
				}
				res.send(ret_obj);

			}).catch(e => {
				sails.log(e);
				res.send("error");
			});
		} catch (e) {
			sails.log(e);
			res.badRequest("Error");
		}
	}

};
function getLastOfAll2(promise_arr, compId, type) {
	let retObj = {};
	retObj[type] = undefined;
	return new Promise((res, rej) => {
		Promise.all(promise_arr).then(a => {
			let ts = 0, maxi = 0, tempTs = 0;

			for (let i = 0; i < a.length; i++) {
				let tempArr = a[i];
				// console.log(tempArr)
				for (let j = 0; j < tempArr.length; j++) {
					if (!(tempArr[j]) || !tempArr[j].timestamp || tempArr[j].componentId != compId) {
						continue;
					}
					if (tempArr[j].type == type) {
						tempTs = ((tempArr[j].timestamp.split("_")));
						tempTs = tempTs.length == 2 ? parseInt(tempTs[1]) : parseInt(tempTs[0]);
						if (tempTs > ts) {
							ts = tempTs;
							maxi = tempArr[j];
						}
						break;
					}
				}
			}
			res(maxi);
		}).catch(e => {
			sails.log.error(e);
			rej(e);
		});
	});
}

function getLastOfAll(promise_arr, compId) {
	return new Promise((res, rej) => {
		Promise.all(promise_arr).then(a => {
			let ts = 0, maxi = 0;
			for (let i = 0; i < a.length; i++) {
				if (!(a[i]) || !a[i].timestamp || a[i].componentId != compId) {
					continue;
				}
				let tempTs = ((a[i].timestamp.split("_")));
				tempTs = tempTs.length == 2 ? parseInt(tempTs[1]) : parseInt(tempTs[0]);
				if (isNaN(tempTs)) {
					continue;
				}
				if (tempTs > ts) {
					ts = tempTs;
					maxi = a[i];
				}
			}
			res(maxi);
		}).catch(e => {
			sails.log.error(e);
			rej(e);
		});
	});
}
