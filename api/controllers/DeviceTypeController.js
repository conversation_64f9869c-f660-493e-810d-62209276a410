const moment = require("moment-timezone");
const vendorConfig = require("../data/vendors.json");
moment.tz.add("Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6");
moment.tz.setDefault("Asia/Kolkata");
module.exports = {
	// CRUD APIs:
	"create": async function (req, res) {
		try {
			let obj = req.body;
			if (!obj) {
				return res.badRequest({
					"err": "Data Object not present."
				});
			}

			if (!obj.driverType || !obj.deviceType) {
				return res.badRequest({
					"err": "Insufficient Parameters."
				});
			}

			let driver = await DeviceType.find({ "driverType": obj.driverType, "deviceType": obj.deviceType });
			if (driver && driver.length !== 0) {
				throw new Error("Driver already exist");
			}

			obj.parameters = obj.parameters.map(p => JSON.stringify(p));

			// Fixing input object for DB entry
			if (typeof (obj.params) == "string") {
				obj.params = JSON.parse(obj.params);
			}

			let data = await DeviceType.create(obj);
			await deviceService.updateAllConfigTs();
			return res.ok(data);
		} catch (e) {
			sails.log.error(e);
			return res.serverError();
		}
	},

	// Returns array of all entries in DB.
	"listAll": function (req, res) {
		DeviceType.find().exec((err, data) => {
			if (err) {
				return res.serverError();
			} else {
				data = data.map(param => {
					let tempArray = helper.toArray(param.parameters);
					param.parameters = tempArray ? tempArray : [];
					param.params = param.parameters.map(helper.toJson);
					return param;
				});
				return res.ok(data);
			}
		});
	},

	// Sends array if Param combination(DeviceType and DriverType) exists. Else returns empty array.
	"findParams": function (req, res) {
		let deviceType = req.param("DeviceType");
		let driverType = req.param("DriverType");
		if (!deviceType || !driverType) {
			res.badRequest({ "err": "Insufficent Parameters." });
		}

		DeviceType.find({ "deviceType": deviceType, "driverType": driverType }).exec((er, data) => {
			if (er) {
				sails.log.error(er);
				return res.serverError();
			} else if (data[0]) {
				return res.ok(data[0].params);
			} else {
				return res.ok([]);
			}
		});

	},

	//   Post request with deviceType and driverType to search for entry to update,
	//  paramArr (Array of strings containing abbr value) ,
	// keyVal (An object with key value pairs containing what needs to be appended/updated).
	"updateParams": function (req, res) {
		let deviceType, driverType, paramArr, keyVal;
		try {
			deviceType = req.body.deviceType != undefined ? req.body.deviceType : "";
			driverType = req.body.driverType != undefined ? req.body.driverType : "";
			paramArr = req.body.paramArr != undefined ? req.body.paramArr : "";
			keyVal = req.body.keyVal != undefined ? req.body.keyVal : "";
			if (deviceType == "" || driverType == "" || keyVal == "" || (typeof (keyVal) != "object") /*|| (typeof(paramArr.constructor.name!='Array') && paramArr!='')*/) {
				return res.badRequest("Insufficient Parameters");
			}
			for (let i in keyVal) {
				if (typeof (keyVal[i]) != "string") {
					return res.badRequest("Incorrect Parameters");
				}
			}
			for (let i in paramArr) {
				if (typeof (paramArr[i]) != "string") {
					return res.badRequest("Incorrect Parameters");
				}
			}

			DeviceType.find({
				"deviceType": deviceType,
				"driverType": driverType
			}).exec((err, data) => {
				if (err) {
					sails.log.error(err);
					return res.serverError();
				} else {
					try {
						data = data[0];
						try {
							if (typeof (data.params) == "string") {
								data.params = JSON.parse(data.params);
							}
							if (typeof (keyVal) == "string") {
								keyVal = JSON.parse(keyVal);
							}
							if (typeof (paramArr) == "string" && paramArr != "") {
								paramArr = JSON.parse(paramArr);
							}
						} catch (e) {
							throw "Some input was a string and couldn't be parsed";
						}
						if ((paramArr == "") || (paramArr.constructor.name == "Array" && paramArr.length == 0)) {
							for (let i in data.params) {
								for (let j in keyVal) {
									data.params[i][j] = keyVal[j];
								}
							}
						} else {
							for (let i in paramArr) {
								for (let j in data.params) {
									if (paramArr[i] == data.params[j]["abbr"]) {
										for (let k in keyVal) {
											data.params[j][k] = keyVal[k];
										}
									}
								}
							}
						}

						DeviceType.update({
							"deviceType": deviceType,
							"driverType": driverType
						}, data)
							.exec((err, data) => {
								if (err) {
									sails.log.error(err);
									return res.serverError("Couldn't update entry after data modification.");
								} else {
									res.ok(data);
								}
							});

					} catch (e) {
						sails.log.error(e);
						return res.serverError();
					}

				}
			});

		} catch (error) {
			sails.log.error(error);
			return res.serverError();
		}
	},

	// Deletes an entry in DB
	"delete": function (req, res) {
		let devType = req.param("DeviceType");
		let drivType = req.param("DriverType");

		DeviceType.destroy({ "deviceType": devType, "driverType": drivType }).exec((err) => {
			if (err) {
				sails.log.error(err);
				return res.serverError();
			} else {
				return res.ok("Record deleted if present.");
			}
		});

	},

	// Returns array of deviceTypes present.
	"findDevice": function (req, res) {
		let deviceType = req.param("DeviceType");

		DeviceType.find({ "deviceType": deviceType }).exec((err, data) => {
			if (err) {
				sails.log.error(err);
				return res.serverError();
			} else {
				return res.ok(data);
			}
		});
	},

	// Returns a single object
	"findOne": function (req, res) {
		let deviceType = req.param("DeviceType");
		let driverType = req.param("DriverType");

		DeviceType.find({ "deviceType": deviceType, "driverType": driverType }).exec((err, data) => {
			if (err) {
				sails.log.error(err);
				return res.serverError();
			} else {
				return res.ok(data[0]);
			}
		});

	},

	/*
	Example of req.body for update function:
	{
		driverType : 'foo',
		deviceType: 'bar',
		params: {<Param Object>},
		newDeviceType : 'newFoo',
		newDriverType : 'newBar'
	}
	*/
	// Updates an entry based on req.body object
	"update": function (req, res) {
		let obj = req.body;
		if (!obj) {
			return res.badRequest({ "err": "Data Object not present." });
		}
		if (!obj.deviceType || !obj.driverType) {
			return res.badRequest({ "err": "Insufficient Parameters." });
		}
		if (typeof (obj.newDeviceType) != "string" && Boolean(obj.newDeviceType)) {
			return res.badRequest({ "err": "Invalid newDeviceType" });
		}
		if (typeof (obj.newDriverType) != "string" && Boolean(obj.newDriverType)) {
			return res.badRequest({ "err": "Invalid newDriverType" });
		}

		// Fetching existing params if params not present in req.body
		if (!obj.params) {
			DeviceType.find({ "deviceType": obj.deviceType, "driverType": obj.driverType }).then((err, data) => {
				try {
					if (err) {
						sails.log.error(err);
						return res.serverError({
							"err": "Unable to fetch previous param list."
						});
					} else {
						obj.params = data[0].params;
					}
				} catch (error) {
					sails.log.error(error);
					res.serverError();
				}
			});
		}
		let searchObj = {};
		try {
			if (typeof (obj.params) == "string") {
				obj.params = JSON.parse(obj.params);
			}
			searchObj = {
				"deviceType": obj.deviceType,
				"driverType": obj.driverType
			};
			if (obj.newDeviceType) obj.deviceType = obj.newDeviceType;
			if (obj.newDriverType) obj.driverType = obj.newDriverType;

		} catch (err) {
			sails.log.error(err);
			return res.serverError({ "err": "Error fixing params array." });
		}

		DeviceType.update(searchObj, obj).exec(async (err, data) => {
			if (err) {
				sails.log.error(err);
				return res.serverError();
			} else {
				if (err) {
					sails.log.error(err);
					return res.serverError({
						"err": "Unable to fetch previous param list."
					});
				}
				await deviceService.updateAllConfigTs();
				return res.ok(data);
			}
		});

	},

	"getVendors": async (req, res) => {
		let vendors = JSON.parse(JSON.stringify(vendorConfig));
		let filteredVendors = vendors.map(v => {
			let cntrl = v.controllers.map(controller => {
				delete controller.boardConfig;
				return controller;
			});
			v.controllers = cntrl;
			return v;
		});
		return res.ok(filteredVendors);
	},
	"getDeviceMapping": async (req, res) => {
		let { vendorId, version, deviceType } = req.allParams();
		if (!vendorId || !version || !deviceType)
			return res.badRequest();
		let vendorConfigArr = JSON.parse(JSON.stringify(vendorConfig));
		let portArr = [];
		try {
			let deviceTypesPromise = DeviceType.find();
			//From vendor config, retrieve the port info of the board requested
			for (let i = 0; i < vendorConfigArr.length; i++) {
				let vendor = vendorConfigArr[i];
				if (vendor.vendorId == vendorId) {
					for (let j = 0; j < vendor.controllers.length; j++) {
						if (vendor.controllers[j].deviceType == deviceType) {
							portArr = vendor.controllers[j].boardConfig[version];
							break;
						}
					}
				}
			}

			let deviceTypes = await deviceTypesPromise;
			let resArr = [];
			for (let i = 0; i < portArr.length; i++) {
				let portInfo = portArr[i];
				let portObj = {};
				portObj.port = portInfo.portNumber;
				portObj.deviceTypes = portInfo.deviceTypes;
				let devTypeSet = new Set();
				let comCatSet = new Set(portInfo.communicationCategory);
				portInfo.deviceTypes.map(e => devTypeSet.add(e.deviceType));
				for (let index = 0; index < deviceTypes.length; index++) {
					let devType = deviceTypes[index];
					let { deviceType, communicationCategory, driverType, communicationType, driverName } = devType;
					if (devTypeSet.has(deviceType) && comCatSet.has(communicationCategory)) {
						if (!portObj[deviceType]) {
							portObj[deviceType] = [];
						}
						portObj[deviceType].push({
							deviceType,
							communicationCategory,
							communicationType,
							driverType,
							driverName
						});

					}
				}
				resArr.push(portObj);
			}
			return res.ok(resArr);

			// let deviceTypeMap = {};
			// //generate a map of devicetypes with key as deviceType to get all possible devices for a port
			// deviceTypes.forEach(device => {
			//     let { deviceType, driverType, communicationCategory, communicationType, driverName } = device;
			//     if (!deviceTypeMap[deviceType])
			//         deviceTypeMap[deviceType] = [];
			//     deviceTypeMap[deviceType].push({ deviceType, driverType, communicationCategory, communicationType, driverName });
			// });
			// let finalPortInfo = portArr.map(obj => {
			//     let deviceTypes = new Set();
			//     obj.deviceTypes.forEach(e => {
			//         deviceTypes.add(e.deviceType);
			//     })
			//     obj.options = [];
			//     deviceTypes.forEach(uniqType => {
			//         deviceTypeMap[uniqType].forEach(portInfo => {
			//             if (obj.communicationCategory.indexOf(portInfo.communicationCategory) != -1) {
			//                 obj.options.push(portInfo);
			//             }
			//         })
			//     });
			//     delete obj.communicationCategory;
			//     delete obj.deviceTypes;
			//     return obj;
			// })

			// return res.ok({ finalPortInfo });

		} catch (e) {
			sails.log.error(e);
			return res.serverError();
		}
	},
	//update DeviceType record use this api url
	"listParams": function (req, res) {
		let type = req.query.deviceType;
		if (!type)
			return res.badRequest({ "err": "Insufficient parameters" });
		let params;
		switch (type) {
			case "vfd":
			case "CNWPump":
			case "CHWPump":
				params = [{
					"abbr": "OutputFrequency",
					"displayName": "Frequency",
					"unit": "Hz"
				},
				{
					"abbr": "InputPower",
					"displayName": "Input Power",
					"unit": "Joule"
				},
				{
					"abbr": "InputTerminalCurrent",
					"displayName": "Input Terminal Current",
					"unit": "Volt"
				},
				{
					"abbr": "InputTerminalVolt",
					"displayName": "Input Terminal Volt",
					"unit": "Volt"
				}
				];
				return res.ok(params);

			case "chiller":
				params = [{
					"abbr": "condEwt",
					"displayName": "Condenser In Temperature",
					"unit": "F"
				},
				{
					"abbr": "condLwt",
					"displayName": "Condenser Out Temperature",
					"unit": "F"
				},
				{
					"abbr": "coolEwt",
					"displayName": "Chilled Water In Temperature",
					"unit": "F"
				},
				{
					"abbr": "coolLwt",
					"displayName": "Chilled Water Out Temperature",
					"unit": "F"
				},
				{
					"abbr": "sctA",
					"displayName": "Refrigerant Condenser Temperature",
					"unit": "F"
				},
				{
					"abbr": "sstA",
					"displayName": "Refrigerant Evaporator Temperature  ",
					"unit": "F"
				},
				{
					"abbr": "SetPoint",
					"displayName": "Running Chilled Water SetPoint",
					"unit": "F"
				}
				];
				break;
			case "tempratureSensor":
				params = [{
					"displayName": "Temperature",
					"unit": "C",
					"abbr": "TMP"
				}];
				break;
			case "joulesense":
			case "temprature_humiditySensor":
				params = [{
					"displayName": "Temperature",
					"unit": "C",
					"abbr": "TMP"
				}, {
					"displayName": "Humidity",
					"unit": "%AGE",
					"abbr": "HUM"
				}];
				break;
			case "em":
				params = [{
					"abbr": "KW",
					"displayName": "Average Active Power",
					"unit": "KW",
				},
				{
					"abbr": "PF",
					"displayName": "Power Factor",
					"unit": "PF"
				},
				{
					"abbr": "A_VLL",
					"displayName": "Line Voltage",
					"unit": "Volt"
				},
				{
					"abbr": "A_VLN",
					"displayName": "Line N Voltage",
					"unit": "Volt"
				},
				{
					"abbr": "A_AMP",
					"displayName": "Average Current",
					"unit": "Amps"
				},
				{
					"abbr": "KVAH",
					"displayName": "Active Energy",
					"unit": "KvAh"
				},
				{
					"abbr": "KVA",
					"displayName": "Average Apparent Power",
					"unit": "KvA"
				},
				{
					"abbr": "KVAR",
					"displayName": "Average Reactive Power",
					"unit": "kVAr"
				},
				{
					"abbr": "IR",
					"displayName": "Current R",
					"unit": "Amps"
				},
				{
					"abbr": "IY",
					"displayName": "Current Y",
					"unit": "Amps"
				},
				{
					"abbr": "IB",
					"displayName": "Current B",
					"unit": "Amps"
				}
				];
				break;
			default:
				params = [];
		}
		return res.ok(params);
	}
};
/*
vfd =>
{
	{
	  "abbr": "OutputFrequency",
	  "displayName":"Frequency",
	  "unit": "Hz"
	},
	{
	  "abbr": "InputPower",
	  "displayName": "Input Power",
	  "unit": "Joule"
	},
	{
	  "abbr": "InputTerminalCurrent",
	  "displayName": "Input Terminal Current",
	  "unit": "Volt"
	},
	{
	  "abbr": "InputTerminalVolt",
	  "displayName": "Input Terminal Volt",
	  "unit": "Volt"
	}
}

EM =>
{
	{
	  "abbr": "KW",
	  "displayName": "Average Active Power",
	  "unit": "KW",
	},
	{
	  "abbr": "PF",
	  "displayName": "Power Factor",
	  "unit": "PF"
	},
	{
	  "abbr": "A_VLL",
	  "displayName": "Line Voltage",
	  "unit": "Volt"
	},
	{
	  "abbr": "A_VLN",
	  "displayName": "Line N Voltage",
	  "unit": "Volt"
	},
	{
	  "abbr": "A_AMP",
	  "displayName": "Average Current",
	  "unit": "Amps"
	},
	{
	  "abbr": "KVAH",
	  "displayName": "Active Energy",
	  "unit": "KvAh"
	},
	{
	  "abbr": "KVA",
	  "displayName": "Average Apparent Power",
	  "unit": "KvA"
	},
	{
	  "abbr": "KVAR",
	  "displayName": "Average Reactive Power",
	  "unit": "kVAr"
	},
	{
	  "abbr": "IR",
	  "displayName": "Current R",
	  "unit": "Amps"
	},
	{
	  "abbr": "IY",
	  "displayName": "Current Y",
	  "unit": "Amps"
	},
	{
	  "abbr": "IB",
	  "displayName": "Current B",
	  "unit": "Amps"
	}
}

CHILLER =>
	{
	  "abbr": "condEwt",
	  "displayName": "Condenser In Temperature",
	  "unit": "F"
	},
	{
	  "abbr": "condLwt",
	  "displayName": "Condenser Out Temperature",
	  "unit": "F"
	},
	{
	  "abbr": "coolEwt",
	  "displayName": "Chilled Water In Temperature",
	  "unit": "F"
	},
	{
	  "abbr": "coolLwt",
	  "displayName": "Chilled Water Out Temperature",
	  "unit": "F"
	},
	{
	  "abbr": "sctA",
	  "displayName": "Refrigerant Condenser Temperature",
	  "unit": "F"
	},
	{
	  "abbr": "sstA",
	  "displayName": "Refrigerant Evaporator Temperature  ",
	  "unit": "F"
	},
	{
	  "abbr": "SetPoint",
	  "displayName": "Running Chilled Water SetPoint",
	  "unit": "F"
	}
  ]

  joulesense =>
  {
	  "displayName": "Temperature",
	  "unit": "C",
	  "abbr": "TMP"
	}, {
	  "displayName": "Humidity",
	  "unit": "%AGE",
	  "abbr": "HUM"
	}


*/
