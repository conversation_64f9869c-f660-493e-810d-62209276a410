const Joi = require('joi');
const flaverr = require('flaverr');
/**
 *  @module Sitescontroller
 *  @description Sitescontroller module handles requests and maintains records about sites.
 */
//TODO : C check multiple networks in a site.
const moment = require("moment-timezone");
const helper = require("../services/helper");
moment.tz.add(
	"Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6"
);
moment.tz.setDefault("Asia/Kolkata");
const DEFAULT_ELECTRICITY_PRICE = 9;
module.exports = {
	//New functions started oct-4
	"initJouleTrack": async function(req, res) {
		let siteId = req.query.siteId;
		let role = req._userMeta["_role"];
		let userId = req._userMeta["id"];
		if (!role) {
			res.badRequest("No role");
			return res.end();
		}
		// [TODO] do a cache hit
		let policy = await Role.findOne({ "roleName": role });
		if (!policy) {
			res.badRequest("Invalid Policy");
			return res.end();
		}

		try {
			let promiseObj = await Promise.all([
				Devices.find({ siteId }),
				UserSiteMap.find({ userId }), //siteId, siteName
				componentService.getComponents(siteId),
				DeviceType.find({}),
				parametersService.getParameters(siteId),
			]);
			let devicesArr,
				usersites,
				componentList,
				currentSite,
				paramArr,
				paramObj = {};
			devicesArr = promiseObj[0];
			usersites = promiseObj[1];
			paramArr = promiseObj[4];
			componentList = promiseObj[2];

			let getSitesListPromise = usersites.map(userOnSiteData =>
				Sites.findOne({ "siteId": userOnSiteData["siteId"] })
			);
			let sites = await Promise.all(getSitesListPromise);

			let sitesList = sites.map(site => {
				if (site.siteId === siteId) currentSite = site;
				return { "siteId": site.siteId, "siteName": site.siteName };
			});
			paramArr.map(parameter => {
				let { deviceId, abbr } = parameter;
				if (!paramObj[deviceId]) paramObj[deviceId] = {};
				paramObj[deviceId][abbr] = parameter;
				delete parameter.deviceId;
				// delete parameter.abbr;
			});

			const devices = devicesArr.map(device => {
				const { deviceId } = device;
				if (paramObj[deviceId]) device.param = paramObj[deviceId];
				delete device.createdAt;
				delete device.updatedAt;
				return device;
			});
			let params = promiseObj[3];
			let resObj = {
				devices,
				componentList,
				sitesList,
				currentSite,
				params,
				policy,
			};
			return res.ok(resObj);
		} catch (e) {
			sails.log.error(e);
			return res.serverError({ "err": true });
		}
	},
	"initJouleTrackV2": async function(req, res) {
		try {
		let siteId = req.params.siteId;
		let role = req._userMeta["_role"];
		let userId = req._userMeta["id"];
		if (!role || !siteId || !userId) {
			res.status(400)
			return res.send({err: "Missing parameters. Please provide all required information."});
		}
		// [TODO] do a cache hit
		let [
			policy,
			site,
		] = await Promise.all([
			Role.findOne({ "roleName": role }),
			Sites.findOne({siteId, status: 1}),
		])
		if (!policy) {
			res.status(400)
			return res.send({err: "Unable to login.Please contact admin"});
		}
		
		if (!site) {
			res.status(400)
			return res.send({err: "Site not exist.Please contact admin"});
		}

		let { policies } = policy;
		policies = helper.toJson(policies);
		let flattenedPolicies = {};
		for (let eachPolicy in policies) {
			let subHeadings = policies[eachPolicy]["subHeadings"];
			let pageViewKey = `${eachPolicy}_View`;
			let pageViewValue = (policies[eachPolicy]["pageView"] === true) ? "1" : "0";
			flattenedPolicies[pageViewKey] = pageViewValue;
			for (let eachSubHead in subHeadings) {
				let innerPolicies = subHeadings[eachSubHead]["policies"];
				let camelCaseSubHead = eachSubHead.charAt(0).toUpperCase() + eachSubHead.slice(1);
				for (let eachInnerPolicy in innerPolicies) {
					let innerPolicyCameCase = eachInnerPolicy.charAt(0).toUpperCase() + eachInnerPolicy.slice(1);
					let key = `${camelCaseSubHead}_${innerPolicyCameCase}`;
					let value = (innerPolicies[eachInnerPolicy]["hasAccess"] === true) ? "1" : "0";
					flattenedPolicies[key] = value;
				}
			}
		}
		policy["policies"] = helper.toString(flattenedPolicies);

	

			let $deviceTypes = DeviceType.find({});
			let [
				devicesArr,
				usersites,
				componentList,
				paramArr,
				plantList,
			] = await Promise.all([
				Devices.find({ siteId }),
				UserSiteMap.find({ userId, status: 1 }), //siteId, siteName
				componentService.getComponents(siteId),
				parametersService.getParameters(siteId),
				processService.find({ "where": { siteId, "type": "plant" } }),
			]);
			let params = await $deviceTypes;
			let currentSite,
				paramObj = {};
			let getSitesListPromise = usersites.map(userOnSiteData =>
				Sites.findOne({ "siteId": userOnSiteData["siteId"] })
			);
			let sites = await Promise.all(getSitesListPromise);

			let sitesList = sites.map(site => {
				if (site.hasOwnProperty('unitCost') && site.unitCost != 0) {
					site.unitCost = Number(site.unitCost)
				} else {
					site.unitCost = DEFAULT_ELECTRICITY_PRICE
				}
				if (site.siteId === siteId) currentSite = site;
				const { siteName } = site;
				return { "siteId": site.siteId, siteName };
			});

			paramArr.map(parameter => {
				let {
					deviceId,
					abbr,
					displayName,
					utilityType,
					paramGroup,
					dau,
					errOffset
				} = parameter;
				if (!paramObj[deviceId]) paramObj[deviceId] = {};
				paramObj[deviceId][abbr] = {
					abbr,
					displayName,
					utilityType,
					paramGroup,
					dau,
					errOffset
				};
			});

			const devices = devicesArr.map(device => {
				const { deviceId } = device;
				if (paramObj[deviceId]) device.param = paramObj[deviceId];
				if(!device.hasOwnProperty("isVirtualDevice")) {
					device["isVirtualDevice"] = "0";
				}
				delete device.createdAt;
				delete device.updatedAt;
				return device;
			});
			const plants = plantList.map(plant => ({
				"name": plant.name,
				"processId": plant.processId,
				"plantCategories": plant.plantCategories,
			}));

			params = params.map(param => {
				let tempArray = helper.toArray(param.parameters);
				param.parameters = tempArray ? tempArray : [];
				param.params = param.parameters.map(helper.toJson);
				return param;
			});

			let resObj = {
				devices,
				componentList,
				sitesList,
				currentSite,
				plants,
				policy,
				params,
			};
			return res.ok(resObj);
		} catch (e) {
			sails.log.error(e);
			res.status(500)
			return res.send({err: 'The server encountered an unexpected condition. Please try again later or contact the administrator if the problem persists.'});
		}
	},
	"addNewSite": async (req, res) => {
		let siteInfo = req.param("siteInfo");
		let currentSiteOfUser = req._userMeta._site;
		if (!siteInfo || typeof siteInfo != "object") {
			sails.log.error("Bad request");
			return res.badRequest();
		}
		let name = siteInfo.siteName.trim();
		let location = siteInfo.location.trim();
		let timezone = siteInfo.timezone;

		if (!name || !location || !timezone) return res.badRequest();

		// Checking if the timezone is valid using moment-timezone
		if (!timezone || !moment.tz.names().includes(timezone)) {
			sails.log.error("Invalid timezone");
			return res.badRequest({ error: "Invalid timezone" });
		}

		let siteId = null;
		try {
			siteId = await siteService.returnSiteId(name, location);
		} catch (e) {
			sails.log.error(e);
			return res.serverError();
		}
		let siteObj = { ...siteInfo, siteId };
		Sites.create(siteObj)
			.then(data => {
				let eventData = {
					"event": "create",
					"data": {
						"siteId": data.siteId,
						"name": data.siteName,
					},
				};
				eventService.notifyJouleTrack(
					currentSiteOfUser,
					"public",
					"newSite",
					eventData
				);
				siteService.giveAccessToDevelopers(data.siteId).catch(error => {
					sails.log.error(
						"[addNewSite] Error giving access to developers! : ",
						error
					);
				});
				return res.ok({ "status": "done", siteId });
			})
			.catch(err => {
				sails.log.error(err);
				return res.serverError({ "status": "SERVER_ERROR" });
			});
	},
	"getSiteInfo": async (req, res) => {
		let siteId = req.param("siteId");
		if (!siteId) return res.badRequest();
		try {
			let data = await Sites.findOne({ "siteId": siteId });
			if (!data) {
				return res.ok({});
			}
			return res.ok(data);
		} catch (e) {
			sails.log.error(e);
			return res.serverError();
		}
	},
	"editSiteInfo": async (req, res) => {
		let siteInfo = req.param("siteInfo");
		let siteId = req.param("siteId");
		let oldSiteInfo = [];
		if (!siteInfo || !siteId) {
			return res.badRequest();
		}

		try {
			oldSiteInfo = await Sites.findOne({ "siteId": siteId });
		} catch (e) {
			sails.log.error(e);
			return res.serverError();
		}
		if (!oldSiteInfo) {
			return res.badRequest({ "err": "This site does not exist" });
		}
		if (
			typeof siteInfo.regions != undefined &&
			typeof siteInfo.areas != undefined &&
			typeof siteInfo.network != undefined
		) {
			Object.keys(siteInfo).forEach(e => {
				if (!siteInfo[e] || (typeof e === "string" && e === ""))
					delete siteInfo[e];
			});
			Sites.update({ "siteId": siteId }, { ...siteInfo })
				.then(data => {
					sails.log(data);
					let eventData = {
						"event": "update",
						"data": data,
					};
					eventService.notifyJouleTrack(
						siteId,
						"public",
						"sites",
						eventData
					);

					return res.ok({ "status": "done" });
				})
				.catch(err => {
					sails.log.error(err);
					return res.serverError({ "status": "SERVER_ERROR" });
				});
		} else {
			return res.badRequest();
		}
	},
	"addRegion": async (req, res) => {
		let siteId = req.param("siteId");
		let region = req.param("region");
		let regionObj = {};
		let existingSite, regionId;

		if (!siteId || !region || !region.name || !region.areaId) {
			return res.badRequest();
		}
		try {
			existingSite = await Sites.findOne({ "siteId": siteId });
		} catch (e) {
			sails.log.error(e);
			return res.serverError();
		}

		//A loop for failsafe if generated string is already an areaId
		while (1) {
			regionId = jwtService.generateRandomString(4);
			if (!existingSite.regions || !existingSite.regions[regionId]) break;
		}
		//region object to be saved in db
		regionObj = {
			"name": region.name,
			"area": region.areaId,
			"controller": [],
		};
		if (!existingSite.regions) {
			existingSite["regions"] = {};
			existingSite.regions[regionId] = regionObj;
		} else {
			//check if region of same name exists in the same area
			for (let rId in existingSite.regions) {
				let regionInfo = existingSite.regions[rId];
				if (
					regionInfo.name === region.name &&
					regionInfo.area === region.areaId
				)
					return res.badRequest({
						"err": "Region With same name exists in the Area",
					});
			}
		}
		if (!existingSite.areas || !existingSite.areas[regionObj.area]) {
			return res.badRequest({ "err": "Not a valid area" });
		} else {
			existingSite.areas[regionObj.area].regions.push(regionId);
		}

		existingSite.regions[regionId] = regionObj;
		Sites.update(
			{ "siteId": siteId },
			{ "regions": existingSite.regions, "areas": existingSite.areas }
		)
			.then(d => {
				let eventData = {
					"data": d,
					"event": "update",
				};
				eventService.notifyJouleTrack(
					siteId,
					"public",
					"sites",
					eventData
				);
				return res.ok(d);
			})
			.catch(e => {
				sails.log.error(e);
				return res.serverError(e);
			});
	},
	"editRegion": async (req, res) => {
		//Only name of region is editable
		let siteId = req.param("siteId");
		let region = req.param("region");
		let existingSite;

		if (!siteId || !region || !region.regionId || !region.name) {
			return res.badRequest();
		}
		try {
			existingSite = await Sites.findOne({ "siteId": siteId });
		} catch (e) {
			sails.log.error(e);
			return res.serverError();
		}
		if (!existingSite.regions || !existingSite.regions[region.regionId]) {
			return res.badRequest();
		} else {
			existingSite.regions[region.regionId]["name"] = region.name;
		}
		Sites.update({ "siteId": siteId }, { "regions": existingSite.regions })
			.then(d => {
				let eventData = {
					"data": d,
					"event": "update",
				};
				eventService.notifyJouleTrack(
					siteId,
					"public",
					"sites",
					eventData
				);

				return res.ok(d);
			})
			.catch(e => {
				sails.log.error(e);
				return res.serverError(e);
			});
	},
	"addArea": async (req, res) => {
		let siteId = req.param("siteId");
		let area = req.param("area");
		let areaObj = {};
		let existingSite, areaId;
		if (!siteId || !area || !area.name) {
			sails.log(area);
			return res.badRequest();
		}
		try {
			existingSite = await Sites.findOne({ "siteId": siteId });
		} catch (e) {
			sails.log.error(e);
			return res.serverError();
		}

		//A loop for failsafe if generated string is already an areaId
		while (1) {
			areaId = jwtService.generateRandomString(4);
			if (!existingSite.areas || !existingSite.areas[areaId]) break;
		}
		areaObj = {
			"name": area.name,
			"regions": [],
		};
		if (!existingSite.areas) {
			existingSite["areas"] = {};
			existingSite.areas[areaId] = areaObj;
		} else {
			//check if area of same name exists in the same area
			for (let aId in existingSite.areas) {
				let areaInfo = existingSite.areas[aId];
				if (areaInfo.name === area.name)
					return res.badRequest({
						"err": "area With same name exists in the Area",
					});
			}
			existingSite.areas[areaId] = areaObj;
		}

		Sites.update({ "siteId": siteId }, { "areas": existingSite.areas })
			.then(d => {
				let eventData = {
					"event": "update",
					"data": d,
				};
				eventService.notifyJouleTrack(
					siteId,
					"public",
					"sites",
					eventData
				);

				return res.ok(d);
			})
			.catch(e => {
				sails.log.error(e);
				return res.serverError(e);
			});
	},
	"editArea": async (req, res) => {
		let siteId = req.param("siteId");
		let area = req.param("area");
		let existingSite;

		if (!siteId || !area || !area.areaId || !area.name) {
			return res.badRequest();
		}
		try {
			existingSite = await Sites.findOne({ "siteId": siteId });
		} catch (e) {
			sails.log.error(e);
			return res.serverError();
		}
		if (!existingSite.areas || !existingSite.areas[area.areaId]) {
			return res.badRequest();
		} else {
			existingSite.areas[area.areaId]["name"] = area.name;
		}
		Sites.update({ "siteId": siteId }, { "areas": existingSite.areas })
			.then(d => {
				sails.log.silly(d);
				let eventData = {
					"event": "update",
					"data": d,
				};
				eventService.notifyJouleTrack(
					siteId,
					"public",
					"sites",
					eventData
				);

				return res.ok(d);
			})
			.catch(e => {
				sails.log.error(e);
				return res.serverError(e);
			});
	},
	"addNetwork": async (req, res) => {
		let siteId = req.param("siteId");
		let network = req.param("network");
		let existingSite;

		if (!siteId || !network || !network.vendor || !network.type) {
			return res.badRequest();
		}
		try {
			existingSite = await Sites.findOne({ "siteId": siteId });
		} catch (e) {
			sails.log.error(e);
			return res.serverError();
		}
		//generateNetworkId
		let networkId = network.vendor + "-" + network.type;
		if (!existingSite.networks) {
			existingSite.networks = {};
			networkId += "-0";
		} else {
			let max = 0;
			Object.keys(existingSite.networks).forEach(key => {
				let number = Number(key.split("-")[2]);
				if (max < number) number = max;
			});
			max += 1;

			networkId += "-" + max;
		}
		existingSite.networks[networkId] = [];
		Sites.update(
			{ "siteId": siteId },
			{ "networks": existingSite.networks }
		)
			.then(d => {
				let eventData = {
					"data": d,
					"event": "update",
				};
				eventService.notifyJouleTrack(
					siteId,
					"public",
					"sites",
					eventData
				);
				return res.ok(d);
			})
			.catch(e => {
				sails.log.error(e);
				return res.serverError(e);
			});
	},
	"publishMockSocketEvents": async(req, res) =>{
		try {

			let [eventRoom, socketRoom, message] = [
				req.param('eventRoom'),
				req.param('socketRoom'),
				req.param('message'),
			];
			const schema = Joi.object({
				eventRoom: Joi.string().regex(/^jt_(.+)_public$/, 'event room format: jt_${siteId}_public').required(),
				socketRoom: Joi.string().required(),
				message: Joi.string().required(),
			}).unknown(true)
			
			// Validate the data against the schema
			const { error } = schema.validate({
				eventRoom,
				socketRoom,
				message
			});
			
			// Check for errors
			if (error) {
				throw flaverr('E_INPUT_VALIDATION', new Error(error.message));
			}
			try {
				message = JSON.parse(message);
			} catch(e) {
				//Nothing
			}
			
		   sails.sockets.broadcast(eventRoom, socketRoom, message);
		   return res.ok({
			msg: "Socket event emitted successfully"
		   })
		} catch(e) {
			if (e.code == 'E_INPUT_VALIDATION') {
				return res.badRequest({err: e.message})
			} else {
				return res.serverError({err: 'Server has encountered an issue. Please contact admin'});
			}
		}
			
	}
};
