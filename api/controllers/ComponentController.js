const moment = require("moment-timezone");
const emitter = eventService.emmitter;
const ComponentService = require('../services/componentService');
const siteService = require("../services/siteService");
const cacheService = require("../services/cacheService");
const { getAccessTokenFromReq } = require('../utils/global');
moment.tz.add(
	"Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6"
);
moment.tz.setDefault("Asia/Kolkata");
module.exports = {
	"getAllComponentsStatus": async (req, res) => {
		let siteId = req.param("siteId");

		if (!siteId) return res.badRequest({ "err": "Invalid Request Format" });

		try {
			let compList = await Component.find({ "siteId": siteId });

			let resultP = compList.map(component => {
				let searchObj = {
					"where": {
						"deviceId": component.deviceId,
						"timestamp": {
							"gte": moment()
								.subtract(15, "minutes")
								.format("YYYY-MM-DD HH:mm"),
						},
					},
					"sort": "-1",
					"limit": 1,
				};
				return dataDeviceService.dataForRecentData(searchObj);
			});
			let data = await Promise.all(resultP);
			let resObj = [];
			for (let i = 0; i < data.length; i++) {
				if (data[i] === undefined) {
					resObj.push({
						"deviceId": compList[i]["deviceId"],
						"status": "NA",
					});
				} else if (data[i]["data"]["status"] == 1) {
					resObj.push({
						"deviceId": compList[i]["deviceId"],
						"status": "On",
					});
				} else if (data[i]["data"]["status"] == 0) {
					resObj.push({
						"deviceId": compList[i]["deviceId"],
						"status": "Off",
					});
				} else {
					resObj.push({
						"deviceId": compList[i]["deviceId"],
						"status": "NA",
					});
				}
			}
			return res.ok(resObj);
		} catch (err) {
			sails.log.error(err);
			return res.ok({
				"err": "Unable to fetch data at present",
				"data": null,
			});
		}
	},
	/**
	 * @function getAllComponents
	 * @description returns all the components of a siteId
	 */
	"getAllComponents": async (req, res) => {
		let siteId = req.param("siteId");
		if (!siteId) {
			return res.badRequest({ "err": "Invalid request format" });
		}
		try {
			const components = await componentService.getComponents(siteId);
			return res.ok(components);
		} catch (err) {
			sails.log.error(err);
			return res.serverError();
		}
	},
	//saves info of component for the first time, when controls and data are not mentioned
  "addComponent": async (req, res) => {
    try {
      const component = req.param("component");
      const { siteId, name, deviceType, driverType, controllerId, regionId, svgId } = component;
      if (!siteId || !name || !deviceType || !controllerId || !regionId || !svgId) {
        return res.badRequest();
      }
      component.driverType = driverType ? String(driverType) : "0";
      component.isVirtualComponent = component.hasOwnProperty("isVirtualComponent") ? component.isVirtualComponent : "0";

      const isDriverDetailExists = await DeviceType.findOne({ driverType: component.driverType, deviceType });
      if (!isDriverDetailExists) {
        return res.badRequest({ message: `driver detail does not exist. driverType: ${driverType} and deviceType: ${deviceType}` });
      }

      const ControllerDeviceRecord = await Devices.findOne({ deviceId: controllerId, siteId });
      if (!ControllerDeviceRecord) {
        return res.badRequest({ message: `Controller ID : ${controllerId} does not exists` });
      }

      const nextComponentIdCounter = await ComponentService.getNextComponentIdNumber(siteId);
      if (nextComponentIdCounter === -1) {
        return res.badRequest({ "err": "Unable to generate deviceId" });
      }
      const nextComponentId = `${siteId}_${nextComponentIdCounter}`;
      component.deviceId = nextComponentId;

      emitter.emit("initChange", req);

      const NewlyAddedComponent = await Component.create(component);
      const createEventData = {
        "event": "create",
        "data": [NewlyAddedComponent],
      };
      eventService.notifyJouleTrack(siteId, "public", "components", createEventData);
      emitter.emit("commitChange", req, "component", [], NewlyAddedComponent);

      const { deviceId: componentId } = NewlyAddedComponent;
      const taggedComponentIds = ControllerDeviceRecord.componentId ? ControllerDeviceRecord.componentId.split(",") : [];
      taggedComponentIds.push(componentId);
      const updatedDeviceRecord = await Devices.update({ deviceId: controllerId, siteId }, { componentId: taggedComponentIds.join(",") });
      const updateEventData = {
        "event": "create",
        "data": updatedDeviceRecord,
      };
      eventService.notifyJouleTrack(siteId, "public", "devices", updateEventData);
      emitter.emit("commitChange", req, "devices", ControllerDeviceRecord, updatedDeviceRecord);
      emitter.emit("pushChange", req);

      await ComponentService.buildComponentControlRelationshipMap(componentId, getAccessTokenFromReq(req));
	  await siteService.removeSiteCategoriesFromCache(siteId)
      return res.created(NewlyAddedComponent);
    } catch (error) {
      sails.log.error(error);
      emitter.emit("stashChange", req);
      return res.serverError({
        "err": "Unable to save data at present",
      });
    }
  },
	//get all information of a specific component from it's deviceId
	"getComponent": function (req, res) {
		let cid = req.param("cid");
		if (!cid) return res.badRequest({ "err": "Invalid request format" });
		Component.findOne({ "deviceId": cid })
			.then(components => {
				helper.convertStringArrayToJSONArray(components.data);
				helper.convertStringArrayToJSONArray(components.controls);
				return res.ok(components);
			})
			.catch(err => {
				sails.log.error(err);
				return res.serverError({
					"err": "Unable to query DB",
					"data": null,
				});
			});
	},
	/**
	 *  @function editComponent
	 *  @param {object} req
	 *  @param {object} res
	 *
	 **/
	"editComponent": async (req, res) => {
		let component = req.param("component");
		let deviceId = req.param("cid");
		if (!component || !deviceId) return res.badRequest();
		let { name, data, controls, regionId, controllerId } = component;
		let newComponentObj = {};

		try {
			let comp = await Component.findOne({
				"deviceId": component.deviceId,
			});
			if (!comp) return req.badRequest();
			//using these helpers till we have support for MAPS in sails
			helper.convertStringArrayToJSONArray(comp.data);
			helper.convertStringArrayToJSONArray(comp.controls);

			let siteId = comp.siteId;
			if (name && name != comp.name) {
				newComponentObj["name"] = name;
			}
			if (regionId && regionId != comp.regionId) {
				newComponentObj["region"] = regionId;
			}
			if (controllerId && controllerId != comp.controllerId) {
				newComponentObj["controllerId"] = controllerId;
			}
			if (data) {
				newComponentObj["data"] = data;
			}
			if (controls) {
				newComponentObj["controls"] = controls;
			}
			newComponentObj.deviceId = comp.deviceId;
			newComponentObj.siteId = comp.siteId;

			if (!comp.data) {
				comp.data = [];
			}
			if (!comp.controls) {
				comp.controls = [];
			}
			let oldDevices = new Set();
			comp.data.map(d => {
				if (d.deviceId) {
					d.deviceId
						.toString()
						.split(",")
						.map(e => {
							if (e == "" || !e) return;
							oldDevices.add(e);
						});
				}
			});
			comp.controls.map(d => {
				if (d.deviceId) {
					d.deviceId
						.toString()
						.split(",")
						.map(e => {
							if (e == "" || !e) return;
							oldDevices.add(e);
						});
				}
				if (d.expression) {
					helper.parseFormulaToIds(d.expression).map(e => {
						oldDevices.add(e);
					});
				}
				//helper
			});

			let newDevices = new Set();
			if (data) {
				data.map(d => {
					if (d.deviceId) {
						d.deviceId
							.toString()
							.split(",")
							.map(e => {
								if (e == "" || !e) return;
								newDevices.add(e);
							});
					}
				});
			}
			if (controls) {
				controls.map(d => {
					if (d.deviceId) {
						d.deviceId
							.toString()
							.split(",")
							.map(e => {
								if (e == "" || !e) return;
								newDevices.add(e);
							});
					}
					if (d.expression) {
						helper.parseFormulaToIds(d.expression).map(e => {
							newDevices.add(e);
						});
					}
				});
			}
			sails.log.info("newDevices", [...newDevices]);
			sails.log.info("oldDevices", [...oldDevices]);

			let addedDev = helper.setDifference(newDevices, oldDevices);
			let deletedDev = helper.setDifference(oldDevices, newDevices);
			sails.log.info("added", [...addedDev]);
			sails.log.info("removed", [...deletedDev]);

			//converting to array of string to be removed after migration
			helper.convertJSONArrayToStringArray(data);
			helper.convertJSONArrayToStringArray(controls);
			emitter.emit("initChange", req);
			Component.update({ "deviceId": deviceId }, { ...newComponentObj })
				.then(d => {
					emitter.emit("commitChange", req, "component", comp, d);
					helper.convertStringArrayToJSONArray(d[0]["data"]);
					helper.convertStringArrayToJSONArray(d[0]["controls"]);
					let eventData = {
						"event": "update",
						"data": d,
					};
					eventService.notifyJouleTrack(
						siteId,
						"public",
						"components",
						eventData
					);

					return deviceService.updateComponentInfoInDevices(
						addedDev,
						deletedDev,
						deviceId,
						siteId
					);
				})
				.then(async data => {
					// console.log(data);
					emitter.emit("pushChange", req);
					await deviceService.updateConfigTs({ siteId });
					return res.ok({ "updated": "true" });
				})
				.catch(e => {
					sails.log.error(e);
					emitter.emit("stashChange", req);
					return res.serverError();
				});
		} catch (e) {
			sails.log.error(e);
			return res.serverError();
		}
	},
	//Will be used while on configuring a components to add/modify data and control objects, returns a list of all devicetypes that can be added to a port in devices.
	"getComponentConfig": async (req, res) => {
		let cid = req.param("cid");
		if (!cid) return req.badRequest();
		try {
			let comp = await Component.findOne({ "deviceId": cid });
			if (!comp)
				return res.badRequest({
					"err": "No such component configured",
				});
			let { driverType, deviceType } = comp;
			let deviceTypeInfo = await DeviceType.findOne({
				"driverType": driverType,
				"deviceType": deviceType,
			});
			if (!deviceTypeInfo) return res.ok(comp);
			let paramList = deviceTypeInfo.parameters.map(helper.toJson);
			let dataKeys = {};
			let controlKeys = {};
			if (!comp.data) {
				comp.data = [];
			}
			if (!comp.controls) {
				comp.controls = [];
			}
			helper.convertStringArrayToJSONArray(comp.data);
			helper.convertStringArrayToJSONArray(comp.controls);
			comp.data.map(c => {
				dataKeys[c.key] = c;
			});
			comp.controls.map(c => {
				controlKeys[c.key] = c;
			});
			paramList.map(e => {
				let keys = {};
				if (e.type == "data") {
					keys = dataKeys;
					if (!keys[e.abbr]) {
            let _parameter={
							"key": e.abbr,
							"expression": "",
							"deviceId": "",
							"displayName": e.displayName,
							"dau": e.dau,
							"paramGroup": e.paramGroup,
							"min": "",
							"max": "",
						}
            if(e.hasOwnProperty("mockDataSet") && Array.isArray(e.mockDataSet) && e.mockDataSet.length){
              _parameter.mockDataSet=e.mockDataSet;
            }
						comp.data.push(_parameter);
					}
				} else {
					keys = controlKeys;
					if (!keys[e.abbr]) {
						comp.controls.push({
							"key": e.abbr,
							"expression": "",
							"deviceId": "",
							"displayName": e.displayName,
							"dau": e.dau,
							"paramGroup": e.paramGroup,
							"min": "",
							"max": "",
							"timeout": "",
						});
					}
				}
			});
			return res.ok(comp);
		} catch (e) {
			sails.log.error(e);
			return res.serverError();
		}
	},
	"deleteComponent": async (req, res) => {
		let { deviceId } = req.allParams();
		if (!deviceId) return res.badRequestWithErrorResponse({ "err": "Insufficient Params" });
		try {
			let comp = await Component.findOne({ deviceId });
			if (comp === undefined) {
				return res.badRequestWithErrorResponse({ "err": "Invalid Params" });
			}

      const cacheKey = `componentConfig:componentId:${deviceId}`;
      await cacheService.del(cacheKey);

			const linkedConfSysNames = await ComponentService.getLinkedConfiguratorSystemNameByComponentId(deviceId);
			if(linkedConfSysNames.length){
				return res.badRequestWithErrorResponse(
					{
						err: `Unable to delete these component id(s) because  component(s) ${linkedConfSysNames.map(it => `"${it.device_id}"`)
							.join(',')} is/are connected to Configurator system(s) ${linkedConfSysNames.map(it => `"${it.name}"`)
							.join(',')} respectively. `,
					}
				);
			}
      const linkedConfPages = await ComponentService.getLinkedConfiguratorPageByComponentId(deviceId);
      if (linkedConfPages.length) {
        return res.badRequestWithErrorResponse(
          {
            err: `Unable to delete these component id(s) because  component(s) ${linkedConfPages.map(it => `"${it.device_id}"`)
              .join(',')} is/are connected to Configurator page(s) ${linkedConfPages.map(it => `"${it.title}"`)
                .join(',')} respectively. `,
          }
        );
      }
			let { siteId } = comp;
			emitter.emit("initChange", req);
			let devices = await getDevicesOnComponent(siteId, deviceId);
			let updatedDevs = await updateDevices(devices);
   			await siteService.removeSiteCategoriesFromCache(siteId)
			emitter.emit("commitChange", req, "devices", updatedDevs, devices);
			let eventData = {
				"event": "update",
				"data": updatedDevs,
			};
			eventService.notifyJouleTrack(
				siteId,
				"public",
				"devices",
				eventData
			);

			// Deleting Component Linking in Parameter configuration added via Bulk Configuration if present.
			const { deviceParameterList } = comp;
			if (deviceParameterList && Array.isArray(deviceParameterList) && deviceParameterList.length != 0){
				const $updateQueries = deviceParameterList.map(deviceId_abbr => {
					return Parameters.update({
						siteId,
						deviceId_abbr
					}, { componentId: "" });
				});
				await Promise.all($updateQueries);
			}

			await Component.destroy({ deviceId, siteId });

			let compEvent = {
				"event": "delete",
				"data": [{ deviceId }],
			};
			eventService.notifyJouleTrack(
				siteId,
				"public",
				"components",
				compEvent
			);

			emitter.emit("commitChange", req, "component", comp, []);
			emitter.emit("pushChange", req);
			await deviceService.updateConfigTs({ siteId });
			await ComponentService.deleteComponentControlRelationship(siteId, deviceId)
			return res.ok({ "done": true });
		} catch (e) {
			sails.log.error(e);
			return res.serverError({ "err": "Internal Server Error" });
		}

		/** Helpers */
		async function getDevicesOnComponent(siteId, componentId) {
			let devices = await Devices.find({ siteId });
			return devices.filter(dev => dev.componentId === componentId);
		}
		async function updateDevices(devices) {
			let promiseArr = [];
			for (let device of devices) {
				let { siteId, deviceId } = device;
				let componentId = "null";
				promiseArr.push(
					Devices.update({ siteId, deviceId }, { componentId })
				);
			}
			return Promise.all(promiseArr);
		}
	},
	/**
	 * Save chemical pricing & Size for each chemicalType in the Chemical component .
	 * { biocidel: {size, prize} }
	 */
	"editComponentChemicalPrice": async (req, res) => {
		try {
			let siteId = req._userMeta._site;
			let { chemicalsPrize, deviceId } = req.body;


			if (chemicalsPrize.constructor.name !== "Object") {
				return res.badRequest({ "problems": ["Invalid request object"] });
			}
			let { biocidel, orpl, tds, phl } = chemicalsPrize;
			let saveObject = {};

			biocidel = componentService.formatChemicalPriceAndCost(biocidel);
			orpl = componentService.formatChemicalPriceAndCost(orpl);
			tds = componentService.formatChemicalPriceAndCost(tds);
			phl = componentService.formatChemicalPriceAndCost(phl);

			if ( biocidel ) {
				saveObject["biocidel"] = biocidel;
			}
			if ( orpl ) {
				saveObject["orpl"] = orpl;
			}
			if ( tds ) {
				saveObject["tds"] = tds;
			}
			if ( phl ) {
				saveObject["phl"] = phl;

			}
			saveObject = JSON.stringify(saveObject);

			await Component.update({ siteId, deviceId }, { "chemicalsPrice": saveObject });

			return res.ok();


		} catch (e) {
			sails.log.error(e);
			return res.serverError();
		}
	}
};
