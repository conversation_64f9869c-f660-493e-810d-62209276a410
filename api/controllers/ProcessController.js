const processUtils = require("../utils/processUtils");
module.exports = {
	/**
	 * @function create
	 * @param {Object} req
	 *
	 * @param {Object} res
	 * @description API endpoint for creating process of type
	 *
	 */
	"create": async function(req, res) {
		try {
			const util = processUtils.create;
			const proc = req.body;
			const inputCheck = util.checkInput(proc);
			if (!inputCheck.status) {
				return res.badRequest(inputCheck);
			}
			const processObj = util.getProcessObj(proc);
			const isInputValid = await processService.validateParameters(
				processObj
			);
			if (!isInputValid) {
				return res.badRequest({
					"err": "Invalid parameter values provided",
				});
			}
			try {
				const processId = await processService.generateProcessId(
					processObj.siteId
				);
				processObj.processId = processId;
			} catch (err) {
				return res.serverError({ err });
			}
			let $processCreate, $deviceUpdate, $parentProcessUpdate;
			$processCreate = processService.create(processObj);
			if (processObj.type === "component"){
				$deviceUpdate = updateControllerConfiguration(processObj);
			}
			$parentProcessUpdate = updateParentProcess(processObj);
			await $processCreate;
			await $deviceUpdate;
			await $parentProcessUpdate;
			sendComponentSocketBroadcast(processObj);
			res.ok({ "status": true });
			
		} catch (err) {
			sails.log.error("[ProcessController > create] Server Error!");
			sails.log(err);
			return res.serverError(err);
		}
		// Helper functions
		/**
		 * @function updateControllerConfiguration
		 * @description If process.type == "component", update controller's "componentId" on which the component is being created. Also, send FE socket broadcasts for changes in both component and device data being stored on FE.
		 * @param {object} processObj Process Object
		 */
		async function updateControllerConfiguration(processObj){
			try {
				let updatedDeviceInfo = await Devices.update(
					{ "deviceId": processObj.controllerId, "siteId": processObj.siteId },
					{ "componentId": processObj.processId }
				);
				let deviceEventData = {
					"event": "create",
					"data": updatedDeviceInfo
				};
				eventService.notifyJouleTrack(processObj.siteId, "public", "devices", deviceEventData);
			} catch (error) {
				throw new Error("Process > create [updateControllerConfiguration]: Error updating controller configuration.", error);
			}
		}
		/**
		 * @function updateParentProcess
		 * @description If "parentProcess" key is present in the object, fetches the parent process and updates its
		 * "childProcesses" string set.
		 * @param {object} processObj Process Object with filtered keys and verified keys
		 */
		async function updateParentProcess(processObj){
			try {
				if (processObj.parentProcess !== undefined && typeof processObj.parentProcess === "string"){
					const { "parentProcess": parentProcessId, processId } = processObj;
					let parentProcessObject = await processService.find({
						"processId": parentProcessId
					});
					parentProcessObject = parentProcessObject[0]; // As process.findOne is not available in this adapter.
					let { childProcesses } = parentProcessObject;
					childProcesses = new Set(childProcesses);
					childProcesses.add(processId);
					childProcesses = Array.from(childProcesses);
					await processService.update(
						{ "processId": parentProcessId },
						{ childProcesses }
					);
				}	
			} catch (error) {
				throw new Error(error);
			}
		}
		function sendComponentSocketBroadcast(processObject){
			processObject["deviceId"] = processObject.processId; // To maintain backward compatibilities with component schema.
			let componentEventData = {
				"event": "create",
				"data": [processObject],
			};
			eventService.notifyJouleTrack( processObject.siteId, "public", "components", componentEventData );
		}
	},
	/**
	 *
	 * @param {Object} req
	 * @param {string} req.params.siteId
	 * @param {string} req.query.type for filtering a specific type of process
	 * @param {string} req.query.componentType for filtering a specific type of component
	 * @param {string} req.query.isDisplayed for filtering if process is to be displayed or not
	 * @param {string} req.query.select keys which should be returned in response
	 * shoule contain , seperated keys
	 * @param {Object} res
	 * @returns array of processes matching specific conditions
	 */
	"findAll": async function(req, res) {
		const { siteId } = req.params;
		const util = processUtils.findAll;
		try {
			const query = util.prepareFindQuery(siteId, req.query);
			const processes = await processService.find(query);
			return res.ok(processes);
		} catch (err) {
			sails.log.error("ERROR", err);
			return res.serverError();
		}
	},
	/**
	 * 
	 * @param {Object} req 
	 * @param {string} req.params.processId Id of the process to be updated
	 * @param {array} req.body.rawParams Array of parameters to be updated
	 * @param {array} req.body.controls Array of control parameters to be updated
	 * @param {object} res 
	 */
	"updateParams": async function(req, res) {
		const { rawParams, controls, queriedParams } = req.body;
		const { processId } = req.params;
		const siteId = req._userMeta._site;
		try {
			let process = await processService.find({ processId });
			if (process.length === 0) {
				return res.badRequest({ "err": "No process found!" });
			}
			process = process[0];
			const util = processUtils.updateRawParams;
			const rawParamsValidity = util.isInputValid(rawParams);
			const queriedParamsValidity = util.isInputValid(queriedParams);
			if (rawParamsValidity.status === false) {
				return res.badRequest({ "err": rawParamsValidity.message });
			}
			if (queriedParamsValidity.status === false) {
				return res.badRequest({ "err": queriedParamsValidity.message });
			}
			const rawParamdeviceIds = util
				.getDiff(process.rawParams, rawParams)
				.map(util.getDeviceIds)
				.reduce((devices, deviceArr) => {
					devices.push(...deviceArr);
					return devices;
				}, []);
			const queriedParamdeviceIds = util
				.getDiff(process.queriedParams, queriedParams)
				.map(util.getDeviceIds)
				.reduce((devices, deviceArr) => {
					devices.push(...deviceArr);
					return devices;
				}, []);
			const deviceIds = [...rawParamdeviceIds, ...queriedParamdeviceIds];
			const uniqueDeviceIds = [...new Set(deviceIds)];
			const $devices = uniqueDeviceIds.map(deviceId =>
				processService.validateParameters({ deviceId })
			);
			const devices = await Promise.all($devices);
			const presentStatus = devices.filter(status => status === false);
			if (presentStatus.length !== 0) {
				return res.badRequest({
					"err": `${presentStatus.length} invalid devices present in expression`,
				});
			}
			const sanitizedRawParams = util.sanitizeParams(rawParams);
			const sanitizedQueriedParams = util.sanitizeParams(queriedParams);
			const sanitizedControls = util.sanitizeParams(controls);
			await processService.update({ processId }, {
				"rawParams": sanitizedRawParams,
				"controls": sanitizedControls, // TODO: Temporarily adding controls as it is. Add checks similar to rawParams,
				"queriedParams": sanitizedQueriedParams,
			});
			await deviceService.updateConfigTs({ siteId });
			res.ok({ "status": "done" });
			sendComponentSocketBroadcast(process, siteId, sanitizedRawParams, sanitizedControls, sanitizedQueriedParams);
		} catch (error) {
			sails.log.error("[updateParams] Server Error!", error);
			return res.serverError({
				"message": "Internal Server Error",
			});
		}
		// Helper functions
		function sendComponentSocketBroadcast(processObject, siteId, sanitizedRawParams, sanitizedControls, sanitizedQueriedParams){
			processObject["rawParams"] = sanitizedRawParams;
			processObject["controls"] = sanitizedControls;
			processObject["queriedParams"] = sanitizedQueriedParams;
			processObject["deviceId"] = processObject.processId; // To maintain backward compatibilities with component schema.
			let componentEventData = {
				"event": "update",
				"data": [processObject],
			};
			eventService.notifyJouleTrack( siteId, "public", "components", componentEventData );
		}
	},
	/**
	 * @function updateProcessInfo
	 * @description API used to update process information like 
	 * @param {object} req.body.processObject Raw Process object which is going to replace the older process Object.
	 */
	"updateProcessInfo": async function (req, res){
		try {
			const { processId } = req.params;
			const siteId = req._userMeta._site;
			let processObject = req.body;
			let process = await processService.find({	
				processId	
			});	
			if (process.length === 0){	
				return res.badRequest("No such process configured!");	
			}	
			delete processObject.processId;	
			delete processObject.siteId;
			await processService.update({ processId }, processObject);
			sendComponentSocketBroadcast(processObject, siteId);
			return res.ok(processObject);	
		} catch (error) {
			sails.log.error("[updateProcessInfo] Server Error!", error);
			return res.serverError({
				"message": "Internal Server Error"
			});
		}
		// Helper functions
		function sendComponentSocketBroadcast(processObject, siteId){
			processObject["deviceId"] = processObject.processId; // To maintain backward compatibilities with component schema.
			let componentEventData = {
				"event": "update",
				"data": [processObject],
			};
			eventService.notifyJouleTrack( siteId, "public", "components", componentEventData );
		}
	},
	/**
	 * @function getProcessConfig
	 * @description Returns Process configuration for a ProcessId and type='component' as requried by Frontend during configuration of its parameters.
	 * @param {*} req 
	 */
	"getProcessConfig": async function(req, res){
		try {
			let util = processUtils.getProcessConfig;

			let inputCheck = util.checkInput(req.params);
			if (inputCheck.status === false) 
				return res.badRequest(inputCheck.message);
			let { processId, siteId } = req.params;
			let processConfig;

			try {
				// TODO: Change to findOne
				processConfig = await processService.find({
					processId,
					siteId
				});
				// TODO: Comment out line after adapter fix.
				processConfig = processConfig.filter(process => process.processId == processId)[0];
			} catch (error) {
				sails.log.error("[getProcessConfig] Error fetching process!", error);
				return res.serverError("Error fetching process!");
			}

			let { deviceType, driverType } = processConfig, driverInfo;
			try {
				driverInfo = await DeviceType.findOne({
					deviceType,
					driverType
				});
				if (driverInfo === undefined)
					return res.ok(processConfig);
			} catch (error) {
				sails.log.error("[getProcessConfig] Error fetching driver details!");
				return res.serverError("Error fetching driver details.");
			}

			util.addDefaultParametersToProcess(driverInfo, processConfig);
			
			return res.ok(processConfig);
		} catch (error) {
			sails.log.error("[getProcessConfig] Server Error!", error);
			return res.serverError();
		}
		
	},
	/**
	 * @function deleteProcess
	 * @description Deletes the process whose processId is passed. Deletes component/ process info from the controller's configuration.
	 * @param {string} processId Passed as query parameter 
	 */
	"deleteProcess": async function(req, res){
		try {
			let { processId } = req.allParams();
			const siteId = req._userMeta._site;
			if (!processId)
				return res.badRequest("No processId provided!");
			let process = await processService.find({
				processId
			});
			if (process.length === 0){
				return res.badRequest("No such process configured!");
			}
			process = process[0]; // As processService.findOne is not present in this adapter.
			let $parentProcessUpdate = updateParentProcess(process);
			let $childProcessesUpdate = updateChildProcesses(process);
			await processService.delete({processId});
			let controllers = await getControllerInComponent(siteId, processId);
			let updatedControllersInfo = await deleteComponentInfoFromController(controllers);
			try {
				await $parentProcessUpdate;
			} catch (error) {
				sails.log.error("[deleteProcess] Error updating parent process while deleting the process:", processId);
				return res.serverError("Error updating parent process!");
			}
			try {
				await $childProcessesUpdate;
			} catch (error) {
				sails.log.error("[deleteProcess] Error updating some child process while deleting the process:", processId);
				return res.serverError("Error updating some child process!");
			}
			res.ok(process);
			await deviceService.updateConfigTs({ siteId });
			sendSocketBroadcasts(updatedControllersInfo, siteId, processId);
		} catch (error) {
			sails.log.error("[deleteProcess] Server Error!", error);
			res.serverError("[deleteProcess] Server Error!");
		}

		// Helper Functions
		async function getControllerInComponent(siteId, componentId){
			let devices = await Devices.find({siteId});
			return devices.filter(device => device.componentId === componentId);
		}
		function deleteComponentInfoFromController(controllers){
			let promiesArr = [];
			const componentId = null; 
			controllers.forEach(controller => {
				let { siteId, deviceId } = controller;
				promiesArr.push(Devices.update(
					{siteId, deviceId},
					{componentId}
				));
			});
			return Promise.all(promiesArr);
		}
		function sendSocketBroadcasts(updatedControllersInfo, siteId, processId){
			let eventData = {
				"event": "delete",
				"data": [{ "deviceId": processId }]
			};
			eventService.notifyJouleTrack(
				siteId,
				"public",
				"components",
				eventData
			);
			const devicesEventData = {
				"event": "update",
				"data": updatedControllersInfo
			};
			eventService.notifyJouleTrack(siteId, "public", "devices", devicesEventData);
		}
		/**
		 * @function updateParentProcess
		 * @description If "parentProcess" key is present in the object, fetches the parent process and updates its
		 * "childProcesses" string set.
		 * @param {object} processObj Process Object with filtered keys and verified keys
		 */
		async function updateParentProcess(processObj){
			try {
				if (processObj.parentProcess !== undefined && typeof processObj.parentProcess === "string"){
					const { "parentProcess": parentProcessId, processId } = processObj;
					let parentProcessObject = await processService.find({
						"processId": parentProcessId
					});
					if (parentProcessObject.length === 0) return; // Incase parent object is not configured or could not be found.
					parentProcessObject = parentProcessObject[0]; // As process.findOne is not available in this adapter.
					let { childProcesses } = parentProcessObject;
					childProcesses = new Set(childProcesses);
					childProcesses.delete(processId);
					childProcesses = Array.from(childProcesses);
					await processService.update(
						{ "processId": parentProcessId },
						{ childProcesses }
					);
					// Sending socket broadcast for the updated parent process
					let eventData = { "event": "update", "data": [processObj] };
					eventService.notifyJouleTrack(process.siteId, "public", "components", eventData);
				}	
			} catch (error) {
				throw new Error(error);
			}
		}
		/**
		 * @function updateChildProcesses
		 * @description Updates all child processes associated with the process being deleted by changing their "parentProcess" key to string value "null".
		 * @param {object} processObj Process object being deleted.
		 */
		async function updateChildProcesses(processObj){
			let { childProcesses } = processObj;
			if (childProcesses === undefined || childProcesses.length === 0) 
				return;
			let $childProcessUpdates = childProcesses.map(childProcess => processService.update({"processId": childProcess}, {"parentProcess": "null"}));
			try {
				await Promise.all($childProcessUpdates);
			} catch (error) {
				sails.log.error("[deleteProcess] Error updating some child process while deleting process:", processObj.processId);
			}
			// Querying all childProcess for socket broadcasts to update frontend local storage after updating them. 
			let $childProcessesObjects =  childProcesses.map(childProcess => processService.find({"processId": childProcess}));
			let childProcessObjects = await Promise.all($childProcessesObjects);
			// Sending socket broadcasts.
			childProcessObjects.forEach(childProcessObject => {
				let eventData = { "event": "update", "data": childProcessObject };
				eventService.notifyJouleTrack(processObj.siteId, "public", "components", eventData);
			});
		}
	},
};
