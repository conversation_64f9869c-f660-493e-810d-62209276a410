const uuid = require('uuid');
const moment = require('moment-timezone');

moment.tz.add('Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6');
moment.tz.setDefault('Asia/Kolkata');
const commandService = require('../services/commandService')

// sjo-del/command/123/jouletrack
module.exports = {
  /**
   * @description api to send command to iot
   * @param {*} req
   * Request Object Definittion
   // req._userMeta = {
   * // 	"_h_" : "test",
    // 	"_site" : "mgch",
    // 	"id"	: "<EMAIL>"
    // };
    // req.body.command ={
    // 	"value"		  : "0",
    // 	"deviceId"	  : 3121,
    // 	"param" 	  : "setfrequency",
    // 	"componentId" : "ssh_16",
    // };
    // req.body.command = {
    //   "value": "195",
    //   "deviceId": 1000,
    //   "param": "changesetpoint",
    //   "componentId": "temp_1",
    // };
   *
   */
  create: async (req, res) => {
    try {
      // Todo verify range,
      // check if controller is connected to internet, then send command
      if (!req._userMeta) {
        return res.badRequest({ err: 'Login First' });
      }
      let command = req.param('command');
      if (!command) {
        return res.badRequest({ err: 'Invalid Command' });
      }
      const {
        value, deviceId, componentId,
      } = command;
      let { param } = command;
      if (!value || !deviceId || !param || !componentId) {
        return res.badRequest({ err: 'Insufficient Parameters' });
      }

      const siteId = req._userMeta._site;
      const socketID = req._userMeta._h_;
      const user = req._userMeta.id;
      const id = `${deviceId}_jt`;
      let device_abbr = await commandService.getDeviceCommandParam(componentId,param);
      if(device_abbr){
        command.component_abbr = param;
        param = device_abbr;
      }
      const uniqId = uuid();
      const infoPacket = {
        deviceId,
        param,
        uniqId,
        value,
        component: componentId,
        type: 'command',
      };
      let actionCreatePacket = {
        triggerTime: moment().startOf('m').unix() * 1000,
        sourceId: `${siteId}_${deviceId}.${param}`,
        source: 'jouletrack',
        sourceInfo: `${siteId}_${user}`,
        uniqId,
        type: 'command',
        response: '0',
        runOn: 'server',
        isInitiated: 'true',
        nest: '{}',
        reachedJb: '0',
        reachedCtrl: '0',
        operation: 'command',
        extra: {
          socketId: socketID,
        },
        info: JSON.stringify(infoPacket),
      };

      actionCreatePacket.siteId = actionCreatePacket.sourceId.split('_')[0];
      await Actions.create(actionCreatePacket);

      actionCreatePacket.info = infoPacket;
      actionCreatePacket.nest = {};

      // TODO: add filter for min max
      command.deviceId = id;
      const timestamp = `${siteId}_${moment().unix()}`;
      command = {
        ...command, timestamp, user, socketID, uniqId,
      };
      await Command.create(command);
      const operation = 'command';
      const key = timestamp;
      const packet = {
        deviceId, param, operation, value, key,
      };
      const packetForOffSet = { deviceId, param, key };
      const topic = `${siteId}/command/${deviceId}/jouletrack`;

      if (param === 'pidsetpoint') {
        packet.operation = 'pidSetpoint';
        const nVal = value.split('_')[0];
        packet.value = nVal;
        packetForOffSet.operation = 'pidOffset';
        const offset = value.split('_')[1];
        packetForOffSet.value = offset;
        const status = await eventService.publish(topic, JSON.stringify(packetForOffSet));
        sails.log('Offset Sent Status ', status.status);
        actionCreatePacket = packet;
      }
      const status = await eventService.publish(topic, JSON.stringify(actionCreatePacket));

      if (status.status === 200) return res.ok({ status: 'done' });
      return res.serverError({ err: 'Unable to notify controller' });
    } catch (e) {
      sails.log.error(e);
      return res.serverError({ err: 'Unable to notify controller' });
    }
  },
  async fixJBCommand(req, res) {
    const { selectedJB, mode } = req.allParams();
    if (!selectedJB || !mode) {
      return res.badRequest({ err: 'Parameters not found' });
    }
    if (!req._userMeta) {
      return res.badRequest({ err: 'User info not present' });
    }
    const cntrlConfig = await Devices.findOne({ deviceId: selectedJB });
    if (!cntrlConfig) return res.badRequest({ err: 'Controller not present in records' });
    const { siteId, deviceType } = cntrlConfig;
    if (deviceType != 'joulestat' && deviceType != 'jouleiocontrol'
      && deviceType != 'joulemomo' && deviceType != 'joulelogger') {
      return res.badRequest({ err: 'Not a controller' });
    }

    // if (!IPAddress) {
    //     return res.serverError({ err: "Network address of controller unknown" })
    // }
    const ts = moment().unix() * 1000;
    const key = `${siteId}_${ts}`;
    const user = req._userMeta.id;

    const socketID = req._userMeta._h_;
    const deviceId = `${selectedJB}_jt`;
    const timestamp = `${siteId}_${ts}`;
    const value = `${selectedJB}_${mode}_${ts}`;
    const param = 'updateJBLocation';
    const command = {
      deviceId, siteId, timestamp, selectedJB, value, user, socketID, param,
    };
    const operation = 'routingUpdateConfig';
    const payload = {
      key,
      value,
      operation,
      deviceId,
    };
    await Command.create(command);
    const controllers = await Devices.find({ siteId });
    controllers.map((device) => {
      const { deviceType } = device;
      // publish here on all controllers

      if (deviceType === 'joulestat' || deviceType === 'jouleiocontrol'
        || deviceType === 'joulemomo' || deviceType === 'joulelogger') {
        eventService.publish(`${device.siteId}/command/${device.deviceId}/jouletrack`, payload);
      }
    });
    return res.ok();
  },
  updateDevices: async (req, res) => {
    const { siteId } = req.allParams();
    if (!siteId) return res.badRequest();
    const key = `${siteId}_configTS`;
    const tsObj = await DyanmoKeyStore.findOne({ key });
    let value;
    if (typeof (tsObj) === 'undefined') {
      value = moment().unix() * 1000;
      await DyanmoKeyStore.create({ key, value });
    } else {
      value = tsObj.value;
    }
    if (value) {
      eventService.publish(`${siteId}/config/all/timestamp`, { ts: value, operation: 'verifyConfigVer' });
      return res.ok();
    }
  },
  latestCommand: async (req, res) => {
    // req.body = { deviceId: ["2237"] } // deviceIds to get last execued command of
    // req._userMeta = { _site : 'mgch'};

    try {
      const deviceIds = req.body.deviceId;
      const siteId = req._userMeta._site;
      const timestamp = moment().unix() * 1000;

      helper.validateParams([siteId], 'Missing deviceId or siteId');
      // let lastCommandInfo = [];
      const respObj = {};

      for (const deviceId in deviceIds) {
        const currDeviceId = deviceIds[deviceId];
        const deviceCommandInfo = await commandService
          .getLastCommand(
            siteId,
            currDeviceId,
            timestamp,
          );

        respObj[`${currDeviceId}`] = deviceCommandInfo;
      }

      return res.ok(
        respObj,
      );
    } catch (err) {
      if (err) {
        res.ok({
          err,
        });
      } else {
        return res.ok({
          err: 'Unable to fetch data at the moment.',
        });
      }
    }
  },
  createProtoCommand: async (req, res) => {
    // todo
  },
  updateProtoCommandStatus: async (req, res) => {
    // todo
  },

};
