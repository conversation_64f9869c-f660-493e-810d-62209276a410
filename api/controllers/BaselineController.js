/**
 * BaselineController
 *
 * @description :: Server-side logic for managing baselines
 * @help        :: See http://sailsjs.org/#!/documentation/concepts/Controllers
 */
const moment = require("moment-timezone");
moment.tz.add(
	"Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6"
);
moment.tz.setDefault("Asia/Kolkata");
const baselineUtil = require("../utils/baselineUtil");
module.exports = {
	/**
   * insert data into table ConsumptionData with type Baseline
   * @param {object} req - Object containing information about the HTTP request.
   * @param {object} res - res to send back the desired HTTP response.
   * @example <caption>Example usage of method.</caption>
   * //json object to POST
   *  {
      timestamp: "timestamp",
      startDate: "startDate",
      endDate: "endDate",
      target: "target",
      siteId: "siteId,
      consumptionValue: "consumptionValue"
    }
   *
   * //On successful attempt returns
   * {
      type: "baseline",
      timestamp: "timestamp",
      startDate: "startDate",
      target: "target",
      baselineValue: baselineValue
      endDate: "endDate",
      siteId: "siteId",
      consumptionValue: "consumptionValue"

    }
   *
   *
   * @returns {object} returns JSON object with the created baseline with the status 200.
   * @throws Will throw an error if DynamoDB fails
   */
	"addBaseline": function (req, res) {
		//TODO consumption data ka timestamp frontend se aayega ya nahi. timestamp pe start date hogi
		let baselineValue =
			(parseFloat(req.param("target")) *
				parseFloat(req.param("consumptionValue"))) /
			100;
		sails.log.silly(baselineValue);
		let dataObj = {
			"startDate": moment(req.param("startDate")).format("MM-DD-YYYY"), //date needs to to formatted in this format because baseline is to be retrieved on basis of months
			"endDate": moment(req.param("endDate")).format("YYYY-MM-DD"),
			"target": req.param("target"),
			"baselineValue": baselineValue, //calculated wali
			"siteId": req.param("siteId"),
			"consumptionValue": req.param("consumptionValue"),
		};
		sails.log.silly(dataObj);
		Baseline.create(dataObj)
			.then(data => {
				return res.json(200, data);
			})
			.catch(e => {
				sails.log.error(e);
				return res.serverError({ "err": "Internal Server Error" });
			});
	},
	/**
     * @description Getting all the baseline data associated with a site.
     * @param {object} req - Object containing information about the HTTP request.
     * @param {object} res - res to send back the desired HTTP response.
     * @example <caption>Example usage of method.</caption>
     * //Just call on the defined route
     *
     * //On successful attempt returns
     * {
        type: "baseline",
        timestamp: "timestamp",
        startDate: "startDate",
        target: "target",
        baselineValue: baselineValue
        endDate: "endDate",
        siteId: "siteId",
        consumptionValue: "consumptionValue"
      }
     *
     * @returns {object} returns JSON object with the required baseline data with the status 200.
     * @throws Will throw an error if DynamoDB fails
     */

	"deleteBaseline": (req, res) => {
		let startDate = moment(req.params.startDate).format("MM-DD-YYYY");
		sails.log.silly(startDate, "param");
		sails.log.silly(req.params, "params");
		Baseline.destroy({
			"siteId": req.params.siteId,
			"startDate": startDate,
		})
			.then(data => {
				sails.log(data, "deleted~~~~~~~~~~~~~~");
				res.ok(data);
			})
			.catch(er => {
				res.serverError(er);
			});
	},
	"updateBaseline": (req, res) => {
		let body = req.body;
		sails.log.info(body, "body");
		let query = {
			"target": Number(body.target.replace(/,/g, "")),
			"consumptionValue": Number(body.value.replace(/,/g, "")),
		};
		sails.log.info(
			{
				"siteId": req.params.siteId,
				"startDate": req.params.startDate,
			},
			"query"
		);
		Baseline.update(
			{
				"siteId": req.params.siteId,
				"startDate": req.params.startDate,
			},
			query
		)
			.then(data => {
				sails.log.info(data, "data");
				res.ok(data);
			})
			.catch(er => {
				sails.log.error(er, "err");
				res.serverError({ "err": "Internal Server Error" });
			});
	},

	"getBaseline": function (req, res) {
		let searchObj = { "siteId": req.param("siteId") };
		Baseline.find(searchObj)
			.then(data => {
				for (let i = 0; i < data.length; i++) {
					let tmpDate = data[i].startDate.split("-");
					let month = tmpDate[0];
					let day = tmpDate[1];
					let year = tmpDate[2];
					tmpDate[0] = year;
					tmpDate[1] = month;
					tmpDate[2] = day;
					data[i].startDate = tmpDate.join("-");
				}
				res.json(200, data);
			})
			.catch(er => {
				sails.log.error(er, "err");
				res.serverError({ "err": "Internal Server Error" });
			});
	},
	"getDataFromPreviousBaseline": (req, res) => {
		// aditys's api, khatri said said to look in it
		let siteId = req.param("siteId");
		let query = req.query;
		sails.log.info(query, "query");
		baselineService
			.getCurrentBaseline1(siteId, query)
			.then(data => {
				sails.log.info(data);
				// var start = moment(data[0].startDate).set('year', 2017).format('YYYY-MM-DD');
				// var end = moment(data[0].endDate).set('year', 2017).format('YYYY-MM-DD');

				sails.log.info(moment(data[0].startDate).get("year"));
				sails.log.info(moment(data[0].endDate).get("year"));

				let startYear = moment()
					.subtract(Number(query.count), "month")
					.get("year");
				let endYear = moment()
					.subtract(Number(query.count) - 1, "month")
					.get("year");
				sails.log.info(
					moment().subtract(Number(query.count), "month"),
					"subsub"
				);

				if (
					moment(data[0].startDate).get("year") ===
					moment(data[0].endDate).get("year")
				) {
					endYear = startYear;
				}
				// var startYear = moment().set('year', Number(query.year)).subtract(Number(query.count), 'month').get('year');
				// var endYear = moment().set('year', Number(query.year)).subtract(Number(query.count) - 1, 'month').get('year');
				sails.log.info(startYear, "start year");
				sails.log.info(endYear, "end year");
				let start = moment(data[0].startDate)
					.set("year", startYear)
					.format("YYYY-MM-DD");
				let end = moment(data[0].endDate)
					.set("year", endYear)
					.format("YYYY-MM-DD");
				data[0].startDate = start;
				data[0].endDate = end;
				sails.log.info(start, "start");
				sails.log.info(end, "end");
				let consumptionObj = {
					"where": {
						"siteId": siteId,
						"timestamp": {
							"between": [start, end],
						},
						"sort": "1",
					},
				};
				DailyConsumption.find(consumptionObj).then(resData => {
					res.ok({ "data": resData, "baseline": data[0] });
				});
			})
			.catch(er => {
				sails.log.error(er);
				return res.ok({
					"err": "Unable to get Baseline Info",
					"data": [],
					"baseline": [],
				});
			});
	},
	"getDataFromPreviousBaseline_new": async (req, res) => {
		// aditys's api, khatri said said to look in it
		let siteId = req.param("siteId");
		{
			/**
			 * MOCKING DASHBOARD API FOR IBMS PRESENTATION ONLY. We are using mgch site for demo purposes
			 * */
			const sunshineIBMSSites = ['be-hyd', 'suh-hyd'];
			if (sunshineIBMSSites.indexOf(siteId) !== -1) {
				siteId = "mgch"
			}
		}
		let query = req.query;
		let _userPrefernce = req._userMeta.unitPref;

		let userConsumptionUnit = await dailyConsumptionService.getUserConsumptionPreference(_userPrefernce, siteId);

		let siteConsumptionUnit = await dailyConsumptionService.getSiteBaselineUnit(siteId);

		sails.log.info(query, "query");
		baselineService
			.getCurrentBaseline1_new(siteId, query, userConsumptionUnit, siteConsumptionUnit)
			.then(data => {

				// let startYear = moment()
				// 	.subtract(Number(query.count), "month")
				// 	.get("year");
				// let endYear = moment()
				// 	.subtract(Number(query.count) - 1, "month")
				// 	.get("year");

				// if (
				// 	moment(data[0].startDate).get("year") ===
				// 	moment(data[0].endDate).get("year")
				// ) {
				// 	// endYear = startYear;
				// }

				let yearDifference = moment().year() - moment(data[0].endDate).year();
				let start = moment(data[0].startDate)
					.add(yearDifference, "year")
					.format("YYYY-MM-DD");

				let end = moment(data[0].endDate)
					.add(yearDifference, "year")
					.format("YYYY-MM-DD");

				data[0].startDate = start;
				data[0].endDate = end;
				sails.log(start, end);
				let consumptionObj = {
					"where": {
						"siteId": siteId,
						"timestamp": {
							"between": [start, end],
						},
						"sort": "1",
					},
				};
				DailyConsumption.find(consumptionObj).then(resData => {
					resData = resData.map(rdata => {
						rdata["actual"] = dailyConsumptionService.getConsumptionAccordingToUnit(rdata, userConsumptionUnit);
						return rdata;
					});
					res.ok({ "data": resData, "baseline": data[0] });
				}).catch(e => {
					sails.log.error(e);
					return res.ok({
						"err": "Unable to get Baseline Info",
						"data": [],
						"baseline": [],
					});
				});
			})
			.catch(er => {
				sails.log.error(er);
				return res.ok({
					"err": "Unable to get Baseline Info",
					"data": [],
					"baseline": [],
				});
			});
	},
	"currentBaseline": function (req, res) {
		let siteId = req.param("siteId");
		if (!siteId)
			return res.badRequest({ "err": "Insufficient parameters" });
		baselineService
			.getCurrentBaseline(siteId)
			.then(data => {
				res.ok(data);
			})
			.catch(err => {
				sails.log.error(err);
				res.serverError(new Error("Couldn't getCurrentBaseline."));
			});
	},
	"amountSaved": function (req, res) {
    sails.log.info('[Baseline > Savings]');
		let siteId = req.param("siteId");
		let electricityCost = 9;
		if (!siteId) return res.badRequest("Insufficient Parameters");
		let bLine;
		let daysFromBaseline;

		baselineService
			.getCurrentBaseline(siteId)
			.then(baseline => {
				//code to find consumption from start of baseline
				if (baseline.length == 0) {
					return Promise.reject(
						"No baseline configured for given site"
					);
				}
				let currentYear = moment().year();
				let startYear = moment(baseline[0].startDate).year();
				let endYear = moment(baseline[0].endDate).year();
				let start, end;
				if (startYear != endYear) {
					//exception for Dec
					start = moment(baseline[0].startDate)
						.year(currentYear - 1)
						.format("YYYY-MM-DD");
				} else {
					start = moment(baseline[0].startDate)
						.year(currentYear)
						.format("YYYY-MM-DD");
				}
				end = moment().format("YYYY-MM-DD");

				daysFromBaseline = moment().diff(moment(start), "days");
				let consumptionObj = {
					"where": {
						"siteId": req.param("siteId"),
						"timestamp": {
							"between": [start, end],
						},
					},
				};
				bLine = baseline[0];

				return DailyConsumption.find(consumptionObj);
			})
			.then(consumption => {
				let totalConsumption = consumption.reduce((sum, item) => {
					return sum + parseInt(item.actual);
				}, 0);
				sails.log.info(totalConsumption);
				sails.log.info(daysFromBaseline);
				let baselineDays = moment(bLine.endDate).diff(
					moment(bLine.startDate),
					"days"
				);
				baselineDays = parseInt(baselineDays) + 1;
				sails.log.info(baselineDays);
				let noOfDays =
					daysFromBaseline < consumption.length
						? daysFromBaseline
						: consumption.length;
				let baselineConsumption =
					(bLine.consumptionValue / baselineDays) * noOfDays;
				sails.log.info(baselineConsumption);
				let savings =
					(baselineConsumption - totalConsumption) * electricityCost;
				savings = Math.round(savings);
				return res.ok({ "err": null, "data": savings });
				//code to find current consumption
			})
			.catch(err => {
				sails.log.error(err);
				return res.ok({ "err": err, "data": null });
			});
	},
	"amountSaved_new": async function (req, res) {
		let siteId = req.param("siteId");
		let _userPrefernce = req._userMeta.unitPref;

		{
			/**
			 * MOCKING DASHBOARD API FOR IBMS PRESENTATION ONLY. We are using mgch site for demo purposes
			 * */
			const sunshineIBMSSites = ['be-hyd', 'suh-hyd'];
			if (sunshineIBMSSites.indexOf(siteId) !== -1) {
				siteId = "mgch"
			}
		}

		/**
		 * MOCKING DASHBOARD API FOR IBMS PRESENTATION ONLY. We are using mgch site for demo purposes
		 * */
		let sunshineIBMSSites = ['be-hyd', 'suh-hyd'];
		if (sunshineIBMSSites.indexOf(siteId) !== -1) {
			siteId = "mgch"
		}


		// get from  dynamoKeyStore, default is 9
		if (!siteId) return res.badRequest("Insufficient Parameters");
		let electricityCost = await baselineService.getSiteElectricityPrice(siteId);
		let bLine, daysFromBaseline,
			userConsumptionUnit, siteConsumptionUnit;

		userConsumptionUnit = await dailyConsumptionService.getUserConsumptionPreference(_userPrefernce, siteId);

		siteConsumptionUnit = await dailyConsumptionService.getSiteBaselineUnit(siteId);

		baselineService
			.getCurrentBaseline_new(siteId, siteConsumptionUnit, userConsumptionUnit)
			.then(baseline => {
				//code to find consumption from start of baseline
				if (baseline.length == 0) {
					return Promise.reject(
						"No baseline configured for given site"
					);
				}
				let currentYear = moment().year();
				let startYear = moment(baseline[0].startDate).year();
				let endYear = moment(baseline[0].endDate).year();
				let start, end;
				if (startYear != endYear) {
					//exception for Dec
					start = moment(baseline[0].startDate)
						.year(currentYear - 1)
						.format("YYYY-MM-DD");
				} else {
					start = moment(baseline[0].startDate)
						.year(currentYear)
						.format("YYYY-MM-DD");
				}
				end = moment().format("YYYY-MM-DD");

				daysFromBaseline = moment().diff(moment(start), "days");
				let consumptionObj = {
					"where": {
						"siteId": req.param("siteId"),
						"timestamp": {
							"between": [start, end],
						},
					},
				};
				bLine = baseline[0];

				return DailyConsumption.find(consumptionObj);
			})
			.then(consumption => {
				let totalConsumption = consumption.reduce((sum, item) => {
					let actualConsumption = dailyConsumptionService.getConsumptionAccordingToUnit(item, userConsumptionUnit);
					return sum + parseInt(actualConsumption);
				}, 0);
				sails.log.info(totalConsumption);
				sails.log.info(daysFromBaseline);
				let baselineDays = moment(bLine.endDate).diff(
					moment(bLine.startDate),
					"days"
				);
				baselineDays = parseInt(baselineDays) + 1;
				sails.log.info(baselineDays);
				let noOfDays =
					daysFromBaseline < consumption.length
						? daysFromBaseline
						: consumption.length;
				let baselineConsumption =
					(bLine.consumptionValue / baselineDays) * noOfDays;
				sails.log.info(baselineConsumption);
				let savings =
					(baselineConsumption - totalConsumption) * electricityCost;
				savings = Math.round(savings);
				return res.ok({ "err": null, "data": savings });
				//code to find current consumption
			})
			.catch(err => {
				sails.log.error(err);
				return res.ok({ "err": err, "data": null });
			});
	},

	/**
	 * fetch the data of production (Rama) for papers.
	 * @returns {Object} data of production.
	 * @param req.param timestamp start_end format (YYYY-MM-DD).
	 * @param {Object} res
	 * @returns production data object in the given time frame.
	 */
	"productionData": async (req, res) => {
		const siteId = req._userMeta._site;
		const timeRange = req.param("data");
		const timeRangeSplit = timeRange.split("_");
		const start = timeRangeSplit[0];
		const end = timeRangeSplit[1];
		const productionObj = {
			"where": {
				"siteId": siteId,
				"timestamp": {
					"between": [start, end],
				},
				"sort": "1",
			},
		};
		try {
			const productionData = await Production.find(productionObj);
			return res.ok({ "data": productionData });
		} catch (err) {
			sails.log.error(err);
			return res.serverError({ "err": "Internal server Error!" });
		}
	},
	/**
	 * update(entries exist for previous dates) the production of papers.
	 * or create(entry) for the current date with production data.
	 * @param req.body.productionData contains date and corrosponding data entered.
	 *  in different categories of paper.
	 * @param productionData.production_shiftA production data in shiftA for all 3 categories
	 * i.e. duplex paper , news paper and printing paper.
	 * @param productionData.production_shiftB read above .
	 * @param productionData.production_shiftC read above.
	 * @param req._userMeta._site contains current siteId.
	 * @param {Object} res
	 * @returns status send 'done' or error.
	 */
	"addProductionData": async (req, res) => {
		try {
			const util = baselineUtil.production;
			const productionData = req.body.productionData;
			const dataLength = productionData.length;
			const startTime = moment(
				productionData[0].date,
				"DD/MMM/YYYY"
			).format("YYYY-MM-DD");
			const endTime = moment(
				productionData[dataLength - 1].date,
				"DD/MMM/YYYY"
			).format("YYYY-MM-DD");
			const siteId = req._userMeta._site;
			const incompleteData = productionData.filter(util.filterBadData);
			if (incompleteData.length > 0) return res.badRequest();
			// Filtering and checks ends
			const dataFetched = await productionService.getCustomProduction(
				siteId,
				startTime,
				endTime
			);
			const dbFormattedData = productionData.map(dateShift =>
				util.formatData(dateShift, siteId)
			);
			const $updateArr = dbFormattedData
				.filter(dateShift =>
					util.filterUpdateEntries(dateShift, dataFetched)
				)
				.map(dateShift => productionService.updateEntry(dateShift));
			const $createArr = dbFormattedData
				.filter(dateShift =>
					util.filterCreateEntries(dateShift, dataFetched)
				)
				.map(dateShift => productionService.createEntry(dateShift));
			await Promise.all([...$updateArr, ...$createArr]);
			return res.ok({ "status": "done" });
		} catch (err) {
			sails.log.error(err);
			return res.serverError();
		}
	},
};


