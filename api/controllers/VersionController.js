/**
 * VersionController
 *
 * @description :: Server-side actions for handling incoming requests.
 * @help        :: See https://sailsjs.com/docs/concepts/actions
 */
let moment = require("moment");
module.exports = {
  
	/**
	 * <AUTHOR> <<EMAIL>> 
	 * @name sendAllVersion
	 * @summary give version list of particular deviceType
	 */
	"sendAllVersion": async (req, res) =>{
		let allVersions = [];
		try {
			let data = await Version.find()
				.where({
					"deviceType": req.param("deviceType")
				});
			if (!data) {
				res.badRequest("Data not found");
			}
			for (let i in data){
				let ver = data[i].version;
				allVersions.push(ver);
			}
		} catch (error) {
			res.serverError("Database errror occurred");
		}
		res.ok({"data": allVersions});
	},

	/**
	 * <AUTHOR> <<EMAIL>> 
	 * @function addVersion
	 * @summary Add version of particular deviceType in Versions table
	 * @param {string} version Overall name of the version being deployed.
	 * @param {string} deviceType To be either "controller" or "joulesense". Specifying the devicetype of the version being saved.
	 * @param {string} dockerApplication Application docker version.
	 * @param {string} dockerFirmware Firmware docker version.
	 * @param {string} dockerJouleBox Joule Box docker version.
	 * @param {string} gitRepoApplication Application Git Version.
	 * @param {string} gitRepoCICD CICD Git version.
	 * @param {string} gitRepoFirmware Firmware git version.
	 * @param {string} gitRepoHostServices Host Services Git version.
	 * @param {string} gitRepoJouleBox Joule Box Git version.
	 * @param {string} gitRepoRoutingService Routing Service Git version.
	 * @returns {object} Returns the object that was succesfully saved in DynamoDB.
	 */
	"addVersion": async (req, res) => {
		// Importing utils
		let util = versionUtil.addVersion;

		// Checking for input parameters
		if (!util.checkInput(req.body)) 
			return res.badRequest({"err": "Invalid input parameters."});
		
		// Adding keys to data object based on deviceType.
		let dataObj = util.populateDataObj(req.body);

		// Saving version to database.
		try {
			let data = await Version.create(dataObj);
			return res.ok({"data": data});
		} catch (error) {
			sails.log.error("Version Controller [addVersion] Error while saving object to Dynamo: ", error);
			return res.serverError("Some Database Error Caused");
		}
	},

	/**
	 * <AUTHOR> <<EMAIL>> 
	 * @name getParticularVersion
	 * @summary give details of a version requested by Frontend of particular device type
	 */
	"getParticularVersion": async (req, res) =>{
		try {
			let data = await Version.find({"version": req.body.version});	
			if (!data) {
				res.badRequest("Data not found");
			}
			let response = data[0];
			if (response.gitRepod2rs == undefined){
				response["gitRepod2rs"] = "{\"version\":\"0.0.0\",\"type\":\"git\",\"name\":\"d2rs\"}";
			}
			res.ok({"data": response});	
		} catch (error) {
			res.serverError("Some Database error caused");
		}
	},

	/**
	 * <AUTHOR> <<EMAIL>> 
	 * @name getLatestVersion
	 * @summary give latest version of a particular device type
	 */
	"getLatestVersion": async (req, res) =>{
		try {
			let data = await Version.find({
				"deviceType": req.param("deviceType")
			});

			if (!data) {
				res.badRequest("Data not found");
			}
			let arrTimeStamp = [];
			for (let i in data){
				let createdAt = moment(data[i].createdAt, "MM-DD-YYYY").unix()*1000;
				arrTimeStamp.push(createdAt);
			}
			let maxValIndex = arrTimeStamp.indexOf(Math.max(...arrTimeStamp));
			let resp = data[maxValIndex];

			if (!resp.hasOwnProperty("gitRepod2rs"))
				resp["gitRepod2rs"] = "{\"version\":\"0.0.0\",\"type\":\"git\",\"name\":\"d2rs\"}";

			res.ok({"data": resp});
		} catch (error) {
			sails.log(error);
			res.serverError("Some DB error caused");
		}
	},

	/**
	 * <AUTHOR> <<EMAIL>> 
	 * @name updateVersion
	 * @summary update the Repo and image version in Version table
	 */
	"updateVersion": async (req, res) => {
		

		let key = Object.keys(req.body);
		let repoList = []; 
		for (let i in key){
			if (key[i].charAt(0)=="g"){
				if (key[i].substring(0, 3)=="git"){
					repoList.push(key[i]);
				}
			} else if (key[i].charAt(0)=="d"){
				if (key[i].substring(0, 6)=="docker"){
					repoList.push(key[i]);
				}
			}
		}
		let dataObj, vd2rs;
		let {currVersion, newVersion, deviceType} = req.body;
		if (req.body.deviceType == "controller"){
			if (!newVersion || !deviceType || !req.body.gitRepoCICD || !req.body.gitRepoApplication || !req.body.gitRepoFirmware || !req.body.gitRepoHostServices || !req.body.gitRepoJouleBox || !req.body.gitRepoRoutingService || !req.body.dockerApplication || !req.body.dockerFirmware || !req.body.dockerJouleBox)
				return res.ok({"err": "Data is incomplete"});

			if (req.body.gitRepod2rs == undefined)
				vd2rs = "{\"version\":\"0.0.0\",\"type\":\"git\",\"name\":\"d2rs\"}";
			else
				vd2rs = req.body.gitRepod2rs;

			dataObj = {
				"version": newVersion,
				"deviceType": deviceType,
				"gitRepoCICD": req.body.gitRepoCICD,
				"gitRepoApplication": req.body.gitRepoApplication,
				"gitRepoFirmware": req.body.gitRepoFirmware, 
				"gitRepoHostServices": req.body.gitRepoHostServices,
				"gitRepoJouleBox": req.body.gitRepoJouleBox,
				"gitRepoRoutingService": req.body.gitRepoRoutingService,
				"gitRepod2rs": vd2rs,
				"dockerApplication": req.body.dockerApplication,
				"dockerFirmware": req.body.dockerFirmware,
				"dockerJouleBox": req.body.dockerJouleBox,
				"repoList": repoList
			};
		} else {
			if (!deviceType || !newVersion)
				return res.badRequest("Data is incomplete");
			dataObj = {
				"version": newVersion,
				"deviceType": deviceType,
			};
		}
		

		try {	
			if (!currVersion)
				res.badRequest("Current Version is undefined");

			await Version.destroy({
				"version": currVersion,
				"deviceType": deviceType
			});

			await Version.create(dataObj).exec((err, data)=> {
				if (err) {
					return res.serverError("error occurred");
				}
				res.ok({"status": "Data Updated"});
			});
		} catch (error) {
			res.serverError({"err": "Some Error occurred"});
		}
	},

	/**
	 * <AUTHOR> <<EMAIL>> 
	 * @name checkVersionNotExist
	 * @summary check whether version exist or not for particular deviceType 
	 */
	"checkVersionNotExist": async (req, res)=>{
		let data;
		try {
			data = await Version.find({
				"version": req.body.version,
				"deviceType": req.body.deviceType
			});

			if (data.length!=0){
				res.ok({"status": true});
			} else {
				res.ok({"status": false});
			}
		} catch (error) {
			res.serverError("Server error occurred");
		}
	},

	"checkVersionExist": async (req, res)=>{
		let data;
		try {
			data = await Version.find({
				"version": req.body.version,
				"deviceType": req.body.deviceType
			});
			if (data.length!=0){
				res.ok({"status": false});
			} else {
				res.ok({"status": true});
			}
		} catch (error) {
			res.serverError("Server error occurred");
		}
	},

	/**
	 * <AUTHOR> <<EMAIL>> 
	 * @name deleteVersion
	 * @summary delete version from Versions table of particular deviceType 
	 */
	"deleteVersion": async (req, res) => {

		let {version, deviceType} = req.body;
		try {
			await Version.destroy({
				"version": version,
				"deviceType": deviceType
			});
		} catch (error) {
			res.serverError("Server error occurred");
		}
		res.ok({"status": "Data Deleted"});
	},
	
};

