/**
 * @namespace ParametersController
 * @description Querying and updating and deleting parameters of Smartjoules devices.
 * Usecases include modifying a registor of modbus parameter or changing default units of devices
 */

const moment = require("moment-timezone");
moment.tz.add(
	"Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6"
);
moment.tz.setDefault("Asia/Kolkata");
module.exports = {
	/**
	 * @function find
	 * @param {object} req must contain siteId and an optional deviceId
	 * @param {object} res
	 * @description Returns all the parameters of a site or a single device based on parameter
	 * @returns {object} If the provided parameters of siteId and deviceId exists the function returns  {params:[{},{}]}

	 * @returns Badrequest with status 400 if siteId is not present
	 * @return ServerError with status code 500 if server encounters any error while processing the request. 
	 * @example curl -X GET 'http://localhost:1337/v1/parameters?siteId=mgch&deviceId=3136'
	 */
	"find": async (req, res) => {
		let { siteId, deviceId } = req.allParams();
		let sObj = {};
		if (!siteId) return res.badRequest({ "err": "insufficient params" });
		sObj = {
			"where": { siteId }
		};
		if (typeof deviceId != "undefined")
			sObj["where"]["deviceId_abbr"] = {
				"contains": deviceId
			};
		try {
			let params = await Parameters.find(sObj);
			return res.ok({ params });
		} catch (e) {
			sails.log.error(e);
			return res.serverError({ "err": "Internal Server Error" });
		}
	},
	/**
	 * @function update
	 * @param {object} req Must contain siteId, deviceId in parameters
	 * @param {object} res
	 * @description Will be used to update a single parameter or multiple parameters of a specific device for a given site 
	 * @returns {object} If the provided parameters of siteId and deviceId exists the function returns  {params:[{},{}]}

	 * @return Badrequest with status 400 if siteId is not present
	 * @return ServerError with status code 500 if server encounters any error while processing the request. 
	 * @example curl -X PUT -H 'Content-Type: application/json' -i http://localhost:1337/v1/parameters --data '{"siteId":"mgch","deviceId":"3136","updates": [{"abbr":"actuator_feedback","index":1}]}'
	 */
	"update": async (req, res) => {
		let { siteId, deviceId, updates } = req.allParams();
		if (!siteId || !deviceId || !updates || !Array.isArray(updates))
			return res.badRequest();
		let isBadRequest = false;
		let updateObj = updates.map(param => {
			let { abbr } = param;
			if (!abbr) {
				isBadRequest = true;
				return null;
			}
			let deviceId_abbr = `${deviceId}_${abbr}`;
			let keyObj = { siteId, deviceId_abbr };
			return { keyObj, param };
		});
		if (isBadRequest) return res.badRequest();
		let $status = updateObj.map(obj =>
			Parameters.update(obj.keyObj, obj.param)
		);
		Promise.all($status)
			.then(d => {
				return res.ok({ "status": "done" });
			})
			.catch(err => {
				sails.log.error(err);
				return res.serverError();
			});
	}
};
