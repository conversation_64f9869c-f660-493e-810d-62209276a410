/**
 * ReportinfoController
 *
 * @description :: Server-side actions for handling incoming requests.
 * @help        :: See https://sailsjs.com/docs/concepts/actions
 */

module.exports = {
	/**
	 * <AUTHOR> <<EMAIL>>
	 * @name getReportDetails
	 * @summary give reports details of generated reports.
	 * @param siteId of genarated report.
	 * @param timestamp of generated report.
	 */
	"getReportDetails": async (req, res) => {
		const { siteId, timestamp } = req.allParams();
		if (!siteId && !timestamp)
			return res.badRequest({ "err": "Parameters missing." });
		try {
			const info = await Reportinfo.findOne({ siteId, timestamp });
			sails.log(info);
			return res.ok(info);
		} catch (err) {
			sails.log.error(err);
			return res.badRequest();
		}
	},
};
