// topics {} mei there will be
// 1. recipe ko jis jis ka data chahie wo
// 2. command ka reply jiss id pr aata wo bhi

const moment = require("moment-timezone");
moment.tz.add("Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6");
moment.tz.setDefault("Asia/Kolkata");
const uuid = require("uuid");
const md5 = require("md5");

const categoryMap = {
	"process": "recipeprocess",
	"comfort": "recipecomfort",
	"routine": "reciperoutine",
	"failsafe": "recipefailsafe",
	"diagnostics": "recipediagnostics"
};

const priorityMap = {
	"0": "low",
	"1": "medium",
	"2": "high",
	"3": "critical"
};


module.exports = {


	"saveAbstract": function (req, res) {

		//  description is only in actions not alerts

		// changes => {
		//     params will contain componentsIds and no normal deviceIds ,
		//     no label,
		//     [idk] fail safe,
		//     [idk] aske user ? actionable.alert mei title, alertdId,description etc ?
		//     actionable.action.did will be  "componentsId.startStop" , and notify mei ask user
		//     actionable.recipe.notify ask user
		// }

		//   //   //  //    whatToAsk while Creating new child

		// {
		//     (params.componets).KVA TO (params.data)['kva']
		//     (label)
		//     actionable and alert
		// }


		// req.body={
		//     formula: 'P||#3||$1||#1||42||#4||$2||#5||50,||20||#2',
		//     // formula: '$1||#1||42',
		//     params: {
		//         $1: 'ahu.kVA'
		//     },
		//    'neo' : "ahu" // device type
		//     "label" : "AHU start/stop",
		//     "type" : "routine", // notRountine
		//     "recipelabel" : [''] ,   //( energy , operation , comfort , dejoule , hvac ) + diagnostic and optimization , failsafe
		//     maxDataNeeded : 20,
		//     maxLogNeeded : 0,
		//     isSchedule: true, // true means there is observable
		//     operators: {
		//       "#1" : '>',
		//       "#2" : ")",
		//       "#3" :"("
		//     },
		//     failSafeAlert : "", // should be just run a recipe
		//     siteId : "sjo",
		//     "startNow" : true, // if recipe need 5min data should it just run taking data from now or prev 5min, false means take prev data
		//     actionable : [
		//         {
		//             "priority": 5,
		//             "description" : "asdf",
		//             "type" : "execute",
		//             "frequency": 60,
		//             "parent" : 'Office AHU',
		//             "notify" : ["ujjal"]
		//         },
		//         {
		//             "type" : "execute",
		//             "description" : "asdf",
		//             "priority": 5,
		//             "parent" : 'Office AHU',
		//             "frequency": 60,
		//             "notify" : ["ujjal"]
		//         },
		//         {
		//             "did": "ahu.startstop",
		//             "type" : "actions",
		//             // "parent": "smt-del_2", create on creating child
		//             "notify" : ["ujjal"],
		//             "type": "start",
		//             "value": "1"
		//         },
		//     ]
		// };
		let errtype = false;
		try {
			let { label, siteId, type, parentId, maxDataNeeded, maxLogNeeded, isSchedule, actionable, recipelabel } = req.body;
			actionable = helper.toArray(actionable);
			if (!label || !actionable || !siteId || !recipelabel || !type) {
				res.send("Error ! Incomplete parameters");
				return res.end();
			}
			label = "[Abstract Recipe]" + label;
			let rid = uuid();


			// actionable = actionable.map(a=> {...a , uniqId : uuid() } )

			actionable.forEach(a => {
				if (a.did && !isNaN(parseInt(a.did))) {
					errtype = true;
					throw "Aghhh action Id should be of component";
				}
			});

			let mainObj = {
				rid,
				siteId,
				type,
				isSchedule,
				"rtype": "abstract",
				"actionable": JSON.stringify(actionable),
				label,
				"isStage": 1, // its always 1, never going to firmware
				"isActive": 0, // always zer0, cant activate abstrace recipe
				"recipelabel": JSON.stringify(recipelabel),
				"notRun": JSON.stringify([]), // never gonna have this property
				"switchOff": "0", // never gonna have
				"alwaysRun": false, // never gonna have
			};
			if (parentId) {
				mainObj["parentId"] = parentId;
			}

			let paramsMap = helper.toJson(req.body.params);
			if (!isSchedule) {
				let formula = req.body.formula;
				let params = req.body.params;
				let operator = req.body.operators;
				mainObj["oldObservable"] = formula;
				if (!maxDataNeeded) {
					maxDataNeeded = 1;
				}
				if (!maxLogNeeded) {
					maxDataNeeded = 1;
				}
				for (let i in paramsMap) {
					if (!isNaN(parseInt(paramsMap[i]))) {
						errtype = true;
						throw "Invalid component " + paramsMap[i];
					}
				}
				if (formula.slice(0, 2) == "||") {
					formula = formula.slice(2, formula.length);
				}
				if (formula.slice(formula.length - 2, formula.length) == "||") {
					formula = formula.slice(0, formula.length - 2);
				}

				mainObj["maxLogNeeded"] = maxLogNeeded;
				mainObj["maxDataNeeded"] = maxDataNeeded;
				mainObj["formula"] = formula;
				mainObj["params"] = JSON.stringify(params);
				mainObj["operator"] = JSON.stringify(operator);
			}

			Recipe.create(mainObj).then(r => {

				return res.send(r);

			}).catch(e => {
				sails.log.error(e);
				return res.end();
			});


		} catch (e) {
			sails.log.error(e);
			if (errtype) {
				res.badRequest(e);
			} else {
				res.badRequest("Server Error");
			}
		}

	},
	/**
	 * Save recipe config onto database
	 * @function
	 * @param {object} req User Request Object having recipe config and user info
	 * @ param {object} res Response Object to respond back user to
	 */
	"saveRecipev2": async function (req, res) {


		// req._userMeta = {
		// 	"id": "Test"
		// };
		// kva alert
		// req.body = { "formula": "||#1||$1||0", "label": "test-alert-normal", "type": "nonRoutine", "recipelabel": ["Energy Diagnostic"], "neo": "simple", "isSchedule": false, "operators": { "$1": ">", "$2": "(", "$3": ")" }, "params": { "#1": "ssh_12.status" }, "siteId": "ssh", "startNow": true, "maxDataNeeded": 1, "actionable": [{ "title": "test-1", "description": "none", "notify": ["<EMAIL>"], "accountable": ["<EMAIL>"], "type": "alert", "priority": 0 }] };

		// req.body = { "formula": "P||$2||#1||$1||1,20||$3||", "label": "test-alert-normal", "type": "nonRoutine", "recipelabel": ["Energy Diagnostic"], "neo": "simple", "isSchedule": false, "operators": { "$1": ">", "$2": "(", "$3": ")" }, "params": { "#1": "ssh_6.kw" }, "siteId": "ssh", "startNow": true, "maxDataNeeded": 1, "actionable": [{ "title": "test-1", "description": "none", "notify": ["<EMAIL>"], "accountable": ["<EMAIL>"], "type": "alert", "priority": 0 }] };

		// calculated param
		// req.body = { "formula": "||#1||$3||1", "label": "test", "type": "nonRoutine", "recipelabel": ["Energy Diagnostic"], "neo": "simple", "isSchedule": false, "operators": { "$1": "-", "$2": "/", "$3": ">", "$4": "(", "$5": ")" }, "params": { "#1": "ssh_6.ikwtr" }, "siteId": "ssh", "startNow": true, "maxDataNeeded": 1, "actionable": [{ "title": "e", "description": "a", "notify": ["<EMAIL>"], "accountable": ["<EMAIL>"], "type": "alert", "priority": 0 }] }

		// tseries alert
		// req.body = { "formula": "||#1||$1||0", "label": "test-series", "type": "nonRoutine", "recipelabel": ["Energy Diagnostic"], "neo": "simple", "isSchedule": false, "operators": { "$1": ">", "$2": "(", "$3": ")" }, "params": { "#1": "ssh_26.cwit" }, "siteId": "ssh", "startNow": true, "maxDataNeeded": 10, "actionable": [{ "title": "test-series", "description": "test-series", "notify": ["<EMAIL>"], "accountable": ["<EMAIL>"], "type": "alert", "priority": 0 }] };


		let errtype = false;

		try {

			let { label, siteId, type, parentId, maxDataNeeded, maxLogNeeded, startNow, isSchedule, actionable, recipelabel, neo } = req.body;
			actionable = helper.toArray(actionable);
			let userId = req._userMeta["id"];

			if (!label || !actionable || !siteId || !recipelabel || !type || !userId) {
				res.send({ "error": true, "data": "Error ! Incomplete parameters" });
				return res.end();
			}

			if (recipelabel.constructor.name !== "Array") {
				return res.badRequest({ "error": true, "data": "Recipelabel must be array" });
			}

			let rid = uuid();
			if (!startNow) {
				startNow = false;
			}

			let notify = [];
			actionable.forEach(a => {
				notify.push(...a.notify);
				notify.push(...a.accountable);
			});
			notify = helper.removeDuplicate(notify);

			let rlab = recipelabel.map(lab => {
				return categoryMap[lab.replace(/ /gi, "").toLowerCase()];
			});
			rlab = rlab.filter(Boolean);


			actionable = actionable.map(a => {
				return { ...a, "uniqId": md5(JSON.stringify(a)), "category": rlab };
			});
			// Creating recipe config object
			let mainObj = {
				rid,
				siteId,
				type,
				neo,
				isSchedule,
				"user": userId,
				"failsafe": JSON.stringify({}),
				"actionable": JSON.stringify(actionable),
				label,
				"isStage": 1,
				"isActive": 0,
				"recipelabel": JSON.stringify(recipelabel),
				"notRun": JSON.stringify([]),
				"switchOff": "0",
				"alwaysRun": false,
				"scheduled": JSON.stringify([])
			};
			if (parentId) {
				mainObj["parentId"] = parentId;
			}

			// devicesToSubscribe includes the one whos data is needed and the one whos feedback is needed from ationable
			mainObj = await RecipeService.getRecipeObj(
				mainObj,
				req.body.formula,
				req.body.params,
				req.body.operators,
				maxDataNeeded,
				maxLogNeeded,
				isSchedule
			);
			Recipe.create(mainObj).then(recipe => {
				return res.send(recipe);
			}).catch(e => {
				sails.log.error(e);
				return res.send({ "error": true, "data": "Unable to create recupe" });
			});

		} catch (e) {
			sails.log.error(e);
			if (errtype) {
				res.badRequest(e);
			} else {
				res.badRequest("Server Error");
			}
		}

	},
	/**
	 * Deploying 1 schedule for a recipe
	 * @function
	 * @param {object} req Request Object having recipeID and ScheduleId to deploy
	 * @param {object} res Response Object to respond back user to
	 */
	"deploySingleSchedule": function (req, res) {

		// req.body = {
		// 	siteId: "ssh",
		// 	rid: "151cb3df-fd87-485c-be84-53dc10c7cc7b",
		// 	sid: "25216298-c455-496d-997b-5b0a17ae8270"
		// }

		let errtype = false;
		try {
			let { siteId, rid, sid } = req.body;

			if (!siteId || !rid || !sid) {
				res.badRequest("Params Incomlpete");
				return res.end();
			}

			let findBy = { siteId, rid };
			let sendController, sendTo, upObj, isItStage, recipe;
			Recipe.findOne(findBy).then(foundRecipe => {
				recipe = foundRecipe;
				if (!recipe) {
					errtype = true;
					throw "No recipe";
				}
				if (recipe.isStage == "0") {
					isItStage = true;
				}

				let schedule = helper.toArray(recipe["scheduled"]);
				if (!schedule) {
					schedule = [];
				}
				if (!schedule.includes(sid)) {
					errtype = true;
					throw "Make schedule first";
				}

				sendController = recipe["runOn"];
				return Schedules.findOne({ "sid": sid, rid });

			}).then(async (schedule) => {

				if (!schedule) {
					errtype = true;
					throw "No schedule in db";
				}
				if (schedule["isDeployed"] == "1") {

					// Recipe.deploy(req, res);
					errtype = true;
					throw "Already Deployed";
				}

				if (sendController === "server") {
					await Schedules.update({ "sid": sid, rid }, { "isDeployed": "1" });
					await Recipe.update(findBy, { "isStage": "0" });
					return "server";
				} else { // recipe needs to be send to controller

					let sInfo = helper.toArray(schedule["ts"]).map((i) => {
						return {
							"id": schedule["sid"],
							"rid": schedule["rid"],
							"cron": i[2],
							"startdate": i[0],
							"enddate": i[1],
						};
					});

					let toSendAction = helper.toJson(recipe.actionable).map(actioni => {
						let title = actioni["title"] ? actioni["title"] : "No title";
						let _priority = priorityMap[actioni["priority"]] ? priorityMap[actioni["priority"]] : "low";
						let toNotify = actioni["notify"];
						let smslist = actioni["smslist"] ? actioni["smslist"] : [];

						if (actioni["type"] == "action") {
							return {
								"title": title,
								"type": actioni["type"],
								"priority": _priority,
								"uniqId": actioni["uniqId"],
								"did": actioni["did"],
								"command": actioni["command"],
								"value": actioni["value"],
								"delay": actioni["delay"],
								"category": actioni["category"],
								"notify": toNotify,
								"smslist": smslist

							};
						} else {
							return {
								"title": title,
								"type": actioni["type"],
								"priority": _priority,
								"uniqId": actioni["uniqId"],
								"category": actioni["category"],
								"notify": toNotify,
								"smslist": smslist
							};
						}

					});

					if (isItStage) {
						upObj = {
							"operation": "recipeInit",
							"scheduleInfo": sInfo,
						};
					} else {

						let topic = helper.toJson(recipe.everyMinuteTopics);
						if (!topic) {
							topic = [];
						}
						let maxDataNeeded = recipe.maxDataNeeded;
						if (!maxDataNeeded) {
							maxDataNeeded = 0;
						}
						let maxLogsNeeded = recipe.maxLogNeeded;
						if (!maxLogsNeeded) {
							maxLogsNeeded = 0;
						}


						upObj = {
							"recipeInfo": {
								"maxLogsNeeded": maxLogsNeeded,
								"failsafe": JSON.stringify({}),
								"everyMinuteTopics": helper.toJson(recipe.everyMinuteTopics),
								"runOn": recipe.runOn,
								"topics": topic,
								"recipe": recipe.formula,
								"syncAsyncAction": "True",
								"dependentOnOthers": helper.toJson(recipe.dependentOnOthers),
								"controllers": helper.toJson(recipe.dependentOnOthers),
								"switchOff": "0",
								"actionAlert": helper.toJson(toSendAction),
								"startNow": "True",
								"maxDataNeeded": maxDataNeeded,
								"rid": recipe.rid,
								"notRun": (recipe.notRun),  // start and end time
								"alwaysRun": recipe.alwaysRun
							},
							"scheduleInfo": sInfo,
							"operation": "recipeInit"
						};
					}

					sendTo = `${siteId}/config/${sendController}/recipelogic`;
					return "controller";
				}

			}).then(runON => {
				if (runON == "controller") {
					eventService.publish(sendTo, JSON.stringify(upObj));
				}
				return res.send("Schedules recipe");
			}).catch(e => {
				sails.log.error(e);
				return res.badRequest();
			});


		} catch (e) {
			sails.log.error(e);
			if (errtype) {
				res.badRequest(e);
			} else {
				res.badRequest("Server Error");
			}
		}

	},
	/**
	 * Take all schedules of a recipe and deploy it to controller
	 * @function
	 * @param {object} req Request Object having recipeID to deploy schedules of
	 * @param {object} res Response Object to respond back user to
	 */
	"deploy": function (req, res) {


		// response will come on recipeService.recipeAdded
		// req.body = {
		// 	siteId: "ssh",
		// 	rid: "3410ee75-af92-45b5-b3c1-574d726a3980",
		// }

		let errtype = false;
		let isItStage = false;
		try {
			let { rid, siteId } = req.body;
			if (!rid || !siteId) {
				res.badRequest("No param given");
				return res.end();
			}
			Recipe.findOne({ rid, siteId }).then(r => {
				if (!r) {
					errtype = true;
					throw ("No recipe");
				}
				if (r.rtype == "abstract") {
					errtype = true;
					throw "Cannot deploy abstract recipe";
				}
				if (r.isStage == "0") {
					isItStage = true;
				}
				let schedules = helper.toJson(r.scheduled);
				let sendController = r["runOn"];

				if (sendController == "server") {
					this.sendRecipeToserver(r, isItStage, schedules, siteId).then(responseUpdate => {
						res.send({ "error": false, "data": "deployed" });
					}).catch(e => {
						res.send({ "error": true, "data": "Unable to deploy recipe" });
					});
				} else {
					this.sendRecipeToController(r, isItStage, schedules, siteId).then(responseSent => {
						if (responseSent) {
							res.send({ "error": false, "data": "deployed" });
						} else {
							res.send({ "error": true, "data": "Unable to deploy recipe" });
						}
					}).catch(e => {
						res.send({ "error": true, "data": "Unable to deploy recipe" });
					});
				}

			}).catch(e => {
				if (errtype) {
					sails.log.error(e);
					res.badRequest(e);
				} else {
					res.serverError("Invalid Request");
				}
			});


		} catch (e) {
			sails.log.error(e);
			if (errtype) {
				res.badRequest(e);
			} else {
				res.badRequest("Server Error");
			}
		}

	},
	"sendRecipeToController": function (recipe, isItStage, schedules, siteId) {
		return new Promise((resolve, reject) => {
			try {

				let recipeId = recipe.rid;
				let sendController = recipe.runOn;

				let toSendObj = {
					"operation": "recipeInit"
				};
				if (!isItStage) {
					// send recipe info also as this is first time recipe is going to controller
					toSendObj = {
						...toSendObj,
						"recipeInfo": {
							"maxLogsNeeded": recipe.maxLogNeeded,
							"failsafe": JSON.stringify({}),
							"everyMinuteTopics": helper.toJson(recipe.everyMinuteTopics),
							"runOn": recipe.runOn,
							"topics": helper.toJson(recipe.everyMinuteTopics),
							"recipe": recipe.formula,
							"syncAsyncAction": "True",
							"dependentOnOthers": helper.toJson(recipe.dependentOnOthers),
							"controllers": helper.toJson(recipe.dependentOnOthers),
							"switchOff": "0",
							"actionAlert": helper.toJson(recipe.actionable),
							"startNow": "True",
							"maxDataNeeded": recipe.maxDataNeeded,
							"rid": recipeId,
							"notRun": (recipe.notRun),  // start and end time
							"alwaysRun": recipe.alwaysRun
						}
					};
					let allSch = schedules.map(sch => {
						return Schedules.findOne({ "rid": recipeId, "sid": sch });
					});
					Promise.all(allSch).then(schedules => {
						if (schedules.length > 0) {

							// only deploy un sent schedules
							schedules = schedules.filter(schedule => {
								if (schedule.isDeployed == "1") {
									return false;
								} else {
									return true;
								}
							});

							let r = schedules.map(s => {
								return helper.toArray(s["ts"]).map((i) => {
									return {
										"id": s["sid"],
										"rid": s["rid"],
										"cron": i[2],
										"startdate": i[0],
										"enddate": i[1],
									};
								});
							});

							toSendObj["scheduleInfo"] = r[0];
							// send sendObj to firmware
							let sendTo = `${siteId}/config/${sendController}/recipelogic`;
							eventService.publish(sendTo, JSON.stringify(toSendObj));
							resolve(true);
						} else {
							resolve(false);
						}
					}).catch(e => {
						sails.log.error("Error in recipecontroller.sendrecipetocontroller :", e);
						reject(false);
					});
				}

			} catch (e) {
				sails.log("Error ar recipecontroller.sendrecipetoController : ", e);
			}
		});

	},
	"sendRecipeToserver": function (recipe, isItStage, schedules, siteId) {

		return new Promise((resolve, reject) => {
			try {
				let updateList = [];

				let rid = recipe["rid"];

				updateList.push(Recipe.update(recipe, { "isStage": "0" }));
				for (let schedule of schedules) {
					updateList.push(Schedules.update({ "rid": rid, "sid": schedule }, { "isDeployed": "1" }));
				}
				resolve(Promise.all(updateList));

			} catch (e) {
				sails.log.error("Error in recipecontroller.sendRecipeToserver :", e);
				reject(e);
			}
		});
	},
	// not in use currently
	"pausedRecipeplay": function (req, res) {
		// to start stop
		// req.body = {
		//     rid : "71429823-59ad-48d3-bc8a-c8917d975595",
		//     siteId : "smt-del"
		// }
		let errtype = false;
		try {
			let { rid, siteId } = req.body;
			if (!rid || !siteId) {
				res.badRequest("Bad param");
				return res.end();
			}

			let findBy = { "rid": rid, "siteId": siteId };
			Recipe.findOne(findBy).then(async (recipe) => {

				if (!recipe["isStage"] || !recipe["isStage"] == "1") {
					errtype = true;
					throw "Recipe Must be Deployed first";
				}
				let runner = recipe["runOn"];

				if (runner === "server") {
					await Recipe.update(findBy, { "switchOff": "0" }); // 0 means this recipe can play now
					res.send("ok");
				} else {
					let upObj = {
						"data": {
							"rid": rid,
						},
						"func": "pausedRecipeplay",
						"operation": "recipeControl"

					};
					let sendTo = `${siteId}/config/${runner}/recipecontrol`;
					eventService.publish(sendTo, JSON.stringify(upObj));
					res.send("ok");
				}

			}).catch(e => {
				sails.log.error(e);
				return res.badRequest();
			});
		} catch (e) {
			sails.log.error(e);
			if (errtype) {
				res.badRequest(e);
			} else {
				res.badRequest("Server Error");
			}
		}
	},
	// not in use currently
	"pauseRecipe": function (req, res) {
		// to start stop
		// req.body = {
		//     rid : "71429823-59ad-48d3-bc8a-c8917d975595",
		//     from : "2018-12-10 12:00",
		//     to : "2018-12-20 12:00",
		//     siteId : "smt-del"
		// }


		let errtype = false;
		try {
			let { rid, from, to, siteId } = req.body;
			if (!rid || !from || !to || !siteId) {
				res.badRequest("Bad param");
				return res.end();
			}

			let findBy = { "rid": rid, "siteId": siteId };
			Recipe.findOne(findBy).then(recipe => {

				if (!recipe["isStage"] || !recipe["isStage"] == "1") {
					errtype = true;
					throw "Recipe Must be Deployed first";
				}
				let runner = recipe["runOn"];
				from = moment(from).format("YYYY-MM-DD HH:mm");
				to = moment(to).format("YYYY-MM-DD HH:mm");
				let fromTo = [from, to];
				let upObj = {
					"data": {
						"rid": rid, fromTo
					},
					"func": "pauseRecipe",
					"operation": "recipeControl"

				};
				let sendTo = `${siteId}/config/${runner}/recipecontrol`;
				eventService.publish(sendTo, JSON.stringify(upObj));
				res.send("ok");

			}).catch(e => {
				sails.log.error(e);
				return res.badRequest();
			});
		} catch (e) {
			sails.log.error(e);
			if (errtype) {
				res.badRequest(e);
			} else {
				res.badRequest("Server Error");
			}
		}

	},
	/**
	 * start/stop a recipe
	 * @function
	 */
	"startStopRecipe": function (req, res) {

		// req.body = {
		// 	rid : "c97e4c05-66aa-4d0c-9c7a-f29f79ffd870",
		// 	siteId : "mgch",
		//   startStop : true // means stop the recipe[STOP KARDO]
		// }

		let errtype = false;
		let upObj = {
			"func": "startStopRecipe",
			"operation": "recipeControl",
			"data": {}
		};
		try {
			let rid = req.body.rid;
			let siteId = req.body.siteId;
			let startStop = req.body.startStop;
			let status = "0"; // 0 means recipe is in play mode Or running.

			if (!rid || !siteId) {
				res.badRequest("Invalid params");
				return;
			}
			if (startStop) {
				status = "0";
			} else {
				status = "1";
			}
			let findBy = { "rid": rid, siteId };

			Recipe.findOne(findBy).then(async (recipe) => {

				if (!rid) {
					errtype = true;
					throw "Not Valid ID";
				}
				let runner = recipe["runOn"];
				if (runner === "server") {
					await Recipe.update(findBy, { "switchOff": status });
					res.send("ok");
				} else {
					upObj["data"]["switchOff"] = status; // stop recipe if startStop = true, and on ctronoller switchoff = 1 means stop
					upObj["data"]["rid"] = rid;

					let sendTo = `${siteId}/config/${runner}/recipecontrol`;
					eventService.publish(sendTo, JSON.stringify(upObj));
					res.send("ok");
				}


			}).catch(e => {
				sails.log.error(e);
				return res.badRequest();
			});

		} catch (e) {
			sails.log.error(e);
			if (errtype) {
				res.badRequest(e);
			} else {
				res.badRequest("Server Error");
			}
		}

	},
	"saveSchedule": function (req, res) {

		// req.body = {
		// 	"rid": "3410ee75-af92-45b5-b3c1-574d726a3980",
		// 	startDate: "2019-02-24",
		// 	endDate: "2019-12-24",
		// 	startTime: "00:00",
		// 	endTime: "22:30",
		// 	all_day: true, // just used to set start time = 00:00 and endtime to 23:59 thats it not to confused with repeat_type's all_day
		// 	// true means we will give stime and etime

		// 	repeat_interval: 'daily', // tells if one time schdule or will it be repeating more than 1 day
		// 	// vals can be daily meaning from start time to end time daily
		// 	// daily means daily strt time to end time
		// 	// weekdays meaning only sat sunday from start time to end time
		// 	siteId: 'ssh'
		// };


		let errtype = false;
		try {
			let { rid, startDate, endDate, startTime, endTime, all_day, repeat_interval, siteId } = req.body;
			let saveCron = false, stime, etime;
			if (!rid) {
				res.send("Error ! No Recipie Id Specified");
				return res.end();
			}
			if (!siteId) {
				res.send("Error ! Site Id not provided");
				return res.end();
			}
			if (all_day) {
				stime = "00:01";
				etime = "23:59";
				startTime = stime;
				endTime = etime;
			}
			if (!startTime || !endTime) {
				errtype = true;
				throw ("Error! Invalid Time");
			}

			if (Boolean(repeat_interval) && repeat_interval !== "No repeat") {

				if (repeat_interval == "daily") {
					startDate = moment().format("YYYY-MM-DD");
					endDate = moment().add(5, "year").format("YYYY-MM-DD");

					if (Boolean(startTime) && Boolean(endTime)) {
						if (/^\d{2}:\d{2}$/.test(startTime) && /^\d{2}:\d{2}$/.test(endTime)) {
							saveCron = IFTTT_helper.parseTime(moment(startDate), moment(endDate), startTime, endTime);
						} else {
							errtype = true;
							throw ("Error! Invalid Time");
						}
					} else {
						errtype = true;
						throw ("Error! Invalid Time");
					}
				} else if (repeat_interval == "weekdays") {
					startDate = moment().format("YYYY-MM-DD");
					endDate = moment().add(5, "year").format("YYYY-MM-DD");
					if (Boolean(startTime) && Boolean(endTime)) {
						if (/^\d{2}:\d{2}$/.test(startTime) && /^\d{2}:\d{2}$/.test(endTime)) {
							saveCron = IFTTT_helper.parseTime(moment(startDate), moment(endDate), startTime, endTime);
						} else {
							errtype = true;
							throw ("Error! Invalid Time");
						}
					} else {
						errtype = true;
						throw ("Error ! Weekly required start and end time/all day");

					}

					for (let k = 0; k < saveCron.length; k++) {
						let d3 = saveCron[k];
						let tmp = (d3).split(" ");
						tmp[2] = "*";
						tmp[4] = "1-5";
						saveCron[k] = tmp.join(" ");
					}
				}

			} else if (all_day) {
				if (startDate && endDate && endTime && startTime) {
					startDate = moment(startDate).format("YYYY-MM-DD");
					endDate = moment(endDate).format("YYYY-MM-DD");
					saveCron = RecipeService.absCron(IFTTT_helper.parseTime(moment(startDate), moment(endDate), startTime, endTime));
				} else {
					res.send("Error! All day must contain start and end Date");
					return res.end();
				}
				repeat_interval = "No repeat";
			} else {
				if (startDate && endDate && startTime && endTime) {
					startDate = moment(startDate).format("YYYY-MM-DD");
					endDate = moment(endDate).format("YYYY-MM-DD");
					if (/^\d{2}:\d{2}$/.test(startTime) && /^\d{2}:\d{2}$/.test(endTime)) {
						saveCron = IFTTT_helper.parseTime(moment(startDate), moment(endDate), startTime, endTime);
					} else {
						res.send("Error! Invalid Time");
						return res.end();
					}
				} else {
					res.send("Error! Invalid scheduling syntax");
					return res.end();
				}
				repeat_interval = "No repeat";

			}
			let toPut = [];
			if (saveCron == null) {
				res.send({ "error": true });
				return res.end();
			}
			saveCron.forEach(cron => {
				toPut.push([startDate + " " + startTime, endDate + " " + endTime, cron]);
			});

			let saveObj = {
				"sid": uuid(),
				"rid": rid,
				siteId,
				"repeat_type": repeat_interval,
				"ts": JSON.stringify(toPut),
				"schedule": all_day, // is this a all day or not
			};
			let cs, allSchedule;
			Recipe.findOne({ "rid": rid, siteId }).then(r => {
				if (!r) {
					errtype = true;
					throw "No recipe";
				}
				allSchedule = helper.toArray(r.scheduled);

				if (!allSchedule) {
					allSchedule = [];
				}
				if (r) {
					saveObj["runOn"] = r["runOn"]; // to run on server or not
					return Schedules.create(saveObj);
				} else {
					return false;
				}

			}).then(d => {
				if (d) {
					cs = d;
					allSchedule.push(d["sid"]);
					return Recipe.update({ "rid": rid, "siteId": siteId }, { "scheduled": JSON.stringify(allSchedule) });

				} else {
					errtype = true;
					throw "Cannot create schedule";
				}


			}).then(c => {
				if (c && c.length > 0) {
					eventService.notifyJouleTrack(
						siteId,
						"recipies",
						"recipes",
						{
							"event": "stage",
							"data": {
								"recipe": c,
								"res": false
							},
						}
					);
					res.send(cs);
				}
				else {
					Schedules.destroy(cs).then(() => { return res.badRequest("cannot create"); });
					eventService.notifyJouleTrack(
						siteId,
						"recipies",
						"recipes",
						{
							"event": "stage",
							"data": {
								"recipe": {},
								"res": false
							},
						}
					);
				}

			}).catch(e => {
				eventService.notifyJouleTrack(
					siteId,
					"recipies",
					"recipes",
					{
						"event": "stage",
						"data": {
							"recipe": {},
							"res": false
						},
					}
				);
				sails.log.error(e);
				res.badRequest();
			});

		} catch (e) {
			sails.log.error(e);
			if (errtype) {
				res.badRequest(e);
			} else {
				res.badRequest("Server Error");
			}
		}

	},
	/**
	 * Update recipe configration
	 * @function
	 * @param {object} req object contains updated recipe config
	 * @param {object} res Response object
	 */

	"updateRecipe": function (req, res) {

		// req.body = { "formula": "P||$4||#1||$2||0,60||$5||", "label": "test-recipe-1-r3", "type": "nonRoutine", "recipelabel": ["Energy Diagnostic", "Energy Optimization"], "neo": "simple", "isSchedule": false, "operators": { "$1": "-", "$2": ">=", "$3": ">", "$4": "(", "$5": ")" }, "params": { "#1": "ssh_42.kva" }, "siteId": "ssh", "startNow": true, "maxDataNeeded": "10", "actionable": [{ "title": "yeahyeah", "description": "yeah", "notify": ["<EMAIL>"], "accountable": ["<EMAIL>"], "type": "alert", "priority": 0, "uniqId": "c1a1b558f4786c0656588e13f07738e7" }], "rid": "33e3211f-c48c-46b4-bba9-9a1b8a0789a7" };

		// req._userMeta = { "id": "test" };
		try {

			let { rid, label, siteId, type, parentId, maxDataNeeded, maxLogNeeded, isSchedule, actionable, recipelabel } = req.body;
			actionable = helper.toArray(actionable);
			let userId = req._userMeta["id"];
			// let userId = "test"; // goto go back
			if (!rid || !label || !actionable || !siteId || !recipelabel || !type || !userId) {
				res.send({ "error": true, "data": "Error ! Incomplete parameters" });
				return res.end();
			}

			// Adding new uniqId again. ;/
			actionable = actionable.map(a => {
				// prevent duplicate actions
				return { ...a, "uniqId": md5(JSON.stringify(a)) };
			});

			let mainObj = {
				rid,
				siteId,
				type,
				"isSchedule": JSON.stringify(isSchedule),
				"failsafe": JSON.stringify({}),
				"actionable": helper.toJson(actionable),
				label,
				"recipelabel": helper.toJson(recipelabel),
				"alwaysRun": "false",
			};
			if (parentId) {
				mainObj["parentId"] = parentId;
			}


			Recipe.findOne({ "rid": rid, "siteId": siteId }).then(async (recipe) => {

				if (!recipe) {
					throw "Error! No Recipe";
				}
				mainObj["user"] = recipe["user"];
				mainObj["isStage"] = recipe["isStage"];
				mainObj["isActive"] = recipe["isActive"];
				mainObj["notRun"] = helper.toJson(recipe["notRun"]);
				mainObj["switchOff"] = helper.toString(recipe["switchOff"]);
				mainObj["scheduled"] = helper.toJson(recipe["scheduled"]);

				mainObj = await RecipeService.getRecipeObj(
					mainObj,
					req.body.formula,
					req.body.params,
					req.body.operators,
					maxDataNeeded,
					maxLogNeeded
				);


				// console.log(recipe, "goto go");
				// throw "err";
				if (recipe.isStage == "1" || recipe["runOn"] === "server") { // if recipe isnot deployed or is run on server

					// stringify the required stuff
					mainObj["notRun"] = recipe["notRun"];
					mainObj["scheduled"] = recipe["scheduled"];
					mainObj["actionable"] = JSON.stringify(mainObj["actionable"]);
					mainObj["recipelabel"] = JSON.stringify(mainObj["recipelabel"]);


					// only on server not on controller
					let updateObjFind = {
						"rid": mainObj["rid"],
						"siteId": mainObj["siteId"],
					};
					try {
						await Recipe.update(updateObjFind, mainObj);

					} catch (e) {
						sails.log.error("Error ", "RecipeController.updaterecipe", e);
					}
				} else {
					// is deployed
					// let sendController = mainObj["runOn"];
					let oldRun = recipe["runOn"];
					mainObj["params"] = helper.toJson(mainObj["params"]);
					mainObj["everyMinuteTopics"] = helper.toJson(mainObj["everyMinuteTopics"]);
					mainObj["dependentOnOthers"] = helper.toJson(mainObj["dependentOnOthers"]);
					mainObj["operator"] = helper.toJson(mainObj["operator"]);

					let sendObj = {
						"data": {
							...mainObj
						},
						"func": "updateRecipe",
						"operation": "recipeControl"
					};
					sails.log(mainObj);
					let sendTo = `${siteId}/config/${oldRun}/recipelogic`;
					let allSchedules = helper.toArray(recipe["scheduled"]);
					// console.log(sendTo, "<<<goto go");
					try {
						// this event will delete the recipe+schedules from controller
						let isSent = await eventService.publish(sendTo, JSON.stringify(sendObj));
						if (!isSent) {
							res.send({ "error": true, "data": "Mqtt is down" });
							return res.end();
						} else {
							// we delete the recipe from controller and unstage it
							await Recipe.update({ "rid": rid, "siteId": siteId }, { "isStage": "1" });
							await RecipeService.unschedulesSchedules(rid, allSchedules);
						}
					} catch (e) {
						sails.log(e);
						res.send({ "error": true, "data": "error! Please report" });
						return res.end();
					}
				}
				return res.send({ "error": false, "data": mainObj });

			}).catch(e => {
				sails.log.error("Error : ", e);
				return res.send({ "error": true, "data": "Query Error" });
			});

		} catch (e) {
			sails.log.error(e);
			res.send({ "error": true, "data": "Error ! Server Error" });
			return res.end();
		}
	},
	// curl -X GET localhost:1337/v1/recepie/get/:siteId
	"getRecipeOf": function (req, res) {
		try {
			let { siteId } = req.params;
			if (!siteId) {
				res.badRequest("Error in params");
				return res.end();
			}

			let feSendObj = {
				"abstract": {},
				"single": {},
				"errors": []
			};
			Recipe.find({ siteId }).then((data) => {
				data.forEach(d => {
					if (d["rtype"] == "abstract") {
						feSendObj["abstract"][d.rid] = { ...d, "childrens": [] };
					}
				});
				data.forEach(d => {
					if (d["parentId"]) {
						// in case of abstract recipe

						if (feSendObj[d["parentId"]]) {
							if (d.isSchedule == "true") {
								d["isActive"] = d["isActive"] == "1" ? true : false;
								d["observable"] = "";
								d["obs_params"] = {};
								d["isSchedule"] = Boolean(d["isSchedule"]);


							} else if (d.isSchedule == "false") {
								d["isActive"] = d["isActive"] == "1" ? true : false;
								d["isSchedule"] = Boolean(d["isSchedule"]);
								d["operators"] = helper.toJson(d["operator"]);
								d["actionable"] = helper.toJson(d["actionable"]);
								d["params"] = helper.toJson(d["params"]);
								d["recipelabel"] = helper.toJson(d["recipelabel"]);
								delete (d["operator"]);

							}
							feSendObj[d["parentId"]]["childrens"].push(d);
						} else {
							feSendObj["errors"].push(d);
						}
					} else {
						let currTime = moment().startOf("m").unix() * 1000;
						if (d.isSchedule == "true") {
							d["isActive"] = (currTime - d["isActive"] < 600000) ? true : false; // if last isActive was updated more than 10 min ago get false
							d["observable"] = "";
							d["obs_params"] = {};
							d["isSchedule"] = Boolean(d["isSchedule"]);
							d["recipelabel"] = helper.toJson(d["recipelabel"]);
							d["actionable"] = helper.toJson(d["actionable"]);

						} else if (d.isSchedule == "false") {
							d["isActive"] = (currTime - d["isActive"] < 600000) ? true : false; // if last isActive was updated more than 10 min ago get false
							d["isSchedule"] = Boolean(d["isSchedule"]);
							d["operators"] = helper.toJson(d["operator"]);
							d["actionable"] = helper.toJson(d["actionable"]);
							d["params"] = helper.toJson(d["params"]);
							d["recipelabel"] = helper.toJson(d["recipelabel"]);
							delete (d["operator"]);

						}
						feSendObj["single"][d.rid] = d;
					}
				});
				res.send(feSendObj);
			}).catch(e => {
				sails.log.error(e);
				res.send("error");
				return res.end();
			});

		} catch (e) {
			sails.log.error(e);
			res.send("Error");
		}

	},
	// dead route
	"maintainRedis": function (req, res) {
		try {

			let siteId = req.body.siteId;
			let nuser = [], rlab = [];
			if (!siteId) {
				throw "No Id Given";
			}
			Recipe.find({ "siteId": siteId }).then(recipes => {
				for (let recipe in recipes) {
					let c = recipes[recipe];
					let actionable = helper.toArray(c.actionable);
					let notify = [];
					let recipelabel = helper.toArray(c.recipelabel);
					rlab = recipelabel.map(lab => {
						return categoryMap[lab.replace(/ /gi, "").toLowerCase()];
					});
					rlab = rlab.filter(Boolean);

					actionable.forEach(a => {
						notify.push(...a.notify);
						notify.push(...a.accountable);
					});

					notify.forEach(u => {
						if (u) {
							nuser.push(alertService.addWrapper(u, siteId, rlab));
						}
					});
				}
				return Promise.all(nuser);
			}).then(r => {
				res.send();
			}).catch(e => {
				sails.log.error(e);
				return res.end();
			});


		} catch (e) {
			return res.badRequest();

		}


	},

	"setActive": function (req, res) {
		try {
			let { siteId, rid } = req.body;
			if (!siteId || !rid || rid.constructor.name != "Array") {
				return res.end();
			}
			Recipe.find({ "siteId": siteId }).then(recipe => {
				recipe.forEach(r => {
					if (rid.includes(r.id)) {
						Recipe.update({ "id": String(r.id), "siteId": siteId }, { "isActive": "1" }).then((d) => { }).catch(e => sails.log(e));
					} else {
						Recipe.update({ "id": String(r.id), "siteId": siteId }, { "isActive": "0" }).then(d => { }).catch(e => sails.log(e));
					}
				});

			}).catch(e => {
				sails.log(e);
				return res.end();
			});

		} catch (e) {
			sails.log(e);
		}
	},
	"deleteRecipe": function (req, res) {
		// req.body = {
		// 	rid: "151cb3df-fd87-485c-be84-53dc10c7cc7b",
		// 	siteId: "ssh"
		// }
		try {
			let { siteId, rid } = req.body;

			if (!siteId || !rid) {
				res.send({ "error": true, "info": "Improper params" });
				return res.end();
			}

			Recipe.findOne({ "rid": rid }).then(async (recipe) => {
				if (!recipe) {
					res.send({ "error": true, "info": "No Recipe" });
					return res.end();
				}
				let runner = recipe["runOn"];
				let isStage = recipe["isStage"];

				if (isStage == "1" || runner === "server") {
					RecipeService.feedback(`${siteId}/`, { "data": { "rid": rid }, "func": "deleteRecipe" });
					res.send({ "error": false, "info": "Recipe Deleted" });
					return res.end();
				} else {
					let upObj = {
						"data": {
							"rid": rid,
						},
						"func": "deleteRecipe",
						"operation": "recipeControl"

					};
					let sendTo = `${siteId}/config/${runner}/recipecontrol`;
					eventService.publish(sendTo, JSON.stringify(upObj));
					res.send({ "error": false, "info": "Removing Schedule" });
				}

			}).catch(e => {
				sails.log.error(e);
				return res.badRequest();
			});
		} catch (e) {
			sails.log.error(e);
			return res.badRequest();
		}

	},
	"getSiteSchedule": function (req, res) {
		let errtype = false;
		let lookup = {};
		try {
			let siteId = req.body.siteId;
			if (!siteId) {
				res.badRequest();
				return res.end();
			}
			let schpromise = [];
			Recipe.find({ siteId }).then((data) => {
				if (!data) {
					errtype = true;
					throw "No site";
				}
				for (let d in data) {
					let recipe = data[d];
					let sch = helper.toArray(recipe.scheduled);
					if (!sch) continue;

					schpromise.push(...sch.map(s => {
						lookup[s] = recipe;
						return Schedules.findOne({ "rid": recipe.rid, "sid": s });
					}
					));
				}
				return Promise.all(schpromise);

			}).then(schedules => {

				let completeInfo = [];
				schedules.forEach(schedule => {
					let allSchedule = getScheduleRun(schedule);
					if (!schedule) {
						return;
					}
					let isdep = schedule.isDeployed == "1" ? true : false;
					completeInfo.push({
						"id": schedule.sid,
						"deploy": isdep,
						"repeat_interval": schedule.repeat_type,
						...allSchedule,
						"all_day": JSON.parse(schedule.schedule),
						"rid": lookup[schedule.sid]["rid"],
						"title": lookup[schedule.sid]["label"]
					});
				});
				return res.send(completeInfo);
			}).catch(e => {
				sails.log.error(e);
				res.send("Error! 1");
				return res.end();
			});

		} catch (e) {
			sails.log.error(e);
			if (errtype) {
				res.badRequest(e);
			} else {
				res.badRequest("Server Error");
			}
		}

	},
	"deleteSchedule": function (req, res) {
		// req.body  ={
		//   sid : 'f2cb47cd-1bc1-43b0-aef6-42a60c194950',
		//   rid : 'dfb86a2d-4838-4ae5-b1ad-0ac0632ecda9'
		// }
		try {
			let { sid, rid } = req.body;

			if (!sid || !rid) {
				res.send("Improper params");
				return res.end();
			}
			let siteId;

			Recipe.findOne({ "rid": rid }).then(recipe => {
				if (!recipe) {
					res.send({ "error": true });
					return res.end();
				}
				siteId = recipe["siteId"];
				let schedules = helper.toArray(recipe["scheduled"]);
				let isStage = recipe["isStage"];
				let runner = recipe["runOn"];
				if (isStage == "1" || runner === "server") {
					RecipeService.feedback(`${siteId}/`, { "data": { "rid": rid, "sid": sid }, "func": "deleteSchedule" });
					res.send({ "error": true, "info": "Schdeule Deleted" });
					return res.end();
				}
				if (!schedules.includes(sid)) {
					res.send({ "error": true, "info": "No Schedule" });
					return res.end();
				}
				let upObj = {
					"data": {
						"rid": rid,
						"sid": sid
					},
					"func": "deleteSchedule",
					"operation": "recipeControl"

				};
				let sendTo = `${siteId}/config/${runner}/recipecontrol`;
				eventService.publish(sendTo, JSON.stringify(upObj));
				res.send({ "error": false, "info": "Removing Schedule" });

			}).catch(e => {
				sails.log.error(e);
				return res.badRequest();
			});


		} catch (e) {
			sails.log.error(e);
			res.badRequest();
		}
	},
	"test": async function (req, res) {

		// try {

		// sendmail.sendTemplateMail({
		// 	"to": "<EMAIL>",
		// 	"alert": "test",
		// 	"label": "test",
		// 	"extra": "test",
		// 	"token": "xyz",
		// 	"subject": "Alert: ",
		// 	"template": "benchmarking",
		// 	"tsExec": "123123",
		// 	"status": "test", // added
		// 	"groups": "test",
		// 	"from": "\"SmartJoules\" <<EMAIL>>"
		// });

		// sendmail.sendTemplateMail({
		// 	"to": "<EMAIL>",
		// 	"alert": "test",
		// 	"label": "test",
		// 	"extra": "test",
		// 	"token": "xyz",
		// 	"subject": "Alert: ",
		// 	"template": "forgot-password",
		// 	"tsExec": "123123",
		// 	"status": "test", // added
		// 	"groups": "test",
		// 	"from": "\"SmartJoules\" <<EMAIL>>"
		// });

		// sendmail.sendTemplateMail({
		// 	"to": "<EMAIL>",
		// 	"alert": "test",
		// 	"label": "test",
		// 	"extra": "test",
		// 	"token": "xyz",
		// 	"subject": "Alert: ",
		// 	"template": "maintenance",
		// 	"tsExec": "123123",
		// 	"status": "test", // added
		// 	"groups": "test",
		// 	"from": "\"SmartJoules\" <<EMAIL>>"
		// });

		// sendmail.sendTemplateMail({
		// 	"to": "<EMAIL>",
		// 	"alert": "test",
		// 	"label": "test",
		// 	"extra": "test",
		// 	"token": "xyz",
		// 	"subject": "Alert: ",
		// 	"tsExec": "123123",
		// 	"status": "test", // added
		// 	"groups": "test",
		// 	"from": "\"SmartJoules\" <<EMAIL>>"
		// });

		// } catch (e) {
		// 	sails.log.error(e);
		// }
		// let newConfig = {
		// 	"recipeprocess": "1",
		// 	"recipecomfort": "1",
		// 	"reciperoutine": "1",
		// 	"recipefailsafe": "1",
		// 	"recipediagnostics": "1"
		// };


		// UserSiteMap.find({"userId" : "<EMAIL>"}).then( async(users) => {

		// 	for (let user of users){
		// 		// djnotif fix
		// 		user["djNotif"] = user["djNotif"] ? (helper.toJson(user["djNotif"])) : {};
		// 		user["djNotif"]["JouleRecipe"] = user["djNotif"]["JouleRecipe"] ? user["djNotif"]["JouleRecipe"] : {};
		// 		user["djNotif"]["JouleRecipe"] = {...user["djNotif"]["JouleRecipe"], ...newConfig};
		// 		user["djNotif"] = JSON.stringify(user["djNotif"]);

		// 		// mailnotif fix
		// 		user["mailConfig"] = user["mailConfig"] ? (helper.toJson(user["mailConfig"])) : {};
		// 		user["mailConfig"]["JouleRecipe"] = user["mailConfig"]["JouleRecipe"] ? user["mailConfig"]["JouleRecipe"] : {};
		// 		user["mailConfig"]["JouleRecipe"] = {...user["mailConfig"]["JouleRecipe"], ...newConfig};
		// 		user["mailConfig"] = JSON.stringify(user["mailConfig"]);

		// 		// msg config fix
		// 		user["msgConfig"] = user["msgConfig"] ? (helper.toJson(user["msgConfig"])) : {};
		// 		user["msgConfig"]["JouleRecipe"] = user["msgConfig"]["JouleRecipe"] ? user["msgConfig"]["JouleRecipe"] : {};
		// 		user["msgConfig"]["JouleRecipe"] = {...user["msgConfig"]["JouleRecipe"], ...{
		// 			"recipeprocess": "1",
		// 			"recipecomfort": "1",
		// 			"reciperoutine": "1",
		// 			"recipefailsafe": "1",
		// 			"recipediagnostics": "1"
		// 		} };
		// 		user["msgConfig"] = JSON.stringify(user["msgConfig"]);
		// 		await UserSiteMap.update({"userId" : user["userId"], "siteId" : user["siteId"]}, user);

		// 	}
		// 	sails.log("Done");
		// });
		// Just a script a use a lot to debug recipe issue so thought to keep it here in code
		// recipes = await Recipe.find()

		// recipes.forEach(recipe => {
		// 	recipe.ts = moment(parseInt(recipe.isActive)).format()
		// 	if(recipe && recipe.createdAt === undefined && recipe.siteId == 'aph-ahm') {
		// 		console.log(recipe.rid,  moment(parseInt(recipe.isActive)).format())
		// 	} else {
				
		// 		// console.log(recipe);
		// 	}
		// if(recipe && recipe.siteId === 'mgch' && recipe.runOn !== 'server' && recipe.isStage === '0') {
		// 	console.log(recipe.rid, recipe.runOn);
		// }
		// })
		// devices = await Devices.find({siteId : 'mgch'});
		// devices = devices.filter(device => device.baudRate !== undefined)
		// devices.forEach(device => {
		// 	console.log(device.siteId, device.deviceId, device.hardwareVer, device.deviceType);
		// })

		// const fs = require('fs')
		// let recipes = await Recipe.find();
		// fs.writeFileSync('recipes', JSON.stringify(recipes));
		// sitecomponentmap = { "mgch_15": "ahu", "mgch_10": "ahu", "mgch_5": "ahu", "mgch_7": "ahu", "mgch_9": "ahu", "mgch_12": "ahu", "mgch_4": "ahu", "mgch_19": "ahu", "mgch_13": "ahu", "mgch_14": "ahu", "mgch_8": "ahu", "mgch_21": "ahu", "mgch_20": "ahu", "mgch_17": "ahu", "mgch_3": "ahu", "mgch_16": "ahu", "mgch_6": "ahu", "mgch_18": "ahu", "mgch_11": "ahu", "mgch_29": "chilledWaterPump", "mgch_28": "chilledWaterPump", "mgch_24": "chilledWaterPump", "mgch_27": "chiller", "mgch_1": "chiller", "mgch_25": "condenserWaterPump", "mgch_22": "coolingTower", "mgch_23": "coolingTower", "mgch_26": "coolingTower" }
		// recipes = JSON.parse(fs.readFileSync('./recipes'));
		// fixmapAlert1 = {
		// 	"recipecomfort": "comfort",
		// 	"recipeprocess": "energywastage",
		// 	"reciperoutine": "maintenance",
		// 	"recipefailsafe": "warning",
		// 	"recipediagnostics": "maintenance",
		// 	"maintenance": "maintenance",
		// 	"comfort": "comfort",
		// 	"energywastage": "energywastage",
		// 	"warning": "warning"
		// }
		// fixmapAction1 = {
		// 	"changesetpoint": "setpoint",
		// 	"stop": "miscellaneous",
		// 	"start": "miscellaneous",
		// 	"setfrequency": "frequencypush",
		// }
		// let $promiseArray = [];
		// let count = 0;
		// try {
		// 	for (let recipe of recipes) {
		// 		let runInterval = recipe['runInterval']
		// 		let updateObj = {}
		// 		// if (recipe.siteId === "sjo-del") {
		// 		// 	continue
		// 		// }
		// 		if (runInterval !== undefined) {
		// 			// console.log("NEW WALI", recipe);
		// 			continue;
		// 		}
		// 		let { rid, siteId } = recipe;
		// 		let prevType = recipe["type"]
		// 		let prevLabel = recipe["recipelabel"]
		// 		let prevActionable = helper.toJson(recipe["actionable"]);
		// 		// console.log(rid)
		// 		if (prevActionable === undefined || prevActionable.constructor.name !== 'Array') {
		// 			if (recipe["everyMinuteTopics"] !== undefined) {
		// 				console.log("ERROR , actionable dont exist", recipe);
		// 				// throw new Error("ERROR , actionable dont exist", recipe)
		// 				count++;
		// 				continue;
		// 			} else {
		// 				// console.log(recipe['rid'])
		// 				count++;
		// 				continue;
		// 			}
		// 		}
		// 		if (prevActionable.length > 1) {
		// 			console.log('recipe with 2 actionables', recipe)
		// 			throw new Error('Ye kaise hogya, recipe with 2 actionables')
		// 		} else if (prevActionable.length == 0) {
		// 			console.log("Empty actionable array: ", recipe)
		// 			throw new Error("Empty actionable");
		// 		}
		// 		let recipeType = prevActionable[0]["type"]
		// 		let newType = undefined
		// 		if (recipeType === "action") {
		// 			let command = prevActionable[0]["command"];
		// 			newType = fixmapAction1[command] || "miscellaneous";
		// 			prevActionable[0].priority = 3
		// 		} else if (recipeType === "alert") {
		// 			newType = fixmapAlert1[prevType] || "energywastage"
		// 			let component = undefined
		// 			if(!prevActionable[0].parent) {
		// 				try{
		// 					component = Object.values(helper.toJson(recipe.params))[0].split(".")[0]
		// 				} catch (e) {
		// 					console.log("Error in getting component from param", recipe.rid)
		// 				}
		// 				prevActionable[0].parent = component || ""
		// 			}

		// 		} else {
		// 			throw new Error("Mujshe na ho paega", recipe);
		// 		}
		// 		if (!newType) {
		// 			throw new Error("new type undefined", newType)
		// 		}
		// 		prevActionable[0].category = [`recipe${newType}`]
		// 		if (!recipe.label) {
		// 			throw new Error("recipe label dont exist", recipe)
		// 		}
		// 		if(prevActionable[0]["description"] === undefined) {
		// 			prevActionable[0]["description"] = recipe.label
		// 		}
		// 		if (!recipe.neo) {
		// 			let component = Object.values(helper.toJson(recipe.params))[0].split(".")[0]
		// 			if (sitecomponentmap[component] === undefined) {
		// 				throw new Error("recipe neo dont exist", recipe)
		// 			} else {
		// 				recipe.neo = sitecomponentmap[component]
		// 				updateObj.neo = sitecomponentmap[component]
		// 			}
		// 		}
		// 		updateObj["type"] = newType
		// 		updateObj["recipelabel"] = JSON.stringify([`recipe${newType}`])
		// 		updateObj["componentsType"] = JSON.stringify([recipe.neo])
		// 		updateObj["runInterval"] = "1"
		// 		updateObj["actionable"] = JSON.stringify(prevActionable)

		// 		// $promiseArray.push(Recipe.update({rid, siteId}, updateObj));
		// 		// if($promiseArray.length == 20) {
		// 		// 	await Promise.all($promiseArray);
		// 		// 	$promiseArray = [];
		// 		// }
		// 		// console.log(updateObj)
		// 		console.log(rid, updateObj);
		// 	}
		// 	// await Promise.all($promiseArray);
		// 	// console.log(recipes.length, count);
		// 	return res.end();

		// } catch(e) {
		// 	console.log(e);
		// 	return res.end();
		// }
	}
};

function getScheduleRun(t) {
	let tempSch = [];
	let retObj = {};
	if (t) {
		let st = moment(JSON.parse(t.ts)[0][0]).format("YYYY-MM-DD HH:mm");
		let et = moment(JSON.parse(t.ts)[0][1]).format("YYYY-MM-DD HH:mm");
		// let td = moment();
		if (st == et) {
			let tempEt = moment(et).add(30, "minutes");
			et = tempEt.format("YYYY-MM-DD HH:mm");
		}
		tempSch = [[st, et]];
		retObj["schedule"] = tempSch;

		let temp1 = st.split(" ");
		let temp2 = et.split(" ");

		// let edTime = temp2[1];
		// let edDate = temp2[0];
		retObj["startDate"] = temp1[0];
		retObj["startTime"] = temp1[1];
		retObj["endDate"] = temp2[0];
		retObj["endTime"] = temp2[1];

		// }
		return retObj;
	}
}
