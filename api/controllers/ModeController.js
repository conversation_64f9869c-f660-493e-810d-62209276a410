/**
 * ModeController
 *
 * @description :: Server-side logic for managing modes
 * @help        :: See http://sailsjs.org/#!/documentation/concepts/Controllers
 */

const moment = require("moment-timezone");
const helper = require("../services/helper");
moment.tz.add("Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6");
moment.tz.setDefault("Asia/Kolkata");
const Joi = require("joi");
const modeService= require("../services/modeService");

module.exports = {

	"publishComponentModeDetailsToSocket": async function(req, res) {
		try {
			const componentIds = req.body['componentIds']
			const siteId = req.params['siteId']
			const schema = Joi.object().keys({
				componentIds: Joi.array().items(Joi.string()).min(1).required(),
				siteId: Joi.string().required(),
			  })
			  const { error } = schema.validate({componentIds, siteId});
			  if( error)  {
				return res.status(400).send({
					err: error.message
				})
			  };
			await modeService.publishComponentModeDetailsToSocket(siteId, componentIds)
			return res.ok({
				message: 'Fetch mode details have been fetched and published to socket successfully'
			})
		} catch (e) {
			return res.serverError({
				"message": "Server has encountered an issue.Please contact admin for further assistance",
				error: e.message
			});
		}
	},
	"getMode": async function (req, res) {
		// req.body={ "did":["70_stop", "70_setfrequency"] };
		try {

			let { did } = req.body;
			if (!did && !componentId) {
				res.badRequest("invalid");
				return res.end();
			}
			
			// Bug HOTFIX changes. In case, componentId is present in the request parameter, 
			// Generating the `did` array of parameters from the components configuration.
			let { componentId } = req.body;
			const isComponentApi = componentId ? true : false;
			let errorMessages = [], deviceAbbrToControlKeyMap;
			if(isComponentApi){
				let componentConfig = await Component.findOne({
					"deviceId": componentId
				});

				// Parsing control parameters
				const controlParameters = helper.convertStringArrayToJSONArray(componentConfig.controls);
				const parsedControlParams = controlParameters.filter(parameterConfiguration =>{
					if(typeof parameterConfiguration != "object"){
						errorMessages.push(`Parameter: ${parameterConfiguration} could not be parsed and hence not sending it's mode.`);
						return false;
					} else return true;
				})

				const deviceIdParameterList = parsedControlParams.map(parameterConfiguration => {
					const { deviceId, device_abbr, key } = parameterConfiguration;
					if (device_abbr) return `${deviceId}_${device_abbr}`;
					else return `${deviceId}_${key}`;
				});
				const uniqueDeviceIdParameterList = [...new Set(deviceIdParameterList)];

				deviceAbbrToControlKeyMap = parsedControlParams.reduce((acc, curr) => {
					const { deviceId, device_abbr, key } = curr;
					let searchKey;
					if (device_abbr){
						searchKey = `${deviceId}_${device_abbr}`;
					} else {
						searchKey = `${deviceId}_${key}`;
					}
					const returnValue = `${deviceId}_${key}`;
					if(!acc[searchKey]) acc[searchKey] = [returnValue];
					else acc[searchKey].push(returnValue);
					return acc;
				}, {});
				did = uniqueDeviceIdParameterList;
			}
			// End of hotfix changes.
			did = (did.constructor.name) == "Array" ? did : did.split("");
			let response = {};
			try {
				let multipleModes = await modeService.multipleGetMode(did)
				for (let deviceIdParam of did){
					if (multipleModes[deviceIdParam] !== undefined) {
						response[deviceIdParam] = multipleModes[deviceIdParam];
					} else {
						response[deviceIdParam] = "jouletrack";
					}
				}	
				// Modifying response packet based on the hotfix changes.
				if(isComponentApi){
					let newResponsePacket = {};
					Object.keys(response).forEach(deviceAbbr =>{
						const value = response[deviceAbbr];
						const controlKeys = deviceAbbrToControlKeyMap[deviceAbbr];
						controlKeys.forEach(controlKey => {
							newResponsePacket[controlKey] = value;
						})
					});
					return res.ok(newResponsePacket);
				} else return res.ok(response);
				
			} catch (error) {
				sails.log.error(error);
				return res.serverError(error);
			}

		} catch (e) {
			sails.log.error(e);
			return res.serverError({
				"message": "Error fetching modes",
				error: e
			});
		}
	},
	"timeOutMode": function (req, res) {

		// req._userMeta = { "id": "test" };
		// req.body = {
		// 	"siteId": "ssh",
		// 	"did": {
		// 		// '1892_s_SetFrequency':"dj",
		// 		// '1892_s_SetPoint' : 'dj',
		// 		// '1894_s_SetPoint':"dj",
		// 		// '1921_s_StartStop':"dj",
		// 		"1182_s_SetPoint": "dj"
		// 		// '2053_ahuSetPoint':"xxx"
		// 	},
		// 	"later": 200


		// };

		let errType = false;
		try {


			// let errType = false;
			// try {
			// 	let x;

		} catch (e) {
			if (errType) {
				return res.badRequest(e);
			} else {
				sails.log.error(e);
				return res.serverError();

			}
		}


	}
};
