const moment = require("moment-timezone");
const alertService = require("../services/alertService");
moment.tz.add("Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6");
moment.tz.setDefault("Asia/Kolkata");

module.exports = {
	"fetchAlerts": async (req, res) => {
		let siteId = req._userMeta._site;
		let userId = req._userMeta.id;
		let filtedAlerts = [];
		let { timestamp, userFlag, isCustom } = req.body;

		if (!siteId || !timestamp) {
			sails.log("Missing siteId and timestamp");
			res.ok({ "err": "Missing parameters" });
			return res.end();
		}

		let timeRange = timestamp.split("_");

		try {
			let alerts = await alertService.findAllAlerts(siteId, userId, timeRange, userFlag, isCustom);

			if (alerts.length == 0) {
				res.ok({
					"msg": "No data found for the give time range",
					"alerts": alerts
				});
				return res.end();
			} else {
				let lastTimestamp = alerts[alerts.length - 1].timestamp;
				filtedAlerts = alertService.formatAlerts(alerts);
				res.ok({
					"alerts": filtedAlerts,
					"scroll": lastTimestamp
				});
				return res.end();
			}
		} catch (err) {
			sails.log(err);
			return res.ok({ "err": "Trouble fetcing data" });
		}
	},

	"latestNotifications": async (req, res) => {
		let maxToFetch = 15;
		let siteId = req._userMeta._site;
		let userId = req._userMeta.id;
		let timeRange = moment().unix();

		siteId = siteId + "_" + userId;

		try {
			let data = await alertService.fetchLatestAlerts(siteId, timeRange, maxToFetch);
			let formattedAlert = alertService.formatAlerts(data);

			return res.ok({
				"alerts": formattedAlert
			});
		} catch (err) {
			sails.log("Error while fetching latest notifications");
			sails.log(err);
			return res.ok({
				"err": "Error occured while fetching data"
			});
		}
	},

	"lastHourNotifications": async (req, res) => {
		let siteId = req._userMeta._site;
		let userId = req._userMeta.id;
		let currentTimestamp = moment().unix() * 1000;
		let prevHourTimestamp = currentTimestamp - 3600 * 1 * 1000; // 1 day
		let formattedAlert = [];

		let timeRange = [`${prevHourTimestamp}`, `${currentTimestamp}`];

		try {
			let data = await alertService.findAllAlerts(siteId, userId, timeRange, "0");

			if (data.length == 0) {
				data = await alertService.fetchLatestAlerts(siteId, currentTimestamp, 2000);
			}
			formattedAlert = alertService.formatAlerts(data);

			return res.ok({
				"alerts": formattedAlert
			});
		} catch (err) {
			sails.log(err);
			return res.ok({
				"err": "Trouble fetching data"
			});
		}
	},

	"addAlert": async (req, res) => {
		sails.log(req.body);
		Alert.create(req.body)
			.then(data => res.ok(data))
			.catch(err => {
				sails.log(err);
				res.ok(err);
			});
	},
};
