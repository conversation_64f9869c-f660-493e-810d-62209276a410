/**
 *  @module UsersController
 *  @description UsersController module handles everything related to users on the app.
 */
const moment = require("moment-timezone");
moment.tz.add(
	"Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6"
);
moment.tz.setDefault("Asia/Kolkata");
let s3url = "https://s3-us-west-2.amazonaws.com/assets.smartjoules";
const policies = require("../../config/policy");
const md5 = require("md5");


// setting defaults
// value : 0 <= n means never come at time interval of n minutes, (0 means never).

const defaultdjNotif = {
	"JouleRecipe": {
		"recipeprocess": "1",
		"recipecomfort": "0",
		"reciperoutine": "0",
		"recipefailsafe": "0",
		"recipediagnostics": "0"

	},
	"MaintenanceAction": {
		"maintainancecritical": "0",
		"maintainancehigh": "0",
		"maintainancelow": "0",
		"maintainancemedium": "0",
	},
	"hvacNotif": {
		"command": "0",
		"mode": "0",
		"config": "0",
	},
};
const defaultmailConfig = {
	"JouleRecipe": {
		"recipeprocess": "1",
		"recipecomfort": "0",
		"reciperoutine": "0",
		"recipefailsafe": "0",
		"recipediagnostics": "0"
	},
	"MaintenanceAction": {
		"maintainancecritical": "0",
		"maintainancehigh": "0",
		"maintainancelow": "0",
		"maintainancemedium": "0",
	},
	"hvacNotif": {
		"command": "0",
		"mode": "0",
		"config": "0",
	},
};
const defaultmsgConfig = {
	"JouleRecipe": {
		"recipeprocess": "1",
		"recipecomfort": "1",
		"reciperoutine": "1",
		"recipefailsafe": "1",
		"recipediagnostics": "1"

	},
	"MaintenanceAction": {
		"maintainancecritical": "0",
		"maintainancehigh": "0",
		"maintainancelow": "0",
		"maintainancemedium": "0",
	},
	"hvacNotif": {
		"command": "0",
		"mode": "0",
		"config": "0",
	},
};
const defaultdj = {

	"JouleRecipeMode": "1",
	"JouleRecipeThermostatMode": "1",
	"MaintenanceMode": "1",
	"OverrideMode": "1",
	"UIMode": "1",
	"DiagnosticGraph": "1",
};
const defunit = {
	"temperature": "degC",
	"delTemperature": "delC",
	"pressure": "kPa",
	"length": "m",
	"cons": "kvah"
};

module.exports = {
	//create a funciton for new user add with persmission
	/**
   * Create a new user
   * @param {object} req - Object containing information about the HTTP request.
   * @param {object} res - res to send back the desired HTTP response.
   * @example <caption>Example usage of method create.</caption>
   * //JSON object for a successful call
   * {
		"userId":"somethingUnique",
		"userRole":"executive",
		"confirmPassword":"password@123",
		"userOrganization":"def",
		"email":"<EMAIL>",
		"password":"password@123",
		"phone":"9999999999"
	 }

   //returns a created object json

   {
	"user": {
		"userId": "somethingUnique",
		"userRole": "executive",
		"userOrganization": "def",
		"email": "<EMAIL>",
		"phone": "9999999999"
	},
	"token": "some long random jwt",
	"siteId": "def"
}
   * @returns {object} returns JSON object with the created User as well as the JWT and siteId with the status 200.
   * @throws error 401:Incorrect password.
   * @throws error -  Server error.
   */

	// optimizations
	// not store whole policy list in redis everytime

	"create": async function (req, res) {
		// req.body ={
		//     email: "<EMAIL>",
		//     name: "testtt",
		//     designation : "hanhan",
		//     phone: '9315677759',
		//     userId: "<EMAIL>",
		//     userOrganization: "Smart Joules",
		//     policiesGroup : {"sjo":"admin"}
		// }

		let isValid = true;
		let defSite = false;
		let policiesGroup = helper.toJson(req.body.policiesGroup);
		req.body.password = "smartjoules123"; // next need to fix this.
		req.body.userRole = "";
		// userRole is sepcial field only admins can have

		let userId = req.body.userId;
		let email = req.body.email;
		let uorg = req.body.userOrganization;
		let phone = req.body.phone;

		delete (req.body["userRole"]);

		if (!uorg) {
			return res.badRequest({ "err": true, "data": "Invalid user Org." });
		}

		if (!userId) {
			userId = email;
		}
		if (!helper.isEmail(userId)) {
			return res.badRequest({ "err": true, "data": "Invalid email address" });
		}
		if (email !== userId) {
			return res.badRequest({ "err": true, "data": "Email and userId didn't match" });
		}

		if (!policiesGroup || Object.keys(policiesGroup).length == 0) {
			return res.badRequest({ "err": true, "data": "Cannot have user that doesn't have a policy attached" });
		}

		for (let i in policiesGroup) {
			try {
				let roleInfo = await Role.findOne({ "roleName": policiesGroup[i] });
				if (!roleInfo) {
					isValid = false;
				}
			} catch (e) {
				sails.log.error(e);
				isValid = false;
			}
		}
		if (!isValid) {
			res.badRequest({ "err": true, "data": "Invalid Role" });
			return res.end();
		}
		if (!req.body.defaultSite) {
			defSite = Object.keys(policiesGroup)[0]; // get the first site from the available sites
			req.body.defaultSite = defSite;
		} else {
			defSite = req.body.defaultSite;
		}
		let defRole = policiesGroup[defSite];

		let userInfo = {
			"userId": userId,
			"email": email,
			phone,
			"userOrganization": uorg,
			"password": "smartjoules123",
			"defaultSite": defSite,
			"designation": req.body.designation,
			"policiesGroup": JSON.stringify(policiesGroup),
			"notify": "[]",
			"accountable": "[]",
			"name": req.body.name,

		};

		Users.findOne({ "userId": userId, "userOrganization": uorg }).then(d => {
			if (d) {
				return undefined; // user already exist.
			} else {
				return Users.create(userInfo);
			}

		}).then(async (user) => {
			if (user) {
				// NOTE: payload is { id: user.id}
				let pgroup = helper.toJson(user.policiesGroup);
				for (let grp in pgroup) {
					try {
						// [TODO] FIx dual query
						let roleInfo = await Role.findOne({ "roleName": pgroup[grp] });
						if (!roleInfo) {
							await Users.destroy(user).then(d => { });
							throw "Error";
						} else {
							roleInfo = {
								"dj": JSON.stringify({ ...defaultdj, ...roleInfo["dj"] }),
								"djNotif": JSON.stringify({ ...defaultdjNotif, ...roleInfo["djNotif"] }),
								"mailConfig": JSON.stringify({ ...defaultmailConfig, ...roleInfo["mailConfig"] }),
								"msgConfig": JSON.stringify({ ...defaultmsgConfig, ...roleInfo["msgConfig"] }),
								"unit": JSON.stringify({ ...defunit, ...roleInfo["unit"] })
							};
						}
						// sails.log({ userId: userId, siteId: grp, role: pgroup[grp] , ...roleInfo });
						let userPhoneNumber = user["phone"];
						let createUserObject = { "userId": userId, "siteId": grp, "role": pgroup[grp], ...roleInfo };
						if (userPhoneNumber) {
							createUserObject["phone"] = userPhoneNumber;
						}
						await UserSiteMap.create(createUserObject);
						// immense speed increase in alerts if we have phone number in usersitemaps,
					} catch (e) {
						await Users.destroy({ "userId": userId }).then(d => { });
						res.serverError();
						return res.end();
					}
				}
				// try{
				//     await subAll(userId,phone);
				// }catch(e){
				//     sails.log("Subscriptions error");
				//     sails.log.error(e);
				// }
				res.json(200, { "user": user.toJSON(), "token": jwtService.issue({ "id": user.userId, "firsTime": true, "_role": defRole, "_site": defSite }), "siteId": user.userOrganization });
			} else {
				res.badRequest({ "err": true, "data": "User exists" });
			}


		}).catch(e => {
			sails.log.error(e);
			return res.serverError();
		});
	},
	"deleteUser": function (req, res) { },
	/**
	 * Update data for a specific user.
	 * @param {object} req - Object containing information about the HTTP request.
	 * @param {object} res - res to send back the desired HTTP response.
	 * @returns {object} returns JSON object with the updated User with the status 200.
	 * @throws error -  Server error.
	 */
	"update": function (req, res) {
		// update user info
		// can only update username,phone,defaultsite,designation
		let data = req.body;
		let userMeta = req._userMeta;

		if (!data) {
			res.badRequest({ "err": true, "data": "Nothing in the request data" });
			return res.end();
		}

		if (!data["userOrganization"]) {
			return res.badRequest({ "err": true, "data": "Must contain userOrganization" });
		}
		let upObj = (({ name, phone, defaultSite, designation, email }) => ({
			name,
			phone,
			defaultSite,
			designation,
			email,
		}))(data);

		for (let i in upObj) {
			if (!upObj[i]) {
				delete upObj[i];
			}
		}

		Users.update(
			{
				"userId": userMeta["id"],
				"userOrganization": data["userOrganization"],
			},
			upObj
		)
			.then(userData => {
				if (!userData) {
					sails.log("User Not found this id", userData);
					return res.ok(userData);
				} else {
					sails.log("Successfully update on this id", userData);
					return res.ok(userData);
				}
			})
			.catch(e => {
				sails.log.error(e);
				return res.serverError();
			});
	},
	"uploadAvatar": (req, res) => {
		let userId = req.user;
		dbHelper
			.uploadFile(req, res, userId)
			.then(data => {
				sails.log(userId);
				Users.findOne({ "userId": userId }).then(user => {
					Users.update(
						{
							"userId": userId,
							"userOrganization": user.userOrganization,
						},
						{ "picture": data.filename }
					).then(userData => {
						userData.img = s3url + "/" + userData.picture;
						res.ok({
							...userData,
							"picture": s3url + "/" + data.filename,
						});
					});
				});
			})
			.catch(er => {
				sails.log("in error");
				res.serverError(er);
				// sails.log(er, 'huhuhu');
			});
	},
	"uploadphoto": function (req, res) {
		try {
			let uid = req._userMeta["id"];
			let userOrg = req.body.userOrganization;
			let purl = req.body.picture;

			if (!uid || !purl) {
				req.badRequest();
				return res.end();
			}

			Users.update(
				{ "userId": uid, "userOrganization": userOrg },
				{
					"picture": purl,
				}
			)
				.then(d => {
					res.send(d);
				})
				.catch(e => {
					sails.log.error(e);
					res.serverError();
				});
		} catch (e) {
			sails.log.error(e);
			res.serverError();
		}
	},
	"changeSetting": async (req, res) => {
		// req.body = {
		//     newRole: [
		//         {
		//             userId: "<EMAIL>",
		//             siteId: "smt-del",
		//             role: "CEO"
		//         }

		//     ]
		// }

		// change role of user on a site

		try {
			let newRoles = helper.toArray(req.body.newRole), response;
			let defaultPreferences = {
				"dj": JSON.stringify(defaultdj),
				"djNotif": JSON.stringify(defaultdjNotif),
				"mailConfig": JSON.stringify(defaultmailConfig),
				"msgConfig": JSON.stringify(defaultmsgConfig),
				"unit": JSON.stringify(defunit)
			};
			if (!newRoles) return res.badRequest();
			if (newRoles.length == 0) return res.badRequest();

			let userSiteRoleMap = {};
			for (let eachRole of newRoles) {
				let { userId, siteId, role } = eachRole;
				if (!(userId in userSiteRoleMap)) userSiteRoleMap[userId] = {};
				userSiteRoleMap[userId][siteId] = role;
			}

			for (let userId in userSiteRoleMap) {
				let userSiteMap = await UserSiteMap.find({ userId });

				for (let eachSite of userSiteMap) {
					let { siteId, role } = eachSite;
					if (siteId in userSiteRoleMap[userId]) {
						if (role !== userSiteRoleMap[userId][siteId]) await UserSiteMap.update({ userId, siteId }, { role: userSiteRoleMap[userId][siteId] })
						delete userSiteRoleMap[userId][siteId];
					} else await UserSiteMap.destroy({ userId, siteId });
				}
			}

			for (let userId in userSiteRoleMap) {
				let userInfo = userSiteRoleMap[userId];
				let $createUserPromiseArray = [];

				for (let siteId in userInfo) {
					$createUserPromiseArray.push(UserSiteMap.create({ userId, siteId, role: userInfo[siteId], ...defaultPreferences }));
				}

				response = await Promise.all($createUserPromiseArray);
			}

			return res.send(response);



			// let delit = {};
			// let farr = [];

			// newRoles.forEach(r => {
			// 	delit[r["userId"]] = r["siteId"];
			// 	farr.push(UserSiteMap.find({ "userId": r["userId"] }));
			// });

			// for (let i in delit) {
			// 	userService
			// 		.delusermap(i)
			// 		.then(dele => {
			// 			let newrolearr = newRoles.map(async role => {
			// 				await Role.findOne({
			// 					"roleName": role["role"],
			// 				}).then(async d => {
			// 					if (d) {
			// 						return UserSiteMap.create({
			// 							"userId": role["userId"],
			// 							"siteId": role["siteId"],
			// 							"role": role["role"],
			// 						});
			// 					} else {
			// 						return;
			// 					}
			// 				});
			// 			});

			// 			return Promise.all(newrolearr);
			// 		})
			// 		.then(p => {
			// 			res.send(p);
			// 		})
			// 		.catch(e => {
			// 			sails.log.error(e);
			// 			return res.serverError();
			// 		});
			// }

			// UserSiteMap.update({userId : id,siteId : siteId},{
			//     role : newRole
			// })

			// Users.findOne({userId : id,userOrganization:org})
			// .then(user=>{
			//     policy = helper.toJson(user.policiesGroup);
			//     if(!policy){
			//         policy = {}
			//     }
			//     policy[siteId] = newRole;
			//     policy = JSON.stringify(policy);
			//     return Users.update({userId : id,userOrganization:org},{
			//         policiesGroup : policy
			//     })
			// }).then(updated=>{
			//     res.send(updated)
			// }).catch(e=>{
			//     sails.log.error(e);
			//     return res.serverError()
			// })
		} catch (e) {
			sails.log.error(e);
			res.serverError();
		}
	},
	"getUsers": (req, res) => {
		// get all users of a site
		// fkin fix this api

		let siteId = req.params.siteId;
		// sails.log("users", req._userMeta._role);
		let userRole = req._userMeta._role;
		if (!siteId) {
			res.badRequest();
		}
		let roleMapArr = {};
		let pgroup = {};
		UserSiteMap.find()
			.then(users => {
				if (!users) {
					return undefined;
				} else {
					let allUsers = [];
					for (let i in users) {
						if (users[i]["siteId"] == siteId) {
							if (userRole === "admin") {
								roleMapArr[users[i].userId] = users[i].role;
								allUsers.push(
									Users.findOne({ "userId": users[i].userId })
								);
							} else {
								if (users[i].role !== "admin") {
									roleMapArr[users[i].userId] = users[i].role;
									allUsers.push(
										Users.findOne({
											"userId": users[i].userId,
										})
									);
								}
							}
						}
						if (!pgroup[users[i].userId]) {
							pgroup[users[i].userId] = {};
						}
						pgroup[users[i].userId][users[i].siteId] =
							users[i].role;
					}
					return Promise.all(allUsers);
				}
			})
			.then(u => {
				let sendObj = [];
				u = u.filter(Boolean);
				for (let i = 0; i < u.length; i++) {
					delete u[i]["password"];
					delete u[i]["createdAt"];
					delete u[i]["updatedAt"];

					if (u[i]) {
						sendObj.push({
							...u[i],
							"role": roleMapArr[u[i].userId],
							"policiesGroup": pgroup[u[i].userId],
						});
					} else {
						sendObj.push({});
					}
				}
				res.send(sendObj);
			})
			.catch(e => {
				sails.log.error(e);
				return res.serverError();
			});
		// var resUsers = []
		//     users.forEach(u => {
		//         resUsers.push({
		//             userId: u.userId,
		//             name: u.name,
		//             email : u.email,
		//             policiesGroup : u.policiesGroup,
		//             actionsToday : [],
		//             energyRanking : 0,
		//             picture : u.picture
		//         })
		//     })
		//     res.ok(resUsers)
	},
	"getUser": (req, res) => {
		sails.log(req.user, "userid");
		let userId = req.user;
		let globaluserData;
		// sails.log('upload avatar!', req.file('avatar'))
		Users.findOne({ "userId": userId })
			.then(userData => {
				globaluserData = userData;
				// userData.picture = userData.picture;
				return UserSiteMap.find({ "userId": userId });
			})
			.then(data1 => {
				let tempObj = {};
				data1.forEach(d => {
					tempObj[d["siteId"]] = d["role"];
				});
				globaluserData["policiesGroup"] = tempObj;
				res.ok(globaluserData);
			})
			.catch(er => {
				sails.log(er);
				res.serverError(er);
			});
	},
	"available": function (req, res) {
		Users.findOne({ "userId": req.body.userId }, function (err, user) {
			if (user) {
				return res.json(401, {
					"err": "UserId is unavailable.Try another",
				});
			}
			res.json(200, { "info": "UserId is available congrats!!!" });
		});
	},
	/**
	 * Invalid requests handler.
	 */
	"invalid": function (req, res) {
		res.json(404, { "err": "Page not found" });
	},
	/**
	 *
	 * @param {array} req  - policyList
	 * @param {String} req - Role_Name
	 * @param {String} req - siteId
	 * @param {object} res
	 */

	"getRole": function (req, res) {
		// get all possible roles
		try {
			Role.find()
				.then(roles => {
					for (let i in roles) {
						let role = roles[i];
						let {
							djNotif,
							mailConfig,
							msgConfig,
							dj,
							unitPreference,
						} = helper.toJson(role["defpref"]);
						djNotif = { ...defaultdjNotif, ...djNotif };
						mailConfig = { ...defaultmailConfig, ...mailConfig };
						msgConfig = { ...defaultmsgConfig, ...msgConfig };
						dj = { ...defaultdj, ...dj };
						unitPreference = { ...defunit, ...unitPreference };
						roles[i]["defpref"] = JSON.stringify({
							djNotif,
							mailConfig,
							msgConfig,
							dj,
							unitPreference,
						});
					}

					res.send(roles);
				})
				.catch(e => {
					sails.log.error(e);
					res.serverError();
				});
		} catch (e) {
			sails.log.error(e);
			res.serverError();
		}
	},
	"getPolicies": function (req, res) {
		// return list of policies

		try {
			let policyMap = {};
			for (let policy in policies) {
				let desc = policies[policy]["_description"];
				let policyControllerName = desc["displayname"];
				if (!policyMap[policyControllerName]) {
					policyMap[policyControllerName] = {};
				}
				for (let j in policies[policy]) {
					if (j[0] == "_") {
						continue;
					}
					let type, displaynm;
					try {
						displaynm = j.split("_")[0];
						type = j.split("_")[1]; // it cannot be that it doesnt have 2nd parameter
					} catch (e) {
						sails.log(
							"Who ever is makeing policy plz assure that there must be a '_' in the policiy seperating [policy]_[type]"
						);
						res.send("Please contact admin, error in policies");
						return res.end();
					}
					if (!policyMap[policyControllerName][displaynm]) {
						policyMap[policyControllerName][displaynm] = {};
					}
					policyMap[policyControllerName][displaynm][type] = j;
				}
			}
			res.send(policyMap);
		} catch (e) {
			sails.log.error(e);
			return res.serverError();
		}
	},
	/**
	 *  This API return all the polcies userX is susbscribed to
	 * @param {array} req  - policyList
	 * @param {String} req - Role_Name
	 * @param {String} req - siteId
	 * @param {object} res
	 */
	"getUserPolicies": function (req, res) {
		try {
			let userId = req.body.userId;

			if (!userId) {
				return {};
			}

			userService
				.getPoliciesOfUser(userId)
				.then(d => {
					let totalMap = [];
					for (let i = 0; i < d.length; i++) {
						let policyMap = {};
						let pNow = d[i]["policies"];
						let roleName = d[i]["roleName"];

						for (let policy in policies) {
							let desc = policies[policy]["_description"];
							let policyControllerName = desc["displayname"];
							if (!policyMap[policyControllerName]) {
								policyMap[policyControllerName] = {};
							}
							for (let j in policies[policy]) {
								if (j[0] == "_") {
									continue;
								}
								let type, displaynm;
								try {
									displaynm = j.split("_")[0];
									type = j.split("_")[1]; // it cannot be that it doesnt have 2nd parameter
									if (
										!policyMap[policyControllerName][
										displaynm
										]
									) {
										policyMap[policyControllerName][
											displaynm
										] = {};
									}
									sails.log(
										policyControllerName,
										displaynm,
										type,
										pNow,
										j
									);
									policyMap[policyControllerName][displaynm][
										type
									] = pNow[j];
								} catch (e) {
									sails.log.error(e);
									sails.log(
										"Who ever is makeing policy plz assure that there must be a '_' in the policiy seperating [policy]_[type]"
									);
									res.send(
										"Please contact admin, error in policies"
									);
									return res.end();
								}
							}
							totalMap.push({
								"role": roleName,
								"policies": policyMap,
							});
						}
					}

					res.send(totalMap);
				})
				.catch(e => {
					sails.log.error(e);
					res.serverError();
				});
		} catch (e) {
			sails.log.error(e);
			return res.serverError();
		}
	},
	"createRole": function (req, res) {
		// req.body = {
		//     policylist : {"Remote SSH_Read":1,"CICD Update_Read":1},
		//     roleName : "test3",
		//     pref : {
		//         mailConfig : {
		//             JouleRecipe: {
		//                 recipecomfortoptimization : "60"
		//             }
		//         }
		//     }
		// }

		try {
			let { policylist, roleName, pref } = req.body;
			pref = helper.toJson(pref);
			policylist = helper.toJson(policylist);

			if (!policylist || !roleName || !pref) {
				return res.badRequest("Error");
			}
			// let {djNotif,mailConfig,msgConfig,dj,unitPreference} = helper.toJson(pref);

			let { djNotif, mailConfig, msgConfig, dj, unitPreference } = pref;
			djNotif = { ...defaultdjNotif, ...djNotif };
			mailConfig = { ...defaultmailConfig, ...mailConfig };
			msgConfig = { ...defaultmsgConfig, ...msgConfig };
			dj = { ...defaultdj, ...dj };
			unitPreference = { ...defunit, ...unitPreference };

			let defPref = JSON.stringify({
				djNotif,
				mailConfig,
				msgConfig,
				dj,
				unitPreference,
			});

			if (Object.keys(policylist).length == 0) {
				return res.badRequest("Error, No policies given");
			}

			let ezAccessPolices = {};
			Object.keys(policies).map(policy => {
				let tempStore = policies[policy];
				for (let i in tempStore) {
					ezAccessPolices[i] = tempStore[i];
				}
			});
			let finalPolicyHash = {};
			for (let orignalpolicy in ezAccessPolices) {
				if (
					!policylist[orignalpolicy] ||
					policylist[orignalpolicy] == 0
				) {
					finalPolicyHash[orignalpolicy] = 0;
				} else {
					finalPolicyHash[orignalpolicy] = 1;
				}
			}

			delete finalPolicyHash["_description"];

			roleName = String(roleName);
			let id = roleName;
			policylist = JSON.stringify(finalPolicyHash);
			let hashObj = { "roleName": id };
			Role.findOne(hashObj)
				.then(d => {
					if (!d) {
						return Role.create({
							...hashObj,
							"defpref": defPref,
							"policies": policylist,
							"isDeleted": "0",
						});
					} else {
						return { "err": "already exist", "policy": d };
					}
				})
				.then(role => {
					if (role["policies"]) {
						// key =manager_sjo , value is sttring of policies
						return cacheService.setExpiryKey(id, policylist);
					} else {
						return role;
					}
				})
				.then(role => {
					res.send(role);
				})
				.catch(e => {
					sails.log.error(e);
					res.serverError();
				});
		} catch (e) {
			sails.log.error(e);
			return res.serverError("Error");
		}
	},
	"updateRole": function (req, res) {
		// req.body = {
		//     policylist : {"Configuration_EDIT":0},
		//     roleName : "CLIENT Operator",
		// }

		try {
			let { policylist, roleName, pref } = req.body;
			policylist = helper.toJson(policylist);
			if (!policylist || !roleName || !pref) {
				return res.badRequest("Error");
			}
			if (policylist.length == 0) {
				return res.badRequest("Error, No policies given");
			}

			let {
				djNotif,
				mailConfig,
				msgConfig,
				dj,
				unitPreference,
			} = helper.toJson(pref);
			djNotif = { ...defaultdjNotif, ...djNotif };
			mailConfig = { ...defaultmailConfig, ...mailConfig };
			msgConfig = { ...defaultmsgConfig, ...msgConfig };
			dj = { ...defaultdj, ...dj };
			unitPreference = { ...defunit, ...unitPreference };

			let defPref = JSON.stringify({
				djNotif,
				mailConfig,
				msgConfig,
				dj,
				unitPreference,
			});

			let ezAccessPolices = {};
			Object.keys(policies).map(policy => {
				let tempStore = policies[policy];
				for (let i in tempStore) {
					ezAccessPolices[i] = tempStore[i];
				}
			});

			let finalPolicyHash = {};
			for (let orignalpolicy in ezAccessPolices) {
				if (!policylist[orignalpolicy]) {
					finalPolicyHash[orignalpolicy] = 0;
				} else {
					finalPolicyHash[orignalpolicy] = 1;
				}
			}

			delete finalPolicyHash["_description"];

			roleName = String(roleName);
			policylist = JSON.stringify(finalPolicyHash);
			let hashObj = { "roleName": roleName };
			Role.findOne(hashObj)
				.then(d => {
					if (d) {
						return Role.update(
							{
								...hashObj,
							},
							{ "policies": policylist, "defpref": defPref }
						);
					} else {
						return undefined;
					}
				})
				.then(role => {
					sails.log(role);
					if (role) {
						res.send(role);
					} else {
						res.json({ "err": "policy doesnot exist" });
					}
				})
				.catch(e => {
					sails.log.error(e);
					res.serverError();
				});
		} catch (e) {
			sails.log.error(e);
			return res.serverError("Error");
		}
	},
	"deleteRole": function (req, res) {
		try {
			let { roleName } = req.body;
			if (!roleName) {
				return res.badRequest();
			}

			Role.find({
				"roleName": roleName,
			})
				.then(d => {
					if (d) {
						UserSiteMap.find({ "role": roleName })
							.then(assocUser => {
								let delArray = assocUser.map(userMap =>
									UserSiteMap.destroy({
										"userId": userMap["userId"],
										"siteId": userMap["siteId"],
									})
								);
								Promise.all(delArray);
							})
							.then(deletedUsers => {
								return Role.destroy({ "roleName": roleName });
							})
							.then(deletedRole => {
								res.send();
							});
					} else {
						res.send();
					}
				})
				.catch(e => {
					sails.log.error(e);
					res.serverError();
				});
		} catch (e) {
			sails.log.error(e);
			return res.serverError();
		}
	},
	"editPassword": function (req, res) {
		// req.body ={
		//     old : "smartjoules123",
		//     passwd : "smartjoules123",
		//     userOrganization : "ssh"
		// }
		try {
			let userInfo = req._userMeta;
			let userId = userInfo["id"];
			let { passwd, userOrganization, old } = req.body;

			if (!passwd || !userOrganization || !userId || !old) {
				res.badRequest("Param error");
				return res.end();
			}
			let findBy = {
				userOrganization,
				userId,
			};
			Users.findOne(findBy)
				.then(user => {
					if (user) {
						return Users.comparePassword(old, user);
					} else {
						return undefined;
					}
				})
				.then(cpass => {
					if (cpass) {
						return Users.update(findBy, { "password": passwd });
					} else {
						return undefined;
					}
				})
				.then(d => {
					if (d) {
						return res.ok("Done");
					} else {
						return res.badRequest();
					}
				})
				.catch(e => {
					sails.log.error(e);
					return res.badRequest();
				});
		} catch (e) {
			sails.log.error(e);
			return res.serverError("contact admin");
		}
	},

	//For resetting user password.
	"resetPassword": async function (req, res) {
		try {
			let password = req.body.password;
			let token = req.body.token;

			if (!password || !token) {
				res.badRequest("Incomplete Parameters!!");
			}
			jwtService.verify(token, function (err, data) {
				if (err) {
					sails.log(err);
					return res.status(401).send({
						"err": err.message,
					});
				}
				sails.log("if verified");
				let decoded = jwtService.decode(token);
				let passwordhashMd5 = decoded.secret.split("-")[0];

				sails.log(decoded);

				//finding user from userId(token)
				Users.findOne({ "userId": decoded.id })
					.then(userData => {
						//checking if it is updated once.
						if (
							md5(userData.password + userData.userId) !==
							passwordhashMd5
						) {
							return res.badRequest({
								"err":
									"Link Expired, Your password is already changed using this link!",
							});
						}
						//updating user password
						let { userId, userOrganization } = userData;
						let query = {
							"userId": userId,
							"userOrganization": userOrganization,
						};
						Users.update(query, { password })
							.then(data => {
								sails.log("changed data : ", data);
								return res.ok({
									"success": "Password reset successfully",
								});
							})
							.catch(err => {
								sails.log(err);
								return res.badRequest({
									"err": "Server Error.",
								});
							});
					})
					.catch(err => {
						sails.log(err);
						return res.badRequest({ "err": "Server Error." });
					});
			});
		} catch (e) {
			sails.log(e);
			return res.badRequest();
		}
	},

	"getPref": function (req, res) {
		try {
			let userInfo = req._userMeta;
			let userId = userInfo.id;
			let siteId = userInfo._site;
			if (!userId || !siteId) {
				return res.forbidden();
			}
			UserSiteMap.findOne({ "userId": userId, "siteId": siteId })
				.then(data => {
					if (!data) {
						let djNotif = defaultdjNotif;
						let mailConfig = defaultmailConfig;
						let msgConfig = defaultmsgConfig;
						let dj = defaultdj;
						let unitPreference = defunit;
						res.send({
							djNotif,
							mailConfig,
							msgConfig,
							dj,
							unitPreference,
						});
					} else {
						let {
							djNotif,
							mailConfig,
							msgConfig,
							dj,
							unitPreference,
						} = data;

						djNotif = helper.toJson(djNotif);
						mailConfig = helper.toJson(mailConfig);
						msgConfig = helper.toJson(msgConfig);
						dj = helper.toJson(dj);
						unitPreference = helper.toJson(unitPreference);

						djNotif = { ...defaultdjNotif, ...djNotif };
						mailConfig = { ...defaultmailConfig, ...mailConfig };
						msgConfig = { ...defaultmsgConfig, ...msgConfig };
						dj = { ...defaultdj, ...dj };
						unitPreference = { ...defunit, ...unitPreference };
						res.send({
							djNotif,
							mailConfig,
							msgConfig,
							dj,
							unitPreference,
						});
					}
				})
				.catch(e => {
					sails.log.error(e);
					res.serverError();
				});
		} catch (e) {
			sails.log.error(e);
			res.serverError();
		}
	},
	"userPref": function (req, res) {
		// update prefrence
		// req.body = {
		//     mailConfig : {
		//         JouleRecipe : {
		//             recipeenergyoptimization : "1440"
		//         }
		//     }
		// }
		let finalPref = {};
		let errType = false;
		try {
			let userInfo = req._userMeta;
			let userId = userInfo.id;
			let siteId = userInfo._site;
			if (!userId || !siteId) {
				return res.forbidden();
			}
			let findBy = { "userId": userId, "siteId": siteId };

			let {
				djNotif,
				mailConfig,
				msgConfig,
				dj,
				unitPreference,
			} = req.body;
			// here is a bug, user can send any amount of config and we will still store it

			djNotif = { ...defaultdjNotif, ...djNotif };
			mailConfig = { ...defaultmailConfig, ...mailConfig };
			msgConfig = { ...defaultmsgConfig, ...msgConfig };
			dj = { ...defaultdj, ...dj };
			unitPreference = { ...defunit, ...unitPreference };
			// a small check to prevent to big payloads
			// if (djNotif.length > 650 || mailConfig.length > 650 || msgConfig.length > 650 || dj.length > 650) {
			//     return res.badRequest();
			// }
			let uInfo = {};
			Users.findOne({ "userId": userId })
				.then(d => {
					if (!d) {
						errType = true;
						throw "No User exist";
					} else {
						uInfo = {
							"email": d.email,
							"phone": d.phone,
						};
						return UserSiteMap.findOne(findBy);
					}
				})
				.then(async OlduserInfo => {
					if (!OlduserInfo) {
						OlduserInfo = {};
					}
					if (OlduserInfo["dj"]) {
						OlduserInfo["dj"] = helper.toJson(OlduserInfo["dj"]);
					}
					if (OlduserInfo["djNotif"]) {
						OlduserInfo["djNotif"] = helper.toJson(
							OlduserInfo["djNotif"]
						);
					}
					if (OlduserInfo["mailConfig"]) {
						OlduserInfo["mailConfig"] = helper.toJson(
							OlduserInfo["mailConfig"]
						);
					}
					if (OlduserInfo["msgConfig"]) {
						OlduserInfo["msgConfig"] = helper.toJson(
							OlduserInfo["msgConfig"]
						);
					}

					OlduserInfo["dj"] = { ...defaultdj, ...OlduserInfo["dj"] };
					OlduserInfo["djNotif"] = {
						...defaultdjNotif,
						...OlduserInfo["djNotif"],
					};
					OlduserInfo["mailConfig"] = {
						...defaultmailConfig,
						...OlduserInfo["mailConfig"],
					};
					OlduserInfo["msgConfig"] = {
						...defaultmsgConfig,
						...OlduserInfo["msgConfig"],
					};

					// sails.log(OlduserInfo,"---",{djNotif, unitPreference, dj, msgConfig, mailConfig});

					// return

					let responsed = await userService.changeSettingfunc(
						siteId,
						OlduserInfo,
						{ djNotif, unitPreference, dj, msgConfig, mailConfig },
						uInfo
					);
					finalPref = unitPreference;
					if (responsed) {
						return UserSiteMap.update(findBy, {
							"djNotif": JSON.stringify(djNotif),
							"msgConfig": JSON.stringify(msgConfig),
							"dj": JSON.stringify(dj),
							"mailConfig": JSON.stringify(mailConfig),
							"unitPreference": JSON.stringify(unitPreference),
						});
					} else {
						errType = true;
						throw "Unable to add user";
					}
				})
				.then(response => {
					//req._userMeta
					req._userMeta.unitPref = finalPref;
					delete req._userMeta.exp;
					delete req._userMeta.iat;
					// console.log(req._userMeta);
					let token = jwtService.issue(req._userMeta);
					let data = response;
					return res.json({ data, token });
				})
				.catch(e => {
					sails.log.error(e);
					if (errType) {
						res.send(e);
					} else {
						return res.serverError();
					}
				});
		} catch (e) {
			sails.log.error(e);
			if (errType) {
				res.send(e);
			} else {
				return res.serverError();
			}
		}
	},
	"defPref": function (req, res) {
		res.json({
			defaultdjNotif,
			defaultmailConfig,
			defaultmsgConfig,
			defaultdj,
			defunit,
		});
	},
};
