const moment = require("moment-timezone");
const md5 = require("md5");
const globals = require('../utils/global');
moment.tz.add(
	"Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6"
);
moment.tz.setDefault("Asia/Kolkata");
module.exports = {
	/**
	 * @param req.body.siteId
	 * @param req.body.app
	 * Used to get token for third party apps, will use an api key to get the
	 * token, currently token is valid for 10 minutes only. In future there will
	 * be a generic module for it.
	 * reportingmodulesjpl
	 */
	"grantToken": (req, res) => {
		const { siteId, app, key } = req.allParams();
		if (!siteId || !app || !key)
			return res.badRequest({ "err": "Invalid Parameters" });
		//TODO : Make a generic structure based on API keys
		if (key !== "612fc5c443dbf6db91c7baf2106e85da")
			return res.badRequest({
				"err": "Invalid API Key"
			});
		const token = jwtService.issue({
			"_site": siteId,
			"id": app,
			"name": app,
			"allow": [""]
		}, 600000);
		return res.ok({ token });
	},
	"hello": (req, res) => {
		return res.ok({ "msg": "hey" });
	},
	"verifyToken": (req, res) => {
		return res.ok({ "verified": true });
	},
	"forgotPasswordVerify": (req, res) => {
		let body = req.body;
		let pass = body.password;
		sails.log(body);
		jwtService.verify(body.token, function (err, data) {
			if (err) {
				return res.status(404).send({
					"message": "UnAuthenticated"
				});
			}
			let decoded = jwtService.decode(body.token);
			sails.log(decoded);
			Users.find({
				"userId": decoded.id
			})
				.then(users => {
					let user = users[0];
					// sails.log(user, 'hehehehehehh2e');
					// user.password = pass;
					return Users.update(
						{
							"userId": user.userId,
							"userOrganization": user.userOrganization
						},
						{
							"password": pass
						}
					);
				})
				.then(user => {
					// user.token = body.token;
					// sails.log(user, 'hehehehehehhe');

					res.send({ "user": user[0], "token": body.token });
				});
		});
	},
	"forgotPassword": (req, res) => {
		let userId = req.body.userId;
		sails.log("userId", userId);
		if (!userId) {
			return res.badRequest({ "err": "Invaid Params" });
		}
		Users.findOne({
			userId
		}).then(user => {
			if (user) {
				sails.log("user", user);
				let secret = md5(user.password + user.userId);
				let token = jwtService.issue(
					{
						"id": user.userId,
						"secret": secret,
						"subject": "reset-password"
					},
					1800
				);
				sails.log("token", token);
				sails.log("mail sent successfully");

				return sendmail.sendTemplateMail({
					"subject": "Restore Password",
					"token": token,
					"from": "\"SmartJoules\" <<EMAIL>>",
					"to": user.email,
					"template": "forgot-password"
				});

			} else {
				return Promise.reject();
			}
		})
			.then(emailRes => {
				res.send({ "status": "Mail Sent" });
			})
			.catch(er => {
				sails.log.error(er, "1");
				res.badRequest({ "err": "Email or UserId Incorrect!" });
			});
	},

	"registerSocket": async (req, res) => {
		if (!req.isSocket) {
			return res.badRequest();
		}
		let clientId = req._userMeta._h_;
		let siteId = req._userMeta._site;
		let socketId = req.socket.id;
		let user = req._userMeta.id;
		let role = req._userMeta._role;
		// sails.log(req._userMeta._h_, socketId);
		if (!clientId || !socketId) return res.badRequest();
		try {
			let cacheRes = await cacheService.sadd(clientId, socketId);
			await cacheService.setKey("u_" + clientId, user);
			let expiry = await cacheService.expire(clientId, 86400); //86400 is seconds for 24 hr
			await cacheService.expire("u_" + clientId, 86400); //86400 is seconds for 24 hr
			eventService.joinJouleTrackList(socketId, siteId, role);
			return res.ok({
				"added": cacheRes,
				"expiry": expiry
			});
		} catch (e) {
			sails.log.error(e);

			return res.serverError(e);
		} finally {
      globals.requestLogger(req, res);
    }
		//
	},
	"logout": async (req, res) => {
		let { secret, siteId } = req.allParams();
		if (!secret || !siteId) return res.badRequest();
		let clientId = siteId + "_" + secret;
		try {
			await cacheService.del(clientId);
			await cacheService.del("u_" + clientId);
		} catch (e) {
			sails.log.error(e);
		}
		return res.ok();
	},
	"issueToken": async (req, res) => {
		let { secret, siteId, prevSiteId } = req.allParams();
		if (!secret || !siteId) return res.badRequest();
		let clientId = prevSiteId ? prevSiteId : siteId;
		clientId += "_" + secret;
		try {
			let userId = await cacheService.getKey("u_" + clientId);
			if (!userId) {
				return res.json(401, {
					"err": "Please Login Again. User not logged In"
				});
			}

			let user = await Users.findOne({ userId });
			let userPref = await UserSiteMap.findOne({ userId, siteId });
			if (!userPref) {
				return res.json(401, {
					"err": "User Not Authenticated For This Site. Contact Admin."
				});
			}

			if (prevSiteId) {
				clientId = siteId + "_" + secret;
			}
			let unitPref;
			let { unitPreference, role } = userPref;
			unitPref = helper.toJson(unitPreference);
			let tokenObj = {
				"id": user.userId,
				"_role": role,
				"_site": siteId,
				"name": user.name,
				"_h_": clientId
			};
			if (unitPref) tokenObj.unitPref = unitPref;
			else {
				tokenObj.unitPref = {
					"temperature": "degC",
					"pressure": "kPa",
					"delTemperature": "delC",
					"length": "m"
				};
			}

			let consumptionUnit = await dailyConsumptionService.getUserConsumptionPreference(tokenObj.unitPref, siteId);
			tokenObj.unitPref["cons"] = consumptionUnit;

			let token = jwtService.issue(tokenObj);
			return res.ok({ token });
		} catch (e) {
			sails.log.error(e);
			return res.serverError();
		}
	},
	/**
	 * Default route to redirect to website.
	 */
	"homepage": function (req, res) {
		//Redirects the user to smartjoules login page
		res.redirect("http://www.smartjoules.co.in");
	},
	/**
	 * Invalid requests handler.
	 */
	"invalid": function (req, res) {
		res.json(404, { "err": "Page not found" });
	},
	//curl localhost:1337/v1/getToken -X POST -d "{/"siteId/":/"kims/",/"mac/":/"abcd/"}"
	"getToken": async (req, res) => {
		let siteId = req.param("siteId");
		let mac = req.param("mac");
		if (!siteId || !mac) return res.badRequest(); //return status code 400
		let macArr;
		try {
			macArr = await DyanmoKeyStore.findOne({ "key": `${siteId}_mac` });
		} catch (e) {
			sails.log.error(e);
		}
		if (!macArr)
			return res.badRequest({ "err": "Host devices not recognized" });
		let isMacPresent = macArr.list.filter(e => e == mac);
		if (isMacPresent.length == 0) {
			return res.badRequest({ "err": "Host devices not recognized" });
			// res.status = 401;
			// return res.send("")
		}
		let token = jwtService.issue({ "siteId": siteId, "mac": mac });
		try {
			await DyanmoKeyStore.create({
				"key": `${siteId}_token`,
				"value": token
			});
		} catch (e) {
			sails.log.error(e);
			return res.serverError();
		}
		return res.ok({ "token": token });
	},
	//new routes
	"login": async (req, res) => {
		let { userId, password } = req.allParams();
		let hash = req.headers.authorization;
		if (!userId || !password || !hash) return res.badRequest();
		let user = await Users.findOne({ "userId": userId });
		if (!user) return res.json(401, { "err": "User not registered" });
		let isNew = password == "smartjoules123";
		try {
			let valid = await Users.comparePassword(password, user);
			let defaultSite = user.defaultSite;
			let siteId = defaultSite;
			let userPref = await UserSiteMap.findOne({ userId, siteId });
			if (!valid)
				return res.json(401, { "err": "Invalid UserId/Password" });
			if (!userPref) {
				return res.json(401, {
					"err": "User Not Authenticated For This Site. Contact Admin."
				});
			}
			let RoleInSite;
			let unitPref;
			try {
				let { unitPreference, role } = userPref;
				RoleInSite = role;
				unitPref = helper.toJson(unitPreference);
			} catch (err) {
				sails.log.error("User preference not set");
			}

			let tokenObj = {
				"id": user.userId,
				"_role": RoleInSite,
				"name": user.name,
				"_site": defaultSite,
				"_h_": defaultSite + "_" + hash
			};
			if (unitPref) tokenObj.unitPref = unitPref;
			else
				tokenObj.unitPref = {
					"temperature": "degC",
					"delTemperature": "delC",
					"pressure": "kPa",
					"length": "m"
				};
			if (isNew) tokenObj.firsTime = true;
			let consumptionUnit = await dailyConsumptionService.getUserConsumptionPreference(tokenObj.unitPref, siteId);
			tokenObj.unitPref["cons"] = consumptionUnit;

			let Ntoken = jwtService.issue(tokenObj);
			let resObj = {
				"token": Ntoken
			};
			//req._userMeta.
			res.json(resObj);
		} catch (e) {
			sails.log.error(e);
			return res.serverError();
		}
	},

	"socketBroadcast": async (req, res) => {
		let { siteId, room, topic, data, secret } = req.allParams();
		if(!siteId || !room || !topic || !data || !secret) res.badRequest({ "err": "Invalid Parameters" });
		if(secret !== "e9caf762-31d0-4643-b8ce-24902c3a7781") res.badRequest({ "err": "Invalid secret" });

		try {
			let response = await eventService.notifyJouleTrack(siteId, room, topic, data);
			if(response.err) throw response;
			res.ok(response);
		} catch (e) {
			sails.log.error(e);
			res.serverError();
		}
	}
};
