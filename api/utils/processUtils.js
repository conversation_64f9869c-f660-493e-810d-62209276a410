// exports functionnames and utils are in the funtion
/**
 * @namespace processUtils
 */
module.exports = {
	"create": {
		/**
		 * @function checkInput
		 * @memberof processUtils
		 * @param {object} body
		 * @param {string} body.siteId required
		 * @param {string} body.type required can contain plant,component,custom
		 * @param {string} body.name required
		 * @param {string} body.regionId required for type component
		 * @param {string} body.controllerId required for type component
		 * @param {boolean} body.isDisplayed required for type component
		 * @param {string} body.deviceType required for type component
		 * @param {string} body.driverType required for type component
		 * @description validates the input provided to the API for process API
		 */
		"checkInput": body => {
			let result = {
				"status": false,
			};
			const validTypes = ["plant", "component", "custom"];
			let {
				siteId,
				type,
				name,
				regionId,
				controllerId,
				isDisplayed,
				deviceType,
				driverType
			} = body;
			if (!siteId || !type || !name) {
				result.message = "Incomplete parameters";
				return result;
			}
			if (!validTypes.includes(type)) {
				result.message = `Invalid process type ${type}`;
				return result;
			}
			if (
				type === "component" &&
				(!regionId || !controllerId || !isDisplayed || !deviceType || !driverType)
			) {
				result.message = "Insufficient process parameters for adding a component";
				return result;
			}
			result.status = true;
			return result;
		},
		/**
		 * @function getProcessObj
		 * @param {object} body
		 * @param {string} body.siteId required
		 * @param {string} body.type required can contain plant,component,custom
		 * @param {string} body.name required
		 * @param {string} body.parentProcess If passed, it also saves current processId in parent processes' "childProcesses" key.
		 * Following are required for type component
		 * @param {string} body.regionId required for type component
		 * @param {string} body.controllerId required for type component
		 * @param {Boolean} body.isDisplayed Being hardcoded in front end for now.
		 * @param {string} body.deviceType required for type component
		 * @param {string} body.driverType required for type component
		 * @description creates process Object that can be saved to db. based on condition it will generate the object.
		 * */
		"getProcessObj": body => {
			const {
				siteId,
				type,
				name,
				regionId,
				controllerId,
				isDisplayed,
				deviceType,
				driverType,
				parentProcess
			} = body;
			let procObj = { siteId, type, name, parentProcess };
			if (type === "component") {
				procObj = { ...procObj, regionId, controllerId, isDisplayed, deviceType, driverType };
			}
			return procObj;
		},
	},
	"findAll": {
		/**
		 * @description returns an object to query processes which can directly
		 * be consumed by processServices.find
		 */
		"prepareFindQuery": (siteId, filters) => {
			let query = { "where": { siteId } };
			const { select } = filters;
			const allowedFilters = ["type", "componentType", "isDisplayed"];
			if (select) query.select = select.split(",");
			delete filters.select;
			Object.keys(filters).forEach(key => {
				if (allowedFilters.indexOf[key] !== -1) {
					query.where[key] = filters[key];
				}
			});
			return query;
		},
	},
	"updateRawParams": {
		/**
		 * @param {Array.<{abbr:string,
		 * expression:string,
		 * displayName:string,
		 * dau:string,
		 * paramGroup:string,
		 * min:number,
		 * max:number,
		 * rogueMin:number,
		 * rogueMax:number,
		 * valueSet:Object<{
		 * 	key:string
		 * 	}>
		 * }
		 * >} body
		 * @description validates if input is valid
		 * @returns {Object.<status:boolean,message:string>} message is only sent if status is false
		 *
		 */
		"isInputValid": body => {
			const result = {
				"status": false,
			};
			if (!Array.isArray(body)) {
				result.message = "Invalid request structure!";
				return result;
			}
			let invalidReq = false;
			body.map(param => {
				const reqParams = [
					"abbr",
					"expression",
					"displayName",
					"dau",
					"paramGroup",
				];
				reqParams.map(key => {
					if (typeof param[key] === "undefined" || param[key] === "")
						invalidReq = true;
				});
			});
			if (invalidReq) {
				result.message = "Invalid 'param' parameters.";
				return result;
			}
			result.status = true;
			return result;
		},
		/**
		 * @param {Array} oldArr
		 * @param {Array} newArr
		 * @description takes two arrays as inputs first is the one present in database and second is the one provided by user and returns diff in array of arrays where interanl array will have <paramabbr>, <paramKey>, <value>.
		 * If the parameter is new and not present in oldArr it returns array as
		 * <paramAbbr>, "all", <object>
		 * @returns {Array.<Array>}
		 */
		"getDiff": (oldArr, newArr) => {
			const diff = [];
			if (!oldArr) oldArr = [];
			const oldParamObj = helper.createMap(oldArr, "abbr");
			newArr.map(param => {
				const { abbr } = param;
				if (!oldParamObj[abbr]) {
					diff.push([abbr, "allKeys", param]);
					return;
				}
				Object.keys(param).forEach(key => {
					const oldParam = oldParamObj[abbr];
					if (param[key] !== oldParam[key]) {
						diff.push([abbr, key, param[key]]);
					}
				});
			});
			return diff;
		},
		/**
		 * @param {Array} diffArr output of @see getDiff
		 * @description look for expression change and return the deviceIds which are in new expression
		 * @returns {Array} deviceIds
		 */
		"getDeviceIds": diffArr => {
			const [, param, value] = diffArr;
			let expression = "";
			if (param === "expression") {
				expression = value;
			}
			if (param === "allKeys") {
				({ expression } = value);
			}
			let deviceIds = helper.parseFormulaToIds(expression);
			return deviceIds;
		},
		/**
		 * @param {array} rawParams
		 * @description ensures that params have only valid keys and are of not nullish
		 */
		"sanitizeParams": paramArray => {
			if (paramArray === undefined) return [];
			return paramArray.map(param => {
				const knownParamKeys = [
					"abbr",
					"expression",
					"displayName",
					"dau",
					"paramGroup",
					"min",
					"max",
					"rogueMin",
					"rogueMax",
					"valueSet",
					"deviceId"
				];
				const validParams = {};
				knownParamKeys.forEach(val => {
					if (
						typeof param[val] !== "undefined" &&
						param[val] !== "" &&
						param[val] !== null
					) {
						validParams[val] = param[val];
					}
				});
				if (validParams["abbr"]) validParams["key"] = validParams.abbr; // Maintaing backward compatibility with components schema
				return validParams;
			});
		},
	},
	"getProcessConfig": {
		/**
		 * @param {object} body.processId ProcessId configured.
		 * @param {object} body.processId Site on which the process is configured.
		 */
		"checkInput": body => {
			let result = {
				"status": false,
			};
			let { processId, siteId } = body;
			if (processId === undefined || processId === "") {
				result.message = "Invalid processId";
				return result;
			}
			if (siteId === undefined || siteId === "") {
				result.message = "Invalid siteId";
				return result;
			}
			result.status = true;
			return result;
		},
		/**
		 * @function addDefaultParametersToProcess
		 * @description If there are any parameters present in the driver details that have not been previously
		 * configured in the process details, defaults are added based on the driver.
		 * @param {object} driverInfo Driver details fetched based on deviceType and driverType of the Process
		 * @param {object} processConfig Process object fetched from process table based on processId
		 */
		"addDefaultParametersToProcess": function (driverInfo, processConfig) {
			// Initialising
			let paramList = driverInfo.parameters.map(helper.toJson);
			let dataKeys = {}, controlKeys = {}, queriedParamKeys = {};
			if (!processConfig.rawParams)
				processConfig.rawParams = [];
			if (!processConfig.queriedParams)
				processConfig.queriedParams = [];
			if (!processConfig.controls)
				processConfig.controls = [];
			helper.convertStringArrayToJSONArray(processConfig.rawParams);
			helper.convertStringArrayToJSONArray(processConfig.controls);

			// Collecting all data and control parameters.
			processConfig.rawParams.forEach(parameter => {
				dataKeys[parameter.abbr] = parameter;
			});
			processConfig.controls.forEach(parameter => {
				controlKeys[parameter.abbr] = parameter;
			});
			processConfig.queriedParams.forEach(parameter => {
				queriedParamKeys[parameter.abbr] = parameter;
			});

			// If a parameter is present in the driver parameter list and not present
			// in the component definition, initialising it's values.
			paramList.forEach(defaultParameter => {
				if (defaultParameter.type == "data") {
					if (!dataKeys[defaultParameter.abbr]) {
						processConfig.rawParams.push({
							"abbr": defaultParameter.abbr,
							"key": defaultParameter.abbr, // Doing this to maintain compatibility with legacy code.
							"expression": "",
							"deviceId": "",
							"displayName": defaultParameter.displayName,
							"dau": defaultParameter.dau,
							"paramGroup": defaultParameter.paramGroup,
							"min": "",
							"max": "",
						});
					}
				} else if (defaultParameter.type == "command") {
					if (!controlKeys[defaultParameter.abbr]) {
						processConfig.controls.push({
							"abbr": defaultParameter.abbr,
							"key": defaultParameter.abbr, // Doing this to maintain compatibility with legacy code.
							"expression": "",
							"deviceId": "",
							"displayName": defaultParameter.displayName,
							"dau": defaultParameter.dau,
							"paramGroup": defaultParameter.paramGroup,
							"min": "",
							"max": "",
							"timeout": "",
						});
					}
				} else if (defaultParameter.type == "queriedParam") {
					if (!queriedParamKeys[defaultParameter.abbr]) {
						processConfig.queriedParams.push({
							"abbr": defaultParameter.abbr,
							"key": defaultParameter.abbr, // Doing this to maintain compatibility with legacy code.
							"expression": "",
							"deviceId": "",
							"displayName": defaultParameter.displayName,
							"dau": defaultParameter.dau,
							"paramGroup": defaultParameter.paramGroup,
							"min": "",
							"max": "",
						});
					}
				}
			});
		}
	},
};
