const moment = require('moment-timezone');
const siteService = require('../services/siteService');
const cacheService = require('../services/cacheService');

const EXPIRY = 15 * 24 * 60 * 60; // 15 days expiry time for timezone cache

module.exports = {
	friendlyName: 'Get site timezone',
	description: 'Fetches and validates the timezone for a site using siteId, utilizing cache.',

	inputs: {
		siteId: {
			type: 'string',
			required: true,
			example: 'sjo-del',
			description: 'The unique identifier for the site.',
		}
	},

	exits: {
		success: {
			description: 'Timezone found and validated successfully.'
		},
		notFound: {
			description: 'No site found with the provided siteId.',
			responseType: 'notFound'
		},
		invalidTimezone: {
			description: 'The provided timezone is invalid.',
			responseType: 'badRequest'
		}
	},

	/**
	 * @function getSiteTimezone
	 * @description Fetches and validates the timezone for a given site based on the siteId, utilizing cache.
	 * @param {Object} inputs - Contains siteId to fetch the site details.
	 * @param {Object} exits - Contains methods to handle success and error responses.
	 * @returns {string} - The validated timezone string.
	 */
	fn: async function ({ siteId }, exits) {
		try {

			const cachedKey = `site:${siteId}:timezone`;
			// Get timezone from the cache
			let timezone = await cacheService.getKey(cachedKey);

			// Validate timezone if fetched from cache
			if (timezone && !moment.tz.zone(timezone)) {
				// Invalidate cache if the timezone is not valid.
				await cacheService.del(cachedKey);
				timezone = null;
			}

			// If timezone is not in cache or invalid, fetch from Sites table.
			if (!timezone) {
				const site = await siteService.getSiteInfo({ siteId });

				// If no site found, return notFound exit status.
				if (!site) {
					return exits.notFound(`No site found with the siteId: ${siteId}`);
				}

				// Get the timezone from site information
				timezone = site.timezone;

				// Validate timezone fetched from DB
				if (!moment.tz.zone(timezone)) {
					return exits.invalidTimezone(`The timezone ${timezone} is invalid.`);
				}

				// Store the valid timezone in cache
				await cacheService.setKeyWithTTL(cachedKey, timezone, EXPIRY);
			}

			return exits.success(timezone);
		} catch (error) {
			sails.log.error(`[get-site-timezone] Error fetching timezone for siteId: ${siteId}`, error);
			return exits.error(error);
		}
	}
};