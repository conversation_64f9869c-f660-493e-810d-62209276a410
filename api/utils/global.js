const moment = require('moment-timezone');
const flaverr = require('flaverr');
const uuid = require('uuid');
const { performance } = require('perf_hooks');

const ALLOWED_FORMATS = ['Z', 'ZZ', 'z', 'utcOffsetInMinute', 'utcOffsetInNumber'];
module.exports = {
  getAccessTokenFromReq(req) {
    return req.headers.authorization.split(' ')[1];
  },
  getSiteDeviceComponentCategoryCacheKey: (siteId) => `siteId:${siteId}:available_device_component_categories`,
  convertTimezone(timezone, format) {
    if (!moment.tz.zone(timezone)) {
      throw flaverr('E_INVALID_TIMEZONE', new Error(`Invalid timezone: ${timezone}`));
    }
    if (!ALLOWED_FORMATS.includes(format)) {
      throw flaverr('E_INVALID_FORMAT', new Error(`Invalid format: ${format}`));
    }
    const momentObj = moment().tz(timezone);
    const offsetMinutes = momentObj.utcOffset(); // offset in mins
    switch (format) {
      case 'utcOffsetInMinute':
        return `${offsetMinutes}m`;
      case 'utcOffsetInNumber':
        return `${offsetMinutes}`;
      default:
        return momentObj.format(format);
    }
  },
  requestLogger(req, res) {
    req.headers['x-transaction-id'] = req.headers['x-transaction-id'] || uuid.v4();
    const start = performance.now();
    const durationInMicroseconds = performance.now() - start;
    const requestSize = parseInt(req.headers['content-length'] || '0', 10);
    const responseSize = parseInt(res.getHeader('Content-Length') || '0', 10);
    const reqLoggingObject = {
      method: req.method,
      body: req.body || {},
      queryParams: req.query || {},
      params: req.params || {},
      transaction_id: req.headers['x-transaction-id'],
      statusCode: res?.statusCode,
      statusMessage: res?.statusMessage,
      fromIP: req.headers['x-forwarded-for'] || req?.connection?.remoteAddress || req?.socket?.id,
      originalUrl: req.originalUrl,
      referer: req.headers.referer || '',
      ua: req.headers['user-agent'],
      responseTime: durationInMicroseconds,
      requestSize,
      responseSize,
    };
    if (req.headers['x-amzn-trace-id']) {
      reqLoggingObject.x_amzn_trace_id = req.headers['x-amzn-trace-id'];
    }
    if (req.headers['x-amzn-request-id']) {
      reqLoggingObject.x_amzn_request_id = req.headers['x-amzn-request-id'];
    }
    // eslint-disable-next-line no-underscore-dangle,no-prototype-builtins
    const userDetail = req.hasOwnProperty('_userMeta') ? JSON.stringify(req._userMeta) : '';
    delete reqLoggingObject.body.password;
    delete reqLoggingObject.params.password;
    delete reqLoggingObject.queryParams.password;
    const requestLogStr = `ts=${new Date().toISOString()} requestId=${reqLoggingObject.transaction_id} x_amzn_trace_id=${reqLoggingObject?.x_amzn_trace_id} x_amzn_request_id=${reqLoggingObject?.x_amzn_request_id} method=${reqLoggingObject.method} route=${reqLoggingObject.originalUrl} statusCode=${reqLoggingObject.statusCode} responseTime=${reqLoggingObject.responseTime} requestSize=${reqLoggingObject.requestSize} responseSize=${reqLoggingObject.responseSize} userDetail=${userDetail}body=${JSON.stringify(reqLoggingObject.body)} queryParams=${JSON.stringify(reqLoggingObject.queryParams)} params=${JSON.stringify(reqLoggingObject.params)} fromIP=${reqLoggingObject.fromIP} referer="${reqLoggingObject.referer}" ua="${reqLoggingObject.ua}"`;
    const successCode = [200, 201, 204];
    if (successCode.includes(parseInt(reqLoggingObject.statusCode, 10))) {
      sails.log.info(requestLogStr);
    } else {
      sails.log.error(requestLogStr);
    }
  },
};
