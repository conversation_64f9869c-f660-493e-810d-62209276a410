const moment = require("moment-timezone");
moment.defaultFormat = "YYYY-MM-DD HH:mm:00";

exports = {

	"isValidTimeSeriesValue": function(value) {
		// check if value is object/array or NaN or things like these. we dont need it
		// currently anything thats not a number for now is rejected.
		if (value === undefined || value === null) {
			return false; 
		}
		if (isNaN(Number(value))) {
			return false;
		} else {
			return true;
		}
	},

	"isValidTimestamp": function(dateTime) {
		if (!dateTime) {
			return false;
		}
		if (typeof dateTime === "string" && dateTime.match(/^\d+$/)) {
			dateTime = parseInt(dateTime);
		}

		let momentTs = moment(dateTime);

		if (momentTs) {
			if (momentTs.isValid()) {
				return true;
			} else {
				return false;
			}
		} else {
			return false;
		}
	}


};


module.exports = exports;
