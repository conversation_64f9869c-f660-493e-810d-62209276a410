/**
 * @module dashboardUtils
 * Contains util functions for each controller function
 */
const moment = require("moment-timezone");
const dataDeviceService = require("../services/dataDeviceService");
const helper = require("../services/helper");
const Excel = require("exceljs");
moment.tz.setDefault("Asia/Kolkata");
moment.defaultFormat = "YYYY-MM-DD HH:mm:ss";
const UNIT_MAPPER = {
	"kvah": "kVAh",
	"kwh": "kWh",
	"conselec": "kWh",
	"conssteam": "kg"
};
/**
 *
 * @param {string} duration [day|week|month]
 * @description returns all timestamps that will be used to query database
 */
const getTimeObj = function (duration) {
	let minutesAddToStartOfDayRAMA = 480; // 8 hours.. only for rama
	const endTS = moment().format();
	const hour = moment()
		.startOf("hour")
		.subtract(1, "h");
	const dayStart = helper
		.actualStartOf(minutesAddToStartOfDayRAMA, "day");
	const yesterdayStart = helper
		.actualStartOf(minutesAddToStartOfDayRAMA, "day")
		.subtract(1, "day");
	const monthStart = helper
		.actualStartOf(minutesAddToStartOfDayRAMA, "month")
		.format();
	const lastWeekStart = helper
		.actualStartOf(minutesAddToStartOfDayRAMA, "isoweek")
		.subtract(1, "week");

	const beginning = moment("2017-01-01").format();
	let defaultStart, defaultEnd;
	defaultEnd = endTS;
	if (duration === "day") {
		defaultStart = dayStart;
	}
	if (duration === "week") {
		defaultStart = lastWeekStart;
	}
	if (duration === "month") {
		defaultStart = monthStart;
	} else {
		defaultStart = hour;
	}

	let timeObj = {
		defaultStart,
		defaultEnd,
		"hour": [
			hour.format(), hour.clone().add(1, "hour").subtract(1, "minutes").format()
		],
		"day": [
			dayStart.format(), moment().format()
		],
		"yesterday": [
			yesterdayStart.format(), yesterdayStart.clone().add(1440 - 1, "minutes").format()
		],
		"lastWeek": [
			lastWeekStart.format(), lastWeekStart.clone().add(1, "week").subtract(1, "minutes").format()
		],
		"tillNow": [beginning, endTS],
	};
	return timeObj;
};

const excelHelpers = {
	"addDefaultColumns": function (sheet) {
		sheet.columns = [
			{ "header": "Name", "key": "name", "width": 50 },
			{ "header": "ID", "key": "deviceId" },
			{ "header": "Selected Time Period", "key": "selectedTime", "width": 12 },
			{ "header": "Yesterday", "key": "yesterday", "width": 12 },
			{ "header": "Last 7 Days", "key": "lastWeek", "width": 12 },
			{ "header": "Last 30 Days", "key": "lastMonth", "width": 12 },
			{ "header": "Selected Time Period Percentage", "key": "selectedTimePercentage", "width": 20 },
			{ "header": "Yesterday Percentage", "key": "yesterdayPercentage", "width": 15 },
			{ "header": "Last 7 Days Percentage", "key": "lastWeekPercentage", "width": 15 },
			{ "header": "Last 30 Days Percentage", "key": "lastMonthPercentage", "width": 15 },
		];
	},
	"addData": function (sheet, deviceTree, consumptionData, level = 0) {
		// Setting level specific properties:
		let color = "";
		switch (level) {
			case 0:
				color = "ff8000";
				break;
			case 1:
				color = "ffa74e";
				break;
			case 2:
				color = "ffc489";
		}
		deviceTree.forEach(device => {
			let { deviceId, name } = device;
			let rowObj = {
				deviceId,
				name,
				...consumptionData[deviceId],
				level
			};
			let row = sheet.addRow(rowObj);
			// Adding level specific styling
			row.eachCell({ "includeEmpty": true }, (cell) => {
				cell.fill = {
					"type": "pattern",
					"pattern": "solid",
					"fgColor": { "argb": color }
				};
			});

			if (device.childNodes.length === 0) return;
			this.addData(sheet, device.childNodes, consumptionData, level + 1);
		});
	},
	"styleRows": function (sheet) {
		sheet.eachRow({ "includeEmpty": true }, row => {
			row.eachCell({ "includeEmpty": true }, cell => {
				cell.alignment = {
					"horizontal": "center",
					"vertical": "middle",
					"wrapText": true
				};
				cell.border = {
					"top": { "style": "thin" },
					"bottom": { "style": "thin" },
					"left": { "style": "thin" },
					"right": { "style": "thin" },
				};
			});
		});
		let firstRow = sheet.getRow(1);
		firstRow.height = 28;
	}
};

module.exports = {
	"consumption": {
		/**
		 * @module dashboardUtils.fillQueryElements
		 * @param {object} query
		 * @param {string} query.start
		 * @param {string} query.end
		 * @param {string} query.duration
		 * @param {string} query.limit
		 * @param {string} query.deviceTypes
		 * @param {string} query.category
		 * @param {string} query.display
		 * @description filters query parameters and fills the missing values with default values.
		 */
		"fillQueryElements": function (query) {
			let {
				start,
				end,
				duration,
				limit,
				deviceTypes,
				category,
				display,
			} = query;
			let devTypes, timeObj;
			if (!deviceTypes) {
				devTypes = ["em"];
				category = "electricity";
			} else {
				devTypes = deviceTypes.split(",");
			}
			if (!limit) limit = -1;
			if (!display) display = "chart";
			if (duration) {
				({ start, end } = getTimeObj(display, duration));
			}
			timeObj = getTimeObj();
			if (start) timeObj.defaultStart = start;
			if (end) timeObj.defaultEnd = end;
			return {
				timeObj,
				limit,
				category,
				display,
				"deviceTypes": devTypes,
			};
		},
		/**
		 * @function dashboardUtils.getAllDeviceIds
		 * @param {Array} plants An array consisting of configuration of plants
		 * @param {Array} deviceTypes An array containing device types names which should be queries from plants
		 * @description returns an array of unique deviceIds from plants configuration. It queries plantDevices property of plants
		 * @returns {Array}
		 */
		"getAllDeviceIds": function (plants, deviceTypes) {
			let deviceIds = new Set();
			plants.map(plant => {
				const { plantDevices } = plant;
				deviceTypes.map(dt => {
					const devices = plantDevices[dt];
					devices.map(dev => deviceIds.add(dev));
				});
			});
			return [...deviceIds];
		},
		/**
		 * @function dashboardUtils.normalize
		 * @param {object} element A record retured by datadeviceService.getConsuption
		 * @param {string} category [electricity|steam] parameters to get
		 * @description As the consumption objects can contain different parameters, like in case of electricity consumption kvah and kwh are used.
		 * So this function normalizes the data appends properties value and display which will have consuption in it.
		 * @returns {Object}
		 */
		"normalize": function (element, category, preferredUnit) {

			element.param = helper.toJson(element.param);
			let paramValues = element.param !== undefined ? element.param : {};
			let elementValueInUnit = "null";

			if (category === "electricity") {
				elementValueInUnit = helper.getConsumptionFromDeviceData(
					paramValues,
					preferredUnit
				);
				if (elementValueInUnit === "null" && paramValues["conselec"] !== undefined) {
					// means kvah and kwh both doesnt exist but conselec does
					elementValueInUnit = paramValues["conselec"]; // unit of this by defaults to kvah
				}
			} else if (category === "steam") {
				if (paramValues["conssteam"]) { // undefined, "" will be rejected here what we wanted so ok to use if(x)
					elementValueInUnit = paramValues["conssteam"];
				}
			}


			if (elementValueInUnit !== "null") {
				elementValueInUnit = helper.returnFilteredNumber(elementValueInUnit);
				element.value = elementValueInUnit;
				element.display = `${elementValueInUnit} ${UNIT_MAPPER[preferredUnit]}`;
				element.unit = UNIT_MAPPER[preferredUnit];
				delete element.param;
			}
			return element;
		},
		/**
		 * @function dashboardUtils.sortConsumption
		 * @param {Array} consumptionArr
		 * @param {number} limit
		 * @description sorts the consumption array in descending order of its consumption and if limit is provided it limits the number returns also.
		 */
		"sortConsumption": function (consumptionArr, limit) {
			if (limit == "-1") limit = consumptionArr.length;
			const sortedObj = consumptionArr
				.filter(ele => !helper.isNullish(ele.value))
				.sort((first, second) => {
					return first.value < second.value ? 1 : -1;
				})
				.filter((ele, index) => index < limit);
			return sortedObj;
		},
		/**
		 * @function dashboardUtils.prepareTable
		 * @param {Array} consumptionArr
		 * @description appends multiple consumption values of a device to single object. So that it can be displayed in tabular form.
		 * @returns {Array}
		 */
		"prepareTable": function (consumptionArr) {
			let consumptionTable = [];
			for (let index = 0; index < consumptionArr.length; index += 4) {
				const { deviceId } = consumptionArr[index];
				const hourConsumption = consumptionArr[index].display || "NA";
				const dayConsumption =
					consumptionArr[index + 1].display || "NA";
				const yesterdayConsumption =
					consumptionArr[index + 2].display || "NA";
				const weekConsumption = consumptionArr[index + 3].display || "NA";
				// const tillNowConsumption = consumptionArr[index + 4].display;
				consumptionTable.push({
					deviceId,
					hourConsumption,
					dayConsumption,
					yesterdayConsumption,
					weekConsumption,
					// tillNowConsumption,
				});
			}
			return consumptionTable;
		},
		/**
		 * 
		 * @param {Array} consumptionArr 
		 * @description adds all consumption values where parameter are present
		 */
		"sumConsumption": function (consumptionArr) {
			return consumptionArr.reduce((aggr, cons) => {
				if (cons.value) {
					return aggr + cons.value;
				}
				return aggr;
			}, 0);
		},
		/**
		 * Using the rawParams key in process object find the keys with abbr
		 * of conselec and get deviceIds in it
		 * @param {object} process Given process object from process table
		 */
		"getMainMeterFromProcess": function (process) {
			if (typeof process !== "object") {
				throw new Error("Expected type of process to be Object, dashboardutils.getmainmeterprocess");
			}

			let rawparams = process.rawParams || [];
			let mainMeters = [];

			for (let param of rawparams) {
				if (param && param.abbr === "conselec") {
					let expression = param.expression || "";
					let deviceIdsParams = expression.match(/(\d+)@\w/g) || [];
					let meters = deviceIdsParams.map(deviceParam =>
						deviceParam.split("@")[0]
					);
					mainMeters = mainMeters.concat(meters);
				}
			}

			return Array.from(new Set(mainMeters));
		}
	},
	"consumptionExcel": {
		/**
		 * @function generateTimeObj
		 * @description Generates time ranges to query data and calculate consumption between. Generates 4 ranges: Selected (based on start and end), Yesterday, last week and last month.
		 * @param {string} query.end Selected "end" time of the consumption request.
		 * @param {string} query.start Selected "start" time of the consumption request.
		 */
		"generateTimeObj": function (query) {
			const { start, end } = query;
			const startTS = moment(start).format();
			const endTS = moment(end).format();
			const yesterdayStart = moment().subtract(1, "d").startOf("day").format();
			const yesterdayEnd = moment().subtract(1, "d").endOf("day").format();
			const lastWeekStart = moment().subtract(8, "d").startOf("day").format();
			const lastMonthStart = moment().subtract(31, "d").startOf("day").format();
			let timeObj = {
				"selectedTime": [startTS, endTS],
				"yesterday": [yesterdayStart, yesterdayEnd],
				"lastWeek": [lastWeekStart, yesterdayEnd],
				"lastMonth": [lastMonthStart, yesterdayEnd]
			};
			return timeObj;
		},
		/**
		 * @function getConsumption
		 * @description	Fetches consumption for a list of deviceIds and different timeranges.
		 * @param {array} deviceList Array of deviceIds for which consumption needs to be calculated
		 * @param {object} timeObj Time Object being generated from the above function
		 */
		"getConsumption": async function (deviceList, timeObj) {
			const param = "kwh";
			let consumptionData = {};
			deviceList.forEach(deviceId => {
				consumptionData[deviceId] = {};
				Object.keys(timeObj).forEach(timeslot => {
					const timeRange = timeObj[timeslot];
					consumptionData[deviceId][timeslot] = dataDeviceService.estimatedElectricityConsumption(deviceId, param, ...timeRange);
				});
			});
			// Waiting for all queries
			for (let deviceId in consumptionData) {
				let deviceConsumption = consumptionData[deviceId];
				for (let timeslot in deviceConsumption) {
					deviceConsumption[timeslot] = await deviceConsumption[timeslot];
				}
			}
			return consumptionData;
		},
		/**
		 * @function simplify
		 * @description Fetches the deviceMap.deviceId.param.kwh parameter and saves it to deviceMap.deviceId.
		 * Also generates percentage consumption data for all child nodes .
		 * @param {Object} consumptionData Map of deviceIds to Timeslots with their respective consumpion data calculated.
		 * @param {object} deviceTree device Tree generated earlier above based on parent child configuration.
		 */
		"simplify": function (consumptionData, deviceTree) {
			let newConsumptionData = {};
			let timeslotKeys = ["yesterday", "selectedTime", "lastWeek", "lastMonth"];
			Object.keys(consumptionData).forEach(deviceId => {
				newConsumptionData[deviceId] = {};
				let deviceData = consumptionData[deviceId];
				Object.keys(deviceData).forEach(timeslot => {
					let timeslotData = deviceData[timeslot].param["kwh"];
					timeslotData = Math.round((Number(timeslotData) + Number.EPSILON) * 100) / 100; // Rounding off to 2 decimal places.
					timeslotData = isNaN(timeslotData) ? " " : timeslotData;
					newConsumptionData[deviceId][timeslot] = timeslotData;
				});
			});
			deviceTree.forEach(device => {
				let { deviceId } = device;
				let rootConsumption = newConsumptionData[deviceId];
				timeslotKeys.forEach(timeslot => {
					let newKey = `${timeslot}Percentage`;
					rootConsumption[newKey] = "100%";
				});
				percentageConsumption(device.childNodes, rootConsumption);
			});
			function percentageConsumption(childNodes, rootConsumption) {
				childNodes.forEach(device => {
					let consumptionPacket = newConsumptionData[device.deviceId];
					timeslotKeys.forEach(timeslot => {
						let newKey = `${timeslot}Percentage`;
						if (consumptionPacket[timeslot] != " " && rootConsumption[timeslot] != " ") {
							let percentageValue = (consumptionPacket[timeslot] / rootConsumption[timeslot]) * 100;
							percentageValue = Math.round((Number(percentageValue) + Number.EPSILON) * 100) / 100; // Rounding off to 2 decimal places.
							percentageValue = String(percentageValue) + "%";
							consumptionPacket[newKey] = percentageValue;
						} else {
							consumptionPacket[newKey] = " ";
						}
					});
					if (device.childNodes.length === 0) return;
					percentageConsumption(device.childNodes, rootConsumption);
				});
			}
			return newConsumptionData;
		},
		"generateDeviceTree": function (devices) {
			let deviceMap = {};
			devices.forEach(device => {
				let { deviceId, parentEM, name } = device;
				deviceMap[device.deviceId] = {
					name,
					deviceId,
					parentEM,
					"childNodes": []
				};
			});
			let deviceTree = [];
			devices.forEach(device => {
				let { deviceId, parentEM } = device;
				if (parentEM && deviceMap[parentEM]) {
					deviceMap[parentEM].childNodes.push(deviceMap[deviceId]);
				} else {
					deviceTree.push(deviceMap[deviceId]);
				}
			});
			return deviceTree;
		},
		"generateExcel": async function (deviceTree, consumptionData) {
			// const fileName = uuid();
			// const filePath = `./.temp/${fileName}.xlsx`;
			const workbook = new Excel.Workbook();
			const sheet = workbook.addWorksheet("Consumption Data");
			excelHelpers.addDefaultColumns(sheet);
			excelHelpers.addData(sheet, deviceTree, consumptionData);
			excelHelpers.styleRows(sheet);
			// await workbook.xlsx.writeFile(filePath);
			// const fileData = fs.readFileSync(filePath);
			// fs.unlinkSync(filePath);
			const fileData = await workbook.xlsx.writeBuffer();
			return fileData;
		},
	},
	UNIT_MAPPER
};
