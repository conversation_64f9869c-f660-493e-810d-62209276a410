/**
 * @module baselineUtils
 * Contains util functions for production in baseline Controller.
 */
const moment = require("moment-timezone");
moment.tz.setDefault("Asia/Kolkata");
moment.defaultFormat = "YYYY-MM-DD HH:mm:ss";
module.exports = {
	"production": {
		/**
		 * formatting data in a better structure.
		 * @param {Object} dateShift  data object corrosponding to a date
		 * (contains production data for each shift).
		 * @param {string} siteId current site id.
		 * @returns {Object} well formated data.
		 */
		formatData(dateShift, siteId) {
			const { date } = dateShift;
			dateShift.timestamp = moment(date, "DD/MMM/YYYY").format("YYYY-MM-DD");
			dateShift.siteId = siteId;
			delete dateShift.date;
			return dateShift;
		},
		/**
		 * filters data if parameters in request are not compatible.
		 * @param {Object} dateShift  data object corrosponding to a date
		 * (contains production data for each shift).
		 * @returns {Boolean} tru for bad data.
		 */
		filterBadData(dateShift) {
			let {
				date,
				production_shiftA,
				production_shiftB,
				production_shiftC,
			} = dateShift;
			if (!date || !production_shiftA || !production_shiftB || !production_shiftC)
				return true;
		},
		/**
		 * find if entry exists if so.
		 * @returns true to update data.
		 * @param {Object} dateShift req formatted object.
		 * @param {Object} dataFetched from dynamo to compare if entry exists.
		 */
		filterUpdateEntries(dateShift, dataFetched) {
			const findEntry = dataFetched.find(
				data => data.timestamp === dateShift.timestamp
			);
			return Boolean(findEntry);
		},
		/**
		 * find if entry exists if so.
		 * @returns true to create data entry.
		 * @param {Object} dateShift req formatted object.
		 * @param {Object} dataFetched from dynamo to compare if entry exists.
		 */
		filterCreateEntries(dateShift, dataFetched) {
			const timestamp = moment(dateShift.date, "DD/MMM/YYYY").format(
				"YYYY-MM-DD"
			);
			const findEntry = dataFetched.find(data => data.timestamp === timestamp);
			const val = !findEntry;
			return val;
		}
	},
};

