const pg = require('pg')
const { Pool } = pg

class PostgresConnection {
  constructor () {
    if (PostgresConnection.instance) {
      return PostgresConnection.instance
    }
    this.pool = new Pool({
      user: process.env.DATABASE_USER,
      host: process.env.DATABASE_HOST,
      database: process.env.DATABASE_NAME,
      password: process.env.DATABASE_PASSWORD,
      port: process.env.DATABASE_PORT,
    })

    PostgresConnection.instance = this
  }

  async query (sql, params) {
    return await this.pool.query(sql, params)
  }

  async close () {
    await this.pool.end()
  }

  async isAuthenticated () {
    let client
    try {
      client = await this.pool.connect()
      const result = await client.query('SELECT 1')
      return result.rows.length === 1
    } catch (error) {
      sails.error.log(`Error while checking connectivity with postgres database`)
      sails.error.log(error)
      return false
    } finally {
      if (client) {
        sails.log(`Postgres is successfully connected`)
        client.release()
      } else {
        // process.exit(13)
      }
    }
  }
}

module.exports=PostgresConnection
