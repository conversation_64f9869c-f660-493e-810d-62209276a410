const moment = require("moment-timezone");
const axios = require("axios");

moment.tz.add(
	"Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6"
);
moment.tz.setDefault("Asia/Kolkata");

// [TODO] make it public

const CALCULATEDPARAMREGEX = /(valueAt|isNan)/; // recipe following the pattern runs on server

module.exports = {
	"absCron": function(c) {
		let stub = [];
		if (!c) return null;
		for (let i = 0; i < c.length; i++) {
			let temp = c[i].split(" ");
			temp[0] = temp[1] = "*";
			stub.push(temp.join(" "));
		}
		return stub;
	},
	"getFormatDate": function(d) {
		return d.format("YYYY-MM-DD HH:mm");
	},
	"v1Parse": async function(formula, observableParams, operatorsDict, size) {
		// formula = ||#1||$1||0 ,
		// observableParams = {\"#1\":\"mgch_12.status\"}
		// operatorsDict = {\"$1\":\"==\",\"$2\":\"(\",\"$3\":\")\"}
		// sz = 10
		try {
			let topicArray = [];
			let didArray = [];
			size = String(size);
			let did, param;
			let runOnServer = false;
			formula = formula.replace(/\|\|/gi, " "); // replacing || with spaces

			for (let i in observableParams) {
				let temp = observableParams[i];
				try {
					let willRunOnServer;
					[did, param, willRunOnServer] = await this.getDidParam(
						...temp.split(".")
					);
					runOnServer = runOnServer || willRunOnServer; // if there is even a single calculated parameter this variables stay true and makes overall recipe run on server.
				} catch (e) {
					sails.log(e);
					return {
						"dids": [],
						"devicesToSubscribe": [],
						"parsedFormula": false,
						runOnServer,
					};
				}
				let regexObservableParam = new RegExp(`\\${i}`, "gi");
				let countPatternInFormula = (
					(formula || "").match(regexObservableParam) || []
				).length; // find number of occurances of

				for (let i = 0; i < countPatternInFormula; i++) {
					// if $1 = 221.kva, and equaltion is $1 > $1, then we will put this it in topic list twice,
					// good question why, bcz when quering db it wont matter and if the formula contains spot queries
					// we can delete one of these topic from array and insert spot topic, complex but only way to do it
					topicArray.push("db/" + did + "/" + param + "/" + size);
				}

				didArray.push(did);
				formula = formula.replace(
					regexObservableParam,
					did + "." + param + "." + size
				);
			}
			for (let i in operatorsDict) {
				let temp = operatorsDict[i];
				let r = new RegExp(`\\${i}`, "gi");
				formula = formula.replace(r, temp);
			}
			formula = this.makeSpotQueries(formula, topicArray, size);
			return {
				"dids": didArray,
				"devicesToSubscribe": topicArray,
				"parsedFormula": formula,
				runOnServer,
			};
		} catch (e) {
			sails.log.error(e);
			return {};
		}
	},
	"makeSpotQueries": function(formula, deviceToSubscribeArray, size) {
		try {
			// now comes regex world , this function task is:
			// forumula = "valueAt(222.kva.23, 23) > 222.kva.10"
			// so this function will create a topic for these valueAt queries bccz these are new spot query

			const regex = /valueAt\s*\(\s*([\d\w_]+)\.([a-zA-Z_]*)\.(\d+)\s*,\s*(\d+)\s*\)/gm; // regex to find spot query
			let m;

			while ((m = regex.exec(formula)) !== null) {
				// iterate on each group of that spot query
				// This is necessary to avoid infinite loops with zero-width matches
				if (m.index === regex.lastIndex) {
					regex.lastIndex++;
				}
				let spotTopic = `spot/${m[1]}/${m[2]}/${m[4]}`; // make spotquerytopic
				let replaceWithTopic = `db/${m[1]}/${m[2]}/${size}`; // make old topic which was inserted before and was wrong, bcz at here we came to know
				// that this thing requires a spot query not normal one, maybe it may need normal db query topic too, but heh that too is covered .
				// The result can be accessed through the `m`-variable.
				let indexMatchOfreplacingValue = deviceToSubscribeArray.indexOf(
					replaceWithTopic
				);
				if (indexMatchOfreplacingValue != -1) {
					deviceToSubscribeArray[
						indexMatchOfreplacingValue
					] = spotTopic;
				}
				let replaceRegex = new RegExp(
					`valueAt\\s*\\(\\s*${m[1]}.${m[2]}.${m[3]}\\s*,\\s*${
						m[4]
					}\\s*\\)`
				); // fix the formula bcz it not valueAt(2222.kva.23,23) but valueAt(2222.kva, 23)
				formula = formula.replace(
					replaceRegex,
					`valueAt ( ${m[1]}.${m[2]} , ${m[4]}) `
				); // finally replace
			}
			return formula; // new formula created , bcz pass by refrence deviceToSubscribeArray is automatically reflected in calle object
			// with new spot topics we inserted
		} catch (e) {
			sails.log.error("ERROR", " recipeservice.makeSpotQueries", e);
		}
	},
	"unstage": function(topic, _dataObj) {
		let errtype = false;
		try {
			let siteId = topic.split("/")[0];
			let dataObj = helper.toJson(_dataObj);
			let schedules = dataObj["scheduleInfo"];
			let rid = schedules[0]["rid"];

			if (!siteId || !rid) {
				errtype = true;
				throw "No Arguments given";
			}
			let findBy = { "siteId": siteId, "rid": rid };

			Recipe.findOne(findBy)
				.then(d => {
					if (d) {
						return Recipe.update(findBy, { "isStage": "0" });
					} else {
						return undefined;
					}
				})
				.then(d1 => {
					return deploySchedules(schedules);
				})
				.then(d2 => {
					sails.log("Recipe reached");
					eventService.notifyJouleTrack(
						siteId,
						"recipies",
						"recipes",
						{
							"event": "deploy",
							"data": {
								"recipe": d2,
							},
						}
					);
					return;
				})
				.catch(e => {
					sails.log.error(e);
				});
		} catch (e) {
			sails.log.error(e);
			if (errtype) {
				sails.log.error(e);
			} else {
				sails.log("Server Error");
			}
		}
	},
	"recipeAdded": function(data) {
		try {
			let { recipeInfo } = data;
			if (!recipeInfo) return;
			let site;
			let rid = recipeInfo.rid;
			if (!rid) {
				return;
			}
			Recipe.findOne({ "rid": rid })
				.then(d => {
					if (d) {
						site = d["siteId"];
						return Recipe.update(d, { "isStage": "1" });
					} else {
						// [FE] send unable to schedule data to front end
						return false;
					}
				})
				.then(done => {
					// [FE] send done to front end
					eventService.notifyJouleTrack(site, "recipies", "recipes", {
						"event": "deploy",
						"data": {
							"recipe": data,
							"res": true,
						},
					});
					sails.log(done);
				})
				.catch(e => {
					// [FE] send unable to schedule data to front end
					sails.log.error(e);
					eventService.notifyJouleTrack(site, "recipies", "recipes", {
						"event": "deploy",
						"data": {
							"recipe": data,
							"res": false,
						},
					});
					return;
				});
		} catch (e) {
			sails.log.error(e);
		}
	},
	"alert": function(topic, feedback) {
		// no need to test as this will be deleted after recipe 2.0 is everywhere
		try {
			feedback = helper.toJson(feedback);
			let siteId = topic.split("/")[0];
			if (!feedback || !siteId) return false;

			feedback["siteId"] = siteId;
			actionableFeedback(feedback);
		} catch (e) {
			sails.log.error(e);
			return false;
		}
	},
	"feedback": function(topic, downObj) {
		// way to hard to test, have to rewrite/refactor
		try {
			downObj = helper.toJson(downObj);
			let siteId = topic.split("/")[0];
			let { func, data, status } = downObj;
			// controllerFeedbackFunctionMap
			let funcDict = {
				"pausedRecipeplay": pausedRecipeplay,
				"pauseRecipe": pauseRecipe,
				"saveRecipeActive": saveRecipeActive,
				"deleteRecipe": deleteRecipe,
				"deleteSchedule": deleteSchedule,
				"startStopRecipe": startStopRecipe,
				"updateRecipe": updateRecipe,
			};

			if (status === false) {
				sails.log("Recipe send false, cmon");
				return;
			}

			if (data["status"]) {
				funcDict[func](siteId, data);
				// [FEEDBACK] frontend done
			} else if (func == "saveRecipeActive") {
				funcDict[func](siteId, data);
			} else {
				funcDict[func](siteId, data);
				// [FEEDBACK] frontend not able to do
			}
		} catch (e) {
			sails.log.error(e);
			return;
		}
	},
	/**
	 * This function will return a random controllerId of a controller at site
	 * @param {string} siteId Site ID
	 */
	"getRandomRunOn": function(siteId) {
		return new Promise((res, rej) => {
			try {
				if (!siteId) {
					rej(new Error("SiteId not provided"));
				}

				Devices.find({ siteId })
					.then(d => {
						if (!d) {
							rej(new Error("Invalid site"));
						}

						let length = d.length;
						let parent = null;
						for (let i = 0; i < d.length; i++) {
							let choosenDevice = Math.floor(
								Math.random() * length
							);
							let actualDevice = d[choosenDevice];
							if (actualDevice["deviceType"] == "joulesense") {
								continue;
							}
							if (actualDevice.controllerId) {
								parent = actualDevice.controllerId;
							} else {
								parent = actualDevice.deviceId;
							}
							break;
						}

						if (parent) {
							res(parent);
						} else {
							rej("No device");
						}
					})
					.catch(e => {
						sails.log.error(e);
						rej("No valid getRunning");
					});
			} catch (e) {
				sails.log.error(e);
				rej("Error: Recipeservice.getRandomRunOn");
			}
		});
	},
	"userMobile": function(uid) {
		// no need to test as this will be deleted after recipe 2.0 is everywhere
		return new Promise((res, rej) => {
			Users.findOne({ "userId": uid })
				.then(user => {
					if (user && user.phone) res(user.phone);
					else res(null);
				})
				.catch(e => {
					rej(e);
				});
		});
	},
	"completeMobile": function(ulist) {
		// no need to test as this will be deleted after recipe 2.0 is everywhere
		return new Promise((res, rej) => {
			let promiseArry = [];
			for (let i in ulist) {
				promiseArry.push(RecipeService.userMobile(ulist[i]));
			}
			Promise.all(promiseArry)
				.then(userMobiles => {
					res([...userMobiles, ...ulist].filter(Boolean));
				})
				.catch(e => {
					sails.log(e);
					rej([]);
				});
		});
	},
	"getRunOn": function(dids, siteId) {
		return new Promise((res, rej) => {
			try {
				dids = dids.filter(Boolean);
				if (!dids && !siteId) {
					rej(new Error("No devices and siteId provided"));
				}

				let findObj = [],
					parents = [];
				for (let i in dids) {
					findObj.push(Devices.findOne({ "deviceId": dids[i] }));
				}
				Promise.all(findObj)
					.then(devices => {
						for (let deviceIndex in devices) {
							if (devices[deviceIndex]) {
								let deviceInfo = devices[deviceIndex];

								if (deviceInfo["deviceType"] == "joulesense") {
									continue;
								}

								if (deviceInfo.controllerId) {
									parents.push(deviceInfo.controllerId);
								} else {
									parents.push(deviceInfo.deviceId);
								}
							}
						}
						if (parents.length > 0) {
							return this.getCount(parents);
						} else {
							return this.getRandomRunOn(siteId);
						}
					})
					.then(d => {
						res(d);
					})
					.catch(e => {
						sails.log.error(e);
						rej(new Error("No valid getRunning"));
					});
			} catch (e) {
				sails.log.error(e);
				rej(new Error("Error in getRunning on"));
			}
		});
	},
	/**
	 * Get the maximum occuring deviceId in the Array
	 * @param {array} dids Array of device ID's
	 */
	"getCount": function(dids) {
		if (!Array.isArray(dids)) {
			throw Error("Unable to get run on controller");
		}
		let cObj = {};
		let mC = 0,
			mOf;
		for (let i in dids) {
			if (cObj[dids[i]]) {
				cObj[dids[i]]++;
			} else {
				cObj[dids[i]] = 1;
			}
			if (cObj[dids[i]] > mC) {
				mOf = dids[i];
				mC = cObj[dids[i]];
			}
		}
		return mOf;
	},

	"getRecipeObj": async function(
		recipeObj,
		formula,
		params,
		operator,
		maxDataNeeded,
		maxLogNeeded
	) {
		try {
			let typeOfSchedule = helper.toJson(recipeObj["isSchedule"]);
			if (typeOfSchedule) {
				// just list of actionable and nothing to observe
				let runArr = [];
				let actionable = helper.toJson(recipeObj["actionable"]);
				for (let i in actionable) {
					if (actionable[i]["did"]) {
						runArr.push(actionable[i]["did"]);
					}
				}
				recipeObj["dependentOnOthers"] = JSON.stringify(runArr);
				recipeObj["runOn"] = await this.getRunOn(
					runArr,
					recipeObj["siteId"]
				);
			} else {
				recipeObj["oldObservable"] = formula;
				if (!maxDataNeeded) {
					maxDataNeeded = 1;
				}
				if (!maxLogNeeded) {
					maxLogNeeded = 1;
				}
				if (formula.slice(0, 2) == "||") {
					formula = formula.slice(2, formula.length);
				}
				if (formula.slice(formula.length - 2, formula.length) == "||") {
					formula = formula.slice(0, formula.length - 2);
				}
				let {
					dids,
					devicesToSubscribe,
					parsedFormula,
					runOnServer,
				} = await this.v1Parse(
					formula,
					params,
					operator,
					maxDataNeeded
				);

				if (!parsedFormula) {
					sails.log.error(
						"Cannot parse formula, recipeservice.getrecipeobj"
					);
					return false;
				}
				parsedFormula = parsedFormula.replace(/ and /gi, " & ");
				parsedFormula = parsedFormula.replace(/ or /gi, " | ");

				recipeObj["maxLogNeeded"] = maxLogNeeded;
				recipeObj["maxDataNeeded"] = maxDataNeeded;
				recipeObj["formula"] = parsedFormula;
				recipeObj["params"] = JSON.stringify(params);
				recipeObj["operator"] = JSON.stringify(operator);
				recipeObj["everyMinuteTopics"] = JSON.stringify(
					devicesToSubscribe
				);
				recipeObj["dependentOnOthers"] = JSON.stringify(dids);
				try {
					if (runOnServer) {
						recipeObj["runOn"] = "server";
					} else if (recipeObj["maxLogNeeded"] > 120) {
						// if recipe needs more than 2 hour of data run it on server
						recipeObj["runOn"] = "server";
					} else if (parsedFormula.match(CALCULATEDPARAMREGEX)) {
						// if recipe have some sort of spot query that is taking value
						//  from database on some x time, run it on server
						recipeObj["runOn"] = "server";
					} else {
						recipeObj["runOn"] = await this.getRunOn(
							dids,
							recipeObj["siteId"]
						);
					}
				} catch (e) {
					sails.log.error(e);
					return false;
				}
			}
			return recipeObj;
		} catch (e) {
			sails.log.error(e);
			return false;
		}
	},
	"unschedulesSchedules": (rid, schedules) => {
		return new Promise((res, rej) => {
			try {
				if (!schedules) {
					schedules = [];
				}
				let promArr = schedules.map(schedule => {
					return Schedules.update(
						{
							"rid": rid,
							"sid": schedule,
						},
						{ "isDeployed": "0" }
					);
				});
				Promise.all(promArr)
					.then(p => {
						res();
					})
					.catch(e => {
						sails.log.error(e);
						res();
					});
			} catch (e) {
				sails.log.error(e);
				res();
			}
		});
	},
	/**
	 * Take an expression and respond if it have calculated parameter or not
	 * @param {Array} expression : An expression like  ( 69@outputfrequency + 69@inputpower ) splitted on space
	 * tell if the expression provided have any arthimetic operation its considered as calculated paramter
	 */
	"haveCalculatedParameter": function(expression) {
		try {
			if (!Array.isArray(expression)) {
				return false;
			}
			// if there;s + - * / in expression
			if (expression.join(" ").match(/[*&|></+-]/)) {
				return true;
			} else {
				return false;
			}
		} catch (e) {
			sails.log(e);
			return false;
		}
	},
	/**
	 * Given an simple non-calculated expression , translate it into its deviceId and paramter.
	 * @param {string} dataExpression A Non-Calulcated Paramter's expression like (69@parameter)
	 */
	"translatedExpressionToDevice": function(dataExpression) {
		if (dataExpression.constructor.name !== "String") {
			return undefined;
		}

		let finds = dataExpression.match(/([a-zA-Z_0-9.]+@[a-zA-Z_]+)/i);
		if (!finds || finds.length == 0) {
			return undefined;
		}
		return finds[0].split("@");
	},
	/**
	 * given component/deviceID and param , return an ID to fetch data of that param from dataDevice Table
	 * @function
	 * @param {string} cid : cid can be either compnentId/deviceId
	 * @param {string} param : parameter of that cid
	 *
	 * Get DeviceId to query data from datadevice table for that particular param. Example for cid = ssh_6 ,
	 * if param=outputfrequency , this is non calculated param so return [ 69, outputfrequency, runONController] , deviceId for outputfrequency is 69
	 * if param=ikwr , this is calculated param so return [ ssh_6, ikwr, runONServer] , because this recipe require calculated parameter, run it on server and this doesnt have a deviceid so ssh_6
	 */
	"getDidParam": function(cid, param) {
		//  NOW CID can be "mgch_123" or "1234" and param is like "outputfrequency" or "ikwr"
		return new Promise((res, rej) => {
			let runOnServer = false;

			try {
				if (!cid) {
					rej("No device id");
					return;
				}
				let type, tempcid;
				try {
					tempcid = parseInt(cid);
					type = "deviceId";
				} catch (e) {
					type = "compId";
				}
				if (isNaN(tempcid)) {
					type = "compId";
				}

				if (type == "deviceId") {
					res([cid, param, runOnServer]);
				} else if (type == "compId") {
					Component.findOne({ "deviceId": cid })
						.then(components => {
							let data = helper.toArray(components["data"]);
							data.forEach(component => {
								component = helper.toJson(component);
								if (component["key"] == param) {
									let _expression = component[
										"expression"
									].split("||");

									if (
										this.haveCalculatedParameter(
											_expression
										)
									) {
										runOnServer = true;
										return res([cid, param, runOnServer]);
									}

									let exp = this.translatedExpressionToDevice(
										component["expression"]
									);
									sails.log(exp);
									if (!exp) {
										sails.log(
											"Invalid parameter provided : ",
											component
										);
										return rej(
											"Invalid parameter provided."
										);
									} else {
										return res([
											exp[0],
											exp[1],
											runOnServer,
										]);
									}
								}
							});
							rej("Parameter doesnt exist");
						})
						.catch(e => {
							rej(e);
							return;
						});
				}
			} catch (e) {
				sails.log(e);
				rej(e);
			}
		});
	},
};

function deleteSchedule(site, data) {
	try {
		data = helper.toJson(data);
		let rid = data["rid"];
		let sid = data["sid"];
		let findObj = { "siteId": site, "rid": rid };
		if (!sid || !rid) {
			return false;
		}
		Recipe.findOne(findObj)
			.then(recipe => {
				let schedules = helper.toArray(recipe["scheduled"]);
				if (!schedules) {
					schedules = [];
				}
				schedules = schedules.filter(schedule => schedule != sid);

				return Recipe.update(findObj, {
					"scheduled": JSON.stringify(schedules),
				});
			})
			.then(done => {
				return Schedules.destroy({ "sid": sid, "rid": rid });
			})
			.then(done => {
				sails.log("SCHDULES DELETED");
				eventService.notifyJouleTrack(site, "recipies", "recipes", {
					"event": "delete",
					"data": {
						"recipe": sid,
					},
				});
				return true;
			})
			.catch(e => {
				sails.log.error(e);
				eventService.notifyJouleTrack(site, "recipies", "recipes", {
					"event": "delete",
					"data": {
						"recipe": data,
						"res": false,
					},
				});
				return;
			});
	} catch (e) {
		sails.log.error(e);
		return;
	}
}

async function updateRecipe(site, data) {
	try {
		data = helper.toJson(data);

		let rid = data["rid"];
		if (!rid) {
			return;
		}

		data["isSchedule"] = helper.toString(data["isSchedule"]);
		data["failsafe"] = helper.toString(data["failsafe"]);
		data["actionable"] = helper.toString(data["actionable"]);
		data["recipelabel"] = helper.toString(data["recipelabel"]);
		data["notRun"] = helper.toString(data["notRun"]);
		data["scheduled"] = helper.toString(data["scheduled"]);
		data["operator"] = helper.toString(data["operator"]);
		data["params"] = helper.toString(data["params"]);
		data["everyMinuteTopics"] = helper.toString(data["everyMinuteTopics"]);
		data["isStage"] = "1";

		// console.log("Updatingggg",data,rid,site);
		await Recipe.update(
			{
				"rid": rid,
				"siteId": site,
			},
			data
		);
		await axios.post("http://localhost:1337/v1/recepie/deploy", {
			"rid": rid,
			"siteId": site,
		});
		sails.log("Update recipe: ");
		return data;
	} catch (e) {
		sails.log.error(e);
	}
}

function startStopRecipe(site, data) {
	try {
		data = helper.toJson(data);
		let id = data["rid"];
		let switchType = String(data["switchOff"]);
		Recipe.update(
			{
				"rid": id,
				"siteId": site,
			},
			{ "isActive": switchType }
		)
			.then(d => {})
			.catch(e => {
				sails.log.error(e);
				return;
			});
	} catch (e) {
		sails.log.error(e);
		return;
	}
}

function deleteRecipe(site, data) {
	try {
		data = helper.toJson(data);
		let id = data["rid"];
		let findFromRecipe = { "siteId": site, "rid": id };
		Recipe.findOne(findFromRecipe)
			.then(recipe => {
				if (!recipe) {
					return;
				}
				let promArr = [];
				let schedules = helper.toArray(recipe["scheduled"]);
				if (!schedules) {
					schedules = [];
				}
				schedules.forEach(sch => {
					promArr.push(Schedules.destroy({ "rid": id, "sid": sch }));
				});
				return Promise.all(promArr);
			})
			.then(done => {
				return Recipe.destroy(findFromRecipe);
			})
			.then(done => {
				eventService.notifyJouleTrack(site, "recipies", "recipes", {
					"event": "delete",
					"data": {
						"recipe": id,
					},
				});
				sails.log("Recipe DELETED");
				return true;
			})
			.catch(e => {
				sails.log.error(e);
				return;
			});
	} catch (e) {
		sails.log.error(e);
		return;
	}
}

function saveRecipeActive(site, data) {
	//  data is {id : 1 , id2 : 1}
	try {
		data = helper.toJson(data);
		let promArr = [];
		for (let rid in data) {
			let isAct = data[rid];
			let findBy = { "siteId": site, "rid": rid };
			promArr.push(
				Recipe.update(findBy, { "isActive": isAct, "isStage": "0" })
			);
		}
		Promise.all(promArr)
			.then(done => {
				sails.log("REcipe is active updated");
				return;
			})
			.catch(e => {
				sails.log.error(e);
			});
	} catch (e) {
		sails.log.error(e);
		return;
	}
}
function pausedRecipeplay(siteId, data) {
	return new Promise((res, req) => {
		try {
			let { rid } = data;
			Recipe.findOne({ rid, siteId })
				.then(d => {
					if (d) {
						return Recipe.update(d, { "notRun": "[]" });
					} else {
						return undefined;
					}
				})
				.then(d1 => {
					sails.log(d1);
					res(true);
				})
				.catch(e => {
					sails.log.error(e);
					res(false);
				});
		} catch (e) {
			sails.log.error(e);
			res(false);
		}
	});
}

function pauseRecipe(siteId, data) {
	return new Promise((res, req) => {
		try {
			let { rid, fromTo } = data;

			if (!rid || !fromTo) {
				res(false);
			}
			let findby = { rid, siteId };
			sails.log(findby);
			Recipe.findOne({ rid, siteId })
				.then(d => {
					if (d) {
						return Recipe.update(d, { "notRun": fromTo });
					} else {
						return undefined;
					}
				})
				.then(d1 => {
					sails.log(d1);
					res(true);
				})
				.catch(e => {
					sails.log.error(e);
					res(false);
				});
		} catch (e) {
			sails.log.error(e);
			res(false);
		}
	});
}

function actionableFeedback(fb) {
	// no need to test as this will be deleted after recipe 2.0 is everywhere
	let rid = fb.rid,
		status = fb.Status,
		ts = fb.ts,
		siteId = fb.siteId;
	if (!rid || !ts || !siteId) {
		sails.log("Log must containt recipe id + status +timestamp");
		return false;
	}

	if (status == 2) return;

	let exitCondition = false;

	try {
		ts = moment(ts).unix() * 1000;
	} catch (e) {
		return;
	}
	let statusCode = {
		"0": "Unable to execute",
		"1": "Executed",
		"2": "Reached Controller",
		"3": "Error in executing",
		"4": "Value is Maintained",
	};
	let recipelabel, toNotify;
	let actionable, labels;

	if (status != 2 || !statusCode[status]) {
		let extra = fb.extra;
		if (!extra) {
			throw "Error, No proper alert args";
		}
		let { uid, actualTs } = extra;
		if (!uid || !actualTs) {
			throw "Error, No proper alert args";
		}
		let ulistpromise = [];
		let alertInfo = {};
		Recipe.findOne({ "rid": rid, "siteId": siteId })
			.then(async recipeInfo => {
				if (recipeInfo) {
					if (!recipeInfo.actionable) {
						actionable = [];
					} else {
						actionable = helper.toArray(recipeInfo.actionable)[0];
					}

					if (!recipeInfo.recipelabel) {
						labels = "[]";
					} else {
						labels = createLabel(recipeInfo.recipelabel);
					}
					recipelabel = helper.toJson(labels);

					let body = JSON.stringify({ ...actionable });
					// no title in actionable of type=='action'
					// also no description

					if (!actionable.title || !actionable.description) {
						actionable.title = `${actionable.command} is set to ${actionable.value}`;
						actionable.description = `${labels}`;
					}
					let alert = {
						"timestamp": ts,
						"eventId": rid,
						"sub_category": labels,
						"readStatus": "unread",
						"body": body,
						"title": actionable.title,
						"description": actionable.description,
						"alertType": "RECIPE",
						"priority": actionable.priority,
					};

					if (!actionable.notify) {
						toNotify = [];
					} else {
						toNotify = actionable["notify"];
					}

					toNotify = await RecipeService.completeMobile(toNotify);

					return alert;
				}
			})
			.then(async alert => {
				if (!alert) throw "No alert added";

				if (status != "4") {
					let desc = `${siteId} : ${alert["description"]}`;

					alertInfo[rid] = {
						"description": desc,
						"title": alert["title"],
						"status": statusCode[status],
						"groupBy": alert["sub_category"],
						"extra": "",
					};

					let isCached = await cacheService.getKey(
						`cacheAlert_${alert["eventId"]}`
					);
					if (isCached == status) {
						exitCondition = true;
						sails.log("Not notifying, cached alert");
						throw "Prematurely ending function call";
					} else {
						for (let l in recipelabel) {
							let temp = recipelabel[l]; // get label of recipe
							ulistpromise.push(
								cacheService.hashMapGet(
									"atc",
									`${siteId}_${temp}`
								)
							);
						}

						if (isCached === null) {
							await cacheService.setKey(
								`cacheAlert_${alert["eventId"]}`,
								status
							);
							await cacheService.expire(
								`cacheAlert_${alert["eventId"]}`,
								1800
							);
						}

						alertService.saveAlert(
							alert,
							siteId,
							"RECIPE",
							toNotify
						);
					}
				}
				return Promise.all(ulistpromise);
			})
			.then(ulist => {
				sails.log(ulist);
				// ulist will be [[atc_l1],[atc_l2]]
				sails.log("//?????????????", ulist);
				let topClassifier = {
					"emails": [],
					"smss": [],
				};

				let notification = {
					"data": "Received a notification",
					"event": `${rid}`,
				};

				eventService.notifyJouleTrack(
					siteId,
					"recipies",
					"alerts",
					notification
				);

				ulist.forEach(users => {
					users.forEach(user => {
						if (user) {
							if (toNotify.includes(user)) {
								if (helper.isEmail(user)) {
									topClassifier.emails.push(user);
								} else {
									topClassifier.smss.push(user);
								}
							}
						}
						// test if mobile number or email address
						// OOps
					});
				});
				if (status != "4") {
					alertService.recipeAlert(
						topClassifier,
						alertInfo[rid],
						actualTs
					);
				}
			})
			.catch(e => {
				if (exitCondition == true) {
					return;
				} else {
					sails.log.error(e);
					return;
				}
			});
	}
}

function deploySchedules(schedules, val = "1") {
	return new Promise((res, rej) => {
		let errtype = false;
		try {
			let scheduleUpdatePromise = schedules.map(schedule => {
				return Schedules.update(
					{
						"sid": schedule["id"],
						"rid": schedule["rid"],
					},
					{ "isDeployed": val }
				);
			});
			Promise.all(scheduleUpdatePromise)
				.then(done => {
					res(done);
				})
				.catch(e => {
					sails.log.error(e);
					rej(e);
				});
		} catch (e) {
			sails.log.error(e);
			if (errtype) {
				rej(e);
			} else {
				rej("Server Error");
			}
		}
	});
}

// Move into a helper
// Untested function!!!
function createLabel(labelString) {
	let finalLabels = [];
	let labelsArray = helper.toArray(labelString);

	labelsArray.forEach(label => {
		label = "recipe" + label.toLowerCase().replace(" ", "");
		finalLabels.push(label);
	});

	return JSON.stringify(finalLabels);
}
