/**
 * @namespace componentService
 * @description A interface to interact with components table.
 */
const pgConnection = require('./pgConnection')
const axios = require('axios');

module.exports = {
	/**
	 * @function getComponents
	 * @param {String} siteId
	 * @description Returns all the components of given siteId
	 * If siteId is undefined or is not valid it will return empty array
	 * @returns {Array}
	 */
	"getComponents": async siteId => {
		if (!siteId) return [];
		// Hardcode only for Rama as of now. As RAMA is the only site with Components configured as Processes.
		if (siteId === "rpml-bij") {
			let processComponents = await processService.find({
				siteId,
				"type": "component"
			});
			let components = processComponents.map(componentService.generateComponentFromProcess);
			return components;
		}
		let components = await Component.find({ siteId });
		let componentArr = components.map(component => {
			helper.convertStringArrayToJSONArray(component.data);
			helper.convertStringArrayToJSONArray(component.controls);

			const maintenanceValue = component.isInMaintenanceMode;
			if (maintenanceValue === true || maintenanceValue === 1 || maintenanceValue === "1") {
				component.isInMaintenanceMode = "1";
			} else {
				component.isInMaintenanceMode = "0";
			}

			delete component.info;
			return component;
		});
		return componentArr;
	},
	/**
	 * @function getComponentOrProcess
	 * @param {String} siteId
	 * @param {String} deviceId
	 * @description Returns the component/process
	 * @returns {Array}
	 */
	"getComponentOrProcess": async (siteId, deviceId) => {
		if (!siteId) return [];
		// Hardcode only for Rama as of now. As RAMA is the only site with Components configured as Processes.

		if (siteId === "rpml-bij") {

			let processComponent = await processService.findOne({

				"processId": deviceId
			});
			let component = componentService.generateComponentFromProcess(processComponent);
			return component;
		}
		let component = await Component.findOne({ siteId, deviceId });
		helper.convertStringArrayToJSONArray(component.data);
		helper.convertStringArrayToJSONArray(component.controls);
		delete component.info;

		return component;
	},
	/**
	 * @function getComponents
	 * @param {String} deviceId of component
	 * @description Returns the configuration of the component with given deviceId
	 * If deviceId is undefined or is not valid it will return empty array
	 * @returns {Object}
	 */
	"getComponent": async deviceId => {
		if (!deviceId) return undefined;
		let component = await Component.findOne({ deviceId });
		// sails.log.debug(component);
		if (component) {
			helper.convertStringArrayToJSONArray(component.data);
			helper.convertStringArrayToJSONArray(component.controls);
		}
    const maintenanceValue = component.isInMaintenanceMode;
    if (maintenanceValue === true || maintenanceValue === 1 || maintenanceValue === "1") {
      component.isInMaintenanceMode = "1";
    } else {
      component.isInMaintenanceMode = "0";
    }
		return component;
	},
	/**
	 * @function getComponentParamMap
	 * @param siteId
	 * @description For a given site Id the function returns a map in the following format
	 * {
	 * 	<componentId>:
	 * 		data:
	 * 			<key>:{<parameter object>}
	 *		controls:
	 *			<key>:{<parameter object>}
	 *  <comonentId>:{}
	 * }
	 * @returns {object}
	 */
	"getComponentParamMap": async siteId => {
		let components = await componentService.getComponents(siteId);
		// console.log(components);
		let compParamMap = {};
		components.map(component => {
			let { deviceId, data, controls } = component;
			let paramObj = {
				"data": {},
				"controls": {}
			};
			data.map(param => {
				let { key } = param;
				paramObj.data[key] = param;
			});
			controls.map(param => {
				let { key } = param;
				paramObj.data[key] = param;
			});
			compParamMap[deviceId] = paramObj;
		});
		return compParamMap;
	},
	/**
	 * @function getComponentControlKeys
	 * @description returns an array of keys of control parameters fo the component
	 * @param componentId
	 * @returns {Array}
	 */
	"getComponentControlKeys": async (componentId) => {
		// returns an array of control param for component ;
		let controlParamForThisComponent = [];
		try {
			let component = await Component.findOne({ "deviceId": componentId });
			let controls = helper.toJson(component["controls"]);
			helper.convertStringArrayToJSONArray(controls);
			for (let controlIndex in controls) {
				let control = controls[controlIndex];
				controlParamForThisComponent.push(control["key"]);
			}

		} catch (e) {
			sails.log.error(e);
			sails.log.error("componentservice.gecomponentcontrolkeys");
		}
		return controlParamForThisComponent;
	},
	/**
	 * @function queryDataKeys
	 * @param {string} componentId
	 * @param {object} findObj A object that will be used to query
	 * @param {Array} filterArr Array of keys that are only required
	 * @description Queries data key of the given component id and returns
	 * the parameters that match the findObj criteria and
	 * return the parmaeter object with only keys present in filterArr
	 * if filterArr is empty or not passed all the keys of data
	 * parameters satisfying the configuration are returned
	 * @returns {Array} An array of objects satifying conditions.
	 */
	"queryDataKeys": async function (componentId, findObj, filterArr = []) {
		const component = await this.getComponent(componentId);
		if (!component) return null;
		const { data } = component;
		if (!data || !Array.isArray(data)) return null;
		const requiredParams = data.filter(obj => {
			let satisfies = true;
			Object.keys(findObj).forEach(objKey => {
				if (
					typeof obj[objKey] === "undefined" ||
					obj[objKey].toLowerCase() !== findObj[objKey].toLowerCase()
				) satisfies = false;
			});
			return satisfies;
		}).map(obj => {
			let filteredObj = {};
			if (filterArr.length >= 0) {
				filterArr.map(filterKey => {
					if (typeof obj[filterKey] !== "undefined") filteredObj[filterKey] = obj[filterKey];
				});
				return filteredObj;
			}
			return obj;
		});
		return requiredParams;
	},
	/**
	 * @function generateComponentFromProcess
	 * @description Converts a process of type "component" to the old schema of a component to maintain backward compatibility
	 * for IOT as well as FrontEnd.
	 * @param {Object} process A process of type "component" being fetched from Processes Table.
	 */
	"generateComponentFromProcess": function (process) {
		// Adding rawParams and calcParams to "data" key if present.
		if (process.data === undefined) {
			process["data"] = [];
		}
		if (process.controls === undefined) {
			process["controls"] = [];
		}
		if (process.rawParams) {
			process.data = [...process.data, ...process.rawParams];
		}
		if (process.calcParams) {
			process.data = [...process.data, ...process.calcParams];
		}
		// Replicating processId to deviceId as per component schema.
		process["deviceId"] = process.processId;
		// Replicating "abbr" to "key" as per compnent schema in all parameters.
		process.data.forEach(parameter => {
			parameter["key"] = parameter.abbr;
		});
		process.controls.forEach(parameter => {
			parameter["key"] = parameter.abbr;
		});
		return process;
	},
	/**
	 * Check if required keys in chemial object exists or not
	 * @param {object} chemical Chemical objbect with price and size
	 */
	"formatChemicalPriceAndCost": function (chemical) {

		if (chemical && chemical.constructor.name === "Object") {
			let { size, price } = chemical;
			size = parseInt(size);
			price = parseInt(price);

			if (!isNaN(size) && !isNaN(price)) {
				return { size, price };
			} else {
				return;
			}
		} else {
			return;
		}
	},
	getLinkedConfiguratorSystemNameByComponentId: async function(componentId){
		const pgDbCon = new pgConnection()
		const taggedSystemsResult = await pgDbCon.query(`select string_agg(cs.name,',') as name,ctd.device_id from configurator_systems cs
join configurator_tagged_devices ctd on ctd.configurator_system_id =cs.id and ctd.status=1 and ctd."device_Type" = 'component'
where ctd.device_id = '${componentId}' and cs.status in (1,2) group by ctd.device_id`)
		const { rows } = taggedSystemsResult
		return rows;
	},
  getLinkedConfiguratorPageByComponentId: async function (componentId) {
    const pgDbCon = new pgConnection()
    const taggedPagesResult = await pgDbCon.query(`with combined_pages as (
      select string_agg(ssp.title,',') as title,ctr."device_id"
      from configurator_table_row ctr
      join configurator_table ct on ct.id=ctr."table_id"
      join configurator_table_group ctg on ctg.id=ct."table_group_id"
      join sub_system_pages ssp on ssp.id=ctg."pages_ref_id" and ctr.status=1 and ct.status=1 and ctg.status=1
      where ctr."device_id"='${componentId}' and ssp.status in (1,2)
      group by ctr."device_id"
      union all
      select string_agg(ssp.title,',') as title,ctd."device_id"
      from sub_system_pages ssp
      join configurator_tagged_devices ctd on ctd."sub_system_page_id"=ssp.id and ctd.status=1 and ctd."device_Type"='component'
      where ctd."device_id"='${componentId}' and ssp.status in (1,2)
      group by ctd."device_id"
      )
      select title,device_id from combined_pages;
    `)
    const { rows } = taggedPagesResult
    return rows;
  },
	buildComponentControlRelationshipMap:async function(componentId,accessToken){
		try {
			const { data } = await axios.post(`${sails.config.SERVICE_JT_API_URL}/m2/v1/driver/control-relationship/component/sync`, { componentId:[componentId] }, {
				"headers": {
					"Content-Type": "application/json",
					'Authorization': `Bearer ${accessToken}`,
				}
			});
		} catch (error) {
      sails.log.error(`Add component > Create component > Build component relationship map error`);
      if (error.response) {
        sails.log.error("Data :", error.response.data);
        sails.log.error("Status :" + error.response.status);
      } else if (error.request) {
        sails.log.error(error.request);
      } else {
        sails.log.error('Error', error.message);
      }
		}
	},
	deleteComponentControlRelationship: async function(siteId,componentId){
		const pgDbCon = new pgConnection()
    await pgDbCon.query(`update device_control_config_relationship SET status = 0 where device_id = '${componentId}' and status=1 and site_id='${siteId}'`);
	},
  getNextComponentIdNumber: async function (siteId) {
    const allComponents = await Component.find({ siteId });
    let max = 0;
    let devIdErr = false;
    allComponents.forEach((cmp) => {
      try {
        let cId = Number(cmp.deviceId.split("_")[1]);
        if (cId > max) {
          max = cId;
        }
      } catch (e) {
        devIdErr = true;
      }
    });
    return devIdErr ? -1 : max + 1;
  },
};
