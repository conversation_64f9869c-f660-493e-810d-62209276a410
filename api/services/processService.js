module.exports = {
	"create": function(record) {
		return Processes.create(record);
	},
	"update": function(query, record) {
		return Processes.update(query, record);
	},
	"delete": function(query) {
		return Processes.destroy(query);
	},
	"find": function(query) {
		return Processes.find( query );
	},
	"findOne": async function(query) {
		let processes = await Processes.find( query );
		return processes[0];
	},
	/**
	 * @function generateProcessId
	 * @param {string} siteId
	 * @description gets procCount_<siteId> from dynamokeystore and append
	 * it with siteId to generate id for the process
	 */
	"generateProcessId": async function(siteId) {
		const procCountObj = await DyanmoKeyStore.findOne({
			"key": `procCount_${siteId}`,
		});
		let procCount = 1;
		if (typeof procCountObj !== "undefined") {
			procCount = parseInt(procCountObj.value) + 1;
		}
		if (typeof procCount !== "number" || isNaN(procCount))
			throw Error(
				`Unable to generate processId, errored obj is ${JSON.stringify(
					procCountObj
				)}`
			);
		await DyanmoKeyStore.create({
			"key": `procCount_${siteId}`,
			"value": procCount,
		});
		return `${siteId}_${procCount}`;
	},
	/**
	 * @function processService.validateParameters
	 * Will validate if the entries provided in configuration actually exist in database
	 *
	 * @param {Object} obj
	 * @param {string} obj.siteId
	 * @param {string} obj.deviceId
	 * @param {string} obj.regionId
	 * @param {string} obj.id
	 * @param {string} obj.controllerId
	 */
	"validateParameters": async function(obj) {
		let dataObj = await processService.getDataFromServices(obj);
		const controllerTypes = [
			"jouleio",
			"jouleiocontrol",
			"joulestat",
			"joulemomo",
			"joulelogger",
		];
		for (let key in dataObj) {
			try {
				const $data = dataObj[key];
				switch (key) {
					case "siteId":
					case "regionId": {
						const site = $data;
						if (!site) return false;
						const regions = helper.toJson(site.regions);
						if (
							key === "regionId" &&
							(!regions || !regions[obj.regionId])
						)
							return false;
						break;
					}
					case "deviceId":
					case "controllerId": {
						const device = $data;
						if (!device) return false;
						if (
							key === "controllerId" &&
							controllerTypes.indexOf(
								device.deviceType.toLowerCase()
							) === -1
						)
							return false;
						break;
					}
					case "parentProcess":
					case "id": {
						let proc;
						if ($data.length === 0)
							return false;
						else
							proc = $data[0];
						if (!proc) return false;
					}
				}
			} catch (err) {
				sails.log.error("[ProcessService > validateParameters] ERROR: ", JSON.stringify(obj), err);
				return false;
			}
		}
		return true;
	},
	/**
	 * @function getDataFromServices
	 * @param {Object} obj
	 * @param {string} obj.siteId
	 * @param {string} obj.deviceId
	 * @param {string} obj.regionId
	 * @param {string} obj.id
	 * @param {string} obj.controllerId
	 * Queries different services and returns data based on primary key
	 * for example for querying devices table deviceId is the primary key, so
	 * passing {deviceId:10} would return a object as {deviceId: {<deviceObjec>}}
	 */
	"getDataFromServices": async function(obj) {
		let $dataObj = {};
		const dataObj = {};
		Object.keys(obj).forEach(key => {
			const val = obj[key];
			switch (key) {
				case "siteId": {
					$dataObj[key] = siteService.getSiteInfo(val);
					break;
				}
				case "regionId": {
					if (($dataObj)["siteId"] === undefined){
						$dataObj[key] = siteService.getSiteInfo(obj.siteId);
					} else {
						$dataObj[key] = $dataObj["siteId"];
					}
					break;
				}
				case "deviceId":
				case "controllerId": {
					$dataObj[key] = Devices.findOne({ "deviceId": val });
					break;
				}
				case "parentProcess": {
					if (val === undefined) break;
					$dataObj[key] = processService.find({
						"where": { "processId": val },
					});
				}
			}
		});
		for (const key in $dataObj) {
			dataObj[key] = await $dataObj[key];
		}
		return dataObj;
	},
};
