/**
 * @module jwtService
 */

let jwt = require("jsonwebtoken");
// tokenSecret = "b52831dd10cbdff819e6635c82c9dd83"; //dejoule-smartjoules
// const bcrypt = require("bcrypt");
// Generates a token from supplied payload
/**
 * Generates a token from supplied payload
 * @param {object} payload - payload to be included in the jwt.
 * @returns {object} returns a signed JSON Web Token with an expiration time of 8 hours.
 */
module.exports = {
	"issue": function(payload, ts=172800) {
		return jwt.sign(
			payload,
			jwtService.secret, // Token Secret that we sign it with
			{
				"expiresIn": ts ///48 hours Token Expire time
			}
		);
	},
	"verify": function(token, cb) {
		return jwt.verify(
			token, // The token to be verified
			jwtService.secret, // Same token we used to sign
			{}, // No Option, for more see https://github.com/auth0/node-jsonwebtoken#jwtverifytoken-secretorpublickey-options-callback
			cb //Pass errors or decoded token to callback
		);
	},
	"decode": function(token) {
		return jwt.decode(token);
	},
	"secret": "b52831dd10cbdff819e6635c82c9dd83",
	"generateRandomString": function(outputLength) {
		let text = "";
		let possible = "abcdefghijklmnopqrstuvwxyz";

		for (let i = 0; i < outputLength; i++)
			text += possible.charAt(Math.floor(Math.random() * possible.length));

		return text;
	}
};