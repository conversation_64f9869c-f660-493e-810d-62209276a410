const AWS = require("aws-sdk");


module.exports = {

  "listTopics": function() {
    return new Promise((res, rej) => {
      try {
        let listTopicsPromise = new AWS.SNS({ "apiVersion": "2010-03-31" }).listTopics({}).promise();
        listTopicsPromise.then(
          function(data) {
            res(data.Topics);
          }).catch((err) => {
          sails.log(err);
          rej(err);
        });
      } catch (e) {
        sails.log(e);
        rej(false);
      }

    });

  },
  "createTopic": function(name) {
    return new Promise((res, rej) => {
      try {
        let createTopicPromise = new AWS.SNS({ "apiVersion": "2010-03-31" }).createTopic({ "Name": name }).promise();
        createTopicPromise.then(
          function(data) {
            sails.log("Topic ARN is " + data.TopicArn);
          }).catch(
          function(err) {
            sails.log.error(err, err.stack);
          });
      } catch (e) {
        sails.log(e);
        rej(false);
      }
    });
  },
  "getSubscriptionAttributes": function(subArn) {
    return new Promise((res, rej) => {
      try {
        let sns = new AWS.SNS({ "apiVersion": "2010-03-31" });
        sns.getSubscriptionAttributes({ "SubscriptionArn": subArn }, function(err, data) {
          if (err) rej(err); // an error occurred
          else {
            sails.log(data);
            res(data);
          }
        });
      } catch (e) {
        rej(e);
      }
    });
  },
  "addFilter": function(subArn, attr) {

    // let params = {
    //     AttributeName: "FilterPolicy",
    //     SubscriptionArn: subArn,
    //     AttributeValue : JSON.stringify({
    //         'siteId' : ['ssh']
    //     })
    // }


    return new Promise((res, rej) => {
      try {
        if (!subArn || !attr) {
          rej(false);
        }
        let params = {
          "AttributeName": "FilterPolicy",
          "SubscriptionArn": subArn,
          "AttributeValue": JSON.stringify(attr),
        };
        sails.log(params);
        let sns = new AWS.SNS({ "apiVersion": "2010-03-31" });
        sns.setSubscriptionAttributes(params, function(err, data) {
          if (err) rej(err); // an error occurred
          else {
            res(data);
          }
        });
      } catch (e) {
        rej(e);
      }
    });
  },
  "listSubscription": function(topicArn) {
    return new Promise((res, rej) => {
      try {
        let params = {
          "TopicArn": topicArn,
        };
        let sns = new AWS.SNS({ "apiVersion": "2010-03-31" });
        sns.listSubscriptionsByTopic(params, function(err, data) {
          if (err) rej(err); // an error occurred
          else {
            sails.log(data);
            res(data);
          }
        });
      } catch (e) {
        rej(e);
      }
    });


  },
  "subscribeTopic": function(arn, protocol, Endpoint) {
    // protocol can be email or sms
    return new Promise((res, rej) => {
      try {


        if (protocol !== "email" || protocol !== "sms") {
          rej(false);
        }
        if (!Endpoint) {
          rej(false);
        }
        let params = {
          "Protocol": protocol,
          "TopicArn": arn,
          "Endpoint": Endpoint,
        };

        let subProm = new AWS.SNS({ "apiVersion": "2010-03-31" }).subscribe(params).promise();
        subProm.then(d => {
          res(d);
        }).catch(e => {
          sails.log(e);
          rej(e);
        });


      } catch (e) {
        sails.log(e);
        rej(e);
      }
    });


  },
  "publish": function(arn, attributes, message, subject) {
    return new Promise((res, rej) => {
      try {

        if (!message || !arn) {
          rej("Error");
        }
        let params = {
          "Message": message,
          "TopicArn": arn,
          "MessageAttributes": {
            "siteId": {
              "StringValue": "ssh",
              "DataType": "String",
            },

          },

        };
        let sns = new AWS.SNS({ "apiVersion": "2010-03-31" });
        sns.publish(params, function(err, data) {
          if (err) rej(err); // an error occurred
          else {
            sails.log(data);
            res(data);
          }
        });
      } catch (e) {
        rej(e);
      }
    });
  },
};
