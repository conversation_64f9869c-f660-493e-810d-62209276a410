module.exports = {
	"gotoMaintainance": (obj) => {
		try {
			let { dids, tid, data, siteId, deadline, userId, ts, attachments, comment } = obj;
			let prepareObj = [];
			for (let i in dids) {
				let j = dids[i];
				prepareObj.push(Repaired.create({ "did": j, "uuid": tid, "data": data, "siteId": siteId, "deadline": deadline, "createdBy": userId, "attachments": attachments, "comment": comment }));
				prepareObj.push(Mode.create({ "did": j + "_maintainance", "timestamp": ts, "siteId": siteId, "changedMode": "On", "currMode": "maintainance", "createdBy": userId }));
			}
			return prepareObj;


		} catch (e) {
			sails.log(e);
			return [];
		}
	},

	"isAccessAllowed": (userRole) => {

		// TODO fix this function to first make a cache hit
		// if found ok else put into cache 
		return new Promise((res, rej) => {

			if (!userRole) {
				res(false);
			}
			if (userRole == "admin") {
				res(true);
			}

			try {
				Role.findOne({ "roleName": userRole }).then(d => {

					if (!d) {
						res(false);
						return;
					} else {
						let policies = helper.toJson(d["policies"]);
						if (policies["Maintainance Mode_Write"] && policies["Maintainance Mode_Write"] === 1) {
							res(true);
						} else {
							res(false);
						}
					}


				}).catch(e => {
					sails.log(e);
					res(false);
				});

			} catch (e) {
				sails.log(e);
				res(false);
			}
		});

	},
	"updateIntoUser": (userlist, adder, funcToDoOnGetter) => {
		return new Promise((res, rej) => {
			try {
				let getter = [];
				userlist.forEach(user => {
					getter.push(Users.findOne({ "userId": user }));
				});
				Promise.all(getter).then(usersinfo => {

					return Promise.all(usersinfo.map(user => funcToDoOnGetter(user, adder)));

				}).then(d => {
					return res(d);
				}).catch(e => {
					sails.log(e);
					rej("err");
				});

			} catch (e) {
				sails.log(e);
				rej("error");
			}
		});
	},
	"pdateNUsers": (userinfo, adder) => {
		// remove all instances of adder in accountable and notif

		let primaryKey = {
			"userId": userinfo["userId"],
			"userOrganization": userinfo["userOrganization"]
		};
		let acc = helper.toArray(userinfo["accountable"]);
		let notif = helper.toArray(userinfo["notify"]);
		if (!acc || acc.constructor.name !== "Array") {
			acc = [];
		}
		if (!notif || notif.constructor.name !== "Array") {
			notif = [];
		}

		let updateObj = {};
		acc = acc.filter(function (value) { return value != adder["accountable"]; });

		updateObj["accountable"] = JSON.stringify(acc);


		notif = notif.filter(function (value) { return value != adder["notif"]; });
		// removing duplicates

		updateObj["notify"] = JSON.stringify(notif);


		sails.log(updateObj);
		return Users.update(primaryKey, updateObj);

	},
	"addUserAccountability": (userinfo, adder) => {

		// adder = {
		//     accountable : "id_uuid",
		//     notify : "id_uuid"
		// }

		let primaryKey = {
			"userId": userinfo["userId"],
			"userOrganization": userinfo["userOrganization"]
		};
		let acc = helper.toArray(userinfo["accountable"]);
		let notif = helper.toArray(userinfo["notify"]);
		if (!acc || acc.constructor.name !== "Array") {
			acc = [];
		}
		if (!notif || notif.constructor.name !== "Array") {
			notif = [];
		}

		let updateObj = {};
		acc = acc.filter(function (value) { return value != adder["accountable"]; });
		notif = notif.filter(function (value) { return value != adder["notif"]; });

		// delete all instances of toAdd uuid from notify and accountable
		// then just add once


		if (adder["accountable"]) {
			acc.push(adder["accountable"]);
			updateObj["accountable"] = JSON.stringify(acc);
		}

		if (adder["notify"]) {
			notif.push(adder["notify"]);
			updateObj["notify"] = JSON.stringify(notif);
		}


		return Users.update(primaryKey, updateObj);

	},
	"getControllerFromParent": (parentid, siteId) => {

		return new Promise((res, rej) => {

			let lis = {};
			try {
				Devices.find({ "siteId": siteId }).then(devices => {
					devices.forEach((device) => {

						// let myId = device["deviceId"];
						let myParent = device["controllerId"];

						if (!myParent) {
							return;
						}
						if (parentid == myParent) {
							lis[device["deviceId"]] = device;
						}
					});
					let x = {};
					x["parent"] = parentid;
					x["data"] = lis;
					res(x);
				}).catch(e => {
					sails.log(e);
					rej("error");
				});


			} catch (e) {
				sails.log(e);
				rej("error");
			}


		});
	},
	"gonagetemall": (prepareObj, accountable, notify, id) => {
		let alldone;
		return new Promise((res, rej) => {
			Promise.all(prepareObj)
				.then(allDone => {
					alldone = allDone;
					let adder = {};
					let ulist = [];
					accountable = helper.toArray(accountable);
					notify = helper.toArray(notify);

					for (let i = 0; i < accountable.length; i++) {
						adder[accountable[i]] = { "accountable": id };
					}
					for (let i = 0; i < notify.length; i++) {
						if (adder[notify[i]]) {
							adder[notify[i]]["notify"] = id;
						} else {
							adder[notify[i]] = { "notify": id };
						}

					}

					for (let i in adder) {

						ulist.push(repairedService.updateIntoUser([i], adder[i], repairedService.addUserAccountability));
					}

					return Promise.all(ulist);

				})
				.then(d => {
					res({ "user": d, "info": alldone });
				}).catch(e => {
					sails.log(e);
					rej("error");
					return;
				});

		});
	},
	"controlWrapper": (clist, siteId, type = 1) => {
		return new Promise((res, rej) => {
			try {
				let mlist = [];
				let mhash = {};
				for (let i = 0; i < clist.length; i++) {
					mhash[clist[i]] = [];
					mlist.push(repairedService.getControllerFromParent(clist[i], siteId));
				}
				Promise.all(mlist).then(controllerInfo => {
					controllerInfo.forEach(ctrl => {
						let temp = [];
						for (let i in ctrl["data"]) {
							temp.push(i);
						}
						if (type == 1) {
							mhash[ctrl["parent"]] = temp;
						} else {
							mhash[ctrl["parent"]] = ctrl["data"];
						}

					});
					res(mhash);
				}).catch(e => {
					sails.log(e);
					rej("ereror");
				});
			} catch (e) {
				rej("err");
				sails.log(e);
			}
		});
	},

	"getChild": (ids, type, siteId) => {

		let fullInfo = {};
		return new Promise((res, rej) => {
			try {
				if (type == "region") {

					let justlis = [];
					let myList = [];
					Sites.findOne({ "siteId": siteId }).then(siteinfo => {
						if (!siteinfo) {
							rej("site donot exist");
							return;
						}
						let regions = helper.toJson(siteinfo["regions"]);
						for (let tempi in ids) {
							let id = ids[tempi];
							let mine = regions[id];
							if (!mine) {
								rej("not valid region");
								return;
							}
							let myControllers = mine["controller"];

							fullInfo[id] = {};
							justlis[tempi] = id;
							myList.push(repairedService.controlWrapper(myControllers, siteId));

						}
						return Promise.all(myList);


					}).then(controllerInfo => {
						for (let i = 0; i < controllerInfo.length; i++) {
							fullInfo[justlis[i]] = controllerInfo[i];
						}
						res(fullInfo);


					}).catch(e => {
						sails.log(e);
						return (fullInfo);
					});

				} else if (type == "controller") {

					repairedService.controlWrapper(ids, siteId).then(d => {
						res(d);
					}).catch(e => {
						sails.log(e);
						res(e);
					});

				} else if (type == "device") {

					res(ids);

				} else {
					rej("invalid request type");
					return;
				}


			} catch (e) {
				sails.log(e);
				rej({});
				return;
			}
		});

	}

};