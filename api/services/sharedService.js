const cacheService = require('./cacheService.js');
const siteService = require('./siteService.js');
const { convertTimezone } = require("../utils/global");
const flaverr = require('flaverr');
const EXPIRY = 15 * 24 * 60 * 60; // 15d

module.exports={
  "getSiteTimezone": async function ({ siteId, timezoneFormat }) {
    const cachedKey = `site:${siteId}:timezone`;
    let timezone = await cacheService.get(cachedKey);
    if (timezone) return convertTimezone(timezone, timezoneFormat);

    let siteDetails;
    try {
      siteDetails = await siteService.getSiteInfo(siteId);
    } catch (error) {
      throw flaverr({ code: 'E_INPUT_VALIDATION', message: `No site found with siteId: ${siteId}`, HTTP_STATUS_CODE: 400 });
    }
    timezone = siteDetails.timezone;
    await cacheService.setKey(cachedKey, timezone);
    await cacheService.expire(cachedKey, EXPIRY);
    return convertTimezone(timezone, timezoneFormat);
  }

}
