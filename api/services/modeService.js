const moment = require("moment-timezone");
const axios = require("axios");
moment.tz.add("Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6");
moment.tz.setDefault("Asia/Kolkata");
const influxModeService = require("./influxModeService");
const cacheService = require("./cacheService");
const eventService = require("./eventService");
const uuid = require("uuid/v4");
module.exports = {
    "setModeRep": async (req) => {
        try {
            let updatedResponse = helper.toJson(req);
            let controller = updatedResponse["extra"]["controllerId"];
            let siteId = updatedResponse["extra"]["siteId"];
            let config = updatedResponse["config"];
            const requestId = updatedResponse?.["requestId"];
            const API_TOKEN = "41f5e8d3dbae45b33f4ce040ff4ce13e23a5ddd4";

            sails.log.info(`level="INFO" app="controller-feedback" operation="modeChange" step="6/8" traceId="${requestId}" requestId="${requestId}" message="Mode change feedback received from RabbitMQ" state="in-progress" siteId="${siteId}" controllerId="${controller}" modeChangeDetail="${JSON.stringify(updatedResponse)}"`);
            if (!siteId || !config || !controller) {
                sails.log("No data recieved");
                return;
            }

            let curr_ts = moment().unix() * 1000; // shouldn't start of minute as multiple time modes can be changed within a minute
            let uniqDeviceIdAndItsModes = {},
                changedModes = {};

            for (let deviceDotParam in config) {
                let [device, param] = deviceDotParam.split(".");
                if (!uniqDeviceIdAndItsModes[device]) {
                    uniqDeviceIdAndItsModes[device] = {};
                }
                uniqDeviceIdAndItsModes[device][param] = config[deviceDotParam]; // if exist param : jt
            }

            let deviceConfigs = {}; // Used for saving modes logs in Influxdb
            for (let deviceId in uniqDeviceIdAndItsModes) {
                let deviceInfo = await Devices.findOne({ "deviceId": deviceId });
                let $createModes = [],
                    createModes;

                if (deviceInfo === undefined) {
                    continue;
                }
                deviceConfigs[deviceId] = deviceInfo;
                let oldModes = helper.toJson(deviceInfo.mode) || {};

                for (let param in uniqDeviceIdAndItsModes[deviceId]) {
                    let newMode = uniqDeviceIdAndItsModes[deviceId][param];
                    if (newMode !== oldModes[param]) {
                        changedModes[`${deviceId}.${param}`] = newMode;
                    }
                }

                // Add changed modes to device table
                $createModes.push(
                    Devices.update(
                        {
                            deviceId,
                            siteId,
                        },
                        {
                            "mode": JSON.stringify(uniqDeviceIdAndItsModes[deviceId]),
                        }
                    )
                );

                // add changed modes to modes table
                for (let param in uniqDeviceIdAndItsModes[deviceId]) {
                    let mode = uniqDeviceIdAndItsModes[deviceId][param];
                    let did = `${deviceId}.${param}`;
                    $createModes.push(
                        Mode.create({
                            did,
                            "timestamp": curr_ts,
                            "currMode": mode,
                        })
                    );
                }

                let componentIds = null;
                // ====== >> Save modes log to influxDB
                try {
                    const influxModesAdapter = new influxModeService(siteId);
                    // Generate packets to be saved.
                    const { componentList } = await influxModesAdapter.packetBuilder(uniqDeviceIdAndItsModes, deviceConfigs);
                    componentIds = componentList;
                    // Save to influxDB.
                    // await influxModesAdapter.insert(packets);
                } catch (error) {
                    sails.log.error("Error saving modes to InfluxDB!");
                    sails.log.error(error);
                }
                // << ====== Save modes log to influxDB

                // Wait for all the changes to be saved.
                try {
                    createModes = await Promise.allSettled($createModes);
                    sails.log.info(`level="INFO" app="controller-feedback" operation="modeChange" step="7/8" traceId="${requestId}" requestId="${requestId}" message="Mode change feedback saved to DynamoDB" state="completed" siteId="${siteId}" controllerId="${controller}" modeChangeDetail="${JSON.stringify(updatedResponse)}"`);
                } catch (e) {
                    sails.log.info(`level="ERROR" app="controller-feedback" operation="modeChange" step="8/8" traceId="${requestId}" requestId="${requestId}" message="Unable to save mode change feedback to DynamoDB" state="completed" siteId="${siteId}" controllerId="${controller}" modeChangeDetail="${JSON.stringify(updatedResponse)}"`);
                    sails.log.error(e);
                }

                // send changed modes to recipe service to stop / auto sync
                // relevant recipes
                try {
                    if (Object.keys(changedModes).length !== 0) {
                        await axios.post(`${sails.config.SERVICE_JT_API_URL}/m2/sync/v2/modeChange`, {
                            "controllerId": controller,
                            siteId,
                            traceId: requestId,
                            requestId: uuid(),
                            "modes": changedModes,
                            "secret": API_TOKEN,
                        });
                    }
                    sails.log.info(`level="INFO" app="controller-feedback" operation="recipePlayPauseOnModeChange" step="1/5" traceId="${requestId}" requestId="${requestId}" message="Request for Recipe Play/Pause after mode change is initiated" state="in-progress" siteId="${siteId}" controllerId="${controller}" modeChangeDetail="${JSON.stringify(updatedResponse)}"`);
                } catch (e) {
                    sails.log.info(`level="ERROR" app="controller-feedback" operation="recipePlayPauseOnModeChange" step="2/5" traceId="${requestId}" requestId="${requestId}" message="Error in placing request for Recipe Play/Pause after mode change is initiated" state="in-progress" siteId="${siteId}" controllerId="${controller}" errorMessage="${e?.message}" modeChangeDetail="${JSON.stringify(updatedResponse)}"`);
                    sails.log.error(e);
                }

                /**Socket Broadcast to send changed modes to Frontend*/
                const isFulfilled = createModes.every((val) => val.status === "fulfilled");
                let componentId;
                if (createModes[0].fulfilled === true) {
                    componentId = createModes[0].value.componentId;
                } else {
                    componentId = null;
                }

                if (!_.isEmpty(changedModes)) {
                    await setModeToCache(changedModes);
                    if (!_.isEmpty(componentIds)) await publishComponentModeDetailsToSocket(siteId, componentIds);
                }

                /**Socket Broadcast*/
                eventService.notifyJouleTrack(siteId, "public", "modes", {
                    "event": "update",
                    "data": {
                        "fulfilled": isFulfilled,
                        "componentId": componentId,
                    },
                    "eventType": isFulfilled ? "success" : "error",
                });
            }
        } catch (e) {
            sails.log.error("[Error > setModeRep] Error through network call to fetchModeDetailsAPI", e);
        }
    },
    "pidSave": function (rdata) {
        // response from controller actual response we perform action on

        let { extra, config } = rdata;
        if (!extra) return null;
        let { siteId, componentId, controlId } = extra;
        if (!config || !componentId || !controlId || !siteId) {
            return null;
        }
        config = helper.toJson(config);
        Devices.update(
            {
                "deviceId": controlId,
                "siteId": siteId,
            },
            { "pid": config, "pidOn": componentId }
        )
            .then((device) => {
                sails.log(device, " Sending this feedBaack");
                return Component.update({ "deviceId": componentId, "siteId": siteId }, { "pidOn": controlId });
            })
            .then((added) => {
                // send response to front end
                broadcastService.cast(siteId, "pidIOFeedback", "PID Config Reached ");
            })
            .catch((er) => {
                sails.log.error(er);
                return er;
            });
    },
    "multipleGetMode": function (dids) {
        // dids = ["1921_startStop"]
        // returns mode of all above devices
        let realIds = [];
        realIds = dids.map((ids) => ids.split("_")[0]);
        let idsModeMap = {};

        dids.forEach((ids) => {
            let [first, second] = this.modBusId(ids);
            idsModeMap[first] = second;
        });
        let uniqueIds = realIds.filter(function (item, i, ar) {
            return ar.indexOf(item) === i;
        });

        return new Promise((res, rej) => {
            try {
                modeService
                    .getAllDevices(uniqueIds)
                    .then((ids) => {
                        let allModes = {};
                        ids.forEach((id) => {
                            try {
                                if (id["mode"]) {
                                    let did = id["deviceId"];
                                    let actualMode = helper.toJson(id["mode"]);
                                    for (let i in actualMode) {
                                        let temp = `${did}_${i}`;
                                        allModes[temp] = actualMode[i];
                                    }
                                }
                            } catch (e) {
                                sails.log(e);
                                return res([]);
                            }
                            // result[id.deviceId+"_"+idsModeMap[id.deviceId]]=temp;
                        });

                        res(allModes);
                    })
                    .catch((e) => {
                        sails.log.error(e);
                        rej([]);
                    });
            } catch (e) {
                sails.log.error(e);
                res([]);
            }
        });
    },
    "modBusId": function (id) {
        try {
            id = String(id);
            let temp1 = id.indexOf("_");
            let first = id.slice(0, temp1);
            let second = id.slice(temp1 + 1);
            return [first, second];
        } catch (e) {
            sails.log.error(e);
            return [-1, -1];
        }
    },
    "getAllDevices": function (arrDevices) {
        return new Promise((res, rej) => {
            try {
                let prom = [];
                arrDevices = arrDevices.constructor.name == "Array" ? arrDevices : arrDevices.split(",");
                arrDevices.forEach((dev) => {
                    prom.push(Devices.findOne({ "deviceId": dev }));
                });
                Promise.all(prom)
                    .then((allDev) => {
                        res(allDev);
                    })
                    .catch((e) => {
                        sails.log.error(e);
                        rej([]);
                    });
            } catch (e) {
                sails.log.error(e);
                rej([]);
            }
        });
    },
    // find all control parameters of a controllerId cid from site siteId
    "getParentComponentInfo": (cid, siteId) => {
        return new Promise((res, rej) => {
            let controlMap = {};
            Component.find({ "siteId": siteId })
                .then((dev) => {
                    for (let i = 0; i < dev.length; i++) {
                        // for each component, if this component is attacked to controller we
                        // recieved as cid in this function,
                        if (dev[i].controllerId && dev[i].controllerId == cid) {
                            let allc = helper.toArray(dev[i].controls);
                            allc.forEach((ctrl) => {
                                let temp = helper.toJson(ctrl);
                                // change here to parseToGet(temp.feedbackExpression)
                                let xx = modeService.parseToGet(temp.expression);
                                if (!xx) {
                                    xx = temp.deviceId;
                                }
                                controlMap[temp.deviceId + "." + temp.key] = xx;
                            });
                        }
                    }
                    res(controlMap);
                    return;
                })
                .catch((e) => {
                    sails.log.error(e);
                    rej("error, no device");
                });
        });
    },
    "parseToGet": (z) => {
        z = z.split("||");
        for (let i in z) {
            // t = z[i].split("@")
            let t = z[i].split(".");
            if (t.length == 2 && isNaN(t[1])) {
                return t[0];
            }
        }
    },
    "actuator": "Actuator",
    "start": "Status",
    "frequency": "EM",
    "thermostat": "thermostat",
    "Actuator": "actuator",
    "Status": "start",
    "EM": "frequency",
    "s_SetPoint": "Actuator",
    "s_StartStop": "Status",
    "s_SetFrequency": "frequency",
    publishComponentModeDetailsToSocket,
};

/**
 * @description Set mode to cache
 * @param {Object} deviceParamChangeMode
 * @example {deviceId.param:newMode, deviceId.param: newMode}
 * @returns
 */
async function setModeToCache(deviceParamChangeMode) {
	for (const [key, mode] of Object.entries(deviceParamChangeMode)) {
		const [deviceId, commandAbbr] = key.split('.');
		const cacheKey = `mode:deviceId:${deviceId}:commandAbbr:${commandAbbr}`;
		const cacheTTL = 60 * 60 // 1 hour

		await cacheService.setKey(cacheKey,JSON.stringify({
			commandAbbr,
			deviceId,
			mode,
			timestamp: new Date().getTime() * 1000,
		}));
		await cacheService.setExpiryInSecond(cacheKey, cacheTTL);
	}
}

/**
 * @description Network call to fetchComponentDetailsAPI and notifyToSocketEvent
 * @param {String} componentIds
 * @example 'string1,string2,string3'
 * @returns null
 */
async function publishComponentModeDetailsToSocket(siteId, componentIds) {
		const authToken = `G0y2PqIAr0AZRMgYiUBQLmGxV5LIPUVlfs7f0uuvtsqgoBB3fO1KzB5vSUPj1D2P`;
		try {
			const fetchModeDetailsAPIUrl = `${sails.config.SERVICE_JT_API_URL}/m2/components/fetch-mode-detail-by-component-list-from-jouletrack-api?authToken=${authToken}`
			const fetchModeDetails = await axios.post(fetchModeDetailsAPIUrl, {
				"componentIds": componentIds,
			},{
				headers: {
					'Content-Type': 'application/json',
				}
			});
			if (_.isEmpty(fetchModeDetails.data)) {
				return;
			}

			eventService.notifyJouleTrack(
				siteId,
				"public",
				"ModeChangeUpdate",
				{
					event: 'update',
					data: fetchModeDetails.data
				}
			);
			return;
		} catch(e) {
			sails.log.error(e, 'Error while fetching the component mode details and publishing to the socket')
		}
}
