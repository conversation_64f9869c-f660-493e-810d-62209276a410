const moment = require("moment-timezone");
moment.tz.add("Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6");
moment.tz.setDefault("Asia/Kolkata");

let defJouleRecipe = {
	"recipeprocess": "0",
	"recipecomfort": "0",
	"reciperoutine": "0",
	"recipefailsafe": "1",
	"recipediagnostics": "0"
};

let defMaintenanceAction = {
	"maintainancecritical": "0",
	"maintainancehigh": "0",
	"maintainancelow": "0",
	"maintainancemedium": "0"
};

// const label = {
// 	"recipecomfort": "",
// 	"recipedejoule": "",
// 	"recipeenergy": "",
// 	"recipehvac": "",
// 	"recipeoperation": "",
// 	"maintainancecritical": "",
// 	"maintainancehigh": "",
// 	"maintainancelow": "",
// 	"maintainancemedium": ""
// };

const tsToRepeatType = {
	"1": "atc",
	"60": "hourly",
	"1440": "daily",
	"10080": "weekly",
	"41760": "monthly",
	"43200": "monthly",
	"0": "0"
};

module.exports = {

	/**
	 * Get a well formed packet for alert as input and send alert(sms/email) accordingly
	 * @function
	 * @param {string} topic dont have a use yet, maybe later
	 * @param {object} actionObject This is complete action packet with and "extra" named key which is object having is this an sms/mail and send it accordingly
	 * @param {string} origin is enum with where did this alert come from.
	 *
	 */
	"sendAlert": function (topic, actionObject, origin) {

		// actionObject = {"sourceId":"ssh_473931c89e0ae7de0d3ef891560bfbab","source":"recipe","sourceInfo":"ssh_d7169171-5078-46b5-afd5-c41d97b71a3d","uniqId":"af2e3711-b2d5-4a6e-908d-524c0ea940d5","type":"alert","runOn":"controller","nest":"{}","info":"{\"title\":\"asdf\",\"description\":\"asdf\",\"notify\":[\"<EMAIL>\",\"<EMAIL>\"],\"accountable\":[\"<EMAIL>\"],\"type\":\"alert\",\"priority\":0,\"uniqId\":\"af2e3711-b2d5-4a6e-908d-524c0ea940d5\",\"category\":[\"Energy Diagnostic\"],\"rid\":\"d7169171-5078-46b5-afd5-c41d97b71a3d\",\"source\":\"recipe\",\"siteId\":\"ssh\",\"delay\":0}","isInitiated":"true","response":*************,"extra":"{\"type\":\"mail\",\"mail\":\"<EMAIL>\"}","triggerTime":*************}

		try {

			let parsedActionObj = helper.toJson(actionObject);
			let alertInfo = helper.toJson(parsedActionObj["info"]);
			let extra = helper.toJson(parsedActionObj["extra"]);
			let tsOfExec = parsedActionObj["triggerTime"];
			if (parsedActionObj["source"]) {
				origin = parsedActionObj["source"].toLowerCase();
			}

			if (origin === "joulerecipe" || origin === "recipe") {
				this.sendRecipeAlert(alertInfo, extra, tsOfExec);
			} else if (origin == "benchmarking") {
				this.sendbenchmarking(alertInfo, extra, tsOfExec);
			} else if (origin == "maintenance") {
				this.sendmaintenance(alertInfo, extra, tsOfExec);
			}

		} catch (e) {
			sails.log.error("Error", "alertservice.sendAlert", e);
		}

	},
	/**
	 * send alert(sms/alert) coming from recipe
	 * @function
	 * @param {object} alertInfo This contains title/ description etc.
	 * @param {object} extra contains users mail/phonenumber to send alert to
	 * @param {string} tsOfExec, time of this alert execution
	 * @return {undefined} undefined
	 */
	"sendRecipeAlert": function (alertInfo, extra, tsOfExec) {

		let typeOFalert = extra["type"];

		if (typeOFalert === "sms") {
			// rate limit here
			sendsms.sendSms({
				"message": "Joule Recipe ALERT : " + alertInfo.title,
				"phone": extra["sms"]
			});
		} else if (typeOFalert === "mail") {
			sendmail.sendTemplateMail({
				"to": extra["mail"],
				"observation": alertInfo.observation,
				"siteId": alertInfo.siteId,
				"label": alertInfo.title,
				"subject": "Alert: " + alertInfo.title,
				"template": "alert",
				"tsExec": moment(tsOfExec).format("YYYY-MM-DD HH:mm:ss"),
				"from": "\"SmartJoules\" <<EMAIL>>"
			});
		} else {
			return false;
		}

	},
	/**
	 * send alert(sms/alert) coming from recipe
	 * @function
	 * @param {object} alertInfo This contains title/ description etc.
	 * @param {object} extra contains users mail/phonenumber to send alert to
	 * @param {string} tsOfExec, time of this alert execution
	 * @return {undefined} undefined
	 */
	"sendbenchmarking": function (alertInfo, extra, tsOfExec) {

		let typeOFalert = extra["type"];
		if (typeOFalert === "mail") {
			// idhar kya kya aara dekh lena and daal dena and alert wil go
			sendmail.sendTemplateMail({
				"to": extra["mail"],
				"alert": alertInfo.description,
				"label": alertInfo.title,
				"data": alertInfo,
				"extra": extra,
				"token": "xyz",
				"subject": "Alert: " + alertInfo.title,
				"template": "benchmarking",
				"tsExec": tsOfExec,
				"from": "\"SmartJoules\" <<EMAIL>>"
			});
		}
	},

	"sendmaintenance": function (alertInfo, extra, tsOfExec) {
		let typeOFalert = extra["type"];
		if (typeOFalert === "mail") {
			// idhar kya kya aara dekh lena and daal dena and alert wil go
			sendmail.sendTemplateMail({
				"to": extra["mail"],
				"alert": alertInfo.description,
				"label": alertInfo.title,
				"data": alertInfo,
				"extra": extra,
				"token": "xyz",
				"subject": "Alert: " + alertInfo.title,
				"template": "maintenance",
				"tsExec": tsOfExec,
				"from": "\"SmartJoules\" <<EMAIL>>"
			});
		}
	},

	"getPref": function (userId, siteId, labelList) {

		// return {
		//    recipeConformt : { sms : atc , mail : daily }
		//    maintainancehigh : { sms : daily , mail : 0 }
		// }

		return new Promise((res, rej) => {
			try {
				let finalObj = {};

				UserSiteMap.findOne({ "siteId": siteId, "userId": userId })
					.then(user => {
						if (!user) {
							rej({});
							return;
						}
						let { mailConfig, msgConfig } = user;
						if (!mailConfig || !msgConfig) {
							rej({});
							return;
						}
						mailConfig = helper.toJson(mailConfig);
						msgConfig = helper.toJson(msgConfig);
						if (!mailConfig || !msgConfig) {
							rej({});
							return;
						}
						let { JouleRecipe, MaintenanceAction } = mailConfig;
						JouleRecipe = { ...defJouleRecipe, ...JouleRecipe };
						MaintenanceAction = { ...defMaintenanceAction, ...MaintenanceAction };
						let total = { ...JouleRecipe, ...MaintenanceAction };
						labelList.forEach(label => {
							finalObj[label] = {
								"sms": 0,
								"mail": 0
							};
						});

						labelList.forEach(label => {
							let temp = tsToRepeatType[total[label]];
							if (!temp) temp = "0";
							finalObj[label]["mail"] = temp;
						});

						JouleRecipe = msgConfig["JouleRecipe"];
						MaintenanceAction = msgConfig["JouleRecipe"];

						JouleRecipe = { ...defJouleRecipe, ...JouleRecipe };
						MaintenanceAction = { ...defMaintenanceAction, ...MaintenanceAction };
						total = { ...JouleRecipe, ...MaintenanceAction };

						labelList.forEach(label => {
							let temp = tsToRepeatType[total[label]];
							if (!temp) temp = "0";
							finalObj[label]["sms"] = temp;
						});

						res(finalObj);
					})
					.catch(e => {
						sails.log.error(e);
						rej({});
					});
			} catch (e) {
				sails.log.error(e);
				rej({});
			}
		});
	},
	"sendToCache": function (userId, siteId, sobj) {
		// atc = {gknmh_maintainancelow : [userId] } thts what needed

		return new Promise((res, rej) => {
			try {
				sobj = helper.toJson(sobj);
				if (!sobj) {
					sails.log("No sobj");
					rej(false);
				}
				Users.findOne({ userId })
					.then(async usr => {
						if (!usr) {
							return [];
						} else {
							for (let i in sobj) {
								if (sobj[i]["mail"] != 0) {
									let temp = sobj[i]["mail"];
									await cacheService.hashMapAppend(temp, `${siteId}_${i}`, usr.email, true);
								}
								if (sobj[i]["sms"] != "0") {
									let temp = sobj[i]["sms"];
									await cacheService.hashMapAppend(temp, `${siteId}_${i}`, usr.phone, true);
								}
							}
						}
						return undefined;
					})
					.then(done => {
						res();
					})
					.catch(e => {
						rej(e);
					});
			} catch (e) {
				rej(e);
			}
		});
	},
	"addWrapper": function (userId, siteId, arr) {
		// Example calling
		// alertService.getPref("<EMAIL>","gknmh",["maintainancelow","maintainancemedium"]).then(d=>{
		//     if(d){
		//         return alertService.sendToCache("<EMAIL>","gknmh",d);
		//     }else{
		//         return undefined;
		//     }
		// }).then(x=>{
		//     console.log(x);
		// })
		return new Promise((res, rej) => {
			if (arr.length == 0) {
				res(false);
				return;
			}
			alertService
				.getPref(userId, siteId, arr)
				.then(d => {
					if (d) {
						return alertService.sendToCache(userId, siteId, d);
					} else {
						return undefined;
					}
				})
				.then(x => {
					res(x);
				})
				.catch(e => {
					res(e);
				});
		});
	},
	"recipeAlert": function (smsMail, rinfo, ts) {
		let promiseArr = [];
		let senderMail = "";
		smsMail.emails.forEach(email => {
			senderMail += email + ",";
		});
		ts = moment(ts).format("YYYY-MM-DD HH:mm");
		if (senderMail != "") {
			promiseArr.push(
				sendmail.sendTemplateMail({
					"to": senderMail,
					"alert": rinfo.description,
					"label": rinfo.title,
					"extra": rinfo.extra,
					"token": "xyz",
					"subject": "Alert: " + rinfo.title,
					"template": "alert",
					"tsExec": ts,
					"status": rinfo.status, // added
					"groups": rinfo.groupBy,
					"from": "\"SmartJoules\" <<EMAIL>>"
				})
			);
		}

		smsMail.smss.forEach(sms => {
			promiseArr.push(
				sendsms.sendSms({
					"message": "Joule Recipe ALERT : " + rinfo.title,
					"phone": sms
				})
			);
		});

	},
	"maintainanceAlert": function () { },

	/**
	 * Get users prefrence for alerts frequency
	 * @function
	 * @param {notifyUserList} notifyUserList is list of users to send alert to
	 * @param {categoryList} categoryList is category list the alert belong to
	 * @param {data} data is Object storing what to send to user
	 * @param {origin} origin is string
	 * @description so alert users mentinoed in toNotify list.
	 * The category alert belongs to is in categoryList and alert
	 * is originated in this case is from JouleRecipe.
	 * This "JouleRecipe" must be a prefrence in userSiteMap
	 */
	"getUsersAlertPrefrence": async function (notifyUserList, siteId, categoryList, data, origin) {


		return new Promise((resolve, reject) => {
			let userPrefrenceObj = {};
			try {
				if (!notifyUserList || notifyUserList.constructor.name !== "Array") {
					reject("Notifyuserlist should be an array");
				}
				if (!categoryList || categoryList.constructor.name !== "Array") {
					reject("categoryList should be an array");
				}

				let getUserInfoPromise = [];
				for (let users of notifyUserList) {
					getUserInfoPromise.push(UserSiteMap.findOne({ "userId": users, siteId }));
				}
				Promise.all(getUserInfoPromise).then(usersInfo => {
					for (let userInfo of usersInfo) {
						let username = userInfo["userId"]; // i should have if(!userInfo instead of whats written below)
						if (!username) {
							continue;
						}
						let parsedDjNotif = helper.toJson(userInfo["djNotif"]);
						let parsedmailConfig = helper.toJson(userInfo["mailConfig"]);
						let parsedmsgConfig = helper.toJson(userInfo["msgConfig"]);
						let phoneNumber = userInfo["phone"];

						if (!parsedDjNotif) {
							continue;
						}

						for (let category of categoryList) {

							if (category == "recipeoperationaldiagnostic") {
								category = "recipeoperationdiagnostic";
							}

							let mailFrequency = this.getFrequencyOfCategory(category, parsedmailConfig, origin);
							let djFrequency = this.getFrequencyOfCategory(category, parsedDjNotif, origin);
							let msgFrequency = this.getFrequencyOfCategory(category, parsedmsgConfig, origin);

							mailFrequency = parseInt(mailFrequency) ? parseInt(mailFrequency) : 0;
							djFrequency = parseInt(djFrequency) ? parseInt(djFrequency) : 0;
							msgFrequency = parseInt(msgFrequency) ? parseInt(msgFrequency) : 0;

							let username_category = `${username}_${category}`;

							userPrefrenceObj[username_category] = { username, mailFrequency, djFrequency, msgFrequency, phoneNumber };
						}
						resolve(userPrefrenceObj);
					}
				}).catch(e => {
					sails.log.error("Error alertservice.alertUsers 1 ", e);
					reject(e);
				});

			} catch (e) {
				sails.log.error("Error alertservice.alertUsers ", notifyUserList, categoryList, data, e);
				reject(e);
			}
		});

	},
	"getFrequencyOfCategory": function (category, userNotifObj, origin) {
		try {

			if (category == "default") {
				return 1;
			}

			let Notif = 0; // default = Donot Notify
			let alertOn = userNotifObj[origin][category];
			if (alertOn) {
				Notif = alertOn;
			}
			return Notif;
		} catch (e) {
			sails.log.error("Error alertservice.getFrequencyOfCategory, category not found ", category, e);
			return 0;
		}
	},

	/**
	 * Creates a custom query for fetching alerts from Dynamo's alerts table.
	 * @function
	 * @param {string} siteId - Site Id of the mentioned site
	 * @param {number} timeRange - Timestamp of when the request is made.
	 * @param {string[]} timeRange - Starting and Ending timestamps for fetching data.
	 * @param {string} type - Type of the alert, Recipe, Maintainance, HVAC etc.
	 * @param {string} limit - Number of entries to be fetched from the database.
	 * The type of timeRange depends upon limit. If limit parameter is provided,
	 * we provide timeRange as a string and return entries from the given timeRange
	 * value and limit the number of values based on the limit parameter.
	 * @returns {object}
	 */
	"createNotificationQuery": (siteId, timeRange, type, limit, isCustom = false) => {

		if (limit) {
			if (!siteId || !type || !timeRange) {
				sails.log("Error: alertService.createNotificationQuery");
				return new Error("Missing parameters");
			}

			return {
				"where": {
					"siteId": siteId + "_" + type,
					"timestamp": {
						"lte": `${timeRange}`
					},
				},
				"limit": limit,
				"sort": "-1"
			};
		} else {
			if (!siteId || !timeRange || !type) {
				sails.log("Error: alertService.createNotificationQuery");
				return new Error("Missing parameters");
			}
			if (isCustom) {
				return {
					"where": {
						"siteId": siteId + "_" + type,
						"timestamp": {
							"between": timeRange
						}
					},
					"limit": 2000,
					"sort": "-1"
				};
			} else {
				return {
					"where": {
						"siteId": siteId + "_" + type,
						"timestamp": {
							"between": timeRange
						},
					},
					"limit": 2000,
					"sort": "-1"
				};
			}
		}
	},

	/**
	 * Find all types of alerts present in the table.
	 * @param {string} siteId - Site Id of the metioned site.
	 * @param {string[]} timeRange - Starting and Ending timestamps for fetching data.
	 * @returns {array}
	 */
	"findAllAlerts": async (siteId, userId, timeRange, userFlag, isCustom) => {
		if (!siteId || !timeRange || !userId) throw "Missing Params";

		if (userFlag == "1") {
			siteId = `${siteId}_${userId}`;
		}

		try {
			let recipeAlerts = alertService.findAlerts(siteId, timeRange, "RECIPE", isCustom);

			let maintainanceAlerts = alertService.findAlerts(siteId, timeRange, "MAINTAINANCE", isCustom);

			let hvacAlerts = alertService.findAlerts(siteId, timeRange, "HVAC", isCustom);

			let resultAlerts = await Promise.all([
				recipeAlerts,
				maintainanceAlerts,
				hvacAlerts
			]);

			return resultAlerts.reduce((res, arr) => res.concat(arr), []);

		} catch (err) {
			sails.log("Unable to fetch data from db");
			throw err;
		}
	},

	/**
	 * Find alerts of a particular type.
	 * @param {string} siteId - Site Id of the mentioned site.
	 * @param {string[]} timeRange - Starting and Ending timestamps for fetching data.
	 * @param {string} type - Mention the type of alerts you want to fetch.
	 * @returns {array}
	 */
	"findAlerts": async (siteId, timeRange, type, isCustom) => {
		if (!siteId || !timeRange || !type) throw "Missing Params";

		let query;

		try {
			query = await alertService.createNotificationQuery(siteId, timeRange, type, undefined, isCustom);
		} catch (err) {
			throw err;
		}

		try {
			let alerts = await Alert.find(query);
			return alerts;
		} catch (err) {
			sails.log(`Error while fetching ${type} alerts`);
			throw err;
		}
	},

	/**
	 * Find a limited number of alerts of a particular type from the given timestamp.
	 * @param {string} siteId - Site Id of the mentioned site.
	 * @param {string} timeRange - Timestamp to filter entries.
	 * @param {string} limit - Maximum number of entries to be fetched from the table.
	 * @param {string} type - Type of the alert that is supposed to be fetched.
	 * @returns {array}
	 */
	"findRecentAlerts": async (siteId, timeRange, limit, type) => {
		if (!siteId || !limit || !type) throw "Missing Params";

		let query;

		try {
			query = alertService.createNotificationQuery(siteId, timeRange, type, limit, false);

			let data = await Alert.find(query);
			return data;
		} catch (err) {
			throw err;
		}
	},

	/**
	 * Creates an entry in the database.
	 * @param {object} alertObject - An object that has key values pairs according
	 * to the data model.
	 * @returns {array}
	 */
	"saveAlert": async (alert, siteId, alertType, notifyList) => {
		try {
			let tableEntries = alertService.createAlerts(alert, siteId, alertType, notifyList);

			for (let entry of tableEntries) {
				await Alert.create(entry);
			}
		} catch (err) {
			throw err;
		}
	},

	/**
	 * Find limited number of alerts from a particular time.
	 * @param {string} siteId - Site Id of the mentioned site.
	 * @param {string} timeRange - Timestamp to filter entries.
	 * @param {string} limit - Maximum number of entries to be fetched from the table.
	 * @returns {array}
	 */
	"fetchLatestAlerts": async (siteId, timeRange, limit) => {
		if (!siteId || !timeRange || !limit) throw "Missing Params";

		let fetchedAlerts = [];

		try {
			let recipeAlerts = alertService.findRecentAlerts(siteId, timeRange, limit, "RECIPE");
			let maintainanceAlerts = alertService.findRecentAlerts(siteId, timeRange, limit, "MAINTAINANCE");
			let hvacAlerts = alertService.findRecentAlerts(siteId, timeRange, limit, "HVAC");
			return fetchedAlerts
				.concat(await recipeAlerts)
				.concat(await maintainanceAlerts)
				.concat(await hvacAlerts);
		} catch (err) {
			throw err;
		}
	},

	"createAlerts": (alert, siteId, alertType, notifyList) => {
		if (!alert || !siteId || !alertType) {
			sails.log("Broken API, RecipeService.");
			sails.log("Error while creating alerts.");
			throw "Error while creating alerts.";
		}

		let entriesList = [];

		if (!notifyList || notifyList.length == 0) {
			siteId = siteId + "_" + alertType;
			alert["siteId"] = siteId;
			entriesList.push(alert);
			return entriesList;
		} else {
			let idList = [];
			notifyList.forEach(notifier => {
				let newId = siteId + "_" + notifier + "_" + alertType;
				idList.push(newId);
			});

			let siteAlert = { ...alert };

			for (let i = 0; i < idList.length; i++) {
				let newAlert = { ...alert };
				newAlert["siteId"] = idList[i];
				entriesList.push(newAlert);
			}

			siteId = siteId + "_" + alertType;
			siteAlert["siteId"] = siteId;
			entriesList.push(siteAlert);

			return entriesList;
		}
	},
	"isValidAlertType": function (alertType) {
		alertType = String(alertType);
		if (alertType === "site" || alertType === "my") {
			return true;
		} else {
			return false;
		}
	},

	"formatAlerts": function (alerts) {
		let formattedAlerts = [], mapOfAlerts = {};
		if (alerts.constructor.name !== "Array") {
			return;
		}

		for (let alert of alerts) {
			let { eventId, timestamp, body } = alert;

			body = helper.toJson(body);
			let priority = body["priority"] !== undefined ? body["priority"] : "unknown";

			if (!isNaN(parseInt(priority))) {
				let digitCriticalTOText = {
					"0": "low",
					"1": "medium",
					"2": "high",
					"3": "critical"
				};
				priority = digitCriticalTOText[priority] ? digitCriticalTOText[priority] : "unknown";
			}
			parseInt(timestamp) ? timestamp = parseInt(timestamp) : null;

			if (!mapOfAlerts[eventId]) {
				mapOfAlerts[eventId] = {
					"title": alert["title"],
					"sub_category": helper.toArray(alert["sub_category"]),
					"alertType": alert["alertType"],
					"description": alert["description"],
					eventId,
					priority,
					"time": {},
				};
			}
			let startTime = moment(timestamp).startOf("day").format("YYYY-MM-DD");
			let fts = helper.getts(timestamp);

			if (!mapOfAlerts[eventId]["time"][startTime]) {
				mapOfAlerts[eventId]["time"][startTime] = [];
			}
			mapOfAlerts[eventId]["time"][startTime].push(fts);
		}
		for (let eventId in mapOfAlerts) {
			formattedAlerts.push(mapOfAlerts[eventId]);
		}
		return formattedAlerts;
	}
};
