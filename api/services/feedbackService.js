//  ===========STATUS CODES FROM CONTROLLER AND ITS mEANING=======
// "0"     Not  Executed
// "1"     Executed
// "2"     reached controller
// "3"     error
// "4"   command before and after are same
// "5"	 mode error
// "6"   cannot validate  feedback of command executed bcz of no data
// "7"   reached joule box
// "-1"  reached joule box
// "-2"  initiated
//  "8"  Wrong mode i.e command recieved from mode is different from what mode the parameter is set on.

/* eslint-disable no-useless-escape */
const moment = require('moment-timezone');
const dbHelper = require('./dbHelper');

moment.tz.add('Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6');
moment.tz.setDefault('Asia/Kolkata');

// dict_to_store_unsavedCmdReachedJB
// dict_to_store_unsavedCmdReachedCtrl
let dict_to_store_unsavedCmdReachedJB = {};
let dict_to_store_unsavedCmdReachedCtrl = {};

module.exports = {

  recieverFeedback: (topic, payload) => {
    try {
      payload = helper.toJson(payload);
      let {
        uniqId,
        status,
      } = payload;
      if (!uniqId) {
        uniqId = payload.key;
      }
      let actionFindBy = {
        uniqId,
      };
      const current_time = moment().unix() * 1000;
      const statusDict = {
        0: {
          key: 'response', value: '-1', message: 'Command Failed', executed: false,
        },
        1: {
          key: 'response', value: current_time, message: 'Command Executed', executed: true,
        },
        2: {
          key: 'reachedCtrl', value: current_time, message: 'Command Reached Controller', executed: true,
        },
        3: {
          key: 'response', value: '-1', message: 'Error Processing Command', executed: false,
        },
        4: {
          key: 'response', value: current_time, message: 'Success', executed: true,
        },
        5: {
          key: 'response', value: '-1', message: 'Error Processing Command', executed: false,
        },
        6: {
          key: 'response', value: '-1', message: 'Not able to execute command due to null/no data', executed: false,
        },
        7: {
          key: 'reachedJb', value: current_time, message: 'Reached Joulebox', executed: true,
        },
        8: {
          key: 'response', value: '-1', message: 'Error. Dejoule Mode mismatch', executed: false,
        },
        '-1': {
          key: 'reachedJb', value: current_time, message: 'Reached JouleBox', executed: true,
        },
        '-2': { key: 'response', value: current_time },
        9: {
          key: 'response', value: '-1', message: 'Error. Command Value outside min-max range', executed: false,
        },
        12: {
          key: 'response', value: '-1', message: 'Error. Command Originated from controller but cant be published due to IOT gateway down', executed: false,
        },
        13: {
          key: 'response', value: '-1', message: 'Error. Incorrect Feedback Expression', executed: false,
        },
      };
      const siteId = topic.split('/')[0];

      try {
        if (status === '1' || status === '4') {
          const currTimeUnix = current_time / 1000;
          dbHelper.updateLastExecutedCommand(uniqId, currTimeUnix).then(() => {
          }).catch((e) => {
            sails.log.error(e);
          });
        }
        const eventRoom = `jt_${siteId}_public`;
        const sockettopic = 'commandfeedback';
        const message = statusDict[status].message || 'Unknown Status Response';
        const isExecuted = statusDict[status].executed || false;
        const resp = { message, isExecuted };
        sails.sockets.broadcast(eventRoom, sockettopic, resp);
      } catch (e) {
        sails.log.error(e);
      }

      Actions.findOne(actionFindBy).then(async (action) => {
        if (action) {
          const updateObj = {
            rcCode: status,
          };
          const statusType = statusDict[status];
          updateObj[statusType.key] = statusType.value;

          actionFindBy = {
            triggerTime: action.triggerTime,
            sourceId: action.sourceId,
          };
          const commandPacket = helper.toJson(action.info);

          const packetForFrontEnd = {
            deviceId: commandPacket.deviceId,
            param: commandPacket.changesetpoint,
            value: commandPacket.value,
            componentId: commandPacket.component,
            status,
          };
          try {
            const socketID = helper.toJson(action.extra).socketId;
            if (socketID) {
              eventService.notifyUser(socketID, 'commandFeedback', packetForFrontEnd);
            }
          } catch (e) {
            // well we dont have socket nothing bad
          }
          return Actions.update(actionFindBy, updateObj);
        }
        if (status == '2') {
          dict_to_store_unsavedCmdReachedCtrl[uniqId] = current_time;
          dict_to_store_unsavedCmdReachedCtrl = fixlatency(dict_to_store_unsavedCmdReachedCtrl, 40);
        } else if (status == '-1' || status == '7') {
          dict_to_store_unsavedCmdReachedJB[uniqId] = current_time;
          dict_to_store_unsavedCmdReachedJB = fixlatency(dict_to_store_unsavedCmdReachedJB, 40);
        } else {
          sails.log.error('feedback arrived before ', action);
        }
        return {};
      }).then((updateObject) => {
        sails.log('Update rsponse');
      }).catch((e) => {
        sails.log.error(e);
      });
    } catch (e) {
      sails.log.error(e);
    }
  },
  initAction: (action) => {
    try {
      action = helper.toJson(action);
      action.info = JSON.stringify(action.info);
      action.nest = JSON.stringify(action.nest);
      action.siteId = action.sourceId.split('_')[0];

      Actions.create(action).then((actionCreateRes) => {
        const { uniqId } = actionCreateRes;
        delete (dict_to_store_unsavedCmdReachedJB[uniqId]);
        delete (dict_to_store_unsavedCmdReachedCtrl[uniqId]);
      }).catch((e) => {
        sails.error.log(e);
        return false;
      });
    } catch (e) {
      sails.log.error(e);
    }
  },
  wrapperFeedbackFromJTCommand(topic, payload) {
    try {
      const parsedPayload = helper.toJson(payload);

      const {
        status,
      } = parsedPayload;
      if (status == '-2') {
        // in case we need to add this command into actions table, this is init functino
        this.initAction(parsedPayload);
      } else {
        // if its an feedback and not init into action table request
        this.recieverFeedback(topic, parsedPayload);
      }
    } catch (e) {
      sails.log.error(e);
    }
  },
  /**
   * In case of recipe from controller executed a alert
   */
  async wrapperFeedbackForAlertFromRecipe(topic, payload) {
    // payload = { 'uniqId': 'af2e3711-b2d5-4a6e-908d-524c0ea940d5', 'isInitiated': '*************', 'nest': {}, 'reachedCtrl': '0', 'reachedJb': '0', 'response': 'false', 'runOn': 'server', 'source': 'recipe', 'sourceId': 'ssh_473931c89e0ae7de0d3ef891560bfbab', 'sourceInfo': 'ssh_d7169171-5078-46b5-afd5-c41d97b71a3d', 'triggerTime': '*************', 'type': 'alert', 'createdAt': '2019-07-19 14:05', 'info': { 'title': 'asdf', 'description': 'asdf', 'notify': ['<EMAIL>', '<EMAIL>'], 'accountable': ['<EMAIL>'], 'type': 'alert', 'priority': 0, 'uniqId': 'af2e3711-b2d5-4a6e-908d-524c0ea940d5', 'category': ['Energy Diagnostic'], 'rid': 'd7169171-5078-46b5-afd5-c41d97b71a3d', 'source': 'recipe', 'siteId': 'ssh', 'delay': 0 } }

    try {
      const parsedPayload = helper.toJson(payload);
      let {
        uniqId, nest, source, sourceId, sourceInfo, triggerTime, info,
      } = parsedPayload;

      if (!triggerTime) {
        triggerTime = moment().unix() * 1000;
      }
      triggerTime = parseInt(triggerTime);
      const currentTime = moment().startOf('m').unix() * 1000;

      // if (sourceId !== "ssh_987f68aca8dd85a01d99505e0dbc89ed") {
      // 	return;
      // }
      const alertInfo = helper.toJson(info);
      let { siteId } = alertInfo;
      let toNotify = helper.toJson(alertInfo.notify);
      let criticality = alertInfo.priority;
      let smslist = helper.toJson(alertInfo.smslist);
      let categoryList = helper.toJson(alertInfo.category);

      if (!categoryList) {
        const recipeId = sourceInfo.split('_')[1];
        const RecipeInfo = await Recipe.findOne({ rid: recipeId });
        categoryList = helper.toJson(RecipeInfo.recipelabel);
        categoryList = categoryList.map((category) => `recipe${category.replace(/ /g, '').toLowerCase()}`);
        if (!toNotify) {
          toNotify = helper.toJson(RecipeInfo.actionable)[0].notify;
        }
        sails.log.error('Please add category list in rid', recipeId);
      }

      if (!isNaN(criticality - 1)) {
        // hack to find if crticality is ( "1" or 1 ) or some text like "low"/"high"
        // if criticality is 0/1/2/3 , it should end up in this ip
        const digitCriticalTOText = {
          0: 'low',
          1: 'medium',
          2: 'high',
          3: 'critical',
        };
        if (!(digitCriticalTOText[criticality])) {
          criticality = 'medium'; // default
        } else {
          criticality = digitCriticalTOText[criticality];
        }
      }

      if (!criticality) {
        criticality = 'low';
        // default low
      }
      if (!smslist) {
        smslist = [];
      }

      if (!alertInfo || !sourceId || !sourceInfo) {
        return false;
      }
      if (!nest) {
        nest = {};
      }

      if (!toNotify || !Array.isArray(toNotify)) {
        toNotify = [];
      }
      if (!siteId) {
        siteId = topic.split('/')[0];
      }

      const recipeId = sourceInfo.split('_')[1];
      const topicToPublish = `${siteId}/sendalerts/0001/${source}`;

      const newActionObject = {
        sourceId,
        source,
        sourceInfo,
        uniqId,
        type: 'alert',
        runOn: 'controller',
        nest: JSON.stringify(nest),
        info: JSON.stringify(alertInfo),
      };
      const data = { ...alertInfo };

      const usersAlertPrefrence = await alertService.getUsersAlertPrefrence(toNotify, siteId, categoryList, data, 'JouleRecipe');

      const alertTypeMap = {
        joulerecipe: 'RECIPE',
        recipe: 'RECIPE',
        benchmarking: 'BENCHMARKING',
        maintenance: 'MAINTAINANCE',
      };
      const alertTypePrefix = alertTypeMap[source] ? alertTypeMap[source] : 'UNKNOWN';

      this.addAlertToDb(triggerTime, `${siteId}_${alertTypePrefix}`, alertInfo, categoryList, recipeId, alertTypePrefix); // global alert
      let globalLastAlert;
      for (const username_category in usersAlertPrefrence) {
        const usernameCategoryInfo = username_category.split('_');
        const category = usernameCategoryInfo.splice(-1);
        const username = usernameCategoryInfo.splice(0, usernameCategoryInfo.length).join('_'); // just in case username have _ in it
        const usersPreference = usersAlertPrefrence[username_category];
        // let userPhoneNumber = usersAlertPrefrence[username_category]["phoneNumber"];
        // let _msgFrequency; // use later
        let _mailFrequency;
        try {
          _mailFrequency = await this.willAlertGoBasedOnCriticality(recipeId, usersPreference.mailFrequency, criticality, currentTime, globalLastAlert);
          // _msgFrequency = await this.willAlertGoBasedOnCriticality(recipeId, usersPreference["msgFrequency"], criticality, currentTime);
        } catch (e) {
          sails.log.error(e);
        }

        if (_mailFrequency == 0) {
          // pass, nothing to do.. User Didnt opted for this email
        } else if (_mailFrequency == 1) {
          // create alert and send
          globalLastAlert = 1;
          const _triggerTime = moment(triggerTime).startOf('m').unix() * 1000;
          const isInitiated = 'true';
          this.addAlertToDb(_triggerTime, `${siteId}_${username}_${alertTypePrefix}`, alertInfo, category, recipeId, alertTypePrefix); // create alert Mail in db to display
          this.addActionToDb(_triggerTime, newActionObject, { type: 'mail', mail: username }, isInitiated);
          this.sendAlert(topicToPublish, newActionObject, _triggerTime, { type: 'mail', mail: username }, isInitiated);
        } else {
          const isInitiated = 'false';
          // create alert for later sending
          this.addAlertToDb(_mailFrequency, `${siteId}_${username}_${alertTypePrefix}`, alertInfo, category, recipeId, alertTypePrefix);
          this.addActionToDb(_mailFrequency, newActionObject, { type: 'mail', mail: username }, isInitiated);
          sails.log('Alert created for ', _mailFrequency);
        }
      }
    } catch (e) {
      sails.log.error('Error in feedbackservice.wrapperFeedbackForAlertFromRecipe: ', payload, e);
    }
  },
  async wrapperFeedbackFromRecipe(topic, payload) {
    try {
      const parsedPayload = helper.toJson(payload);
      const {
        status,
      } = parsedPayload;

      if (status == '-2') {
        // -2 means this is init command, we have to put this in database, so check if iss command ka pehle koi feedback toh nahi aaya hua, so that if there is , just get it from dict and put it here.
        const { uniqId } = parsedPayload;
        const tsOfExecReachedJB = dict_to_store_unsavedCmdReachedJB[uniqId];
        const tsOfExecReachedCtrl = dict_to_store_unsavedCmdReachedCtrl[uniqId];

        if (tsOfExecReachedJB) {
          parsedPayload.reachedJb = tsOfExecReachedJB;
        }
        if (tsOfExecReachedCtrl) {
          parsedPayload.reachedCtrl = tsOfExecReachedCtrl;
        }
        this.initAction(parsedPayload);
      } else {
        this.recieverFeedback(topic, parsedPayload);
      }
    } catch (e) {
      sails.log.error(e);
    }
  },
  /**
   * Add action to database and send alert(sms/email) if its time is now
   * @function
   * @param {unixtimestamp} triggertime Time when this alert came
   * @param {object} dataPacket alert info.Where did it come from, where to send, what to send.
   * @param {object} extra contains users mail/phonenumber to send alert to
   */
  addActionToDb(triggertime, dataPacket, extra, isInitiated) {
    // have to add isInitiated, response, triggerTime, extra,

    try {
      const _dataPacket = {
        ...dataPacket,
        isInitiated,
        response: triggertime,
        extra: JSON.stringify(extra),
        triggerTime: triggertime,
      };
      _dataPacket.siteId = _dataPacket.sourceId.split('_')[0];
      Actions.create(_dataPacket).then((actionPacker) => { }).catch((e) => { sails.log.error(e); });
    } catch (e) {
      sails.log.error('Error', 'feedbackservice.addActionToDb', e);
    }
  },
  /**
   * Add alert to database
   * @function
   * @param {unixtimestamp} triggertime Time when this alert came
   * @param {string} siteId_user_origin primary key for alerts table, siteName_username_RECIPE
   * @param {object} alertInfo Description, title etc of alert
   * @param {string} recipeId Recipe Id, who made this alert
   * @param {string[]} category array of category this alert belong to
   * @param {string} eventId String representing what was parent of event like recipe/benchmarking etc
   */
  addAlertToDb(triggertime, siteId_user_origin, alertInfo, category, eventId, alertType) {
    if (!alertType) {
      alertType = 'UNKNOWN';
    }

    const newAlertObject = {
      siteId: siteId_user_origin,
      timestamp: triggertime,
      alertType,
      body: JSON.stringify(alertInfo),
      description: alertInfo.description ? alertInfo.description : 'Description Not available',
      eventId,
      readStatus: 'unread',
      sub_category: JSON.stringify(category),
      title: alertInfo.title ? alertInfo.title : 'Title',

    };

    Alert.create(newAlertObject).then((alertPacket) => { }).catch((e) => { sails.log(e); });
  },
  /**
   * Service function to create an alert packet and send to a topic
   * @param {string} topicToPublish is the topic to send alert to
   * @param {object} dataPacket is a object with info about alert
   * @param {unixtimestamp} triggertime is timestamp of sending alert
   * @param {object} extra contains users mail/phonenumber to send alert to
   * @param {string} isInitiated is true/false for is this alert being send or not to user based on trigger time
   */

  sendAlert(topicToPublish, dataPacket, triggertime, extra, isInitiated) {
    const _dataPacket = {
      ...dataPacket,
      isInitiated,
      response: triggertime,
      extra: JSON.stringify(extra),
      triggerTime: triggertime,
    };
    eventService.publish(topicToPublish, JSON.stringify(_dataPacket));
  },
  /**
   * Find alert should be sent to user or not on bases of preference and criticality
   * @param {string} recipeId the uniq recipe identifier for this recipe
   * @param {string} userFrequencyFromHisConfig is user's alert preference according to setting
   * @param {string} criticality a recipe config classified into : high, medium or low
   * @param {int} currentTime is current time in unix
   * @return {int} 0/1/ts : 0 means no alert, 1 means send alert, ts is timeStamp to send alert on.
   */
  async willAlertGoBasedOnCriticality(recipeId, userFrequencyFromHisConfig, criticality, currentTime, globalLastAlert) {
    const crticalityToTime = {
      medium: 360,
      low: 1440,
      critical: 1,
      high: 30,
    };

    if (criticality.constructor.name !== 'String') {
      criticality = 'medium';
      sails.log.error('Invalid criticality ! Defaulted to medium');
    }
    try {
      userFrequencyFromHisConfig = parseInt(userFrequencyFromHisConfig);
      currentTime = parseInt(currentTime);
    } catch (e) {
      throw new Error('Unretriavable Error feedbackservice.willlAlertGoBasedOnCriticality. Cannot parse to int');
    }
    if (recipeId.constructor.name !== 'String') {
      throw new Error('Recipe Id should be string');
    }
    let lastAlertFromThisRecipe;
    try {
      lastAlertFromThisRecipe = await cacheService.getKey(`cacheAlert_${recipeId}`);
    } catch (e) {
      // prolly didnt find the key
      sails.log(e);
      lastAlertFromThisRecipe = undefined;
    }
    if (lastAlertFromThisRecipe) {
      try {
        lastAlertFromThisRecipe = parseInt(lastAlertFromThisRecipe);
      } catch (e) {
        sails.log.error(e, ' feedbackservice.willAlertGoBasedOnCriticality cannot parse lastAlertFromThisRecipe to int');
        lastAlertFromThisRecipe = undefined;
      }
    }

    if (userFrequencyFromHisConfig === 0) {
      return 0; // user haven't opeted for this alert. donot create/update alert related any info
    }
    if (globalLastAlert === 1) {
      return 1;
    }

    let alertFreq = 1;
    if (criticality in crticalityToTime) {
      alertFreq = crticalityToTime[criticality];
    } else {
      sails.log.error('Invalid criticality given at feedbackservice, recovered to medium');
      alertFreq = 360; //
    }
    if (lastAlertFromThisRecipe) {
      if (currentTime - lastAlertFromThisRecipe > alertFreq * 60000) {
        // freq * 60000 to convert minute to milliseconds bcz thats what current - lastalert will return
        // if it comes here, it means it time to send alert
        // it shoukd not come here though bcz expire should have deleted this key by now
        await cacheService.setKey(`cacheAlert_${recipeId}`, currentTime);
        await cacheService.expire(`cacheAlert_${recipeId}`, alertFreq * 60); // multiply 60 to chenge seconds to minutes
        return 1;
      }
      return helper.addMinuteInChunks(lastAlertFromThisRecipe, alertFreq) * 1000;
    }
    await cacheService.setKey(`cacheAlert_${recipeId}`, currentTime);
    await cacheService.expire(`cacheAlert_${recipeId}`, alertFreq * 60);
    return 1; // 1 means create alert in db + send to user right now
  },

};

// usage fixlatency({"a":2,"b":2,"c":3,"d":4,"e":5},3) : will return { e: 5, d: 4, c: 3 } top 3 newest vals
function fixlatency(dictionary, count) {
  let fillerArray = new Array(count);
  for (const id in dictionary) {
    const temp_dict = {};
    temp_dict.key = id;
    temp_dict.val = dictionary[id];
    fillerArray.push(temp_dict);
  }
  fillerArray = fillerArray.sort((obj1, obj2) => { obj2.val - obj1.val; });
  const slicedDict = fillerArray.slice(0, count);
  const responseDict = {};
  for (const index in slicedDict) {
    responseDict[slicedDict[index].key] = slicedDict[index].val;
  }
  return responseDict;
}
