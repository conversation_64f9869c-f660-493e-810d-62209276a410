let defaultdjNotif = {
	"JouleRecipe": {
		"recipeprocess": "0",
		"recipecomfort": "0",
		"reciperoutine": "0",
		"recipefailsafe": "0",
		"recipediagnostics": "0"

	},
	"MaintenanceAction": {
		"maintainancecritical": "0",
		"maintainancehigh": "0",
		"maintainancelow": "0",
		"maintainancemedium": "0"
	},
	"hvacNotif": {
		"command": "0",
		"mode": "0",
		"config": "0"
	}
};
let defaultmailConfig = {
	"JouleRecipe": {
		"recipeprocess": "0",
		"recipecomfort": "0",
		"reciperoutine": "0",
		"recipefailsafe": "1",
		"recipediagnostics": "0"

	},
	"MaintenanceAction": {
		"maintainancecritical": "0",
		"maintainancehigh": "0",
		"maintainancelow": "0",
		"maintainancemedium": "0"
	},
	"hvacNotif": {
		"command": "0",
		"mode": "0",
		"config": "0"
	}
};
let defaultmsgConfig = {
	"JouleRecipe": {
		"recipeprocess": "0",
		"recipecomfort": "0",
		"reciperoutine": "0",
		"recipefailsafe": "0",
		"recipediagnostics": "0"
	},
	"MaintenanceAction": {
		"maintainancecritical": "0",
		"maintainancehigh": "0",
		"maintainancelow": "0",
		"maintainancemedium": "0"
	},
	"hvacNotif": {
		"command": "0",
		"mode": "0",
		"config": "0"
	}
};
let defaultdj = {

	"JouleRecipeMode": "1",
	"JouleRecipeThermostatMode": "1",
	"MaintenanceMode": "1",
	"OverrideMode": "1",
	"UIMode": "1",
	"DiagnosticGraph": "1"
};


module.exports = {
	"delusermap": (userId) => {

		return new Promise((res, rej) => {

			UserSiteMap.find({ "userId": userId }).then(allU => {

				let darr = allU.map(user => {
					sails.log({ "userId": user["userId"], "siteId": user["siteId"] });
					return UserSiteMap.destroy({ "userId": user["userId"], "siteId": user["siteId"] });
				});

				return Promise.all(darr);
			}).then(x => {
				res("good");
			})
				.catch(e => {
					sails.log(e);
					rej("error");
				});
		});
	},
	"changeSettingfunc": (siteId, prevConfig, newConfig, userInfo) => {

		// usrInfo should have mail and phone : {mail : "mail.com" , phone : "*********"}
		let timeMap = {
			"1": "atc",
			"60": "hourly",
			"1440": "daily",
			"10080": "weekly",
			"41760": "monthly",
			"43200": "monthly",
			"0": "0"
		};
		return new Promise(async (res, rej) => {
			try {
				let { dj, djNotif, mailConfig, msgConfig } = helper.toJson(prevConfig);

				let { email, phone } = userInfo;
				dj = helper.toJson(dj);
				djNotif = helper.toJson(djNotif);
				mailConfig = helper.toJson(mailConfig);
				msgConfig = helper.toJson(msgConfig);

				dj = { ...defaultdj, ...dj };
				djNotif = { ...defaultdjNotif, ...djNotif };
				mailConfig = { ...defaultmailConfig, ...mailConfig };
				msgConfig = { ...defaultmsgConfig, ...msgConfig };
				// let ndj = newConfig["dj"];
				let ndjNotif = newConfig["djNotif"];
				let nmailConfig = newConfig["mailConfig"];
				let nmsgConfig = newConfig["msgConfig"];
				sails.log(dj);

				let djnotif = { ...djNotif["JouleRecipe"], ...djNotif["MaintenanceAction"] };
				let ndjnotif = { ...ndjNotif["JouleRecipe"], ...ndjNotif["MaintenanceAction"] };
				for (let i in djnotif) {
					let label = djnotif[i];
					if (label != "0") {
						try {
							let userListForLabel = await cacheService.hashMapGet(timeMap[label], `${siteId}_${i}`);
							if (userListForLabel.includes(email)) {
								// delete me from (atc) and add me to (hourly)
								await cacheService.hashMapRemove(timeMap[label], `${siteId}_${i}`, email);

								if (ndjnotif[i] != "0") {
									await cacheService.hashMapAppend(timeMap[ndjnotif[i]], `${siteId}_${i}`, email);
								}

							} else {
								if (ndjnotif[i] != "0") {
									await cacheService.hashMapAppend(timeMap[ndjnotif[i]], `${siteId}_${i}`, email);
								}
							}
						} catch (e) {
							sails.log(e);
							sails.log("User preference change error 1");
						}
					}
				}

				let fullmailconfig = { ...mailConfig["JouleRecipe"], ...mailConfig["MaintenanceAction"] };
				nmailConfig = { ...nmailConfig["JouleRecipe"], ...nmailConfig["MaintenanceAction"] };

				for (let i in fullmailconfig) {
					let label = fullmailconfig[i];
					try {
						if (label != "0") {
							let userListForLabel = await cacheService.hashMapGet(timeMap[label], `${siteId}_${i}`);
							if (userListForLabel.includes(email)) {
								// delete me from (atc) and add me to (hourly)
								try {
									sails.log(timeMap[label], label, `${siteId}_${i}`, email, "&&&&");
									await cacheService.hashMapRemove(timeMap[label], `${siteId}_${i}`, email);
								} catch (e) {
									sails.log("Why heree");
								}
								if (nmailConfig[i] != "0") {
									try {
										await cacheService.hashMapAppend(timeMap[nmailConfig[i]], `${siteId}_${i}`, email);
									} catch (e) {
										sails.log(e);
									}

								}
							}
						} else {
							if (nmailConfig[i] != "0") {
								try {
									await cacheService.hashMapAppend(timeMap[nmailConfig[i]], `${siteId}_${i}`, email);
								} catch (e) {
									sails.log(e);
								}
							}
						}
					} catch (e) {
						sails.log(e);
						sails.log("User preference change error 2");
					}

				}

				let fullmsgconfig = { ...msgConfig["JouleRecipe"], ...msgConfig["MaintenanceAction"] };
				nmsgConfig = { ...nmsgConfig["JouleRecipe"], ...nmsgConfig["MaintenanceAction"] };
				for (let i in fullmsgconfig) {
					try {
						let label = fullmsgconfig[i];
						if (label != "0") {
							let userListForLabel = await cacheService.hashMapGet(timeMap[label], `${siteId}_${i}`);
							if (userListForLabel.includes(phone)) {
								// delete me from (atc) and add me to (hourly)
								try {
									await cacheService.hashMapRemove(timeMap[label], `${siteId}_${i}`, phone);
								} catch (e) {
									sails.log(e);
									sails.log("up 1");
								}
								if (nmsgConfig[i] != "0") {
									try {
										await cacheService.hashMapAppend(timeMap[nmsgConfig[i]], `${siteId}_${i}`, phone);
									} catch (e) {
										sails.log(e);
										sails.log("up 1");
									}
								}
							}
						} else {
							if (nmsgConfig[i] != "0") {
								await cacheService.hashMapAppend(timeMap[nmsgConfig[i]], `${siteId}_${i}`, phone);
							}
						}
					} catch (e) {
						sails.log(e);
						sails.log("User preference change error 3");
					}

				}

				res(true);

			} catch (e) {
				sails.log.error(e);
				res(false);
			}
		});

	},
	"getPoliciesOfUser": (userId) => {

		return new Promise((res, rej) => {
			if (!userId) {
				res({});
			}
			UserSiteMap.find({ "userId": userId }).then(usersmap => {
				let userPomise = [];
				if (usersmap.length > 0) {
					for (let i = 0; i < usersmap.length; i++) {
						userPomise.push(Role.findOne({ "roleName": usersmap[i]["role"] }));
					}

				}
				return Promise.all(userPomise);
			}).then(info => {
				let allPolicies = [];
				if (info) {
					sails.log(info);
					for (let i = 0; i < info.length; i++) {
						let plc = helper.toJson(info[i]["policies"]);
						allPolicies.push({
							"roleName": info[i]["roleName"],
							"policies": plc
						});
					}

				}
				res(allPolicies);
			}).catch(e => {
				sails.log(e);
				res([]);

			});
		});

	},
	"userHasSiteAccess": async (userId, siteId) => {
		const userSiteMapCacheKey = `user:${userId}:siteAccessList`;
		if (await cacheService.sismember(userSiteMapCacheKey, siteId)) return true;

		let userSiteAccess = await UserSiteMap.findOne({ userId, siteId });
		return userSiteAccess ? true : false;
	}

};
