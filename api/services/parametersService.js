/**
 * @namespace ParametersService
 * @description Provides service methods that helps in operations that are to be done on parameters table.
 */
module.exports = {
	/**
	 * @function addDeviceParameters
	 * @param {string} deviceType
	 * @param {string} driverType
	 * @param {string} deviceId
	 * @description Method adds parameters of a new device based on its devicetype and driver type. It will link the parameters of that device type with the deviceId that is created
	 * @returns {boolean} true if successful else false
	 */
	"addDeviceParameters": async function (deviceType, driverType, deviceId, siteId) {
		if (!deviceType || !deviceId || typeof driverType === "undefined" || driverType === null || !siteId)  {
			return false;
		}
		try {

			let devicetype = await deviceTypeService.getParameters( deviceType, driverType );
			if (!devicetype) {
				sails.log.error("Device type does not exist in db");
				return false;
			}
			let { parameters } = devicetype;

			//append deviceId and flatten filter object
			let paramList = parameters.map(origObj => {
				let paramObj = JSON.parse(JSON.stringify(origObj));
				let { filter, abbr, type, properties } = paramObj;
				paramObj["deviceId"] = deviceId;
				if (typeof filter != "undefined") {
					Object.keys(filter).forEach((key) => {
						let val = filter[key];
						paramObj[`filter_${key}`] = val;
					});
				}
				if ( properties){
					Object.keys(properties).forEach((key)=>{
						paramObj[key]=properties[key];
					});
				}
				paramObj.siteId=siteId;
				paramObj.mode = "jt";
				paramObj.utilityType = type;
				paramObj.deviceId_abbr=`${deviceId}_${abbr}`;
				paramObj.inheritedFrom = `${deviceType}@${driverType}`;
				delete paramObj.filter;
				delete paramObj.properties;
				return paramObj;
			});
			// console.log(JSON.stringify(paramList));
			await Parameters.create(paramList);
			return true;
		} catch (e) {
			sails.log.error(e);
			return false;
		}
	},
	"getParamGroup": (siteId, deviceId, param) => {
		if (!siteId || !deviceId || !param) return null;
		return Parameters.findOne({
			siteId,
			"deviceId_abbr": `${deviceId}_${param}`,
		});
	},

	/**
	 * @function getParameters
	 * @param {String} siteId
	 * @description Returns the parameters of the mentioned site
	 * @returns {Array}
	 */
	"getParameters": async siteId =>{
		if (!siteId)
			return [];
		const params = await Parameters.find({siteId});
		return params.map(parameter =>{
			delete parameter.deviceId_abbr;
			return parameter;
		});
	},
};
