const moment = require("moment-timezone");
moment.tz.add("Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6");
moment.tz.setDefault("Asia/Kolkata");
moment.defaultFormat = "YYYY-MM-DD HH:mm:ss";
const AWS = require("aws-sdk");
const elasticsearch = require("elasticsearch");
const awsHttpClient = require("http-aws-es");
const axios = require("axios");
const uuid = require("uuid/v4");

let client = elasticsearch.Client({
  "host": "search-sj-elasticsearchcluster-w3v6acfblmbzq47k2k5x2aukqy.us-west-2.es.amazonaws.com",
  "connectionClass": awsHttpClient,
});
/**
 * Diagnostics Service Module.
 */

/**
 * <AUTHOR> <<EMAIL>>
 * @name generatePayloadForController
 * @param {*} deviceId
 * @param {*} oldVersion
 * @param {*} newVersion
 * @param {*} key
 * @param {*} hardReboot
 * @summary It generate payload for controllers and publish it on its respective topic
 */
let generatePayloadForController = async (deviceId, oldVersion, newVersion, key, hardReboot) => {

  if (!deviceId || !oldVersion || !newVersion || !key) {
    return { "err": "Params are invalid" };
  }
  let newVerData = await Version.find({ "version": newVersion });
  let oldVerData = await Version.find({ "version": oldVersion });
  let topic = `global/command/${deviceId}/cicd`;

  let payload = {
    "param": "firmware",
    "key": key,
    "operation": "jioc_update",
  };
  for (let i in newVerData) {
    let gitVal = [];
    let dockerVal = [];
    let repoList = newVerData[0]["repoList"];
    let repoLength = repoList.length;
    for (let j = 0; j < repoLength; j++) {
      let repo = repoList[j];
      let oldVerDataInJSON = helper.toJson(oldVerData[0][repo]);
      let newVersionDataInJSON = helper.toJson(newVerData[i][repo]);

      if (oldVerDataInJSON == undefined) {
        oldVerDataInJSON = {
          "version": "0.0.0",
          "type": "git",
          "name": "d2rs",
        };
      }

      if (oldVerDataInJSON["version"] === newVersionDataInJSON["version"]) {
        continue;
      } else {
        if (newVersionDataInJSON["type"] === "git") {
          let valueObject = {
            "repo": newVersionDataInJSON["name"],
            "version": newVersionDataInJSON["version"],
            "option": "clone",
            "softReboot": true,
          };
          gitVal.push(valueObject);
        } else {
          let valueObject = {
            "version": newVersionDataInJSON["version"],
            "name": newVersionDataInJSON["name"],
          };
          dockerVal.push(valueObject);
        }
      }
    }
    //push data in payload
    let value = {
      "git": gitVal,
      "docker": dockerVal,
      "hardReboot": hardReboot,
      "version": newVerData[0]["version"],
    };
    payload["value"] = value;
    payload = JSON.stringify(payload);
    eventService.publish(topic, payload);
  }
  sails.log("New Update Started");
};
let checkUpdateCallBack = async (deviceId, oldVersion) => {
  if (!deviceId || !oldVersion) {
    return { "err": "ERROR!!" };
  }
  let data = await Devices.find({ "deviceId": deviceId });
  if (oldVersion == "") {
    oldVersion = data[0].currentVersion;
  }
  let newVersion = data[0].expectedVersion;
  let key = "This is a Key";
  let hardReboot = true;
  await generatePayloadForController(deviceId, oldVersion, newVersion, key, hardReboot);
  return { "status": "payload published" };
};

/**
 * <AUTHOR> <<EMAIL>>
 * @name checkDataExistInCache
 * @param {*} skey
 * @param {*} val
 * @param {*} deviceAlreadyUpdating
 * @param {*} responseArray
 * @param {*} device
 * @param {*} oldVersion
 * @param {*} newVersion
 * @param {*} key
 * @param {*} hardReboot
 * @summary Help to put the check, so that multiple update command will be restricted for a device
 */
let checkDataExistInCache = async (skey, val, deviceAlreadyUpdating, responseArray, device, oldVersion, newVersion, key, hardReboot) => {
  if (!skey || !val || !deviceAlreadyUpdating || !responseArray || !device || !oldVersion || !newVersion || !key || hardReboot == undefined)
    return { "err": "Data is not accurate" };

  await cacheService.sismember(skey, val).then(value => {
    if (value == 1) {
      deviceAlreadyUpdating.push(device.deviceId);
    } else {
      responseArray.push(device);
      generatePayloadForController(
        device.deviceId,
        oldVersion,
        newVersion,
        key,
        hardReboot);
    }
  });
};

let checkDataExistInCacheForJouleSense = async (commandControllerId, version, deviceAllData, toSkip, socketId, responseArray, deviceAlreadyUpdating) => {
  let deviceList = [];
  if (!commandControllerId || !version || !deviceAllData || toSkip == undefined || !socketId) {
    return { "err": "ERROR!! Check Params" };
  }
  for (let i in deviceAllData) {
    if (deviceAllData[i].deviceType == "joulesense") {
      await cacheService.sismember("upgradeCICD", deviceAllData[i].deviceId).then(value => {
        if (value == 1) {
          deviceAlreadyUpdating.push(deviceAllData[i].deviceId);
        } else {
          responseArray.push(deviceAllData[i]);
        }
      });
    }
  }
  await cacheService.sismember("upgradeCICD", commandControllerId).then(val => {
    if (val == 1) {
      deviceAlreadyUpdating.push(commandControllerId);
    } else {
      cacheService.sadd("upgradeCICD", commandControllerId).then(err => {
        sails.log("error occured");
      });
      for (let j in responseArray) {
        deviceList.push(responseArray[j].deviceId);
      }
      deviceList = deviceList.join(",");
      diagnosticService.generatePayloadForJouleSense(
        commandControllerId,
        version,
        deviceList,
        toSkip,
        socketId,
      );
    }
  });
};
module.exports = {
  "client": client,
  "generatePayloadForController": generatePayloadForController,
  "checkUpdateCallBack": checkUpdateCallBack,
  "checkDataExistInCache": checkDataExistInCache,
  "checkDataExistInCacheForJouleSense": checkDataExistInCacheForJouleSense,

  /**
   * @function generateElasticIndex
   * @summary Generates elasticsearch query index parameters based on startTime and endTime of the query range. Appends date as "index_date1,index_date2"
   * @param {string} startTime Start time of the query.
   * @param {string} endTime end time of the query.
   * @param {string} index index that it is searching on.
   * <AUTHOR> Gupta
   */
  "generateElasticIndex": function(startTime, endTime, index, groupBy = "day") {
    let dateFormat, momentFunctionName;
    if (groupBy === "months") {
      dateFormat = "YYYY-MM";
      momentFunctionName = "month";
    } else {
      dateFormat = "YYYY-MM-DD";
      momentFunctionName = "date";
    }
    let startDate = moment(startTime), endDate = moment(endTime), returnIndex = `${index}_${startDate.format(dateFormat)},`;

    let currentDate = moment(startDate);
    while (currentDate[momentFunctionName]() < endDate[momentFunctionName]())
      returnIndex = returnIndex.concat(`${index}_${currentDate.add(1, groupBy).format(dateFormat)},`);
    return returnIndex;
  },

  "diagnosticsResponseFeedback": function(topic, payload) {
    sails.log("Diagnostic Info Topic Procced!");
    try {
      topic = topic.split("/");
      let siteId = topic[0], deviceId = topic[2], socketId;
      payload = helper.toJson(payload);
      payload["deviceId"] = deviceId;
      // TODOI: Differentiate between RasPi and JouleSense feedbacks via specific key and not
      if (payload.hasOwnProperty("param")) {
        switch (payload.param) {
          case "info":
            // JS info packet.
            payload["event"] = "info";
            break;
          case "recab":
            // JS Recalibrate packet.
            payload["event"] = "recab";
            break;
          case "restart":
            // JS Restart packet.
            payload["event"] = "restart";
            if (payload.deviceType == "js") {
              if (payload.status == "start") payload["feedback"] = "Restarting device.";
              else if (payload.status == "stop") payload["feedback"] = "Succesfully finished restarting.";
            }
            break;
        }
      } else {
        // Temporary hot fix since controller info packet is not sending any param back
        payload["event"] = "info";
      }
      if (payload.hasOwnProperty("key")) {
        socketId = payload.key;
      } else {
        eventService.notifyJouleTrack(siteId, "diagnostics", "diagnostics", payload);
      }
      eventService.notifyUser(socketId, "diagnostics", payload);
    } catch (error) {
      sails.log("Error in [diagnosticsInfoFeedback]:", error);
    }

  },

  /**
   * <AUTHOR> <<EMAIL>>
   * @name cachingUpdatingDevices
   * @param {*} resArr
   * @description Add the deviceIds in the cache which is Updating its firmware
   */
  "cachingUpdatingDevices": async (resArr) => {
    if (!resArr)
      return { "err": "Params isn't correct" };

    for (let i in resArr) {
      cacheService.sadd("upgradeCICD", resArr[i].deviceId).then(error => {
        sails.log(error);
      });
    }
  },

  /**
   * <AUTHOR> <<EMAIL>>
   * @name checkUpdate
   * @param {*} topic
   * @param {*} payload
   * @summary Called by raspi when it get restarted and check whether it is on it expexcted version or not.
   */
  "checkUpdate": async (topic, payload) => {
    //Checking update on boot
    //payload = {currentVersion : "1.0.1"}
    if (!topic || !payload) {
      return { "err": "ERROR!!" };
    }
    let currVersion = payload.currentVersion;
    topic = topic.split("/");
    let deviceId = topic[2];
    let data = await Devices.find({ "deviceId": deviceId });
    if (currVersion == undefined || currVersion == "" && data[0].currentVersion == undefined) {
      currVersion = "0.0.0";
    } else if (currVersion == "" && data[0].currentVersion) {
      currVersion = data[0].currentVersion;
    }

    let expectedVersion = data?.[0]?.expectedVersion;

    if (expectedVersion != undefined) {
      if (currVersion != expectedVersion) {
        sails.log("Update Failed Last Time: Trying Again To Update");
        let response = await checkUpdateCallBack(deviceId, currVersion);
        return response;
      } else {
        return { "status": "Device is upto date" };
      }
    }
  },

  //Coming Soon
  /**
   * <AUTHOR> <<EMAIL>>
   * @name generatePayloadForJouleSense
   * @param {*} commandControllerId
   * @param {*} version
   * @param {*} deviceList
   * @param {*} toSkip
   * @param {*} socketId
   * @summary Called to generate payload for Joule Sense devices and publish it on respective topic.
   */
  // "generatePayloadForJouleSense": (commandControllerId, version, deviceList, toSkip, socketId)=>{

  // 	if (!commandControllerId || !version || !deviceList || toSkip == undefined || !socketId){
  // 		return {"err": "ERROR!! Check Params"};
  // 	}

  // 	let topic = "global/command/" + commandControllerId + "/cicd";
  // 	// Preparing payload packet
  // 	let payload = {
  // 		"param": "firmware",
  // 		"value": {
  // 			"version": version,
  // 			"deviceList": deviceList,
  // 			"toSkip": toSkip
  // 		},
  // 		"key": socketId,
  // 		"operation": "js_update"
  // 	};
  // 	payload = JSON.stringify(payload);
  // 	cacheService.sadd("upgradeCICD", commandControllerId).then(err=>{
  // 		sails.log(err);
  // 	});
  // 	eventService.publish(topic, payload);
  // 	return {"status": "Published"};
  // },

  "generatePayloadForJouleSense": (commandControllerId, version, deviceList, toSkip, socketId) => {

    if (!commandControllerId || !version || !deviceList || toSkip == undefined || !socketId) {
      return { "err": "ERROR!! Check Params" };
    }

    let topic = "global/command/" + commandControllerId + "/cicd";
    // Preparing payload packet
    let payload = {
      "param": "firmware",
      "value": {
        "version": version,
        "deviceList": deviceList,
        "toSkip": toSkip,
      },
      "key": socketId,
      "operation": "js_update",
    };
    payload = JSON.stringify(payload);
    eventService.publish(topic, payload);
    return { "status": "Published" };
  },

  /**
   * @function fetchJouleSenseVersion
   * @summary Searches and returns JouleSense Version
   * @param {string} version Overall version to be searched in Version's table.
   * @param {string} deviceType deviceType of the version being stored
   * <AUTHOR> Gupta
   */
  "fetchJouleSenseVersion": async function(version, deviceType) {
    let searchResult;
    try {
      searchResult = await Version.find({
        "version": version,
        "deviceType": deviceType,
      });
    } catch (error) {
      sails.log.error(`[fetchJouleSenseVersion] Error: ${error}`);
      return {
        "status": false,
        "message": "Error finding version in database.",
      };
    }
    if (searchResult.length == 0)
      return {
        "status": false,
        "message": "Could not find version in database",
      };
    else
      return {
        "status": true,
        "version": searchResult[0].gitTag,
      };
  },
  /**
   * @function setExpectedVersion
   * @summary Sets expected version of a device
   * @param {object} device Contains device info
   * @param {deviceId} device.deviceId deviceId
   * @param {siteId} device.siteId siteId
   * @param {string} version Version being stored in the Version's table
   * <AUTHOR> Gupta
   */
  "setExpectedVersion": async function(device, version) {
    try {
      await Devices.update({
        "deviceId": device.deviceId,
        "siteId": device.siteId,
      }).set({
        "expectedVersion": version,
      });
    } catch (error) {
      sails.log.error(`[setExpectedVersion] Error: ${error}`);
    }
  },

  /**
   * @function publishJouleSenseV2Payload
   * @summary Generates and publishes CICD MQTT payload for JouleSense of hardware version "v2".
   * @param {*} device device object recieved from Front End
   * @param {*} version Version to update to.
   * @param {*} socketId socketId of the user.
   * <AUTHOR> Gupta
   */
  "publishJouleSenseV2Payload": function(device, version, socketId) {
    let topic = `${device.siteId}/command/${device.deviceId}/cicd`;
    let payload = {
      "param": "update",
      "key": socketId,
      "socketId": socketId,
      "version": version,
    };
    payload = JSON.stringify(payload);
    eventService.publish(topic, payload);
  },

  "updateAPICallBack": async (device, newVersion, key, hardReboot, responseArray, deviceAlreadyUpdating) => {
    if (!device || !newVersion || !key || !responseArray || !deviceAlreadyUpdating) {
      return { "err": "ERROR!! Params is not validated" };
    }
    let oldVersion = device.currentVersion;
    if (oldVersion == undefined) {
      oldVersion = "0.0.0";
    }
    if (oldVersion != newVersion) {
      await checkDataExistInCache(
        "upgradeCICD",
        device.deviceId,
        deviceAlreadyUpdating,
        responseArray,
        device,
        oldVersion,
        newVersion,
        key,
        hardReboot);
    } else {
      return { "status": "Already Upto date" };
    }
  },
  "cicdFeedback": async function(topic, payload, deviceId) {
    try {
      sails.log("CICD Feedback recieved!");
      payload = helper.toJson(payload);
      payload["event"] = "cicd";
      payload["deviceId"] = deviceId;
      let socketId = payload.key;
      // ReverseSSH specific modifcations.
      let siteId;
      switch (payload.code) {
        case 21: {
          payload["event"] = "remoteip";
          let cmdObj = this.sshCommands(payload.port);
          payload = { ...payload, ...cmdObj };
          eventService.notifyUser(socketId, "diagnostics", payload);
          siteId = await Devices.find({ "deviceId": deviceId }).then(devices => {
            return devices[0].siteId;
          });
          Devices.update(
            { "deviceId": deviceId, "siteId": siteId },
            { "remoteAccess": true, "remoteAccessPort": payload.port })
            .then(data => {
              sails.log("Data after updating devices table for reverse ssh port:", data);
            })
            .catch(err => {
              sails.log("Error!!:", err);
            });
          break;
        }
        case 23:
          payload["event"] = "remoteip";
          eventService.notifyUser(socketId, "diagnostics", payload);
          siteId = await Devices.find({ "deviceId": deviceId }).then(devices => {
            return devices[0].siteId;
          });
          Devices.update(
            { "deviceId": deviceId, "siteId": siteId },
            { "remoteAccess": false, "remoteAccessPort": null })
            .then(data => {
              sails.log("Data after updating:", data);
            })
            .catch(err => {
              sails.log("Error!!:", err);
            });
          break;
        case 11:
          eventService.notifyUser(socketId, "diagnostics", payload);
          try {
            let data = await Devices.find({
              "deviceId": deviceId,
            });
            await Devices.update({
              "deviceId": deviceId,
              "siteId": data[0].siteId,
            }).set({
              "currentVersion": data[0].expectedVersion,
            });
            cacheService.srem("upgradeCICD", deviceId).then(error => {
              sails.log(error);
            });
          } catch (error) {
            sails.log(error);
          }
          break;
        case 0:
        case 3:
        case 5:
        case 6:
        case 8:
        case 9:
        case 12:
          if (socketId)
            eventService.notifyUser(socketId, "diagnostics", payload);
          else
            eventService.notifyJouleTrack(siteId, "diagnostics", "diagnostics", payload);

          cacheService.srem("upgradeCICD", deviceId).then(error => {
            sails.log(error);
          });
          break;
        case 53:
          // JouleSense Successful Update Code
          // Setting Current version as the expected version of the device.
          if (socketId)
            eventService.notifyUser(socketId, "diagnostics", payload);
          else
            eventService.notifyJouleTrack(siteId, "diagnostics", "diagnostics", payload);
          try {
            let data = await Devices.find({
              "deviceId": deviceId,
            });
            await Devices.update({
              "deviceId": deviceId,
              "siteId": data[0].siteId,
            }).set({
              "currentVersion": data[0].expectedVersion,
            });
          } catch (error) {
            sails.log(error);
          }
          break;
        default:
          if (socketId)
            eventService.notifyUser(socketId, "diagnostics", payload);
          else
            eventService.notifyJouleTrack(siteId, "diagnostics", "diagnostics", payload);
          break;
      }

      // if (payload.code == 21){

      // }
      // else if (payload.code == 23){

      // }
      // eventService.notifyJouleTrack(siteId, 'diagnostics', 'diagnostics', payload);
    } catch (error) {
      sails.log("Error in [cicdFeedback]:", error);
    }
  },

  "jouleSenseFeedback": function(topic, payload) {
    sails.log("JouleSense feeback received!");
    // sails.log('payload: ', payload);
    // sails.log('topic: ', topic);
    try {
      payload = helper.toJson(payload);
      payload["event"] = "cicd";
      topic = topic.split("/");
      let siteId = topic[0], socketId = payload.key;
      switch (payload.code) {
        case 38:
        case 39:
        case 34:
          //delete controller and jS from caches
          cacheService.srem("upgradeCICD", payload.deviceList).then(error => {
            sails.log(error);
          });
          break;
        case 36:
          cacheService.srem("upgradeCICD", topic[2]).then(error => {
            sails.log(error);
          });
          break;
      }
      if (socketId)
        eventService.notifyUser(socketId, "diagnostics", payload);
      else
        eventService.notifyJouleTrack(siteId, "diagnostics", "diagnostics", payload);

    } catch (error) {
      sails.log("Error in [jouleSenseFeedback]:", error);
    }

  },

  "sshCommands": function(port) {
    if (port == null) {
      let retObj = {
        "command1": "Port number not stored in DB.",
        "command2": "Port number not stored in DB.",
      };
      return retObj;
    }
    let retObj = {
      "command1": `ssh -ti "remoteAccess.pem" <EMAIL> 'sshpass -p joulesO23 ssh pi@localhost -p ${port}'`,
      "command2": `ssh -t <EMAIL> 'sshpass -p joulesO23 ssh pi@localhost -p ${port}'`,
    };
    return retObj;
  },

  /**
   * <AUTHOR> <<EMAIL>>
   * @name getJiraTaskID
   * @param {Object} params (Contains different data to create Maintenance Card)
   * @param {string} uuid
   * @summary Collect data and Create maintenance card on JIRA
   * @returns {object}
   */
  "getJiraTaskID": async (params, uuid) => {
    if (!params || !uuid) {
      return { "err": "Params is not fulfilled" };
    }
    let region = [];
    let component = [];
    //Collecting all the regions
    for (let i in params.rd.region) {
      if (!region.includes(params.rd.region[i].id)) region.push(params.rd.region[i].id);
    }
    //Collecting all the components
    for (let i in params.rd.component) {
      if (!component.includes(params.rd.component[i].id)) component.push(params.rd.component[i].id);
    }
    //Collecting all the IO parameters
    let data = await diagnosticService.getioparms(component, params.siteId);
    //Uppercasing the first letter of Priority due to Jira acceptability
    let priority = params.priority.charAt(0).toUpperCase() + params.priority.substring(1);
    //Collecting data in valid format
    let obj = {
      "uuid": uuid,
      "Suggested By": params.reporter,
      "Sheet_Id": "1QpdX6z6_hR-8eAJFpHoAHVGzd1fXUoyXJm1o-LgQ9V8",
      "SheetName": `${params.siteId}`,
      "Summary": params.summary,
      "Description": params.description,
      "PC_REGION": region.toString(),
      "PC_ASSET": "None",
      "PC_READ_WRITE": "None",
      "PC_DATE_IDENTIFICATION": moment(params.issueObservedAt).format("YYYY-MM-DD"),
      "PC_TIME_IDENTIFICATION": moment(params.issueObservedAt).format("hh:mmA"),
      "PC_TIME_INSPECTION": moment().format("hh:mmA"),
      "PC_DATE_INSPECTION": moment().format("YYYY-MM-DD"),
      "PC_POC_SITE": `site_${params.siteId}`,
      "PC_VALUE_ON_JT": "None",
      "PC_VALUE_EXPECTED": "None",
      "PC_VALUE_PHYSICAL_INSPECTION": "None",
      "Priority(Site)": priority,
      "TypeofIssue": "Internal",
    };

    if (data.length != 0) obj["PC_POINT_IO"] = data.toString();
    else obj["PC_POINT_IO"] = "None";

    if (params.attachments.length != 0) obj["Attachment"] = params.attachments.toString();
    else obj["Attachment"] = "NA";
    //Hitting the API to create card on JIRA
    let response = await axios.post("https://yfr9r795wf.execute-api.ap-south-1.amazonaws.com/dev/users/Create", obj);
    // Collecting data for Kinesis stream by passing JIRA card ID as a uuid
    let res = await diagnosticService.collectMaintenanceData(params, response.data["issue_key"]);
    return res;
  },

  /**
   * <AUTHOR> <<EMAIL>>
   * @name collectMaintenanceData
   * @param {Object} params (Contains different data to create Maintenance Card)
   * @param {string} uuid
   * @summary Collecting data to Create maintenance card through Kinesis Stream
   * @returns {object}
   */
  "collectMaintenanceData": async (params, uuid) => {
    if (!params || !uuid) {
      return { "err": "Params is not fulfilled" };
    }
    let todynamo = {};
    let realArr = [];
    let virtualArr = [];

    // Fields taken from front end.
    todynamo["type"] = params.typeOfIssue;
    todynamo["summary"] = params.summary;
    todynamo["description"] = params.description;
    todynamo["observed_at"] = moment(params.issueObservedAt).format("YYYY-MM-DD HH:mm:ss");
    todynamo["repoter"] = params.reporter;
    todynamo["accountable"] = params.accountable;
    todynamo["due_date"] = moment(params.deadline).format("YYYY-MM-DD HH:mm:ss");
    todynamo["expected_resolve"] = moment(params.expectedResolve).format("YYYY-MM-DD HH:mm:ss");
    todynamo["notify"] = params.notify;
    todynamo["site"] = params.siteId;
    todynamo["priority_site"] = params.priority;
    todynamo["priority_dev"] = params.priority;

    // Fields generated on creation.
    todynamo["cid"] = uuid;
    todynamo["creation_time"] = moment().format("YYYY-MM-DD HH:mm:ss");
    todynamo["status"] = "ToDo";
    todynamo["resolved_time"] = null;
    // todynamo["status"] = "Release";
    // todynamo["resolved_time"] =	moment(params.expectedResolve).format("YYYY-MM-DD HH:mm:ss");

    let real = {
      "process": [],
      "plant": [],
      "jouleRecipi": [],
    };
    let virtual = {
      "deviceId": [],
      "IOpoint": [],
      "process": [],
      "plant": [],
      "jouleRecipi": [],
    };
    let data;
    let deviceId = [];
    let component = [];
    let controller = [];
    let region = [];
    let area = [];
    let network = [];
    //Collecting all the slaves
    for (let i in params.rd.slave) {
      if (!deviceId.includes(params.rd.slave[i].id)) deviceId.push(params.rd.slave[i].id);
    }
    //Collecting all the components
    for (let i in params.rd.component) {
      if (!component.includes(params.rd.component[i].id)) component.push(params.rd.component[i].id);
    }
    //Collecting all the controllers
    for (let i in params.rd.controller) {
      if (!controller.includes(params.rd.controller[i].id)) controller.push(params.rd.controller[i].id);
    }
    //Collecting all the regions
    for (let i in params.rd.region) {
      if (!region.includes(params.rd.region[i].id)) region.push(params.rd.region[i].id);
    }
    //Collecting all the Area
    for (let i in params.rd.area) {
      if (!area.includes(params.rd.area[i].id)) area.push(params.rd.area[i].id);
    }
    //Collecting all the Networks
    for (let i in params.rd.network) {
      if (!network.includes(params.rd.network[i].id)) network.push(params.rd.network[i].id);
    }

    // sails.log(params.modeType);
    //Collecting Data on the basis of what is put into Maintenance
    switch (params.modeType) {
      case "device":
        data = await diagnosticService.getDeviceDetails(deviceId, params.siteId);
        real["Region"] = [];
        real["controller"] = [];
        real["deviceId"] = deviceId;
        realArr.push(deviceId.toString());
        real["component"] = [];
        real["IOpoint"] = await diagnosticService.getioparms(data.componentId, params.siteId);
        realArr.push(real.IOpoint.toString());
        real["netwoktopoly"] = [];
        virtual["Region"] = data.regionId;
        virtualArr.push(data.regionId.toString());
        virtual["component"] = data.componentId;
        virtualArr.push(data.componentId.toString());
        virtual["controller"] = data.controllerId;
        virtualArr.push(data.controllerId.toString());
        virtual["netwoktopoly"] = data.network;
        virtualArr.push(data.network.toString());
        break;

      case "controller":
        data = await diagnosticService.getDeviceDetails(controller, params.siteId);
        real["Region"] = [];
        real["controller"] = controller;
        realArr.push(controller.toString());
        real["deviceId"] = deviceId;
        realArr.push(deviceId.toString());
        real["component"] = [];
        real["IOpoint"] = await diagnosticService.getioparms(data.componentId, params.siteId);
        realArr.push(real.IOpoint.toString);
        real["netwoktopoly"] = [];
        virtual["Region"] = data.regionId;
        virtualArr.push(data.regionId.toString());
        virtual["component"] = data.componentId;
        virtualArr.push(data.componentId.toString());
        virtual["controller"] = [];
        virtual["netwoktopoly"] = data.network;
        virtualArr.push(data.network.toString());
        break;

      case "component":
        data = await diagnosticService.getComponentsDetails(deviceId, params.siteId);
        real["Region"] = [];
        real["controller"] = controller;
        realArr.push(controller.toString());
        real["deviceId"] = deviceId;
        realArr.push(deviceId.toString());
        real["component"] = component;
        realArr.push(component.toString());
        real["IOpoint"] = await diagnosticService.getioparms(data.componentId, params.siteId);
        realArr.push(real.IOpoint.toString());
        real["netwoktopoly"] = [];
        virtual["Region"] = data.regionId;
        virtualArr.push(data.regionId.toString());
        virtual["component"] = [];
        virtual["controller"] = [];
        virtual["netwoktopoly"] = data.network;
        virtualArr.push(data.network.toString());
        break;

      case "region":
        real["Region"] = region;
        realArr.push(region.toString());
        real["controller"] = controller;
        realArr.push(controller.toString());
        real["deviceId"] = deviceId;
        realArr.push(deviceId.toString());
        real["component"] = component;
        realArr.push(component.toString());
        real["IOpoint"] = await diagnosticService.getioparms(component, params.siteId);
        realArr.push(real.IOpoint.toString());
        real["netwoktopoly"] = network;
        realArr.push(network.toString());
        virtual["Region"] = [];
        virtual["component"] = [];
        virtual["controller"] = [];
        virtual["netwoktopoly"] = [];
        break;

      case "network":
        data = await diagnosticService.getComponentsDetails(deviceId, params.siteId);
        virtual["Region"] = data.regionId;
        virtualArr.push(data.regionId.toString());
        virtual["component"] = [];
        virtual["controller"] = [];
        virtual["netwoktopoly"] = [];
        real["netwoktopoly"] = network;
        realArr.push(network.toString());
        real["component"] = component;
        realArr.push(component.toString());
        real["controller"] = controller;
        realArr.push(controller.toString());
        real["deviceId"] = deviceId;
        realArr.push(deviceId.toString());
        real["IOpoint"] = await diagnosticService.getioparms(data.componentId, params.siteId);
        realArr.push(real.IOpoint.toString());
        break;
    }
    todynamo["Real"] = real;
    todynamo["Virtual"] = virtual;

    // Correcting response Objects for FrontEnd requirements
    realArr = flattenIds(realArr);
    virtualArr = flattenIds(virtualArr);

    // Helper function to correct arrays
    function flattenIds(array) {
      let totalDevices = [];
      array.forEach(deviceIds => {
        if (typeof (deviceIds) != "string") return;
        let devices = deviceIds.split(",");
        devices.forEach(device => totalDevices.push(device));
      });
      return totalDevices;
    }

    return [todynamo, realArr, virtualArr];
  },

  /**
   * <AUTHOR> <<EMAIL>>
   * @name getComponentsDetails
   * @param {Array} deviceId (Contains deviceIds)
   * @param {string} siteId
   * @summary Collecting data all the IDs which goes in upper hierarchy of Components
   * @returns {object}
   */
  "getComponentsDetails": async (deviceId, siteId) => {
    try {
      if (!deviceId || !siteId) {
        return { "err": "Params are incomplete" };
      }

      let controller = [];
      let region = [];
      let network = [];
      let res = {};
      let data;
      for (let i in deviceId) {
        //Getting details of each Components from DB
        let response = await Component.find({
          "deviceId": deviceId[i],
          "siteId": siteId,
        });
        for (let i in response) {
          if (!region.includes(response[i].regionId)) region.push(response[i].regionId);
          if (!controller.includes(response[i].controllerId)) controller.push(response[i].controllerId);
        }
      }
      for (let j = 0; j < controller.length; j++) {
        data = await diagnosticService.getControllerDetails(controller[j], siteId);
        if (!network.includes(data.network)) network.push(data.network);
      }

      res["regionId"] = region;
      res["network"] = network;
      return res;
    } catch (error) {
      throw error;
    }
  },

  /**
   * <AUTHOR> <<EMAIL>>
   * @name getControllerDetails
   * @param {Array} controllerId (Contains controllerIds)
   * @param {string} siteId
   * @summary Collecting data all the IDs which goes in upper hierarchy of Controller
   * @returns {object}
   */
  "getControllerDetails": async (controllerId, siteId) => {
    try {
      if (!controllerId || !siteId) {
        return { "err": "Params are incomplete" };
      }
      let response = await Devices.find({ "controllerId": controllerId });
      let region = [];
      let component = [];
      let network = [];
      let res = {};
      for (let i in response) {
        if (!region.includes(response[i].regionId))
          region.push(response[i].regionId);
        if (!network.includes(response[i].networkId))
          network.push(response[i].networkId);
        if (!component.includes(response[i].componentId) && response[i].componentId != undefined)
          component.push(response[i].componentId);
      }
      res["regionId"] = region;
      res["componentId"] = component;
      res["network"] = network;
      return res;
    } catch (error) {
      throw error;
    }
  },

  /**
   * <AUTHOR> <<EMAIL>>
   * @name getDeviceDetails
   * @param {Array} deviceId (Contains deviceIds)
   * @param {string} siteId
   * @summary Collecting data all the IDs which goes in upper hierarchy of Devices
   * @returns {object}
   */
  "getDeviceDetails": async (deviceId, siteId) => {
    try {
      let res = {};
      let region = [];
      let controller = [];
      let network = [];
      let component = [];

      if (!deviceId || !siteId) {
        return { "err": "Params aren't complete" };
      }
      for (let i in deviceId) {
        let response = await Devices.find({
          "deviceId": deviceId[i],
          "siteId": siteId,
        });
        let data = response[0];
        let tempComponent;
        if (!region.includes(data.regionId)) region.push(data.regionId);
        if (!controller.includes(data.controllerId)) controller.push(data.controllerId);
        if (!network.includes(data.networkId)) network.push(data.networkId);
        if (data.componentId != undefined) {
          tempComponent = (data.componentId).split(",");
          for (let i in tempComponent) {
            if (!component.includes(tempComponent[i])) {
              component.push(tempComponent[i]);
            }
          }
        }
      }

      res["regionId"] = region;
      res["controllerId"] = controller;
      res["network"] = network;
      res["componentId"] = component;
      return res;
    } catch (error) {
      throw error;
    }
  },

  /**
   * <AUTHOR> <<EMAIL>>
   * @name getioparms
   * @param {Array} componentId (Contains componentId)
   * @param {string} siteId
   * @summary Collecting data all the IDs which goes in upper hierarchy of IO params
   * @returns {object}
   */
  "getioparms": async (componentId, siteId) => {
    let map = {};
    let io = [];
    let con = [];

    if (!componentId || !siteId) {
      return { "err": "Params  aren't complete" };
    }
    for (let j in componentId) {

      let response = await Component.find({
        "deviceId": componentId[j],
        "siteId": siteId,
      });

      for (let i in response) {
        let lol = response[i].controls;
        let controls = helper.toJson(lol);
        for (let j in controls) {
          lol = helper.toJson(controls[j]);
          let dlol = ((lol.deviceId).trim(",")).split(",");
          map[lol.key] = dlol;
          io.push(lol.key);
        }
      }
      let lol, dlol;
      for (let i in response) {
        let data = response[i].data;
        let controls = helper.toJson(data);
        for (let j in controls) {
          lol = helper.toJson(controls[j]);
          dlol = ((lol.deviceId).trim(",")).split(",");
          map[lol.key] = dlol;
          io.push(lol.key);
        }
      }
      con = con.concat(io.filter(function(item) {
        return con.indexOf(item) < 0;
      }));
    }
    return con;
  },

  /**
   * <AUTHOR> <<EMAIL>>
   * @name getAllCards
   * @param {Object} data (Contains data extracted from Elastic Search)
   * @param {string} id
   * @summary Return all the Cards containing the specific id either in Real or Virtual
   * @returns {Array}
   */
  "getAllCards": (data, id) => {

    if (!data || !id) {
      return { "err": "Params are not complete" };
    }

    let resArr = [];
    for (let i in data) {
      let realData = data[i]._source.Real;
      let virtualData = data[i]._source.Virtual;
      for (let j in realData) {
        //Checking id exists in Real
        if (realData[j].includes(id))
          resArr.push(data[i]);
      }
      for (let k in virtualData) {
        //Checking id exists in Virtual
        if (virtualData[k].includes(id))
          resArr.push(data[i]);
      }
    }
    return resArr;
  },

  /**
   * <AUTHOR> <<EMAIL>>
   * @name sortingRealVirtualIDs
   * @param {Object} data (Contains data extracted from Elastic Search)
   * @summary Return the list of all Real or Virtual devices
   * @returns {object} {"Real": [], "Virtual": []}
   */
  "sortingRealVirtualIDs": async (data) => {
    let real = [];
    let virtual = [];
    let response = {};

    if (!data) {
      return { "err": "Params are incomplete" };
    }
    for (let i in data) {
      let virtualData = data[i]._source.Virtual;
      let realData = data[i]._source.Real;
      // sails.log(virtualData, realData);
      //Extracting the Virtual Devices
      for (let j in virtualData) {
        if (virtualData[j].length != 0 && typeof virtualData[j] == "object") {
          for (let k in virtualData[j]) {
            virtual.push(virtualData[j][k]);
          }
        }
      }
      //Extracting the Real Devices
      for (let j in realData) {
        if (realData[j].length != 0 && typeof realData[j] == "object") {
          for (let k in realData[j]) {
            real.push(realData[j][k]);
          }
        }
      }
    }
    response["real"] = real;
    response["virtual"] = virtual;
    return response;
  },
  /**
   * @function sendMaintenanceAlert
   * @summary Saves an object in 'Actions' table so that the alert service can send an email to the required people.
   * @param {object} maintenanceInfo Maintenance object being stored in dynamo
   * @param {boolean} eventFlag "True": Card has been resolved. "False": Card has been created.
   */
  "sendMaintenanceAlert": async (maintenanceInfo, eventFlag) => {
    try {
      // Checking input parameters
      checkInput(maintenanceInfo);
      // Fetching details of devices and regions in Real Maintenance
      await fetchDeviceDetails(maintenanceInfo);

      // Information required by the alert pipeline.
      let additionalInfo = {
        "timestamp": moment().format(),
        "category": ["default"], // Required by alert service.
        "priority": "critical", // Priority that decides frequency and cool down of alerts.
        // "notify": ["<EMAIL>"],
        "title": "Maintenance Alert",
        "isResolved": eventFlag,
      };
      maintenanceInfo = { ...maintenanceInfo, ...additionalInfo };

      // Fixing maintenance type as the one saved and displayed are different.
      fixMaintenanceType(maintenanceInfo);
      let alertObject = {
        "triggerTime": String(moment().add(2, "m").startOf("m").valueOf()),
        "sourceId": `${maintenanceInfo.site}_${maintenanceInfo.cid}`,
        "source": "maintenance",
        "sourceInfo": `${maintenanceInfo.site}_${maintenanceInfo.cid}`,
        "uniqId": uuid(),
        "type": "alert",
        "response": String(moment().valueOf()),
        "runOn": "server",
        "isInitiated": "false",
        "info": JSON.stringify(maintenanceInfo),
      };
      // sails.log("alertObject: ", alertObject);
      // TODOI: Temporarily removed saving the alert till Saurav pushes his code. Remove comment after done.
      alertObject["siteId"] = alertObject["sourceId"].split("_")[0];
      await Actions.create(alertObject);
      return {
        "status": true,
      };
    } catch (error) {
      sails.log.error(`[sendMaintenanceAlert] Error while creating maintenance alert! : ${error}`);
      return {
        "status": false,
        "error": error,
      };
    }

    // Helper functions
    function checkInput(maintenanceInfo) {
      if (maintenanceInfo.notify === undefined || maintenanceInfo.notify.length == 0) {
        throw new Error("Not sending alert as Invalid notifiable users.");
      }
      if (typeof (maintenanceInfo.Real) === "string")
        maintenanceInfo.Real = helper.toJson(maintenanceInfo.Real);
      if (typeof (maintenanceInfo.Virtual) === "string")
        maintenanceInfo.Virtual = helper.toJson(maintenanceInfo.Virtual);
      if (maintenanceInfo.Real === undefined || maintenanceInfo.Virtual === undefined) {
        throw new Error("Error parsing real or virtual devices. Alert not created.");
      }
    }

    async function fetchDeviceDetails(maintenanceInfo) {
      let $componentsArr = [], $devicesArr = [], $controllerArr = [], $site;

      let componentList = maintenanceInfo.Real.component;
      fetchDeviceFromList(componentList, Component, $componentsArr);
      let deviceList = maintenanceInfo.Real.deviceId;
      fetchDeviceFromList(deviceList, Devices, $devicesArr);
      let controllerList = maintenanceInfo.Real.controller;
      fetchDeviceFromList(controllerList, Devices, $controllerArr);
      $site = Sites.find({
        "siteId": maintenanceInfo.site,
      }).then(site => site[0]);

      let components = await Promise.all($componentsArr);
      let devices = await Promise.all($devicesArr);
      let controllers = await Promise.all($controllerArr);
      let site = await $site;

      components = components.map(filterIdAndName);
      maintenanceInfo.Real.component = components;
      devices = devices.map(filterIdAndName);
      maintenanceInfo.Real.deviceId = devices;
      controllers = controllers.map(filterIdAndName);
      maintenanceInfo.Real.controller = controllers;
      let regionArr = [];
      maintenanceInfo.Real.Region.forEach(region => {
        regionArr.push(site.regions[region]);
      });
      maintenanceInfo.Real.Region = regionArr;
      maintenanceInfo.siteName = site.siteName;

      function fetchDeviceFromList(deviceList, table, $array) {
        for (let device in deviceList) {
          let $device = table.find({
            "deviceId": deviceList[device],
          }).then(device => device[0]);
          $array.push($device);
        }
      }

      function filterIdAndName(obj) {
        let newObj = {
          "id": obj.deviceId,
          "name": obj.name,
        };
        return newObj;

      }
    }

    function fixMaintenanceType(maintenanceInfo) {
      // Maintenance Type's are shown different compared to how they are saved.
      let maintenanceMap = {
        "DJ": "DeJoule",
        "Deploy": "Deployment",
        "Man": "Maintenance",
        "process": "Process",
        "breakdown": "Breakdown",
        "preventive": "Preventive",
        "sheduled": "Scheduled",
      };
      maintenanceInfo["maintenanceType"] = maintenanceMap[maintenanceInfo.type];
      if (maintenanceInfo.maintenanceType == undefined) maintenanceInfo["maintenanceType"] = "New Alert Type. Ask backend to add to list.";
    }

  },

  /**
   * <AUTHOR> <<EMAIL>>
   * @name createUnderMaintenanceGraphData
   * @param {Object} data (Contains data extracted from Elastic Search)
   * @param {Array} arr  (Contains data of different devices)
   * @summary Return the list of all Real or Virtual devices
   * @returns {object}
   */
  "createUnderMaintenanceGraphData": async (arr, data) => {
    let completeResponse = [];

    if (!arr || !data) {
      return { "err": "Params are incomplete" };
    }

    for (let k in arr) {
      let state = true;
      let jsonRes = {};
      //Extracting the particular deviceId
      let searchId = arr[k].id;
      for (let i in data) {
        let virtualData = data[i]._source.Virtual;
        let realData = data[i]._source.Real;
        for (let j in virtualData) {
          //if searchId exists
          if (virtualData[j].includes(searchId) || realData[j].includes(searchId)) {
            //timestamp ranges from creation time of card to current time
            jsonRes["timestamp"] = [moment(data[i]._source.creation_time).unix() * 1000, moment().unix() * 1000];
            jsonRes["deviceId"] = searchId;
            state = false;
            completeResponse.push(jsonRes);
            jsonRes = {};
          }
        }
      }
      //when data for deviceID doesn't exists
      if (state) {
        jsonRes["timestamp"] = [null, null];
        jsonRes["deviceId"] = searchId;
        completeResponse.push(jsonRes);
        jsonRes = {};
      }
    }
    return completeResponse;
  },

  /**
   * <AUTHOR> <<EMAIL>>
   * @name updateDeviceInternetStatus
   * @param {string} topic
   * @param {object} payload
   * @summary Called when Device changes its internet status, it also updates the status on Database
   */
  "updateDeviceInternetStatus": async (topic, payload) => {
    try {
      let splittedTopic = topic.split("/");
      let controllerId = splittedTopic[2];
      let data = await Devices.find({
        "deviceId": controllerId,
      });
      //Updating the device internet status
      await Devices.update({
        "deviceId": controllerId,
        "siteId": data[0].siteId,
      }).set({
        "internetStatus": payload.status,
      });
    } catch (error) {
      sails.log("Error in [updateDeviceInternetStatus]:", error);
    }
  },
};
