const math = require("mathjs");
math.createUnit("delC", { "definition": "1 degC" });
math.createUnit("delF", { "definition": "0.5555555555555556 delC" });
math.createUnit("cfm", { "definition": "7.4805195566834 gallon / minute" });
/**
 * Tell frontend to send two values simultaneously when temperature value is modified
 */
const Units = {
	"pressure": "kPa",
	"unitless": "NA",
	"voltage": "V",
	"frequency": "Hz",
	"flow": "gallon / min",
	"airFlow": "cfm",
	"length": "m",
	"voltamps": "kV*A",
	"voltampshrs": "kV*A*hr",
	"power": "kW",
	"temperature": "degC",
	"delTemperature": "delC",
	"time": "hr",
	"current": "A",
	"energy": "kWh",
	"velocity": "m / s",
	"liquidVolume": "gallon",
	"co2level": "mg/L",
	"tonnage": "refrigeration_ton",
	"volume": "m3",
	"massflow": "kg/hr",
	"alarm": "NA"
};

const groupTransformer = new Map();

module.exports = {
	/**
	 * @function groupTransform This Function Transforms The Group As A Fallback Check
	 * @param {string} group The group which is to be transformed
	 */
	"groupTransform": group => groupTransformer[group],

	/**
	 * @function unitTransform This Function Transforms The Unit As A Fallback Check
	 * @param {string} unit The unit which is to be transformed
	 */
	// "unitTransform": unit => unitTransformer[unit],

	/**
	 *This Function Converts Data Based On User Preference
	 * @param {string} paramGroup Parameter Group Of The Data That Is To Be Filtered
	 * @param {string} userPref User Preferred Unit(Unit In Which Data Is To Be Converted)
	 * @param {object} data Array Containing The Data
	 */
	"filterSecond": (paramGroup, userPref, data) => {
		if (!(process.env.daurelease && process.env.daurelease === "done")) {
			return data;
		}
		if (!paramGroup || !userPref || !data) {
			return data;
		}
		if (!Units[paramGroup] || Units[paramGroup] == "NA" || !paramGroup) {
			return data;
		} else {
			const dau = Units[paramGroup];
			if (dau == userPref) {
				return data;
			} else {
				if (
					userPref === "NA" ||
					userPref === "" ||
					!userPref ||
					userPref === "na"
				) {
					return data;
				} else {
					return data.map(d => {
						try {
							const converted =
								d[1] != null
									? math
										.unit(Number(d[1]), dau)
										.toNumber(userPref)
									: d[1];
							return [
								d[0],
								helper.returnFilteredNumber(
									converted
										? parseFloat(converted)
										: converted
								),
							];
						} catch (e) {
							// sails.log.debug(e);
							return d;
						}
					});
				}
			}
		}
	},

	/**
	 *This Function Converts Data Based On User Preference
	 * @param {string} paramGroupFirst Parameter Group Of data[0] That Is To Be Filtered
	 * @param {string} paramGroupSecond Parameter Group Of data[1] That Is To Be Filtered
	 * @param {string} userPrefFirst User Preferred Unit(Unit In Which Data Is To Be Converted) for data[0]
	 * @param {string} userPrefSecond User Preferred Unit(Unit In Which Data Is To Be Converted) for data[1]
	 * @param {object} data Array Containing The Data
	 */
	"filterBoth": (
		paramGroupFirst,
		paramGroupSecond,
		userPrefFirst,
		userPrefSecond,
		data
	) => {
		if (!(process.env.daurelease && process.env.daurelease === "done")) {
			return data;
		}
		const dauFirst = Units[paramGroupFirst];
		const dauSecond = Units[paramGroupSecond];
		let toFilterFirst = true;
		if (!paramGroupFirst) {
			toFilterFirst = false;
		}
		if (dauFirst == userPrefFirst) {
			toFilterFirst = false;
		}
		if (userPrefFirst === "NA" || userPrefFirst === "na") {
			toFilterFirst = false;
		}

		let toFilterSecond = true;
		if (!paramGroupSecond) {
			toFilterSecond = false;
		}
		if (dauSecond == userPrefSecond) {
			toFilterSecond = false;
		}
		if (userPrefSecond === "NA" || userPrefSecond === "na") {
			toFilterSecond = false;
		}
		sails.log.debug(toFilterFirst, toFilterSecond);
		return data.map(d => {
			try {
				const convertedSecond =
					toFilterSecond && d[1] != null
						? math
							.unit(Number(d[1]), dauSecond)
							.toNumber(userPrefSecond)
						: d[1];
				const convertedFirst =
					toFilterFirst && d[0] != null
						? math
							.unit(Number(d[0]), dauFirst)
							.toNumber(userPrefFirst)
						: d[0];
				return [
					helper.returnFilteredNumber(
						convertedFirst
							? parseFloat(convertedFirst)
							: convertedFirst
					),
					helper.returnFilteredNumber(
						convertedSecond
							? parseFloat(convertedSecond)
							: convertedSecond
					),
				];
			} catch (e) {
				// sails.log.debug(e);
				return d;
			}
		});
	},

	/**
	 *This Function Converts Data Based On User Preference
	 * @param {string} paramGroup Parameter Group Of The Data That Is To Be Filtered
	 * @param {string} userPref User Preferred Unit(Unit In Which Data Is To Be Converted)
	 * @param {string|number} data The data to be filtered Note: (This Is Not An Array)
	 */
	"filter": (paramGroup, userPref, data) => {
		if (!paramGroup || !userPref || !data) {
			return data;
		}
		let dau = Units[paramGroup];
		if (!dau || dau === "NA" || dau === "na" || dau == userPref) {
			return data;
		}
		try {
			const filtered =
				data != null
					? math.unit(Number(data), dau).toNumber(userPref)
					: data;
			return helper.returnFilteredNumber(filtered);
		} catch (e) {
			return data;
		}
	},
};
