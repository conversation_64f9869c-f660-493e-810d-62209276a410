let hbs = require("nodemailer-express-handlebars");


module.exports = {

	/**
	 * Send benchmarking related mails
	 * @param {object} data
	 * @param {string} data.from "from" what emailId mail should go
	 * @param {string} data.subject Mail's subject
	 * @param {string} data.template Template to use for sending mail(default alert)
	 * @param {Array} data.to List of email Id's to mail to
	 * @return {boolean} was able to send mail or not.
	 */
	"sendTemplateMail": (data) => {

		let attachments = {
			"alert": [{
				"filename": "logo_sm.png",
				"path": __dirname + "/../../images/logo_sm.png",
				"cid": "logo_sm"
			},
			{
				"filename": "jr.png",
				"path": __dirname + "/../../images/jr.png",
				"cid": "jr"
			}]
		};

		let template = data.template ? data.template : "alert";
		if (!sails.config.transporter[template]) {
			sails.log.error("Error sending mail sendmail.sendTemplateMail");
			data["subject"] = `Please report to Backend for ${template} not exist`;
			data["data"] = "";
			template = "alert"; // this tempate always exist
		}
		sails.config.transporter[template].use(
			"compile",
			hbs({
				"viewEngine": {
					"extName": ".hbs",
					"defaultLayout": template,
					"layoutsDir": "views",
					"partialsDir": "views/partials",
				},
				"viewPath": "views",
				"extName": ".hbs"
			})
		);
		let mailOptions = {
			"subject": data.subject,
			"from": data.from,
			"to": data.to,
			"context": data,
			"template": template
		};

		if ( attachments[template]) {
			mailOptions["attachments"] = attachments[template];
		}

		try {
			sails.config.transporter[template].sendMail(mailOptions);
			return true;
		} catch (e) {
			sails.log.error(e);
			return false;
		}
	}

};
