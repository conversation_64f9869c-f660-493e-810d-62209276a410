/**
 * @module dataDeviceService
 * @description Exposes helper functions to interact with Datadevices table
 */
const moment = require("moment-timezone");
moment.defaultFormat = "YYYY-MM-DD HH:mm:ss";
// moment.tz.add("Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6");
// moment.tz.setDefault("Asia/Kolkata");

const SHIFTS_RAMA = 3;
const SHIFTS_DURATION_RAMA = 8 * 60 ; // 8hours in minutes
const influxClient = require('./influxEnterPriseService')


module.exports = {
	// /**
	//  * @function getWeekConsumption
	//  * @param {string} siteId
	//  * @description calculates whole week consumption and and per day consumption for the current week based on the daily consumption values stored in dailyconumption table
	//  * @returns {Promise}
	//  */
	// "getWeekConsumption": function(siteId) {
	// 	let searchObj = {
	// 		"where": {
	// 			"siteId": siteId,
	// 			"timestamp": {
	// 				"gte": moment()
	// 					.subtract(7, "days")
	// 					.format("YYYY-MM-DD"),
	// 			},
	// 		},
	// 		"limit": 7,
	// 		"sort": "-1",
	// 	};
	// 	return new Promise((resolve, reject) => {
	// 		DailyConsumption.find(searchObj).exec(function(err, data) {
	// 			if (err) {
	// 				sails.log.error(err);
	// 				resolve({ "weekCons": "NA", "weekConsPattern": [] });
	// 			}
	// 			if (!data || data.length == 0) {
	// 				sails.log.info("Data not Present for site");
	// 				resolve({ "weekCons": "NA", "weekConsPattern": [] });
	// 			}
	// 			let weekConsPattern = [];
	// 			let totalWeeklyConsumption = 0;
	// 			for (let i = 0; i < data.length; i++) {
	// 				totalWeeklyConsumption += data[i].actual;
	// 				let day = moment(data[i].timestamp).unix() * 1000;
	// 				let cons = data[i].actual;
	// 				let tmp = [day, cons];
	// 				weekConsPattern.push(tmp);
	// 			}
	// 			return resolve({
	// 				"weekCons": totalWeeklyConsumption,
	// 				"weekConsPattern": weekConsPattern,
	// 			});
	// 		});
	// 	});
	// },

	/**
	 *
	 * @function getAllDataObjects
	 * @param {string} compId
	 * @description For a given componentId it returns a promise which will return componentInfo, latest data packet of that component, first data point of that day, first data point of that week and first data point of that week.
	 * It is now used in recentData API.
	 * @returns {Promise}
	 */
	"getAllDataObjects": async function (siteId, compId) {

		let nowObj = {
			"where": {
				"deviceId": compId,
				"timestamp": {
					"between": [
						moment()
							.startOf("day")
							.format("YYYY-MM-DD HH:mm"),
						moment().format("YYYY-MM-DD HH:mm"),
					],
				},
			},
			"limit": 1,
			"sort": "-1",
		};
		let dayObj = {
			"where": {
				"deviceId": compId,
				"timestamp": {
					"gte": moment()
						.startOf("day")
						.format("YYYY-MM-DD HH:mm"),
				},
			},
			"limit": 1,
			"sort": "1",
		};

		let weekObj = {
			"where": {
				"deviceId": compId,
				"timestamp": {
					"gte": moment()
						.startOf("week")
						.format("YYYY-MM-DD HH:mm"),
				},
			},
			"limit": 1,
			"sort": "1",
		};
		let monthObj = {
			"where": {
				"deviceId": compId,
				"timestamp": {
					"gte": moment()
						.startOf("month")
						.format("YYYY-MM-DD HH:mm"),
				},
			},
			"sort": "1",
			"limit": 1,
		};
		let compPromise = componentService.getComponentOrProcess(siteId, compId);
		let nowPromise = dataDeviceService.dataForRecentData(nowObj);
		let dayPromise = dataDeviceService.dataForRecentData(dayObj);
		let weekPromise = dataDeviceService.dataForRecentData(weekObj);
		let monthPromise = dataDeviceService.dataForRecentData(monthObj);
		return {
			"component": await compPromise,
			"nowData": await nowPromise,
			"dayData": await dayPromise,
			"weekData": await weekPromise,
			"monthData": await monthPromise,
		};
	},

	/**
	 *	@function dataForRecentData
	 *	@param {Object} obj structure used in find() of sails
	 *	@description Uses native dynamo sdk instead of waterline functions to query dynamo
	 *	It was written because waterline fails to get data if it is stored as JSON object in datadevices table.
	 *	To be explicitly used for datadevices table only.
	 *	@returns {Promise}
	 */
	"dataForRecentData": async obj => {
		sails.log.error(`[DynamoDB Datadevice] method=dataForRecentData obj=${JSON.stringify(obj)}`);
		let time = Object.keys(obj.where.timestamp)[0];
		let asc = true;
		if (obj.sort == "-1") {
			asc = false;
		}
		let comp;
		if (time == "gte") comp = "GE";
		else if (time == "between") comp = "BETWEEN";
		else if (time == "lte") comp = "LE";
		let timefilter = [];
		if (Array.isArray(obj.where.timestamp[time]) === true) {
			timefilter = obj.where.timestamp[time];
		} else {
			timefilter.push(obj.where.timestamp[time]);
		}
		let queryObj = {
			"TableName": "datadevices",
			"KeyConditions": {
				"deviceId": {
					"ComparisonOperator": "EQ",
					"AttributeValueList": [obj.where.deviceId],
				},
				"timestamp": {
					"ComparisonOperator": comp,
					"AttributeValueList": timefilter,
				},
			},
			"ScanIndexForward": asc,
		};
		if (obj.limit) {
			queryObj.Limit = obj.limit;
		}
		let result = await sails.config.helper.dynamo.query(queryObj).promise();
		let { Items } = result;
		let objArr = [];
		for (let packet of Items) {
			packet.data = helper.toJson(packet.data);
			objArr.push(packet);
		}
		if (objArr.length == 0) {
			return undefined;
		} else if (objArr.length == 1) {
			return objArr[0];
		}
		return objArr;
	},

	"getLastDataPoint": function (deviceId, ts) {
		//Get last data point for a particular deviceId before a time

		let searchObj = {
			"where": {
				"deviceId": deviceId,
				"timestamp": {
					"lte": moment(ts).format("YYYY-MM-DD HH:mm:ss"),
				},
			},
			"sort": "-1",
		};
		return Promise.resolve(Datadevice.findOne(searchObj));
	},
	/**
	 * @function getDeviceTypeDataByRegion
	 * @param {string} siteId
	 * @param {string} region
	 * @param {string} deviceType
	 * @description get latest data packet of all the devicetypes which are installed in a region last 15 minutes
	 */
	"getDeviceTypeDataByRegion": async function (siteId, region, deviceType) {
		let finalObj = {
			"jouleSense": [],
		};
		try {
			let devicesInRegion = await deviceService.getDevicesInRegion(
				siteId,
				region
			);
			let jouleSenses = devicesInRegion.filter(
				js => js.deviceType == deviceType
			);
			let ts = moment()
				.subtract(15, "m")
				.format("YYYY-MM-DD HH:mm");
			for (let js of jouleSenses) {
				let data = await dataDeviceService.getLastDataPoint(
					js.deviceId,
					moment()
				);
				if (data && data.timestamp >= ts) {
					let jsData = data.data;
					for (let param in jsData) {
						jsData[param.toLocaleLowerCase()] = jsData[param];
					}
					let tempObj = {};
					tempObj["name"] = js.name;
					tempObj["deviceId"] = js.deviceId;
					tempObj["deviceType"] = js.deviceType;
					tempObj["tmp"] = jsData.tmp || "NA";
					tempObj["hum"] = jsData.hum || "NA";
					finalObj.jouleSense.push(tempObj);
				}
			}
			return finalObj;
		} catch (e) {
			sails.log.error(e);
			return finalObj;
		}
	},
	"filterX": (response, xAxis) => {
		let data = [];
		response.forEach(e => {
			let ts =
				moment(e.timestamp)
					.startOf("minute")
					.unix() * 1000;
			let xPointObj = JSON.parse(e.data || "{}");
			let xPoint = xPointObj[xAxis.param];
			if (xPoint) {
				data.push([
					helper.returnFilteredNumber(ts),
					helper.returnFilteredNumber(xPoint),
				]);
			}
		});
		return data;
	},

	////to filter data of xAxis in format {{ts:yAxisPoint},{},{}}
	"filterY": (response, yAxis) => {
		let data = {};
		response.forEach(e => {
			let ts =
				moment(e.timestamp)
					.startOf("minute")
					.unix() * 1000;
			let yPointObj = JSON.parse(e.data || "{}");
			let yPoint = Number(yPointObj[yAxis.param]);
			if (yPoint) {
				data[ts] = helper.returnFilteredNumber(yPoint);
			}
		});
		return data;
	},

	//filtering numeric values
	"truncateNumber": num => {
		if (!num) return null;
		if (isNaN(Number(num))) return null;
		else if (num > 0 && num < 1) return Math.round(num * 100) / 100;
		else if (num > 1 && num < 10) return Math.round(num * 10) / 10;
		else return Math.round(num);
	},
	"filterData": (data, groupBy, params, res) => {
		if (!groupBy) groupBy = "minutes";
		let resObj = {};
		let paramsArr = params.split(",");
		if (paramsArr.length == 0) {
			return res.ok(data);
		}
		let startTs = null;
		let tmpObj = {};
		data.forEach((ele, index) => {
			let ts = moment(ele.timestamp)
				.startOf(groupBy)
				.format("YYYY-MM-DD HH:mm");
			let devData = ele.data;

			if (!startTs)
				//intitialization condition
				startTs = ts;
			//adding values to each param
			paramsArr.forEach(param => {
				if (devData[param] != undefined && devData[param] != null) {
					if (!tmpObj[param]) {
						//case for first value in each group by
						tmpObj[param] = parseFloat(devData[param]);
						tmpObj[param + "counter"] = 1;
					} else {
						tmpObj[param] += parseFloat(devData[param]);
						tmpObj[param + "counter"] += 1;
					}
				}
			});
			if (ts != startTs || index == data.length - 1) {
				//push calculated data for hour
				paramsArr.forEach(param => {
					if (tmpObj[param] === undefined || tmpObj[param] === null) {
						return;
					}
					if (!resObj[param]) {
						resObj[param] = [];
					}
					let calcValue = tmpObj[param] / tmpObj[param + "counter"];
					if (!calcValue || !isNaN(calcValue))
						resObj[param].push([
							moment(startTs).unix() * 1000,
							Math.round(calcValue * 100) / 100,
						]);
				});
				tmpObj = {};
				startTs = ts;
			}
		});
		return res.ok(resObj);
	},
	"generateSearchObj": (devId, from, to) => {
		let searchObj = null;
		if (from && to) {
			if (to < from) {
				return undefined;
			}
			searchObj = {
				"where": {
					"deviceId": devId,
					"timestamp": {
						"between": [from, to],
					},
				},
				"sort": "-1",
			};
		}
		if (!from && !to) {
			searchObj = {
				"where": {
					"deviceId": devId,
				},
				"limit": 100,
				"sort": "-1",
			};
		} else if (from && !to) {
			searchObj = {
				"where": {
					"deviceId": devId,
					"timestamp": {
						"gte": from,
					},
				},
				"sort": "-1",
			};
		}
		return searchObj;
	},

	"getMode": async (dids, from, to) => {
		(from = String(moment(from).unix() * 1000)),
		(to = String(moment(to).unix() * 1000));
		let baseFind = { "timestamp": { "between": [from, to] } };
		let total = {};
		for (let i = 0; i < dids.length; i++) {
			let data = await Mode.find({
				"sort": "-1",
				"limit": 1,
				"timestamp": { "lt": from },
				"did": dids[i],
			});
			let data2 = await Mode.find({ ...baseFind, "did": dids[i] });
			data = [...data, ...data2];
			data = data.map(d => [d.timestamp, d.changedMode]);
			total[dids[i]] = data;
		}
		return total;
	},

	/**
	 * @function paramDiff
	 * @param {string} did deviceId
	 * @param {string} groupBy
	 * @param {string} ts
	 * @param {string} params comma seperated list of parameters
	 * @description For a single device Id it will return the difference between the mentioned params of (ts) and (ts-1) where unit of -1 is decided by the 'groupBy'
	 */
	"paramDiff": async (did, groupBy, ts, params) => {
		if (!did || !moment(ts, "YYYY-MM-DD").isValid()) {
			return null;
		}
		let startTime = moment(ts);
		let endTime = moment(ts).subtract(1, groupBy);
		let startData = await dataDeviceService.getLastDataPoint(
			did,
			startTime
		);
		let endData = await dataDeviceService.getLastDataPoint(did, endTime);

		let retObj = {};
		for (let param of params) {
			let unixTs = endTime.startOf(groupBy).unix() * 1000;
			if (!startData || !endData) {
				retObj[param] = [unixTs, null];
				continue;
			}
			let startVal = startData.data[param] || startData.data[param] ===0 ? startData.data[param] : null;

			let endVal = endData.data[param] || endData.data[param] === 0 ? endData.data[param] : null;

			if (!startVal || !endVal) {
				retObj[param] = [unixTs, null];
				continue;
			}
			let diff = Number(startVal - endVal);
			if (diff < 0) diff = null;
			retObj[param] = [unixTs, diff];
		}
		return retObj;
		// return { runHrs, consumption };
	},
	/**
	 *
	 */
	"consumption": async (
		siteId,
		deviceType,
		param,
		startTime = null,
		endTime = null
	) => {
		startTime = moment()
			.subtract(1, "d")
			.format();
		endTime = moment().format();
		let devices = await deviceService.getDeviceTypes(siteId, deviceType);
		let $consumption = devices
			.map(device => device.deviceId)
			.map(deviceId =>
				dataDeviceService.customConsumption(
					deviceId,
					[param],
					startTime,
					endTime
				)
			);
		let cons = Promise.all($consumption);
		return cons;
	},
	/**
	 * @param {string} deviceId
	 * @param {string} startTime in format YYYY-MM-DD HH:mm:ss otherwise results can be unexpected
	 * @param {string} endTime in format YYYY-MM-DD HH:mm:ss otherwise results can be unexpected
	 * @param {Array} params array of parameters whose difference is required
	 * @description Calculates consumption for a deviceId based on the param keys passed between 2 timestamps.
	 * Known problems: Queries data based on last data point available which is less than given timestamps, which means if no data is available for a month, it will calculate consumptions based on a month old data point, making it inaccurate.
	 * Also, if the deviceId[param] value in the data packet is null for either of the start or end timestamps, it will get NaN as a difference and will return
	 * blank consumption data.
	 * @returns {object} {deviceId, param:{<param>:<val>,<param>:<val>}
	 *
	 */
	"customConsumption": async (deviceId, params, startTime, endTime) => {
		startTime = moment(startTime).format();
		endTime = moment(endTime).format();
		const $startData = dataDeviceService.getDeviceData(deviceId, startTime, 10, 10, params);

		const $endData = dataDeviceService.getDeviceData(deviceId, endTime, 10, 10, params);
		const [startData, endData] = await Promise.all([$startData, $endData]);
		const diffObj = {
			deviceId,
			"param": {},
		};
		try {
			if (startData === null || endData === null) return diffObj;
			params.map(param => {
				let firstPoint = startData.data;
				let secondPoint = endData.data;
				const diff = secondPoint[param] - firstPoint[param];
				if (!isNaN(diff)) diffObj.param[param] = diff;
			});
		} catch (err) {
			sails.log.error(
				"Error : customConsumption",
				err,
				deviceId,
				params,
				startTime,
				endTime
			);
		}

		return diffObj;
	},
	/**
	 * @function estimatedElectricityConsumption
	 * @param {string} deviceId
	 * @param {string} startTime in format YYYY-MM-DD HH:mm:ss otherwise results can be unexpected
	 * @param {string} endTime in format YYYY-MM-DD HH:mm:ss otherwise results can be unexpected
	 * @param {string} param parameter whose difference is required
	 * @description Calculates nearest consumption values possible for a deviceId based on the param key passed between 2 timestamps.
	 * It will query for +-15 data values within timestamp range and calculate difference between the params value passed.
	 * This is a copy of customConsumption with the exception that it calls getNearestValidDataPoint() instead of getLastDataPoint() internally.
	 */
	"estimatedElectricityConsumption": async (deviceId, param, startTime, endTime) => {
		startTime = moment(startTime).format();
		endTime = moment(endTime).format();

		const $startData = dataDeviceService.getNearestValidDataPoint(deviceId, startTime, param, 15);
		const $endData = dataDeviceService.getNearestValidDataPoint(deviceId, endTime, param, 15);
		const [startData, endData] = await Promise.all([$startData, $endData]);

		const returnObject = {
			deviceId,
			"param": {},
		};
		try {
			if (!startData || !endData) return returnObject;
			let firstPoint = startData.data;
			let secondPoint = endData.data;
			const diff = secondPoint[param] - firstPoint[param];
			if (!isNaN(diff)) returnObject.param[param] = diff;
		} catch (error) {
			sails.log.error("[estimatedElectricityConsumption] Error! :", deviceId, param, startTime, endTime, error);
		}

		return returnObject;
	},
	/**
	 * @function getNearestValidDataPoint
	 * @param {string} deviceId
	 * @param {string} timestamp
	 * @param {string} param The key present in the data packet whose validity is being checked on.
	 * @param {integer} timerange The +- difference on data values to be queried.
	 */
	"getNearestValidDataPoint": async (deviceId, timestamp, param, timerange) => {
		let deviceData;
		const upperLimit = moment(timestamp).add(timerange, "m").format();
		const lowerLimit = moment(timestamp).subtract(timerange, "m").format();
		const query = {
			deviceId,
			"timestamp": {
				"between": [lowerLimit, upperLimit]
			}
		};
		try {
			deviceData = await Datadevice.find(query);
		} catch (error) {
			sails.log.error("[dataDeviceService >> getNearestValidDataPoint] Error!", error);
			return null;
		}
		if (!deviceData || deviceData.length === 0) return null;
		let timestampMoment = moment(timestamp);
		let closestDeviceData = deviceData
			.map(datapoint => {
				datapoint["sortKey"] = Math.abs(moment(datapoint["timestamp"]).diff(timestampMoment, "m"));
				return datapoint;
			})
			.sort((datapoint1, datapoint2) => datapoint1["sortKey"] - datapoint2["sortKey"])
			.filter(datapoint => datapoint.data[param] !== "null");
		if (closestDeviceData.length === 0) return null;
		return closestDeviceData[0];
	},
	"isValidGroupBy": function (groupBy) {
		return ["hour", "day", "month"].indexOf(groupBy) !== -1;
	},
	"segregateTimeByGroupBy": function (startTime, endTime, groupBy) {
		let upperCount = 800, segregrateTS = [];
		const FORMAT = "YYYY-MM-DD HH:mm:00";

		if (startTime.constructor.name !== "Moment") {
			startTime = moment(startTime);
		}
		if (endTime.constructor.name !== "Moment") {
			endTime = moment(endTime);
		}

		let count = endTime.diff(startTime, groupBy);
		if (count > upperCount) return [];

		for (let i = 0; i < count; i++) {
			segregrateTS.push(startTime.clone().add(i, groupBy).format(FORMAT));
		}

		return segregrateTS;
	},
	/**
	 *
	 * @param {string} deviceId Device Id to query
	 * @param {string} ts Timestamp to get data of
	 * @param {integer} positive upper limit in minutes to add to in timestamp
	 * @param {integer} negative lower limit in minutes to add to in timestamp
	 * @returns {object} Data Object or Null
	 */
	"getDeviceData": async function (deviceId, ts, positive = 1, negative = 0, params = []) {
		let tsMoment, tsUpperLimit, tsLowerLimit, dataPoints,
			query, deviceData, paramsObj;
		ts = moment(ts).format("YYYY-MM-DD HH:mm:00");
		params = params.constructor.name === "Array" ? params : [];

		if (!deviceId) {
			return null;
		}
		positive = parseInt(positive);
		negative = parseInt(negative);
		tsUpperLimit = moment(ts).add(positive, "m").format("YYYY-MM-DD HH:mm:00");
		tsLowerLimit = moment(ts).subtract(negative, "m").format("YYYY-MM-DD HH:mm:00");
		query = {
			deviceId,
			"timestamp": {
				"between": [tsLowerLimit, tsUpperLimit]
			}
		};
		try {
			dataPoints = await Datadevice.find(query);
		} catch (e) {
			throw (e);
		}
		if (!dataPoints || dataPoints.length === 0) {
			return null;
		}
		deviceData = dataPoints.find(dataPoint => dataPoint["timestamp"] === ts);
		if (deviceData && params.length === 0) {
			return deviceData;
		} else {
			tsMoment = moment(ts);
			dataPoints = dataPoints.map(dataPoint => {
				dataPoint["sortKey"] = Math.abs(
					moment(dataPoint["timestamp"]).diff(tsMoment, "m")
				);
				return dataPoint;
			}).sort((dataPoint1, dataPoint2) => {
				return dataPoint1["sortKey"] - dataPoint2["sortKey"];
			});
			// dataPoints are sorted list of data in terms of timestamp
			paramsObj = params.reduce((acc, param) => {
				acc[param] = undefined; return acc;
			}, {});
			for (let dataPoint of dataPoints) {
				for (let param in paramsObj) {
					if (paramsObj[param] === undefined) {
						let value = dataPoint.data[param];
						if (!!value && dataPoint.data[param] !== "null") {
							paramsObj[param] = value;
						}
					}
				}
			}
			dataPoints[0]["timestamp"] = ts; // we normalize ts
			dataPoints[0]["data"] = {...dataPoints[0]["data"], ...paramsObj};
			return dataPoints[0];
		}


	},

	/**
	 * get First Known Device Data Value Between 2 given dates
	 * @param {string} deviceId Device Id to query
	 * @param {string} startTime start Time of data
	 * @param {string} endTime end Time of data
	 */
	"getFirstKnownDeviceDataValueBetween2Dates": async function(
		deviceId,
		startTime,
		endTime
	) {

		let startTimeMoment = moment(startTime);
		let endTimeMoment = moment(endTime);

		if (startTimeMoment.isValid() === false
			|| endTimeMoment.isValid() === false) {
			return undefined;
		} else {
			startTime = startTimeMoment.format("YYYY-MM-DD HH:mm:00");
			endTime = endTimeMoment.format("YYYY-MM-DD HH:mm:00");
		}

		if (typeof deviceId !== "string" ) {
			return undefined;
		}

		let dataPoint, query;

		query = {
			deviceId,
			"timestamp": {
				"between": [startTime, endTime]
			},
			"limit": 1,
			"sort": "1",
		};

		try {
			dataPoint = await Datadevice.find(query);
		} catch (e) {
			throw (e);
		}

		if (dataPoint.length ===0 ) {
			return undefined;
		} else {
			if (dataPoint.length !== 1) {
				throw new Error("datadeviceservice.getFirstKnownDeviceDataValueBetween2Dates Unexpected output from database");
			} else {
				dataPoint = dataPoint[0];
				dataPoint.data = helper.toJson(dataPoint.data);
				return dataPoint;
			}
		}
	},

	/**
	 * get Last Known Device Data Value Between 2 given dates
	 * @param {string} deviceId Device Id to query
	 * @param {string} startTime start Time of data
	 * @param {string} endTime end Time of data
	 */
	"getLastKnownDeviceDataValueBetween2Dates": async function(
		deviceId,
		startTime,
		endTime
	) {

		let startTimeMoment = moment(startTime);
		let endTimeMoment = moment(endTime);

		if (startTimeMoment.isValid() === false
			|| endTimeMoment.isValid() === false) {
			return undefined;
		} else {
			startTime = startTimeMoment.format("YYYY-MM-DD HH:mm:00");
			endTime = endTimeMoment.format("YYYY-MM-DD HH:mm:00");
		}

		if (typeof deviceId !== "string" ) {
			return undefined;
		}

		let dataPoint, query;

		query = {
			deviceId,
			"timestamp": {
				"between": [startTime, endTime]
			},
			"limit": 1,
			"sort": "-1",
		};

		try {
			dataPoint = await Datadevice.find(query);
		} catch (e) {
			throw (e);
		}

		if (dataPoint.length ===0 ) {
			return undefined;
		} else {
			if (dataPoint.length !== 1) {
				throw new Error("datadeviceservice.getLastKnownDeviceDataValueBetween2Dates Unexpected output from database");
			} else {
				dataPoint = dataPoint[0];
				dataPoint.data = helper.toJson(dataPoint.data);
				return dataPoint;
			}
		}
	},

	"calculateConsumption": function (didDeviceDataObject, parameter, groupBy, startTime) {
		let currConsumption = 0, nextConsumption = 0, deviceWiseConsumption = {},
			currPacket, nextPacket, consumption = [], deviceData, ts;

		for (let deviceId in didDeviceDataObject) {
			deviceData = didDeviceDataObject[deviceId];
			ts = startTime;
			for (let index = 0; index < deviceData.length - 1; index++) {
				currPacket = deviceData[index];
				nextPacket = deviceData[index + 1];
				if (!currPacket || !nextPacket) {
					// add 1 groupBy to previous timestamp
					ts = moment(ts).add(1, groupBy).format("YYYY-MM-DD HH:mm:00");
					consumption.push([ts, null]);
					continue;
				}
				// ts = nextPacket["timestamp"];
				ts = currPacket["timestamp"];
				currConsumption = helper.getConsumptionFromDeviceData(
					helper.toJson(currPacket.data), parameter
				);
				nextConsumption = helper.getConsumptionFromDeviceData(
					helper.toJson(nextPacket.data), parameter
				);
				if (!currConsumption || !nextConsumption) {
					ts = moment(ts).add(1, groupBy).format("YYYY-MM-DD HH:mm:00");
					consumption.push([ts, null]);
					continue;
				}
				if (currConsumption === "null" || nextConsumption === "null") {
					ts = moment(ts).add(1, groupBy).format("YYYY-MM-DD HH:mm:00");
					consumption.push([ts, null]);
					continue;
				}
				consumption.push([ts, nextConsumption - currConsumption]);
			}
			deviceWiseConsumption[deviceId] = consumption;
			consumption = [];
		}

		return deviceWiseConsumption;
	},
	"runHourAnalysisRama": async function (siteId, dates, deviceList) {
		let response = { "data": {} };
		let deviceDataShift = {};
		const FORMAT = "YYYY-MM-DD HH:mm";
		// let shiftsTiming = [[8, 16], [16, 24], [24, 32]];
		try {
			let shift0Start, shift0Stop,
				shift1Start, shift1Stop,
				shift2Start, shift2Stop;
			for (let formattedDate of dates) {
				let date = moment(formattedDate).startOf("day");
				shift0Start = date.add(8, "hour").format(FORMAT);
				shift0Stop = date.add(8, "hour").format(FORMAT);
				shift1Start = shift0Stop;
				shift1Stop = date.add(8, "hour").format(FORMAT);
				shift2Start = shift1Stop;
				shift2Stop = date.add(8, "hour").format(FORMAT);

				for (let i = 0; i < deviceList.length; i++) {
					let deviceId = deviceList[i];
					if (!deviceDataShift[deviceId]) {
						deviceDataShift[deviceId] = {};
					}
					deviceDataShift[deviceId][formattedDate] = await Promise.all([
						readShiftDataTS(deviceId, shift0Start, shift0Stop),
						readShiftDataTS(deviceId, shift1Start, shift1Stop),
						readShiftDataTS(deviceId, shift2Start, shift2Stop),
					]);

				}

			}
			//  deviceDataShift[deviceId][date] = [shift0Start, shift0Stop, --- shift2Stop ]
			Object.keys(deviceDataShift).forEach(deviceId => {
				let dates = deviceDataShift[deviceId];
				let shiftRunHours = {
					"production_shiftA": 0,
					"production_shiftB": 0,
					"production_shiftC": 0
				};
				// add runningMin of all the dates to get sum of run minutes device ran
				Object.keys(dates).forEach(shiftTime => {
					let shiftDatas = deviceDataShift[deviceId][shiftTime];
					shiftRunHours["production_shiftA"] += ( calculateRunningHours(shiftDatas[0]) );
					shiftRunHours["production_shiftB"] += ( calculateRunningHours(shiftDatas[1]) );
					shiftRunHours["production_shiftC"] += (calculateRunningHours(shiftDatas[2]) );
					response["data"][deviceId] = [
						shiftRunHours["production_shiftA"],
						shiftRunHours["production_shiftB"],
						shiftRunHours["production_shiftC"]
					];
				});
			});
			return response["data"];

		} catch (e) {
			sails.log.error(e);
			return response;
		}

		async function readShiftDataTS(deviceId, startDate, endDate) {
			return dataDeviceService.aggreagateBetween2TS(
				deviceId,
				"runminutes",
				startDate,
				endDate
			);
		}
		function calculateRunningHours(shift1) {
			if (!shift1) {
				// return NaN;
				return 0;
			}
			let shift1Data = helper.toJson(shift1["data"]);

			if (!shift1Data) {
				// return NaN
				return 0;
			}
			let shift1RunMin = shift1Data["runminutes"];

			return Number(shift1RunMin);
		}
	},
	"tonsteamperproductRama": async function (siteId, startDate, endDate, plantId) {
		// {duplexPaper :
		// 	{  shiftA: [value,aboveBenchmark],
		// 	   shiftB: [value,aboveBenchmark],
		// 	   shiftC: [value,aboveBenchmark],
		// 	},

		let paperProduced, streamConsumed, ratio = {
			"duplexPaper": {
				"shiftA": [0, 0],
				"shiftB": [0, 0],
				"shiftC": [0, 0],
			},
			"newsPaper": {
				"shiftA": [0, 0],
				"shiftB": [0, 0],
				"shiftC": [0, 0],
			},
			"printingPaper": {
				"shiftA": [0, 0],
				"shiftB": [0, 0],
				"shiftC": [0, 0],
			},
		};
		try {
			// get production of paper on Date "date"
			// get steam consumption on Date "date"
			paperProduced = await this.getProductionOfPaper(siteId, startDate, endDate);
			streamConsumed = await this.getShiftWiseConsumptionInRama(siteId, startDate, endDate, plantId, "steam");

			// duplex
			ratio["duplexPaper"]["shiftA"][0] = helper.returnFilteredNumber( (streamConsumed["shift0"] / 1000 ) / paperProduced["duplex"][0]); // divide by 1000 to convert kg to ton
			ratio["duplexPaper"]["shiftB"][0] = helper.returnFilteredNumber( (streamConsumed["shift1"] / 1000 ) / paperProduced["duplex"][1]);
			ratio["duplexPaper"]["shiftC"][0] = helper.returnFilteredNumber( (streamConsumed["shift2"] / 1000 ) / paperProduced["duplex"][2]);
			// news
			ratio["newsPaper"]["shiftA"][0] = helper.returnFilteredNumber( (streamConsumed["shift0"] / 1000 ) / paperProduced["news"][0]);
			ratio["newsPaper"]["shiftB"][0] = helper.returnFilteredNumber( (streamConsumed["shift1"] / 1000 ) / paperProduced["news"][1]);
			ratio["newsPaper"]["shiftC"][0] = helper.returnFilteredNumber( (streamConsumed["shift2"] / 1000 ) / paperProduced["news"][2]);
			// printing
			ratio["printingPaper"]["shiftA"][0] = helper.returnFilteredNumber( (streamConsumed["shift0"] / 1000 ) / paperProduced["printing"][0]);
			ratio["printingPaper"]["shiftB"][0] = helper.returnFilteredNumber( (streamConsumed["shift1"] / 1000 ) / paperProduced["printing"][1]);
			ratio["printingPaper"]["shiftC"][0] = helper.returnFilteredNumber( (streamConsumed["shift2"] / 1000 ) / paperProduced["printing"][2]);


			for (let papertype in ratio) {
				for (let shift in ratio[papertype]) {
					if (isFinite(ratio[papertype][shift][0]) === false) ratio[papertype][shift][0] = 0;
				}
			}

			return ratio;


		} catch (e) {
			sails.log.error(e);
			return ratio;
		}
	},
	"tonElectricityperproductRama": async function (siteId, startDate, endDate, plantId) {

		let paperProduced, ElecConsumed, ratio = {
			"duplexPaper": {
				"shiftA": [0, 0],
				"shiftB": [0, 0],
				"shiftC": [0, 0],
			},
			"newsPaper": {
				"shiftA": [0, 0],
				"shiftB": [0, 0],
				"shiftC": [0, 0],
			},
			"printingPaper": {
				"shiftA": [0, 0],
				"shiftB": [0, 0],
				"shiftC": [0, 0],
			},
		};
		try {
			// get production of paper on Date "date"
			// get electricity consumption on Date "date"
			paperProduced = await this.getProductionOfPaper(siteId, startDate, endDate);
			ElecConsumed = await this.getShiftWiseConsumptionInRama(siteId, startDate, endDate, plantId, "electricity");

			// duplex
			ratio["duplexPaper"]["shiftA"][0] = helper.returnFilteredNumber( (ElecConsumed["shift0"]) / paperProduced["duplex"][0]);
			ratio["duplexPaper"]["shiftB"][0] = helper.returnFilteredNumber( (ElecConsumed["shift1"]) / paperProduced["duplex"][1]);
			ratio["duplexPaper"]["shiftC"][0] = helper.returnFilteredNumber( (ElecConsumed["shift2"]) / paperProduced["duplex"][2]);
			// news
			ratio["newsPaper"]["shiftA"][0] = helper.returnFilteredNumber( (ElecConsumed["shift0"]) / paperProduced["news"][0]);
			ratio["newsPaper"]["shiftB"][0] = helper.returnFilteredNumber( (ElecConsumed["shift1"]) / paperProduced["news"][1]);
			ratio["newsPaper"]["shiftC"][0] = helper.returnFilteredNumber( (ElecConsumed["shift2"]) / paperProduced["news"][2]);
			// printing
			ratio["printingPaper"]["shiftA"][0] = helper.returnFilteredNumber( (ElecConsumed["shift0"]) / paperProduced["printing"][0]);
			ratio["printingPaper"]["shiftB"][0] = helper.returnFilteredNumber( (ElecConsumed["shift1"]) / paperProduced["printing"][1]);
			ratio["printingPaper"]["shiftC"][0] = helper.returnFilteredNumber( (ElecConsumed["shift2"]) / paperProduced["printing"][2]);

			for (let papertype in ratio) {
				for (let shift in ratio[papertype]) {
					if (isFinite(ratio[papertype][shift][0]) === false) ratio[papertype][shift][0] = 0;
				}
			}

			return ratio;


		} catch (e) {
			sails.log.error(e);
			return ratio;
		}
	},
	"getProductionOfPaper": async function (siteId, startDate, endDate) {
		let paperProduced = {
			"duplex": [0, 0, 0], // shift 1, 2, 3
			"news": [0, 0, 0],
			"printing": [0, 0, 0]
		};
		try {
			let productionDatas, shift1, shift2, shift3;
			startDate = moment(startDate).format("YYYY-MM-DD");
			endDate = moment(endDate).format("YYYY-MM-DD");
			if (moment(endDate).diff(moment(), "day") === 0 ) {
				// endDate is same day
				endDate = moment().subtract(1, "day").format("YYYY-MM-DD");
			}
			productionDatas = await productionService.getCustomProduction(siteId, startDate, endDate);
			productionDatas.forEach(productionData => {
				shift1 = shiftDataToInt(productionData["production_shiftA"]);
				shift2 = shiftDataToInt(productionData["production_shiftB"]);
				shift3 = shiftDataToInt(productionData["production_shiftC"]);
				paperProduced["duplex"][0] += shift1["duplexPaper"];
				paperProduced["duplex"][1] += shift2["duplexPaper"];
				paperProduced["duplex"][2] += shift3["duplexPaper"];

				paperProduced["news"][0] += shift1["newsPaper"];
				paperProduced["news"][1] += shift2["newsPaper"];
				paperProduced["news"][2] += shift3["newsPaper"];

				paperProduced["printing"][0] += shift1["printingPaper"];
				paperProduced["printing"][1] += shift2["printingPaper"];
				paperProduced["printing"][2] += shift3["printingPaper"];
			});
		} catch (e) {
			sails.log.error(e);
		}
		return paperProduced;

		function shiftDataToInt(shiftData) {
			let duplex, news, print;
			shiftData = helper.toJson(shiftData);
			let responseData = {
				"duplexPaper": 0,
				"newsPaper": 0,
				"printingPaper": 0
			};
			duplex = parseInt(shiftData["duplexPaper"]);
			news = parseInt(shiftData["newsPaper"]);
			print = parseInt(shiftData["printingPaper"]);
			if (!isNaN(duplex)) {
				responseData["duplexPaper"] = duplex;
			}
			if (!isNaN(news)) {
				responseData["newsPaper"] = news;
			}
			if (!isNaN(print)) {
				responseData["printingPaper"] = print;
			}
			return responseData;

		}
	},
	/**
	 * Given a plant and its start and endtime, this function will calculate consumption
	 * of that plant for each shift between the 2 time. So shift0, shift1 & shift2
	 * total consumption between 2 time.
	 * @param {sting} siteId Unique id of site
	 * @param {string} startData start date of consumption
	 * @param {string} endData enddate for consumption calulcation
	 * @param {string} plantId Id of plant whose cosnumption needed to be caculated
	 * @param {string} consumptionType ENUM[electricity, steam] which tell what type of consumption to calculate
	 */
	"getShiftWiseConsumptionInRama": async function (siteId, startData, endData, plantId, consumptionType) {

		const SHIFTS = SHIFTS_RAMA; // we have siteId , so we can generlize this function when needed
		const DURATION = SHIFTS_DURATION_RAMA;
		const FORMAT = "YYYY-MM-DD HH:mm:00";

		let consumption = {
			"shift0": 0,
			"shift1": 0,
			"shift2": 0
		};
		let dates = dataDeviceService.segregateTimeByGroupBy(startData, endData, "day");
		let queries = {};

		for (let date of dates) {
			let startDate = moment(date);
			queries[date] = [];

			for (let shift = 0; shift < SHIFTS; shift++) {
				let shiftStartTime = startDate.clone().add(shift * DURATION, "minute").format(FORMAT);
				let shiftEndTime = startDate.clone().add((shift + 1) * DURATION - 1, "minute").format(FORMAT);
				switch (consumptionType) {
					case "steam":
						queries[date].push(
							this.getSteamConsumed(siteId, shiftStartTime, shiftEndTime, plantId) // shift[i]
						);
						break;
					case "electricity":
						queries[date].push(
							this.getElectricityConsumed(siteId, shiftStartTime, shiftEndTime, plantId) // shift[i]
						);
						break;
					default:
						break;
				}
			}
		}

		for (let date in queries) {
			let [shift0, shift1, shift2] = await Promise.all(queries[date]);
			if (!isNaN (shift0)) {
				consumption["shift0"] += shift0;
			}
			if (!isNaN (shift1)) {
				consumption["shift1"] += shift1;
			}
			if (!isNaN (shift2)) {
				consumption["shift2"] += shift2;
			}
		}


		return consumption;


	},

	/**
	 * Get the steam consumed between 2 times or NaN in case of any error.
	 */
	"getSteamConsumed": async function (siteId, startDate, endDate, plantId) {
		try {

			let plantDataPKT = await this.aggreagateBetween2TS(
				plantId,
				"conssteam",
				startDate,
				endDate
			);

			if (!plantDataPKT) {
				return NaN;
			}
			let plantData = helper.toJson(plantDataPKT["data"]);

			if (!plantData) {
				return NaN;
			}

			return Number(plantData.conssteam);

		} catch (e) {
			sails.log.error(e);
			return NaN;
		}
	},
	"getElectricityConsumed": async function (siteId, startData, endData, plantId) {
		try {

			let plantStartPacket = await this.getDeviceData(plantId, startData, 10, 10, ["conselec"]);
			let plantEndPacket = await this.getDeviceData(plantId, endData, 10, 10, ["conselec"]);

			if (!plantStartPacket || !plantEndPacket) {
				return NaN;
			}
			let plantStartData = helper.toJson(plantStartPacket["data"]);
			let plantEndData = helper.toJson(plantEndPacket["data"]);

			if (!plantStartData || !plantEndData) {
				return NaN;
			}

			let plantStartCons = plantStartData["conselec"];
			let plantEndCons = plantEndData["conselec"];

			return plantEndCons - plantStartCons;

		} catch (e) {
			sails.log.error(e);
			return NaN;
		}
	},
	/**
	 * Get devices's parameter data between 2 timestamp and sum the parameter value
	 * @param {string} plantId Plant id to uniquely identify a plant
	 * @param {string} param Parameter of plant to get data of
	 * @param {string} startTime Start time of query
	 * @param {string} endTime End time of query
	 */
	"aggreagateBetween2TS": async function(plantId, param, startTime, endTime) {
		try {

			let query = `select sum(measure_value::double) as ${param} from smartjoules.aggregatedParams
				 where
					measure_name='${param}'
					and deviceId='${plantId}'
					and time between '${startTime}'-330m and '${endTime}'-330m`;

			let data = await timeSeries.AggregatedParams.rawQuery(query, [param]);

			return {"data": data[0]};

		} catch (e) {
			sails.log.error("dashboardservice::aggreagateBetween2TS ", e);
			return [];
		}
	},
  getDataBetweenTwoTimeStampsFromInfluxdb: async function({deviceId,startTime,endTime,sortInDescending=false,format="raw"}){
    const measurement = await this._getMeasurementByDeviceId(deviceId);
    const assetColumnName = measurement == 'components' ? 'componentId' : 'deviceId';
    const query = `
      from(bucket: "device_component/autogen")
        |> range(start: {{startTime}}, stop: {{endTime}})
        |> filter(fn: (r) => r["_measurement"] == "{{measurement}}")
        |> filter(fn: (r) => r["{{assetColumnName}}"] == "{{deviceId}}")
        |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
        |> sort(columns: ["_time"], desc: {{sortInDescending}})
        |> yield(name: "deviceData")
    `;
    const dataPoints = await influxClient.runQuery(query, {
      replacements: {
        measurement,
        startTime,
        endTime,
        deviceId,
        sortInDescending,
        assetColumnName
      },
      debug: true,
    });


    return dataPoints.map(record => {
      const {
        siteId,
        _time: timestamp
      } = record
      delete record._measurement;
      delete record._start;
      delete record._stop;
      delete record.field1;
      delete record.result;
      delete record.deviceId;
      delete record.componentId;
      delete record._time;
      delete record.table;

      Object.keys(record)
        .forEach(key => {
          record[key] = isNaN(record[key]) || record[key] === '' ? record[key] : Number(record[key]);
        })
      return {
        deviceId,
        siteId,
        timestamp: moment(timestamp)
          .format('YYYY-MM-DD HH:mm:ss'),
        data: record
      }
    })
  },
  _getMeasurementByDeviceId: async function(deviceId){

    //it can be potentially component id because component id format is `${siteId}_${incremental}`
    if(isNaN(deviceId)){
      return "components"
    }
    return "device";
  }


};
