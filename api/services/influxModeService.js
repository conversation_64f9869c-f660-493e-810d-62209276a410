const Influx = require("influx");
const Joi = require('joi');
const moment = require("moment-timezone");

const config = {
	host: process.env.INFLUX_DEV_HOST || 'dev-timeseries.smartjoules.org',
	username: process.env.INFLUX_DEV_USERNAME,
	password: process.env.INFLUX_DEV_PASSWORD,
	port: 80
};

// Checking for environment variables. Not crashing server on boot since it only impacts mode ingestion.
const configSchema = Joi.object()
	.keys({
		host: Joi.string().required(),
		username: Joi.string().required(),
		password: Joi.string().required(),
	})
	.unknown(true);
const { error } = configSchema.validate(config);
if (error && process.env.NODE_ENV !== "development")
	sails.log.error(`InfluxDB credentials not found in environment variable: - ${error}`);
// TODO: Add sentry integration

class InfluxService {
	constructor(siteId) {
		this.database = "modes-event-log";
		this.siteId = siteId;
		this.timestamp = this._formattedTimestamp();

		this.connection = new Influx.InfluxDB({
			host: config.host,
			username: config.username,
			password: config.password,
			port: config.port,
			schema: [
				{
					measurement: "siteId",
					tags: ["componentId", "control_key"],
					fields: {
						currMode: Influx.FieldType.INTEGER,
						deviceId: Influx.FieldType.STRING
					},
				}
			],
			database: this.database,
		});
	}

	async insert(payload) {
		try {
			let databases = await this.connection.getDatabaseNames();
			if (databases.indexOf(this.database) === -1) {
				throw new Error(`Database: "${this.database}" not found in InfluxDB. Either the database does not exist or the configured user does not have access to it. Unable to save data.`);
			}
			let response = await this.connection.writePoints(
				payload,
				{
					database: this.database,
					precision: Influx.Precision.Seconds,
				}
			);
			return response;
		} catch (error) {
			throw new Error(error)
		}
	}

	async packetBuilder(uniqDeviceIdAndItsModes, deviceConfigs) {
		const flattenedPackets = this._flattenPackets(uniqDeviceIdAndItsModes);
		const {componentControlConfigurations, componentList} = await this._fetchRelevantComponentControlConfigs(deviceConfigs);
		const flattenedPacketsWithComponentIds = this._addComponentIds(flattenedPackets, componentControlConfigurations);
		const ingestionPayload = this._formatToInfluxDBIngestionPackets(flattenedPacketsWithComponentIds);
		return {packets: ingestionPayload, componentList};
	}

	_flattenPackets(uniqDeviceIdAndItsModes) {
		const flattenedPackets = [];
		Object.keys(uniqDeviceIdAndItsModes).forEach(deviceId => {
			const devicePacket = uniqDeviceIdAndItsModes[deviceId];
			Object.keys(devicePacket).forEach(commandParameter => {
				const mode = devicePacket[commandParameter];
				flattenedPackets.push({
					deviceId,
					commandParameter,
					mode,
					timestamp: this.timestamp,
					siteId: this.siteId
				});
			});
		});
		return flattenedPackets;
	}

	async _fetchRelevantComponentControlConfigs(deviceConfigs) {
		let componentIdSet = new Set(), deviceIdComponentsMap = {};
		Object.keys(deviceConfigs).forEach(deviceId => {
			let deviceConfiguration = deviceConfigs[deviceId];
			let componentListString = deviceConfiguration["componentId"];
			componentListString = componentListString.replace(/\s/g, ''); // Removing any whitespaces from the comma separated component list
			let componentList = componentListString.split(",");
			deviceIdComponentsMap[deviceId] = componentList;
			componentList.forEach(componentIdSet.add, componentIdSet);
		});
		let componentList = [...componentIdSet];
		let $componentConfigurations = componentList.map(deviceId => Component.findOne({deviceId}));
		let componentConfigQueries = await Promise.allSettled($componentConfigurations);
		
		let componentControlConfigurations = componentConfigQueries.map(result => {
			if (result.status === "fulfilled" ) return result.value;
			else return null
		}).filter(Boolean)
		.map(config => {
			if (config.controls) helper.convertStringArrayToJSONArray(config.controls)
			if (Array.isArray(config.controls) && config.controls.length == 0) return null; // Modes can only be applied to control parameters. 
			return config
		}).filter(Boolean);
		return {componentControlConfigurations, componentList};
	}

	_addComponentIds(flattenedPackets, componentControlConfigurations){
		const newPackets = [];
		flattenedPackets.forEach(packet => {
			let componentFound = false;
			componentControlConfigurations.forEach(component => {
				const { controls } = component;
				controls.forEach(parameter => {
					if(parameter.deviceId == packet.deviceId && (parameter.key == packet.commandParameter || parameter.device_abbr == packet.commandParameter)){
						componentFound = true;
						let componentId = component.deviceId;
						newPackets.push({
							...packet,
							componentId
						});
					}
				})
			});
			if(!componentFound){
				newPackets.push({
					...packet,
					componentId: "NA"
				});
			}
		});
		return newPackets;
	}

	_formatToInfluxDBIngestionPackets(flattenedPacketsWithComponentIds){
		const payload = flattenedPacketsWithComponentIds.map(packet => {
			const formattedMode = this._formatMode(packet.mode);
			const formattedPacket = {
				measurement: packet.siteId, // TODO: Confirm if this is ideal or if we should save siteId as a tag
				tags: {
					componentId: packet.componentId,
					control_key: packet.commandParameter
				},
				fields: {
					deviceId: packet.deviceId,
					currMode: formattedMode,
				},
				timestamp: new Date(packet.timestamp)
			};
			if(formattedMode === -1) formattedPacket.fields["mode"] = packet.mode; // Saving mode as string as well if unknown mode received for debugging.
			return formattedPacket;
		});
		return payload;
	}

	_formatMode(mode){
		const modeMap = {
			"jouletrack": 0,
			"joulerecipe": 1,
			"thermostat": 2
		}
		const formattedMode = modeMap[mode];
		if(formattedMode != undefined) return formattedMode;
		else return -1;
	}

	_formattedTimestamp() {
		const dateFormat = "YYYY-MM-DDTHH:mm:ssZ"; //RFC 339 format for influxdb
		const timezone = "Asia/Kolkata";
		const currentTime = moment().tz(timezone).valueOf();
		const timestamp = moment(currentTime).format(dateFormat);
		return timestamp;
	}
}

module.exports = InfluxService;