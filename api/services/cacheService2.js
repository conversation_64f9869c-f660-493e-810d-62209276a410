/**
 * @module cacheService2
 */
const moment = require("moment-timezone");
moment.tz.add("Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6");
moment.tz.setDefault("Asia/Kolkata");

const { promisify } = require("util");

let redis = require("redis"),
	client = redis.createClient(sails.config.cachePort, sails.config.cacheHost, {
		"retry_strategy": function (options) {
			if (options.error && options.error.code === "ECONNREFUSED") {
				// End reconnecting on a specific error and flush all commands with a individual error
				return new Error("The server refused the connection");
			}
			if (options.total_retry_time > 1000 * 60 * 60) {
				// End reconnecting after a specific timeout and flush all commands with a individual error
				return new Error("Retry time exhausted");
			}
			if (options.attempt > 1000) {
				// End reconnecting with built in error
				return undefined;
			}
			// reconnect after
			sails.log.error("reconnecting");
			return Math.min(options.attempt * 100, 3000);
		}
	});

const get = promisify(client.get).bind(client);
const set = promisify(client.set).bind(client);

async function asyncHandler(){
	if ( arguments.length === 0 ){
		return undefined;
	}
	let fn = arguments[0];
	let args = Array.prototype.slice.call(arguments, 1);
	try {
		let response = await fn([...args]);
		return response;
	} catch ( e ){
		sails.log.error(e);
		return undefined;
	}


}

let wrapperObject = {
	async get(_, key) {
		try {
			return asyncHandler(get, key);
		} catch (e){
			return undefined;
		}
	},
	async set(_, key, value){
		try {
			return asyncHandler(set, key, value);
		} catch (e){
			return undefined;
		}
	}
};


let Redis = new Proxy({}, wrapperObject);

module.exports = {
	"Redis": Redis,
};
