const moment = require('moment-timezone');

moment.tz.add('Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6');
moment.tz.setDefault('Asia/Kolkata');

// const commandTypes = [
// 	"jt",	//commands from joule track
// 	"jr",	//commands from joule recipes
// 	"pid"	//commands from PID logics
// ];

module.exports = {
  cmdFeedback: async (fb) => {
    const feedback = fb;
    let { deviceId, key, status } = feedback;
    const devID = deviceId;
    if (!deviceId || !key) {
      sails.log.warn('invalid command params', fb);
      return;
    }
    deviceId = `${deviceId}_jt`;
    const timestamp = key;
    try {
      let command = await Command.findOne({ deviceId, timestamp });

      if (!command) {
        sails.log.warn('Invalid feedback', command);
        return;
      }
      const {
        componentId, param, value, socketID, siteId,
      } = command;
      if (status == 1) {
        const executed = moment().unix();
        command = { ...command, executed };
      } else if (status == 2 || status == 4) {
        // if command given is executed properly(status 2)
        // or command given is same state as before(status 4)
        const reachedController = moment().unix();
        command = { ...command, reachedController };
      } else {
        const commandError = moment().unix();
        command = { ...command, commandError };
        sails.log.error('not in condition');
      }

      await Command.update({ deviceId, timestamp }, command);
      // console.log(value);
      if (command.param === 'updateJBLocation' && status == 1) {
        // Joulebox update command success;
        const cmdArr = command.value.split('_');
        const mode = cmdArr[1];
        // dont know what this is so plz confirm cuz this is not in new feedback
        const jbConfig = await DyanmoKeyStore.findOne({ key: `${siteId}_jb` });
        const dev = await Devices.findOne({ deviceId: devID, siteId });
        const { networkId } = dev;
        const key = `${siteId}_jb`;
        if (!dev) {
          sails.log.error('Feedback controller not valid');
          return;
        }
        if (!jbConfig) {
          const obj = {};
          obj[networkId] = `${devID},${mode}`;
          const valuee = JSON.stringify(obj);
          await DyanmoKeyStore.create({ key, value: valuee });
        } else {
          const stringVal = jbConfig.value;
          const val = helper.toJson(stringVal);
          val[networkId] = `${devID},${mode}`;
          const finalStr = JSON.stringify(val);
          await DyanmoKeyStore.update({ key }, { value: finalStr });
        }
      }
      deviceId = devID;
      eventService.notifyUser(socketID,
        'commandFeedback',
        {
          componentId, param, value, deviceId, status,
        });
    } catch (e) {
      sails.log.error(e);
    }
  },

  getLastCommand: async (siteId, deviceId, timestamp) => {
    try {
      let startTime = moment(timestamp).subtract(1, 'month').unix() * 1000;
      let endTime = timestamp;

      startTime /= 1000;
      endTime /= 1000;

      const latestCommandDictForEachDeviceParam = {};
      let startStopFound = false;
      const latestCommandList = [];

      const queryForCommandsFromJouleTrack = {
        deviceId: `${deviceId}_jt`,
        timestamp: {
          between: [`${siteId}_${startTime}`, `${siteId}_${endTime}`],
        },
        sort: '-1',
      };
      // get commands sent from jouletrack since last months, get them all
      const recentEntries = await Command.find(queryForCommandsFromJouleTrack);

      if (recentEntries.length === 0) {
        return latestCommandList;
      }

      recentEntries.forEach((commandGivenByJT) => {
        const { param } = commandGivenByJT;

        /* if this param is already in latestcommandict ,
        means we have that commands latest value already
        if start is there or stop is there, we just have to add it once and the latest one only
        */

        if ((param === 'start' || param === 'stop') && startStopFound) {
          // when we have found a start or stop before we can just return
          return;
        }
        const { value } = commandGivenByJT;
        const execTimestamp = commandGivenByJT.executed;
        const { user } = commandGivenByJT;

        if (!execTimestamp) {
          return;
        }

        if (param == 'start' || param == 'stop') {
          startStopFound = true;
        }
        if (!latestCommandDictForEachDeviceParam[param]) {
          latestCommandDictForEachDeviceParam[param] = 1;
          latestCommandList.push({
            type: param, value, execTimestamp, user,
          });
        }
      });

      return latestCommandList;
    } catch (err) {
      sails.log(err);
      sails.log('**FAILED AT**');
      sails.log('commandService.getLastCommand');
      throw err;
    }
  },
  /**
   * @description getDeviceCommandParam function return device_abbr value from component command parameter.
   * This function is decoupling the component abbr from device abbr so BMS team easily configure it correctly.
   * Before that functionality component command abbr is always same with device abbr.
   * <AUTHOR> Kumar
   * @param {string} componentId | component_id of asset e.g sjo-del_14
   * @param { string } component_abbr | abbr key of command parameter
   * @returns null || device_abbr
   */
  getDeviceCommandParam: async (componentId, component_abbr) => {
    try{
    const component = await Component.findOne({ deviceId: componentId });
    if (!component) {
      sails.log.error(`getDeviceCommandParam::unable to find component: ${component}`);
      return null;
    }
    let { controls,siteId } = component;
    let controlsInfo = controls.map( controlParameter =>{
      try {
        if(typeof controlParameter == "string") 
          return JSON.parse(controlParameter);
        else return controlParameter;
      } catch (error) {
        sails.log.error(`Error Parsing parameter for component ${component.deviceId}`);
        sails.log.error(error);
      }
    });
    let _commandParam = controlsInfo.filter(it=>{
      if(it.key === component_abbr) {
        return true
      } else {
        return false;
      }
    })
    if(_commandParam.length === 0) {
      sails.log.error(`getDeviceCommandParam:: command_abbr(${component_abbr} does not exist in component's command control section)`);
      return null;
    }
    if(_commandParam.length > 1) {
      sails.log.error(`getDeviceCommandParam:: command_abbr(${component_abbr} should be unique in control section of component. )`);
      return null;
    }
    const {deviceId, device_abbr,displayName} = _commandParam[0];
    if(!device_abbr) {
      sails.log.error(`getDeviceCommandParam:: device_abbr key is missing from command param - ${displayName})`);
      return null;
    }
    let deviceParameter = await Parameters.findOne({siteId,deviceId_abbr:`${deviceId}_${device_abbr}`});
    if(!deviceParameter){
      sails.log.error(`getDeviceCommandParam:: device_abbr not found in parameter table.deviceId:${deviceId},abbr:${device_abbr})`);
      return null;
    }
    return device_abbr;
  }
   catch(err) {
    sails.log.error(`getDeviceCommandParam:: ${err.message}`);
    sails.log.error(err);
    return null;
   }
  }

};
