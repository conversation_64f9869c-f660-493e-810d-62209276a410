const moment = require("moment-timezone");
moment.tz.add("Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6");
moment.tz.setDefault("Asia/Kolkata");

module.exports={
	"config": {
		"+": {
			"pres": 5
		},
		"-": {
			"pres": 5
		},
		"*": {
			"pres": 4
		},
		"/": {
			"pres": 4
		},
		">": {
			"pres": 3
		},
		"<": {
			"pres": 3
		},
		">=": {
			"pres": 3
		},
		"<=": {
			"pres": 3
		},
		"==": {
			"pres": 3
		},
		"and": {
			"pres": 2
		},
		"or": {
			"pres": 2
		},
		"&": {
			"pres": 2
		},
		"||": {
			"pres": 2
		},
		"!": {
			"pres": 2
		},
		"NOT": {
			"pres": 2
		},
		"(": {
			"pres": 1
		},
		")": {
			"pres": 1
		}

	},
	"higherPrecedence": function(a, b){
		return a>b;
	},
	"parseData": function(data){
		let complete=data.split("||");
		let completeString=this.parser(complete);
		return completeString;
	},
	"parser": function(complete){
		let completeString="";
		let presedence, temp;
		let operators=new this.Stack();
		for (let i=0;i<complete.length;i++){
			temp=this.config[complete[i]];
			if (!temp){
				completeString+=complete[i]+",";
			} else if (complete[i]=="("){
				operators.push("(");
			} else if (complete[i]==")"){
				while (!operators.isEmpty() && operators.top()!="("){
					completeString+=operators.pop()+",";
				}
				operators.pop();
			}
			else {
				presedence=temp.pres;
				while ( !operators.isEmpty() && complete[i]!="(" && this.higherPrecedence(this.config[operators.top()].pres, presedence) ){
					completeString+=operators.pop()+",";
				}
				operators.push(complete[i]);
			}
		}
		while (!operators.isEmpty()){
			completeString+=operators.pop()+",";
		}
		return completeString;
	},
	"parseTime": function(start, end, stime, etime){
		let month="*";
		let days="*";
		// let year="*";
		let hour="*";
		let min="*";
		let minHour;
		try {
			let totalMonths=(parseInt(end.month())+1)-(parseInt(start.month())+1);
			let totalYear=parseInt(end.year())-parseInt(start.year());

			if (start>end){
				return null;
			}

			if (stime==etime){
				hour=parseInt(stime.split(":")[0]);
				min=parseInt(stime.split(":")[1]);
				minHour=[[min, hour]];
				// start - stop a device
			} else {
				let [stHour, stMin]=stime.split(":");
				let [enHour, enMin]=etime.split(":");
				stMin=parseInt(stMin);
				enMin=parseInt(enMin);
				stHour=parseInt(stHour);
				enHour=parseInt(enHour);
				minHour=[];
				if (enMin==stMin && stMin==0){
					if ((stHour==enHour) )
						minHour.push([parseInt(enMin), `${stHour}`]);
					else
						minHour.push(["*", `${stHour}-${enHour-1}`]);
				} else {
					if (stMin==0 && enMin!=0){
						if ( stHour!=enHour  )
							minHour.push(["*", `${stHour}-${enHour-1}`]);
						minHour.push([`0-${enMin}`, `${enHour}`]);
					} else if (stMin!=0 && enMin==0){
						minHour.push([`${stMin}-59`, `${stHour}`]);
						if (stHour!=enHour && (stHour+1!=enHour) )
							minHour.push(["*", `${stHour+1}-${enHour-1}`]);
					} else {
						if (stHour==enHour){
							minHour.push([`${stMin}-${enMin}`, `${stHour}`]);
						} else {
							minHour.push([`${stMin}-59`, `${stHour}`]);
							if (stHour+1!=enHour)
								minHour.push(["*", `${stHour+1}-${enHour-1}`]);
							minHour.push([`0-${enMin}`, `${enHour}`]);
						}

					}
				}
			}

			if (totalYear==0){

				if (totalMonths==0){
					month=parseInt(end.month())+1;
					let totaldates=parseInt(end.date())-parseInt(start.date());
					if (totaldates==0){
						days=String(end.date());
					} else if (totaldates>31){
						return (null);
					} else {
						days=new Array(totaldates+1).fill().map((v, i)=>i+start.date()).join(",");
					}
				}
				else if (totalMonths<12){
					month=(new Array(totalMonths+1).fill().map((v, i)=>i+1+start.month())).join(",");
				} else if (totalMonths==12){
					month="*";
				} else {
					return (null);

				}
			}
			let final=[];
			for (let i=0;i<minHour.length;i++){

				final.push([(minHour[i])[0], (minHour[i])[1], days, month, "*"].join(" "));
			}

			return (final);
		} catch (e){
			sails.log(e);
			return null;
		}

	},
	"Stack": function(){
		this.s=[];
		this.push=function(data){
			this.s.push(data);
			return;
		};
		this.pop=function(data){
			let temp=this.s.pop(data);
			return temp;
		};
		this.top=function(){
			let l=this.s.length;
			if (l>0)
				return this.s[this.s.length-1];
		};
		this.isEmpty=function(){
			return !this.s.length;
		};
	}

};
