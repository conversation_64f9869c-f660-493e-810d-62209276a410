const moment = require("moment-timezone");
const AWS = require("aws-sdk");
let fs = require("fs");
let Jimp = require("jimp");
AWS.config.update({ region: process.env?.REGION, logger: console });
let s3Bucket = new AWS.S3({ "params": { "Bucket": "assets.smartjoules" } });
const documentClient = new AWS.DynamoDB.DocumentClient();

// AWS.config.loadFromPath(__dirname + '/aws-config.json');

module.exports = {

  "queryData": function(start, end, deviceId, callback, finalCallback) {

    sails.log.error("[DynamoDB datadevices] db<PERSON><PERSON><PERSON> is calling Dynamodb");

    let complete_data = { "Count": 0, "Items": [] };


    function merge(obj1) {
      complete_data["Count"] += obj1["Count"];
      complete_data["Items"] = [...complete_data["Items"], ...obj1["Items"]];
    }

    start = String(start);
    end = String(end);
    deviceId = String(deviceId);
    let docClient = new AWS.DynamoDB.DocumentClient();

    let table = "datadevices";
    let params = {
      "TableName": table,
      "KeyConditionExpression": "deviceId=:d and #t between :l and :l1",
      "ExpressionAttributeNames": {
        "#t": "timestamp",
        "#d": "data",
      },
      "ProjectionExpression": "#t,#d",
      "ExpressionAttributeValues": {
        ":l": start,
        ":l1": end,
        ":d": deviceId,
      },
    };

    function getDataParts() {
      return new Promise(function(resolve, reject) {
        docClient.query(params, (err, data) => {
          if (err) {
            sails.log.error(err);
            reject("err in getdata parts");
          } else {
            if (data.LastEvaluatedKey != undefined) {
              params.ExpressionAttributeValues[":l"] = data.LastEvaluatedKey.timestamp;
              resolve({ "data": data, "done": false });
            } else {
              resolve({ "data": data, "done": true });
            }
          }
        });
      });
    }

    async function process() {
      let res = { "done": false };
      while (!res.done) {
        try {
          res = await getDataParts();
        } catch (e) {
          return;
        }

        let main_data = res.data;
        callback(main_data);
        merge(main_data);
      }
      finalCallback(complete_data);

    }

    process();
  },
  "checkExist": function(arr) {
    try {
      let err = false;
      if (typeof (arr) != "undefined" && arr.constructor.name == "Array") {
        arr.forEach(function(v, i, a) {
          if (typeof (v) == "undefined" || v == "") {
            err = true;
          }
        });
      } else {
        err = true;
      }
      if (err) return true;
      else return false;
    } catch (e) {
      return true;
    }

  },
  "checkTime": function(arr) {
    try {
      let err = false;
      if (typeof (arr) != "undefined" && arr.constructor.name == "Array") {
        arr.forEach(function(v, i, a) {
          if (v.includes("Invalid")) {
            err = true;
          }
        });
      } else {
        err = true;
      }
      if (err) return true;
      else return false;

    } catch (e) {
      return true;
    }
  },
  "getData": function(data_obj) {
    sails.log.error();

    return new Promise((resolve, reject) => {
      try {
        let startTime = data_obj.startTime;
        let endTime = data_obj.endTime;
        let deviceId = data_obj.deviceId;
        let reqst = data_obj.reqst;
        // let type = data_obj.type || null;
        let groupBy = data_obj.groupBy;
        let result = [];

        if (this.checkExist([deviceId])) {
          reject(new Error("Required deviceId"));
          return;
        }
        if (this.checkExist(reqst)) {
          reject(new Error("Required parameters"));
          return;
        }
        if (this.checkTime([startTime])) {
          reject(new Error("Invalid start and end time"));
          return;
        }

        this.queryData(startTime, endTime, deviceId, function(partial_data) {
          //    res.send(result);
        }, (complete_data) => {
          if (data_obj.type == "custom") {
            result.push(complete_data.Items);
          }
          if (data_obj.type == "candle")
            result.push(this.filterDataCandel(complete_data.Items, groupBy, reqst));
          else if (data_obj.type == "line")
            result.push(this.filterData(complete_data.Items, groupBy, reqst));
          else if (data_obj.type == "spec")
            result.push(this.filterDataspec(complete_data.Items, groupBy, reqst));
          else if (data_obj.type == "box")
            result.push(this.filterDatabox(complete_data.Items, reqst));
          else if (data_obj.type == "thermalIndex")
            result.push(complete_data.Items);
          else if (data_obj.type == "heatmap")
            result.push(this.filterDataHeat(complete_data.Items, startTime, reqst, endTime));
          else
            result.push((complete_data.Items));
          resolve(result);
        });

      } catch (e) {
        reject(new Error(e));
      }


    });
  },
  "filterParam": function(data, paramsArr, groupBy) {

    if (!groupBy) {
      groupBy = "minute";
    }

    let returner = {};
    paramsArr.forEach((p) => {
      returner[p] = [];
    });
    data.forEach((indata) => {
      let paramData = typeof (indata.data) == "object" ? indata.data : JSON.parse(indata.data);
      paramsArr.forEach((v) => {
        if (paramData[v]) {
          returner[v].push(paramData[v]);
        }
      });
    });
    return returner;
  },
  "jsonIt": function(obj) {
    return typeof (obj) == "object" ? obj : JSON.parse(obj);
  },
  "filterDataHeat": function(data, startTime, paramsArr, endTime) {

    let returner = {};
    paramsArr.forEach((p) => {
      returner[p] = {};
    });

    try {
      data.forEach((indata) => {
        let paramData = JSON.parse(indata.data);
        let beginTime = moment((indata.timestamp)).startOf("hours").format("YYYY-MM-DD HH:mm");
        paramsArr.forEach((v) => {
          if (paramData[v]) {
            if (returner[v][beginTime]) {
              returner[v][beginTime].push(paramData[v]);
            } else {
              returner[v][beginTime] = [paramData[v]];
            }
          }
        });
      });
      let min, max, now_check;

      for (let i in returner) {
        Object.keys(returner[i]).forEach((v) => {
          returner[i][v] = this.avg(returner[i][v]);
          now_check = parseFloat(returner[i][v]);
          min = min < (now_check) ? min : now_check;
          max = max > now_check ? max : now_check;
        });
      }


      let difference = moment(endTime).diff(moment(startTime), "days");
      let hawk = [];
      for (let i = 0; i < difference; i++) { // going with days
        for (let j = 0; j < 24; j++) { // going with hours
          hawk.push([i, j, 0]);
        }
      }
      let st = moment(startTime);
      Object.keys(returner).forEach((v) => {
        Object.keys(returner[v]).forEach((v1) => {
          let d = moment(v1).diff((st), "days");
          let h = moment(v1).hours();
          let exct = d * 24 + h;
          (hawk[exct])[2] = (returner[v])[v1];

        });
        returner[v] = hawk;
      });
      returner["minimum"] = min;
      returner["maximum"] = max;
      return (returner);


    } catch (e) {
      sails.log.error(e);
    }

  },
  "avg": function(data) {
    data = this.removeNan(data);
    let leng = data.length;
    let total = 0;
    data.forEach((v) => {
      total += parseInt(v);
    });
    return (String(total / leng)).substr(0, 4);

  },
  "avg2": function(data) {
    data = this.removeNan(data);
    let leng = data.length;
    let total = 0;
    data.forEach((v) => {
      total += parseInt(v);
    });
    return (total / leng);
  },

  "filterData": function(data, groupBy, paramsArr) {
    try {
      if (!groupBy)
        groupBy = "minutes";
      let resObj = {};
      if (paramsArr.length == 0) {
        return (data);
      }
      let startTs = null;
      let tmpObj = {};
      data.forEach((ele, index) => {
        let ts = moment(ele.timestamp).startOf(groupBy).format("YYYY-MM-DD HH:mm");
        let devData = JSON.parse(ele.data);

        if (!startTs)
          startTs = ts;

        paramsArr.forEach((param) => {
          if (devData[param] != undefined && devData[param] != null) {
            if (!tmpObj[param]) {
              //case for first value in each group by
              tmpObj[param] = parseFloat(devData[param]);
              tmpObj[param + "counter"] = 1;
            } else {
              tmpObj[param] += parseFloat(devData[param]);
              tmpObj[param + "counter"] += 1;
            }

          }
        });
        if (ts != startTs || index == data.length - 1) {
          //push calculated data for hour
          paramsArr.forEach((param) => {
            if (tmpObj[param] === undefined || tmpObj[param] === null) {
              return;
            }
            if (!resObj[param]) {
              resObj[param] = [];
            }
            let calcValue = tmpObj[param] / tmpObj[param + "counter"];
            if (!calcValue || !isNaN(calcValue))
              resObj[param].push([moment(startTs).unix() * 1000, Math.round(calcValue * 100) / 100]);

          });
          tmpObj = {};
          startTs = ts;
        }

      });
      return resObj;
    } catch (e) {
      throw new Error(e);
    }

  },
  "maxmin": function(arr, param) {
    let maximum = 0;
    let minimum = 10000000;
    arr.forEach((i) => {
      if (i[param] && i[param] > maximum) {
        maximum = i[param];
      }
      if (i[param] && i[param] < minimum) {
        minimum = i[param];
      }
    });
    if (maximum == 0 && minimum == 10000000) {
      throw new Error();
    }
    return [parseFloat(maximum), parseFloat(minimum)];
  },
  "start": function(arr, param) {
    for (let i = 0; i < arr.length; i++) {
      if ((arr[i])[param]) {
        return (arr[i])[param];
      }
    }

  },
  "end": function(arr, param) {
    for (let i = arr.length - 1; i >= 0; i--) {
      if ((arr[i])[param]) {
        return (arr[i])[param];
      }
    }
  },
  "filterDataCandel": function(data, groupBy, params) {
    try {
      if (!groupBy) {
        groupBy = "hours";
      }
      if (!params || typeof (params) == "undefined") {
        return "Params are required";
      }
      let observer = {};
      let returner = {};

      params.forEach((p) => {
        returner[p] = [];
      });
      data.forEach((indata) => {
        let paramData = JSON.parse(indata.data);
        let ts = indata.timestamp;
        ts = moment(ts).startOf(groupBy).format("YYYY-MM-DD HH:mm");
        let unix_tm = moment(ts).unix() * 1000;
        let temp = {};

        params.forEach((v) => {
          if (paramData[v]) {
            temp[v] = paramData[v];
          }
        });
        if (!observer[unix_tm]) {
          observer[unix_tm] = [temp];
        } else {
          observer[unix_tm].push(temp);
        }
      });

      Object.keys(observer).forEach((v, i) => {
        let now_data = observer[v];
        now_data = this.removeNan(now_data);
        Object.keys(returner).forEach((v1) => {
          try {
            returner[v1].push([parseInt(v), parseFloat(this.start(now_data, v1)), ...this.maxmin(now_data, v1), parseFloat(this.end(now_data, v1))]);
          } catch (e) {
            returner[v1].push();
          }
        });

      });

      return (returner);

    } catch (e) {
      return ("server error");
    }

  },
  "filterDataspec": function(data, groupBy, params) {
    try {
      if (!groupBy) {
        groupBy = "minutes";
      }
      if (!params || typeof (params) == "undefined") {
        return "Params are required";
      }
      // let observer = {};
      let returner = {};

      params.forEach((p) => {
        returner[p] = {};
      });
      data.forEach((indata) => {
        let paramData = JSON.parse(indata.data);

        params.forEach((v) => {
          if (paramData[v]) {
            let temp_val = Math.floor(paramData[v]);
            if ((returner[v])[temp_val]) {
              ((returner[v])[temp_val]) += 1;
            } else {
              ((returner[v])[temp_val]) = 1;
            }
          }
        });

      });
      // let to_return = [];
      let final_ret = {};
      Object.keys(returner).map((d) => {
        final_ret[d] = [];
        Object.keys(returner[d]).map((d1) => {
          final_ret[d].push([d1, (returner[d])[d1]]);
        });

      });

      return (final_ret);

    } catch (e) {
      sails.log.error(e);
      return ("server error");
    }

  },
  "removeNan": function(data) {
    return data.filter(Boolean);
  },
  "boxplotter": function(data) {
    data = this.removeNan(data);
    let leng = data.length;
    if (leng == 0) {
      return [0, 0, 0, 0, 0];
    }
    if (leng == 1) {
      let d = data[0];
      return [d, d, d, d, d];
    }
    let max = data[leng - 1];
    leng -= 1;
    let min = data[0];
    //  lq =lower quatile and uq is upper quatile
    let med = parseInt(leng / 2);
    if (Number.isInteger(med)) {
      med = data[med];
    } else {
      med = (data[med] + data[med + 1]) / 2;
    }
    let uq = (3 / 4 * (leng));
    if (Number.isInteger(uq)) {
      uq = Math.floor(uq);
      uq = data[uq];
    } else {

      uq = Math.floor(uq);
      uq = (data[uq] + data[uq + 1]) / 2;
    }
    let lq = (1 / 4 * (leng));
    if (Number.isInteger(lq)) {
      lq = Math.floor(lq);
      lq = data[lq];
    } else {
      lq = Math.floor(lq);
      lq = (data[lq] + data[lq + 1]) / 2;
    }

    return [min, lq, med, uq, max];
  },
  "partition": function(items, left, right) {

    let pivot = items[Math.floor((right + left) / 2)],
      i = left,
      j = right;


    while (i <= j) {

      while (items[i] < pivot) {
        i++;
      }

      while (items[j] > pivot) {
        j--;
      }

      if (i <= j) {
        this.swap(items, i, j);
        i++;
        j--;
      }
    }

    return i;
  },
  "swap": function(items, firstIndex, secondIndex) {
    let temp = items[firstIndex];
    items[firstIndex] = items[secondIndex];
    items[secondIndex] = temp;
  },
  "quickSort": function(items, left, right) {

    let index;

    if (items.length > 1) {

      left = typeof left != "number" ? 0 : left;
      right = typeof right != "number" ? items.length - 1 : right;

      index = this.partition(items, left, right);

      if (left < index - 1) {
        this.quickSort(items, left, index - 1);
      }

      if (index < right) {
        this.quickSort(items, index, right);
      }

    }

    return items;
  },
  "filterDatabox": function(data, params) {
    try {
      let returner = {};
      params.forEach((p) => {
        returner[p] = [];
      });
      data.forEach((indata) => {
        let paramData = JSON.parse(indata.data);

        params.forEach((v) => {
          returner[v].push(paramData[v]);
        });

      });
      let final_ret = {};
      Object.keys(returner).map((d) => {
        let vals = returner[d];
        for (let i = 0; i < vals.length; i++) {
          vals[i] = parseFloat(vals[i]);
        }
        vals = this.quickSort(vals);


        final_ret[d] = [...this.boxplotter(vals)];
      });
      return final_ret;

    } catch (e) {
      sails.log.error(e);
      return ("server error");
    }
  },
  "uploadFile": function(req, res, userId) {
    return new Promise((resolve, reject) => {
      req.file("avatar").upload({
        // dirname: __dirname + "/assets"
      }, (er, files) => {
        if (er) {
          sails.log.error(er);
          return res.serverError(er);
        }
        // var file = files[0].filename;
        // fileType = file.split('.').pop();
        let name = files[0].fd;
        Jimp.read(name).then(function(image) {
          image.resize(200, 200).write(name);
          fs.readFile(name, (er, fileData) => {
            let base64data;
            try {
              base64data = new Buffer(fileData, "binary");
            } catch (err) {
              return reject({ "message": "Cannot upload... Try again...", "err": err });
            }
            // var base64data = new Buffer(fileData, 'binary');

            let data = { "Key": userId + ".jpeg", "Body": base64data, "ACL": "public-read" };
            s3Bucket.putObject(data, function(err, data) {
              sails.log.error(err);
              if (err) {
                sails.log.error("Error uploading data: ", data);
                fs.unlink(name);
                reject(err);
              } else {
                sails.log.error("succesfully uploaded the image!", data);
                fs.unlink(name);
                resolve({ "data": data, "filename": userId + ".jpeg" });
              }
            });
          });
          // do stuff with the image
        }).catch(function(err) {
          // handle an exception
        });


        // Jimp.read(name, function(err, lenna) {
        //     if (err) {
        //         console.log(err, 'error')
        //         throw err
        //     };
        //     lenna.resize(200, 200) // resize
        //         .quality(100) // set JPEG quality
        //         // .greyscale() // set greyscale
        //         .write(filename); // save

        //     fs.readFile(filename, (er, fileData) => {

        //         try {
        //             var base64data = new Buffer(fileData, 'binary');
        //         } catch (err) {
        //             return reject({ message: 'Cannot upload... Try again...', err: err })
        //         }
        //         // var base64data = new Buffer(fileData, 'binary');

        //         var data = { Key: userId + '.jpeg', Body: base64data, ACL: 'public-read' };

        //         s3Bucket.putObject(data, function(err, data) {
        //             console.log(err)
        //             if (err) {
        //                 console.log('Error uploading data: ', data);
        //                 fs.unlink(__dirname + '/assets/resized/' + userId + '.jpeg')
        //                 reject(err)

        //             } else {
        //                 console.log('succesfully uploaded the image!', data);
        //                 fs.unlink(__dirname + '/assets/resized/' + userId + '.jpeg')
        //                 resolve({ data: data, filename: filename });
        //             }
        //         });

        //     })
        // });
      });
    });

  },
  "basicFilter": function(allData) {

    let returner = [];
    let temp = {};
    allData.forEach((indata) => {
      let paramData = typeof (indata.data) == "object" ? indata.data : JSON.parse(indata.data);
      let KW = paramData.kw;
      let ts = moment(indata["timestamp"]).startOf("minute").format("YYYY-MM-DD HH:mm");
      if (KW === 0 || KW) {
        temp[ts] = KW;
      }
    });
    Object.keys(temp).forEach(d => {
      returner.push({
        "time": d,
        "kw": parseInt(temp[d]),
      });
    });

    return returner;
  },
  "filterBuilding": function(alldata, comparer) {

    let returner = {};
    // let leng = [];
    let ts, kw; //starthour;
    alldata.forEach(data => {
      data.forEach(inside => {
        ts = inside.time;
        kw = inside.kw;
        if (returner[ts]) {
          returner[ts].push(kw);
        } else {
          returner[ts] = [kw];
        }
      });
    });
    let buildingLoad = {};
    let shour;
    let sums;
    Object.keys(returner).forEach(nts => {
      if (returner[nts].length == comparer) {
        sums = returner[nts].reduce((a, b) => a + b, 0);
        shour = moment(nts).startOf("hour").format("YYYY-MM-DD HH:mm");
        if (buildingLoad[shour]) {
          buildingLoad[shour].push(sums);
        } else {
          buildingLoad[shour] = [sums];
        }
      }
    });
    let totalLoad = {};
    let tmp1, tmp2, tmp3;
    Object.keys(buildingLoad).forEach(nts => {
      tmp1 = moment(nts).unix() * 1000;
      tmp2 = Math.round((this.avg2(buildingLoad[nts])) * 100) / 100;
      tmp3 = moment(nts).startOf("day").format("YYYY-MM-DD HH:mm");
      if (totalLoad[tmp3]) {

        totalLoad[tmp3].push([tmp1, tmp2]);
      } else {
        totalLoad[tmp3] = [
          [tmp1, tmp2],
        ];
      }
    });

    return totalLoad;


  },
  "getMultiple": function(table, primary, arrDevices) {
    return new Promise((res, rej) => {
      try {
        let prom = [];
        arrDevices = arrDevices.constructor.name == "Array" ? arrDevices : arrDevices.split(",");
        arrDevices.forEach(dev => {
          let x = {};
          x[primary] = dev;
          prom.push(table.findOne(x));
        });
        Promise.all(prom).then(allDev => {
          res(allDev);
        }).catch(e => {
          sails.log.error(e);
          rej([]);
        });

      } catch (e) {
        sails.log.error(e);
        rej([]);
      }
    });
  },
  async updateLastExecutedCommand(uniqId, executedAt) {
    try {

      let query = {
        TableName: "commands",
        IndexName: "uniqId_timestamp_global_index",
        KeyConditionExpression: "#uniqId = :uniqId",
        ExpressionAttributeNames: {
          "#uniqId": "uniqId",
        },
        ExpressionAttributeValues: {
          ":uniqId": uniqId,
        },
      };

      const commandResponse = await documentClient.query(query).promise();
      const command = commandResponse.Items[0];
      if (command === undefined) {
        return;
      }
      const { deviceId, timestamp } = command;

      await Command.update({ deviceId, timestamp }, { executed: executedAt });
      return;

    } catch (e) {
      sails.log.error(e);
    }
  },
};
