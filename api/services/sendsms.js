const AWS = require("aws-sdk");
AWS.config.update({ region: process.env?.REGION, logger: console });
const sns = new AWS.SNS();

module.exports = {
  "sendSms": (data) => {
    let params = {
      "Message": data.message, // message body
      "PhoneNumber": "+91" + data.phone, // message phone number
      /* required */
      "Subject": "SJ Alert",
      // TopicArn: 'arn:aws:sns:us-west-2:878252606197:sms-alerts'
    };
    return new Promise((resolve, reject) => {
      sns.publish(params, function(err, data) {
        if (err) {
          sails.log("error occured");
          sails.log(err, err.stack);
          return reject(err);
        }
        resolve(data);
        // an error occurred
        // successful response
      });

    });

  },
};
