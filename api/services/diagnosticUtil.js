/**
 * @file File exports utility functions for Diagnostics Controllers. To be used specifically only for the APIs that call it to increase testability of the code. Kindly refrain from using them.
 * <AUTHOR>
 */

const moment = require("moment-timezone");
moment.tz.setDefault("Asia/Kolkata");
moment.defaultFormat = "YYYY-MM-DD HH:mm:ss";

module.exports = {
	"diagnosticBenchmarkingGraph": {
		/**
		 * @function checkInput 
		 * @summary Checks API input parameters for irregularities.
		 * @param {object} body req.body of the API request.
		 * <AUTHOR>
		 */
		"checkInput": function (body){
			let result = {
				"status": false
			};
			let { startTime, endTime, deviceObject, siteId } = body;
			if (startTime == undefined || endTime == undefined || siteId == undefined){
				result["message"] = "Either 'startTime' OR 'endTime' OR 'siteId' not present.";
				return result;
			}
			if (deviceObject == undefined){
				result["message"] = "'deviceObject' not present.";
				return result;
			} else {
				// Fetching list of deviceIds
				let deviceList = [];
				for (let i in deviceObject){
					deviceList.push(deviceObject[i].id);
				}
				body["deviceList"] = deviceList;
			} 
			return {
				"status": true
			};
		},

		/**
		 * @function aggregateBenchmarkingData
		 * @summary Aggregates the benchmarking data extracted from elasticsearch index based on timestamp. All parameters values at a single timestamp are collected and then averaged based on the number of values present. Also adds null values wherever no benchmarking data is present at an expected timestamp.
		 * @param {*} benchmarkingData 
		 * @param {*} benchmarkingParamList 
		 * <AUTHOR> Gupta
		 */
		"aggregateBenchmarkingData": function (benchmarkingData, benchmarkingParamList){
			// Initializing return variables
			let dataSet = {};
	
			// Grouping data based on startTime
			benchmarkingData.forEach(dataPoint => {
				dataPoint = dataPoint._source;
				if (dataSet[dataPoint.start_time] == undefined)
					dataSet[dataPoint.start_time] = [ dataPoint ];
				else
					dataSet[dataPoint.start_time].push(dataPoint);
			});

			// Adding empty arrays wherever no benchmarking data is present at an expected timestamp.
			this.expectedDataSet(dataSet);

			// Aggregating data set based on devices at a particular timestamp
			let averageDataSet = this.aggregateDataSet(dataSet, benchmarkingParamList);
	
			return {
				dataSet,
				averageDataSet
			};
		},

		/**
		 * @function expectedDataSet
		 * @summary Adds an empty array whenever a timestamp does not exist in the interval of 15 minutes. So that when benchmarking parameters are being aggregated, null values are filled in appropriately.
		 * @param {object} dataSet Object containing benchmarking parameters clustered with respect to their timestamp
		 * <AUTHOR> Gupta
		 */
		"expectedDataSet": function(dataSet){
			let timestampKeys = Object.keys(dataSet);
			let startTime = moment(timestampKeys[0]), endTime = moment(timestampKeys[timestampKeys.length - 1]);
			let iteratorTime = startTime;
			while (iteratorTime <= endTime){
				let time = iteratorTime.format();
				if (dataSet[time] == undefined) dataSet[time] = [];
				iteratorTime.add(15, "m");
			}
		},

		/**
		 * @function aggregateDataSet
		 * @param {object} dataSet Object containing benchmarking data grouped by start_time
		 * @param {array} benchmarkingParamList List of benchmarking Parameters.
		 * @summary Aggregates for each benchmarking parameter of different devices at a particular timestamp.
		 */
		"aggregateDataSet": function(dataSet, benchmarkingParamList){
			let averageDataSet = {};
			benchmarkingParamList.forEach(parameter => {
				averageDataSet[parameter] = [];
			});

			let timestampArr = Object.keys(dataSet).sort(); // Sorting keys of the object. Required for making response array that is to be rendered in a graph.

			timestampArr.forEach( timestamp => {
				benchmarkingParamList.forEach(parameter => {
					let total = dataSet[timestamp].length, sum = 0, average;
					// Calculating average for one parameter of on device at one timestamp
					dataSet[timestamp].forEach(timestampData => {
						if (timestampData[parameter] == "NULL" || timestampData[parameter] == null)
							total -= 1;
						else
							sum += timestampData[parameter];
					});
					if (total <= 0)
						average = null;
					else
						average = sum / total;
					averageDataSet[parameter].push([moment(timestamp).valueOf(), average]);
				});
			});

			return averageDataSet;
		},

		/**
		 * @function extendDataSelectParameters
		 * @summary Replicates values of hardcoded benchmarking parameters if the following value was null.
		 * @param {object} aggregatedData Aggregated data returned by aggregateBenchmarkingData.
		 * <AUTHOR> Gupta
		 */
		"extendDataSelectParameters": function(aggregatedData) {
			// Hardcoding parameters for which data needs to be extended.
			const parameters = ["alpha", "alphajr", "alphajt", "alphath"];
			let benchmarkingData = aggregatedData.averageDataSet;
			parameters.forEach(parameter => {
				let dataArray = benchmarkingData[parameter];
				for (let i=0; i<dataArray.length - 1; i++){
					let currentData = dataArray[i], nextData = dataArray[i + 1];
					if (currentData[1] == null) continue;
					else if (nextData[1] == null) nextData[1] = currentData[1];
				}
			});
		},

		/**
		 * @function parseResponseData
		 * @summary Forms the resposne object that the frontEnd is expecting.
		 * @param {*} benchmarkingParamList Hard coded param list of items to search for.
		 * @param {*} aggregatedData Aggregated benchmarking data.
		 * @param {*} strictnessData Strictness data of each benchmarking parameter.
		 * <AUTHOR> Gupta
		 */
		"parseResponseData": function (benchmarkingParamList, aggregatedData, strictnessData){
			let parameters = [], response = {}, events = [];
			benchmarkingParamList.forEach(parameter => {
				parameters.push({
					"name": parameter,
					"data": aggregatedData.averageDataSet[parameter],
					"strictness": strictnessData[parameter]
				});
			});
			response = {
				parameters,
				events
			};
			response["rawData"] = aggregatedData.dataSet;
			return response;
		},

		/**
		 * @function getStrictness
		 * @summary Gets strictness for each benchmarking parameter configured for that site.
		 * @param {array} benchmarkingParamList Hard coded benchmarking parameter list.
		 * @param {string} siteId siteId
		 * <AUTHOR> Gupta
		 */
		"getStrictness": async function (benchmarkingParamList, siteId){
			let strictnessData = {}, promiseArr = [];
			benchmarkingParamList.forEach(parameter => {
				let dynamoData = DyanmoKeyStore.find({
					"key": `diagnosticStrictness-${parameter}-${siteId}`
				}).then(data => {
					if (data.length == 0)
						strictnessData[parameter] = null;
					else {
						let value = helper.toJson(data[0].value);
						strictnessData[parameter] = value ? value[parameter] : null;
					}
				});
				promiseArr.push(dynamoData);
			});
			await Promise.all(promiseArr);
			return strictnessData;
		},

	},

	"getDQIGraph": {
		/**
		 * @function checkInput
		 * @summary Checks input parameters of the API call.
		 * @param {object} body Request Parameters of the API call
		 * @param {array} body.deviceObj Containing device Objects.
		 * @param {string} body.startTime Start time
		 * @param {string} body.endTime end time
		 * @param {string} body.siteId SiteID
		 * @param {string} body.groupBy Either "Hours" or "Days".
		 */
		"checkInput": function (body) {
			let result = {
				"status": false
			};
			let { deviceList, startTime, endTime, siteId, groupBy } = body;
			if (startTime == undefined || endTime == undefined || siteId == undefined){
				result["message"] = "Either startTime OR endTime OR siteID not present in payload.";
				return result;
			}

			if (deviceList == undefined || groupBy == undefined){
				result["message"] = "Either deviceList OR groupBy not present in payload.";
				return result;
			} else if ( groupBy!= "Hours" && groupBy != "Days"){
				result["message"] = "Unrecognized groupBy";
				return result;
			} 
			return {
				"status": true
			};
		},

		/**
		 * @function generateElasticsearchQuery
		 * @summary Generates elasticsearch query. Query is used to aggregate data based on a specified time interval.
		 * @param {object} body Request Parameters of the API call
		 * @param {array} body.deviceObj Array containing device Objects.
		 * @param {string} body.startTime Start time
		 * @param {string} body.endTime end time
		 * @param {string} body.siteId SiteID
		 * @param {string} body.groupBy Either "Hours" or "Days".
		 */
		"generateElasticsearchQuery": function(body){
			// Init variables
			let { startTime, endTime, siteId, deviceList, groupBy} = body;
			let index = `${siteId}_data`, interval;
			if (groupBy == "Hours") interval = "1H";
			else if (groupBy == "Days") interval = "1D";
			else throw new Error("Error!: 'groupBy' in API call not recognized!");
			startTime = moment(startTime).format();
			endTime = moment(endTime).format();

			// Generate elasticsearch index.
			let indexDate = diagnosticService.generateElasticIndex(startTime, endTime, index);
			let indexMonth = diagnosticService.generateElasticIndex(startTime, endTime, index, "months");
			index = indexDate.concat(indexMonth);
			let query = {
				index,
				"type": "_doc",
				"ignore_unavailable": true,
				"body": {
					"query": {
						"bool": {
							"filter": {
								"terms": {
									"deviceId": deviceList
								}
							},
							"must": [{
								"range": {
									"timestamp": {
										"gte": startTime,
										"lte": endTime
									}
								}
							}]
						}
					},
					"aggs": {
						"dqi_over_time": {
							"date_histogram": {
								"field": "timestamp",
								"interval": interval
							}
						}
					},
					"size": 0
				}
			};
			return query;
		},

		/**
		 * @function parseResponseData
		 * @summary Modifies and stores the calculated DQI percentage in the data object.
		 * @param {array} data Response data from elasticsearch query.
		 * @param {string} groupBy 'Hours' or 'Days'.
		 */
		"calcDQIPercentage": function(data, groupBy, startTime, endTime, deviceTotal){
			let total, startTotal, endTotal;
			if (groupBy == "Hours"){
				startTotal = moment(startTime).endOf("hours").diff(moment(startTime), "m") + 1;
				endTotal = moment(endTime).diff(moment(endTime).startOf("hour"), "m") + 1;
				startTotal *= deviceTotal;
				endTotal *= deviceTotal;
				total = 60 * deviceTotal;	
			} 
			else if (groupBy == "Days"){
				startTotal = moment(startTime).endOf("days").diff(moment(startTime), "m") + 1;
				endTotal = moment(endTime).diff(moment(endTime).startOf("days"), "m") + 1;
				startTotal *= deviceTotal;
				endTotal *= deviceTotal;
				total = 60 * 24 * deviceTotal;
			} 
			else return;

			// First element
			let firstElement = data[0];
			/* eslint-disable camelcase */
			firstElement.doc_count = (firstElement.doc_count / startTotal) * 100;
			// Middle elements
			for (let i=1; i<data.length-1; i++){
				let count = data[i].doc_count;
				data[i].doc_count = (count / total) * 100;
			}
			// Last Element
			let lastElement = data[data.length - 1];
			lastElement.doc_count = (lastElement.doc_count / endTotal) * 100;
			/* eslint-enable camelcase */
		},
	},

	"deleteAlertRecipes": {
		/**
		 * @function checkInput
		 * @summary Checks input parameters of the API call.
		 * @param {object} body Request Parameters of the API call
		 * @param {array} body.recipeList Array of recipe objects.
		 * @param {string} body.recipeList[0].siteId Site ID.
		 * @param {string} body.recipeList[0].recipeId Recipe ID.
		 */
		"checkInput": function(body){
			let response = {
				"status": false
			};
			if (body.recipeList == undefined){
				response["message"] = "'recipeList' not present in body.";
				return response;
			}
			return {
				"status": true
			};
		}
	},

	"deleteMaintenanceInfo": {
		/**
		 * @function checkInput
		 * @summary Checks input parameters of the API call.
		 * @param {object} body Request Parameters of the API call
		 * @param {string} body.id _id of the elasticsearch document to be deleted.
		 */
		"checkInput": function(body){
			let {id} = body;
			if (id == undefined){
				return {
					"status": false,
					"message": "'id' not present."
				};
			}
			return {
				"status": true
			};
		}
	},

	"updateMaintenanceInfo": {
		/**
		 * @function checkInput
		 * @summary Checks input parameters of the API call.
		 * @param {object} body Request Parameters of the API call
		 * @param {string} body.id _id of the elasticsearch document to be updated.
		 */
		"checkInput": function(body){
			let {id} = body;
			if (id == undefined){
				return {
					"status": false,
					"message": "'id' not present."
				};
			}
			return {
				"status": true
			};
		}
	}

};