const AWS = require("aws-sdk");

module.exports = {

  "sendMsg": function(template, to) {
    let params = {
      "Destination": { /* required */
        //   CcAddresses: [ 'EMAIL_ADDRESS'],
        "ToAddresses": [to],
      },
      "Message": { /* required */
        "Body": { /* required */
          "Html": {
            "Charset": "UTF-8",
            "Data": "HTML_FORMAT_BODY",
          },
          "Text": {
            "Charset": "UTF-8",
            "Data": "TEXT_FORMAT_BODY",
          },
        },
        "Subject": {
          "Charset": "UTF-8",
          "Data": "Test email",
        },
      },
      "Source": "<EMAIL>", /* required */
    };

    let sendPromise = new AWS.SES({ "apiVersion": "2010-12-01" }).sendEmail(params).promise();
    sendPromise.then((data) => {
      sails.log(data.MessageId);
    }).catch(function(err) {
      sails.log.error(err, err.stack);
    });
  },
};
