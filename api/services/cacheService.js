/**
 * @module cacheService
 */
const moment = require("moment-timezone");
moment.tz.add("Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6");
moment.tz.setDefault("Asia/Kolkata");
const helper = require("./helper");

let redis = require("redis"),
	client = redis.createClient(sails.config.cachePort, sails.config.cacheHost, {
		"retry_strategy": function (options) {
			if (options.error && options.error.code === "ECONNREFUSED") {
				// End reconnecting on a specific error and flush all commands with a individual error
				return new Error("The server refused the connection");
			}
			if (options.total_retry_time > 1000 * 60 * 60) {
				// End reconnecting after a specific timeout and flush all commands with a individual error
				return new Error("Retry time exhausted");
			}
			if (options.attempt > 1000) {
				// End reconnecting with built in error
				return undefined;
			}
			// reconnect after
			sails.log.error("reconnecting");
			return Math.min(options.attempt * 100, 3000);
		}
	});
const { promisify } = require("util");
const sadd = promisify(client.sadd).bind(client);
// const lrange = promisify(client.lrange).bind(client);
const srem = promisify(client.srem).bind(client);
const smembers = promisify(client.smembers).bind(client);
const expire = promisify(client.expire).bind(client);
const del = promisify(client.del).bind(client);
const keys = promisify(client.keys).bind(client);
const exists = promisify(client.exists).bind(client);
const scard = promisify(client.scard).bind(client);
const srandmember = promisify(client.srandmember).bind(client);
const get = promisify(client.get).bind(client);
const sismember = promisify(client.sismember).bind(client);
// const sendCommand = promisify(client.sendCommand).bind(client);
/*/
/*
var redis = require("redis"),
  client = redis.createClient(6379, "**********");
*/
module.exports = {

	"client": client,
	/**
   * @description Getting the current load from the ltPanel devices.(Gives the current load in real time)
   * @param {String} siteId - siteId of the site of which we want the currentLoad.
   * @param {object} res - res to send back the desired HTTP response.
   * @param {function} callback - if cache fails launches {@link module:DatadeviceController~getCurrentLoadDynamo|callback}
   * @returns {Object}res - Returns "res" with a status of 200 and a json object with currentLoad(integer)
   * @throws Will throw an error if cache fails.
   * @see is being used in DatadeviceController {@link module:DatadeviceController.getCurrentLoad|getCurrentLoad}
   */
	"test": function () {
		//saveDailyConsumption(50, "ssh", "xyz"); getKey()
	},
	"get": get,
	"exists": exists,
	"scard": scard,
	"srandmember": srandmember,
	"sismember": sismember,
	/**
	 * @description Sets a value in cache with a TTL.
	 * @param {string} key 
	 * @param {string} value 
	 * @param {string} seconds Time in seconds. Sets TTL to this amount.
	 */
	"setKeyWithTTL": async function(key, value, seconds){
		// return await sendCommand('SET', key, value, 'EX', seconds);
		client.set(key, value);
		return expire(key, seconds);
	},

	/**
   * @description Sets the default values in the cache required (runs when the site is registered).
   * @param {String} siteId - siteId of the site of which we want the devices consumption.
   */
	"setDefaultvalues": function (siteId) {
		//cache mai default timestamp
		//error keys
		let errkey = siteId + "_error";
		let nexttimestamp = siteId + "_nexttimestamp";
		let timestampnextday = nextDayTimestamp(Date.now());
		client.set(nexttimestamp, timestampnextday, redis.print);
		client.rpush(errkey, "false", function (err, reply) {
			client.rpush(errkey, "0", function (err, reply) {
				client.rpush(errkey, "false", function (err, reply) {
					client.rpush(errkey, "0", function (err, reply) {
						client.rpush(errkey, "false", function (err, reply) {
							client.rpush(errkey, "0", function (err, reply) {
							});
						});
					});
				});
			});

		});


	},
	"checkKeys": function (res) {
		client.keys("ssh_*", function (err, data) {
			return res.ok(data);
		});
	},
	"pushDeviceConsumption": function (siteId, deviceId, consumption) {
		sails.log("f:pushDeviceConsumption");
		sails.log(`params:${siteId} | ${deviceId} | ${consumption}`);
		let key = `${siteId}_consumption_${deviceId}`;
		client.lpush(key, consumption, function (err, data) {
			if (err) {
				sails.log(`Saving cache exception @ ${siteId} - ${deviceId} Timestamp : ${new Date()}`);
				sails.log(err);
			}
			client.ltrim(key, 0, 1339);
		});
	},
	"pushSiteConsumption": function (siteId, consumption) {
		sails.log("f:pushSiteConsumption");
		sails.log(`params:${siteId}| ${consumption}`);
		let key = `${siteId}_consumption`;
		if (!isNaN(consumption)) {
			client.lpush(key, consumption, function (err, data) {
				if (err) {
					sails.log(`Saving cache exception @ ${siteId} -  Timestamp : ${new Date()}`);
					sails.log.error(err);
				}
				client.ltrim(key, 0, 1339);
			});
		}
	},
	"pushLoad": function (siteId, load) {
		sails.log("f:pushLoad");
		sails.log(`params:${siteId}| ${load}`);
		let key = `${siteId}_load`;
		if (!isNaN(load)) {
			client.lpush(key, load, function (err, data) {
				if (err) {
					sails.log.error(`Saving cache exception @ ${siteId} -  Timestamp : ${new Date()} `);
					sails.log.error(err);
				}
				client.ltrim(key, 0, 1439);
			});
		}
	},
	"saveLastDataTime": function (siteId, ts) {
		let key = siteId + "_lastDataTime";
		client.set(key, ts);
	},
	"getLastDataTime": function (siteId, res) {
		let key = siteId + "_lastDataTime";
		client.get(key, function (err, data) {
			if (err) {
				sails.log("f:getLastDataTime");
				sails.log(err);
				return res.ok({ "data": null, "err": "Unable to fetch records!" });
			}
			if (!data) {
				return res.ok({ "err": "Info not available!", "data": null });
			}
			res.ok({ "time": data });
		});

	},
	"saveEstimatedConsumption": function (siteId, ltPanelList, isDataRecieved, index, consumption) {
		if (index >= ltPanelList.length) {
			//when all the id's that were missed has been iterated and their last reading is fetched.
			client.lpush(siteId + "_consumption", consumption, function (err, data) {
				if (err) {
					sails.log(err);
					return;
				}
				return;
			});
		}
		for (let i = index; i < ltPanelList.length; i++) {
			if (!isDataRecieved[i]) {
				let key = `${siteId}_consumption_${ltPanelList[i]}`;
				client.lrange(key, 0, 0, function (err, data) {
					if (err) {
						sails.log("Error fetching data from cache");
						return;
					}
					else {
						if (data[0] != null) {
							consumption += parseInt(data[0]);
							module.exports.saveEstimatedConsumption(siteId, ltPanelList, isDataRecieved, i, consumption);
							return;
						}
					}
				});
			}
		}
	},
	"getTodayConsumption": function (siteId, res) {
		let key = siteId + "_consumption";
		client.lrange(key, 0, -1, function (err, data) {
			if (err) {
				sails.log(err);
				return res.ok({ "data": null, "err": "Unable to fetch records" });
			}
			if (!data || data.length == 0) {
				return res.ok({ "data": null, "err": "Data not present for this site!" });
			}
			let consumption = parseInt(data[0]) - parseInt(data[data.length - 1]);
			return res.ok({ "consumption": consumption });
		});
	},
	"isNextDay": function (siteId, ts) {
		let currTimestamp = ts;
		// let consumptionkey = siteId + "_consumption";
		let timestampKey = `${siteId}_nexttimestamp`;
		client.get(timestampKey, function (err, savedTime) {
			//save the daily consumption
			if (!savedTime) {
				sails.log("Setting next day time for the first time");
				let nextday = moment(ts).endOf("day").format("YYYY-MM-DD HH:mm:ss");
				client.set(timestampKey, nextday, redis.print);
				savedTime = nextday;
			}
			if (moment(currTimestamp).isAfter(moment(savedTime))) {
				sails.log("the current Timestamp is greater than savedTime");
				calculateDayConsumptionAndSave(siteId, currTimestamp);
				let nextday = moment(ts).endOf("day").format("YYYY-MM-DD HH:mm:ss");
				client.set(timestampKey, nextday, redis.print);
			}
		});
	},
	"getConsumptionDashboard": function (req, res, dashBoardEM) {
		//To start this function em_siteId key must be filled before in DyanmoKeyStore
		let groupBy = req.param("group");
		let userPreferredUnit = req._userMeta.unitPref.cons || 'kvah';
		// let siteId = req.param("siteId");

		// let subtracted_val = (groupBy == "hour") ? 15 : 0;
		let start = undefined, end = undefined;
		if (groupBy == "custom") {
			try {
				start = (req.query.start);
				end = (req.query.end);

			} catch (e) {
				sails.log.error(e);
				return "ERROR";
			}
		}
		let ts = start ? moment(start).format("YYYY-MM-DD HH:mm") : moment().subtract(1, groupBy).format("YYYY-MM-DD HH:mm");
		let response = {
			"err": [],
			"data": [],
		};
		let promises = dashBoardEM.map(function (devId) {
			// from part
			let searchObjEnd = {
				"deviceId": devId,
				"timestamp": {
					"gte": ts,
				},
				"limit": 1
			};

			// to
			let searchObjBeg = {
				"deviceId": devId,
				"timestamp": {},
				"sort": "-1",
				"limit": 1,
			};

			if (groupBy == "hour") {
				(searchObjBeg["timestamp"])["gte"] = moment().subtract(15, "m").format("YYYY-MM-DD HH:mm"); // last known data point
				// in last 15 minutes
			} else if (groupBy == "custom") {
				(searchObjBeg["timestamp"])["lte"] = moment(end).format("YYYY-MM-DD HH:mm");
			}
			else {
				(searchObjBeg["timestamp"])["lte"] = moment().format("YYYY-MM-DD HH:mm");
			}
			return calculateConsumptionWithUserPreference(searchObjBeg, searchObjEnd, userPreferredUnit);
		});
		Promise.all(promises).then(resData => {
			for (let i = 0; i < resData.length; i++) {
				if (resData[i].data) {
					response.data.push(resData[i].data);
				} else {
					response.err.push(resData[i].err);
				}
			}
			return res.ok(response);
		}).catch(err => {
			sails.log.error(err);
			response.data = null;
			response.err = "Error Fetching Data";
			return res.ok(response);
		});
	},
	"getConsumptionDashboard_new": function (req, res, dashBoardEM, consUnit) {
		//To start this function em_siteId key must be filled before in DyanmoKeyStore
		let groupBy = req.param("group");
		let start = undefined, end = undefined;
		if (groupBy == "custom") {
			try {
				start = (req.query.start);
				end = (req.query.end);

			} catch (e) {
				sails.log.error(e);
				return "ERROR";
			}
		}
		let ts = start ? moment(start).format("YYYY-MM-DD HH:mm") : moment().subtract(1, groupBy).format("YYYY-MM-DD HH:mm");
		let response = {
			"err": [],
			"data": [],
		};
		let promises = dashBoardEM.map(function (devId) {
			// from part
			let searchObjEnd = {
				"deviceId": devId,
				"timestamp": {
					"gte": ts,
				},
				"limit": 1
			};

			// to
			let searchObjBeg = {
				"deviceId": devId,
				"timestamp": {

				},
				"sort": "-1",
				"limit": 1,
			};

			if (groupBy == "hour") {
				(searchObjBeg["timestamp"])["gte"] = moment().subtract(15, "m").format("YYYY-MM-DD HH:mm");
			} else if (groupBy == "custom") {
				(searchObjBeg["timestamp"])["lte"] = moment(end).format("YYYY-MM-DD HH:mm");
			}
			else {
				(searchObjBeg["timestamp"])["lte"] = moment().format("YYYY-MM-DD HH:mm");
			}
			return calculateConsumption_new(searchObjBeg, searchObjEnd, consUnit);
		});
		Promise.all(promises).then(resData => {
			for (let i = 0; i < resData.length; i++) {
				if (resData[i].data) {
					response.data.push(resData[i].data);
				} else {
					response.err.push(resData[i].err);
				}
			}
			return res.ok(response);
		}).catch(err => {
			sails.log.error(err);
			response.data = null;
			response.err = "Error Fetching Data";
			return res.ok(response);
		});
	},

	"getLastAlertTime": function (cb) {
		client.get("alert_time", function (err, data) {
			if (!data) {
				cb();
				client.set("alert_time", moment().format("YYYY-MM-DD HH:mm"));
				return;
			}

			if (moment().diff(moment(data), "minutes") > 15) {
				cb();
				client.set("alert_time", moment().format("YYYY-MM-DD HH:mm"));
				return;
			}
		});
	},
	"getKey": function (key) {
		return new Promise((resolve, reject) => {
			client.get(key, function (err, data) {
				if (err) {
					sails.log.error(err);
					return resolve(null);
				}
				return resolve(data);

			});
		});
	},
	"setKey": function (key, value) {
		return new Promise((resolve, reject) => {
			client.set(key, value);
			resolve(true);
		});
	},
	"setExpiryKey": function (key, value) {
		return new Promise((resolve, reject) => {
			try {
				client.set(key, value);
				client.expireat(key, parseInt((+new Date) / 1000) + 86400);
				resolve(true);
			} catch (e) {
				reject("Unable to set redis key");
			}

		});
	},
	"enqueue": function (key, data,) {
		return new Promise((resolve, reject) => {
			client.rpush(key, data, function (err, data) {
				if (err) {
					sails.log(`Saving cache exception @ ${key} Timestamp : ${new Date()}`);
					reject(err);
				}
				// eventService.emmitter.emit('enqueue',data);
				resolve(data);
			});
		});
	},
	"dequeue": function (key, data) {
		return new Promise((resolve, reject) => {
			client.lpop(key, function (err, data) {
				if (err) {
					sails.log(`Saving cache exception @ ${key} Timestamp : ${new Date()}`);
					reject(err);
				}
				resolve(data);
			});
		});
	},
	"setHKey": function (key, data) {
		return new Promise((resolve, reject) => {

			if (!data["_obj_"] || !data["_findBy_"] || !data["_broadCastBy_"] || !data["_broadCastJB_"] || !data["_type_"]) {
				reject(false);
				return;
			}
			broadcastService.cast(data["_broadCastBy_"], data["_broadCastJB_"], data["mainObj"]);
			if (!data["_counter_"]) data["_counter_"] = 0;
			data = JSON.stringify(data);

			// temp is main variable name, and then data is stored as temp[key]=data
			client.hmset(["temp", key, data], function (err, res) {
				if (err) {
					sails.log(`Saving cache exception @ ${key} Timestamp : ${new Date()}`);
					reject(err);
				}
				resolve(res);
			});
		});
	},
	"sadd": function (key, values) {
		let valArr = [];
		if (Array.isArray(values))
			valArr = values;
		else if (typeof values === "string")
			valArr.push(values);
		else
			valArr = null;
		if (!valArr || !key) {
			return Promise.reject({ "err": "SAdd:No Topics Specified" });
		}
		let promiseArr = valArr.map(value => sadd(key, value));
		return Promise.all(promiseArr);
	},
	"srem": function (key, values) {
		let valArr = [];
		if (Array.isArray(values))
			valArr = values;
		else if (typeof values === "string")
			valArr.push(values);
		else
			valArr = null;
		if (!valArr || !key) {
			return Promise.reject({ "err": "SRem: No Topics Specified" });
		}
		let promiseArr = valArr.map(value => srem(key, value));
		return Promise.all(promiseArr);
	},
	"expire": (key, seconds) => {
		return expire(key, seconds);
	},
	"smembers": function (key) {
		if (!key)
			return Promise.reject({ "err": "No key specified" });
		return smembers(key);
	},
	"getHKey": function (key = undefined) {
		return new Promise((resolve, reject) => {
			client.hgetall("temp", function (err, data) {
				if (err) {
					sails.log(`Cannot get hash key Timestamp : ${new Date()}`);
					reject(err);
				}
				if (!data) {
					resolve({});
					return;
				}
				if (key)
					resolve(data[key]);
				else
					resolve(data);
			});
		});
	},
	"delHKey": function (key) {
		return new Promise((resolve, reject) => {
			this.getHKey(key).then(d => {
				if (d) {
					client.hdel("temp", key, function (err, data) {
						if (err) {
							sails.log(`Cannot get hash key Timestamp : ${new Date()}`);
							reject(err);
						}
						if (data == 1) {
							resolve(true);
						} else {
							resolve(false);
						}
					});
				} else {
					resolve(true);
				}

			}).catch(e => {
				sails.log(e);
				resolve(true);
			});

		});
	},
	"del": (key) => {
		return del(key);
	},
	"keys": (key) => {
		return keys(keys);
	},
	"getHMapKey": function (variableName, key = undefined) {
		return new Promise((resolve, reject) => {
			client.hgetall(variableName, function (err, data) {
				if (err) {
					sails.log(`Cannot get hash key Timestamp : ${new Date()}`);
					reject(err);
				}
				if (!data) {
					resolve({});
					return;
				}
				if (key)
					resolve(data[key]);
				else
					resolve(data);
			});
		});
	},
	"hashMapAppend": (var_name, key, value, remDuplicate = false) => {
		return new Promise((res, rej) => {
			try {
				if (!var_name || !key || !value) {
					rej(false);
				}
				let arr = [], sendObj = {};
				client.hgetall(var_name, function (err, data) {
					if (err) {
						rej(err);
					}
					if (data) {
						sendObj = helper.toJson(data);
						if (!sendObj) {
							sendObj = {};
						}
						let arrk = sendObj[key];
						arr = helper.toArray(arrk);
						if (!arr) arr = [];
					}
					if (remDuplicate) arr = arr.filter(v => v != value); // remove duplicates
					arr.push(value);
					client.hmset([var_name, key, JSON.stringify(arr)], function (err, d) {
						if (err) {
							sails.log(`Saving cache exception @ ${key} Timestamp : ${new Date()}`);
							rej(err);
						}
						res(d);
					});

				});
			} catch (e) {
				rej(e);
			}
		});
	},
	"hashMapRemove": (var_name, key, value) => {
		return new Promise((res, rej) => {
			try {
				if (!var_name || !key || !value) {
					rej(false);
				}
				let arr = [], sendObj = {};
				client.hgetall(var_name, function (err, data) {
					if (err) {
						rej(err);
					}
					if (data) {
						sendObj = helper.toJson(data);
						if (!sendObj) {
							sendObj = {};
						}
						let arrk = sendObj[key];
						arr = helper.toArray(arrk);
						if (!arr) arr = [];
					}
					arr = arr.filter(v => v != value);
					client.hmset([var_name, key, JSON.stringify(arr)], function (err, d) {
						if (err) {
							sails.log(`Saving cache exception @ ${key} Timestamp : ${new Date()}`);
							rej(err);
						}
						res(d);
					});

				});


			} catch (e) {
				rej(e);
			}


		});
	},
	"hashMapGet": (var_name, key) => {
		return new Promise((res, rej) => {

			try {

				let arr = [];

				client.hgetall(var_name, function (err, data) {
					if (err) {
						rej(err);
					}
					if (data) {
						let sendObj = helper.toJson(data);
						if (!sendObj) {
							sendObj = {};
						}
						let arrk = sendObj[key];
						arr = helper.toArray(arrk);
						if (!arr) arr = [];
					}
					res(arr);
				});
			} catch (e) {
				rej(e);
			}


		});

	},
	"setExpiryInSecond": async function (key, value) {
		return client.expire(key,value);
	}

};


/**
 * @description Returns the timestamp for next day 0000hrs in IST
 * @param {String} timeStamp - Timestamp for which want the next day 0000hrs.
 */
function calculateDayConsumptionAndSave(siteId, timestamp) {
	let key = siteId + "_mainMeter";
	//getting ids of energy meters which sum up to act as main meter
	DyanmoKeyStore.find({ "key": key })
		.then(store => {
			if (store.length == 0) {
				return Promise.reject("Site's Main Meter Not Configured.");

			}
			let ids = store[0].value;
			let idArr = ids.split(",");

			let promiseArr = idArr.map(devId => {
				//SearchObj for getting data of starting of day and end of day
				let sObjStart = {
					"where": {
						"deviceId": devId,
						"timestamp": {
							"lt": moment(timestamp).startOf("day").format("YYYY-MM-DD HH:mm"),
						}
					},
					"sort": "-1",
					"limit": 1
				};
				let sObjEnd = {
					"where": {
						"deviceId": devId,
						"timestamp": {
							"gte": moment(timestamp).subtract(1, "d").startOf("day").format("YYYY-MM-DD HH:mm"),
						}
					},
					"sort": "1",
					"limit": 1
				};
				return calculateConsumption(sObjStart, sObjEnd);
			});
			//returning array of promises so it can be processed
			return promiseArr;
		}).then((promiseArr) => {
			//summing up all consumption values of devices
			Promise.all(promiseArr).then(data => {
				let consumption = data.reduce((sum, number) => {
					return sum + number.data.consumption;
				}, 0);

				return consumption;
			}).then(consumption => {
				//saving data in database
				saveDailyConsumption(consumption, siteId, timestamp);
			});
		}).catch(err => {
			sails.log(err);
		});
}
//returns the timestamp for next day 0000hrs in IST
function nextDayTimestamp(timeStamp) {
	let nextTimeStampinUTC = timeStamp - 19800000 + 86400000;//19800000 is the milliseconds in 5:30 hours
	let dateinUTC = new Date(nextTimeStampinUTC).toDateString();
	//  var dateinUTC=new Date(Date.now()+86400000).toDateString();
	let timestamp = new Date(dateinUTC).getTime() + 19800000;
	return timestamp;
}

function saveDailyConsumption(consumption, siteId, timestamp) {
	getCurrentBaseline(siteId).then(baseline => {
		let startDate = moment(baseline.startDate);
		let endDate = moment(baseline.endDate);
		let diff = endDate.diff(startDate, "days");
		let target = Math.round(parseInt(baseline.baselineValue) / diff);
		let obj = {
			"siteId": siteId,
			"timestamp": moment(timestamp).subtract(1, "day").format("YYYY-MM-DD"),
			"actual": parseInt(consumption),
			"target": target,
		};
		sails.log(obj);
		DailyConsumption.create(obj).exec(function (err, data) {
			if (err) {
				sails.log("f:saveDailyConsumption");
				sails.log(err);
				return;
			}
			client.ltrim(siteId + "_consumption", 0, 0);
		});
	});


}
async function calculateConsumptionWithUserPreference(startObj, endObj, userPreferredUnit) {

	try {
		const deviceId = startObj.deviceId || startObj.where.deviceId;
		let [startData, endData] = await Promise.all([
			Datadevice.find(startObj),
			Datadevice.find(endObj)
		]);

		if (startData.length === 0 || endData.length === 0) {
			return {
				"err": { "deviceId": deviceId, "err": "No start data" }
			}
		}
		sD = startData[0].data;
		eD = endData[0].data;

		for (let key in sD) {
			sD[key.toLowerCase()] = sD[key];
		}
		for (let key in eD) {
			eD[key.toLowerCase()] = eD[key];
		}


		let startCons = helper.getConsumptionFromDeviceData(sD, userPreferredUnit)
		let endCons = helper.getConsumptionFromDeviceData(eD, userPreferredUnit)
		let consumption = startCons - endCons;

		if (isNaN(consumption) || consumption < 0) {
			consumption = 0;
		}
		return {
			"data": {
				"deviceId": deviceId,
				"consumption": helper.returnFilteredNumber(consumption),
				"kw": startData[0].data.kw
			}
		};
	}
	catch (e) {
		sails.log.error(e);
		return {
			"err": { "deviceId": startObj.deviceId, "err": "No start data" }
		}
	}

}


function calculateConsumption(startObj, endObj) {
	return new Promise((resolve, reject) => {
		let consParams = ["kvah", "kwh"];
		Datadevice.find(startObj).exec((err, startData) => {
			if (err || !startData || startData.length == 0) {
				resolve({
					"err": {
						"deviceId": startObj.deviceId,
						"err": "No start data"
					}
				});
				return;
			}
			Datadevice.find(endObj).exec((err, endData) => {
				if (err, !endData || endData.length == 0) {
					resolve({
						"err": {
							"deviceId": startObj.deviceId,
							"err": "No end data"
						}
					});
					return;
				}
				let sD, eD;
				if (Array.isArray(startData))
					sD = startData[0].data;
				else if (typeof startData === "object")
					sD = startData.data;
				else
					sD = {};
				if (Array.isArray(endData))
					eD = endData[0].data;
				else if (typeof endData === "object")
					eD = endData.data;
				else
					eD = {};

				for (let key in sD) {
					sD[key.toLowerCase()] = sD[key];
				}
				for (let key in eD) {
					eD[key.toLowerCase()] = eD[key];

				}
				let consumption = 0;
				consParams.forEach(param => {
					let startPoint = sD[param];
					let endPoint = eD[param];
					if (!helper.isNullish(startPoint) && !helper.isNullish(endPoint) && consumption === 0) {
						consumption = helper.returnFilteredNumber(startPoint - endPoint);
					}
				});
				if (isNaN(consumption) || consumption < 0) {
					consumption = 0;
				}
				resolve({
					"data": {
						"deviceId": startObj.deviceId || startObj.where.deviceId,
						"consumption": consumption,
						"kw": startData[0].data.kw
					}
				});
			});
		});
	});
}
function calculateConsumption_new(startObj, endObj, consUnit) {
	return new Promise((resolve, reject) => {
		Datadevice.find(startObj).exec((err, startData) => {
			if (err || !startData || startData.length == 0) {
				resolve({
					"err": {
						"deviceId": startObj.deviceId,
						"err": "No start data"
					}
				});
				return;
			}
			Datadevice.find(endObj).exec((err, endData) => {
				if (err, !endData || endData.length == 0) {
					resolve({
						"err": {
							"deviceId": startObj.deviceId,
							"err": "No end data"
						}
					});
					return;
				}
				let sD, eD;
				if (Array.isArray(startData))
					sD = startData[0].data;
				else if (typeof startData === "object")
					sD = startData.data;
				else
					sD = {};
				if (Array.isArray(endData))
					eD = endData[0].data;
				else if (typeof endData === "object")
					eD = endData.data;
				else
					eD = {};

				for (let key in sD) {
					sD[key.toLowerCase()] = sD[key];
				}
				for (let key in eD) {
					eD[key.toLowerCase()] = eD[key];

				}
				let consumption = 0;
				let startPoint = helper.getConsumptionFromDeviceData(sD, consUnit);
				let endPoint = helper.getConsumptionFromDeviceData(eD, consUnit);
				if (!helper.isNullish(startPoint) && !helper.isNullish(endPoint) && consumption === 0) {
					consumption = helper.returnFilteredNumber(startPoint - endPoint);
				}
				if (isNaN(consumption) || consumption < 0) {
					consumption = 0;
				}

				resolve({
					"data": {
						"deviceId": startObj.deviceId || startObj.where.deviceId,
						"consumption": consumption,
						"kw": startData[0]?.data?.kw|| 0
					}
				});
			});
		});
	});
}
//TODO tell the joulebox to send data always in order historically older first
//TODO group by the data each component is sending
function getCurrentBaseline(siteId) {
	let searchObj = {
		"where": {
			"siteId": siteId,
			"startDate": {
				"lte": moment().format("MM-DD-YYYY"),
			},
			"sort": "-1",
			"limit": 1
		}
	};

	return new Promise((resolve, reject) => {
		Baseline.find(searchObj).exec(function (err, data) {
			if (err) {
				reject(err);
				return;
			}
			if (data.length == 0) {
				if (moment().month() == 0) {
					let updatedObj = {
						"where": {
							"siteId": siteId,
							"startDate": {
								"beginsWith": "12"
							},
							"sort": "-1",
							"limit": 1
						}
					};
					Baseline.find(updatedObj).exec((err, data) => {
						if (err) {
							reject({ "err": "Unable to fetch data at present" });
						}
						if (data.length > 0) {
							let tmpDate = data[0].startDate.split("-");
							let month = tmpDate[0];
							let day = tmpDate[1];
							let year = tmpDate[2];
							tmpDate[0] = year;
							tmpDate[1] = month;
							tmpDate[2] = day;
							data[0].startDate = tmpDate.join("-");
							return resolve(data[0]);
						} else {
							reject({ "err": "No data present" });
						}
					});
				} else {
					reject({ "err": "No data present" });
					return;
				}
			} else {
				let tmpDate = data[0].startDate.split("-");
				let month = tmpDate[0];
				let day = tmpDate[1];
				let year = moment().format("YYYY");
				tmpDate[0] = year;
				tmpDate[1] = month;
				tmpDate[2] = day;
				data[0].startDate = tmpDate.join("-");
				return resolve(data[0]);
			}
		});
	});
}
