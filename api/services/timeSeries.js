const AWS = require("aws-sdk");
const timestreamwrite = new AWS.TimestreamWrite({
	"region": process.env.REGION,
	"maxRetries": 10,
});
const timestreamQueryClient = new AWS.TimestreamQuery({
	"region": process.env.REGION,
	"maxRetries": 10,
});
const moment = require("moment-timezone");
const utils = require("../utils/timeSeriesUtil");


const {
	promisify
} = require("util");

const describeTable = promisify(timestreamwrite.describeTable).bind(timestreamwrite);
const writeRecords = promisify(timestreamwrite.writeRecords).bind(timestreamwrite);
const queryTimeStream = promisify(timestreamQueryClient.query).bind(timestreamQueryClient);


class TimeSeriesModel {

	TIME_DATA_TYPE = "TIME"; // value in dimensions that indicate time field

	constructor(database, tablename, dimensions) {

		this.isValidDimension(dimensions); // will throw error in case invalid dimension
		this.dimensions = dimensions;
		this.database = database;
		this.tablename = tablename;
		for (let key in this.dimensions) {
			if (this.dimensions[key] === this.TIME_DATA_TYPE) {
				this.TIME_KEY_IN_DATAPKT = key;
				break;
			}
		}
	}

	async describeTable() {
		let res, query = {
			"DatabaseName": this.database,
			"TableName": this.tablename
		};

		try {
			res = await describeTable(query);
		} catch (e) {
			throw new Error(e);
		}
		return res;
	}
	/**
	 * @description Write data to timeseries DB. All keys except the required keys in "dataPkt" will be added as measures in timeseries DB.
	 * @param {object} dataPkt Must contain the keys: "timestamp, siteId, deviceId".
	 * @param {object} dataPkt.timestamp Can be passed in the following format: YYYY-MM-DD HH:mm:00
	 * @param {object} dataPkt.deviceId Contains deviceId/ processId of Compoennt / Process configuration.
   	*/
	async writeData(dataPkt) {

		if (!dataPkt) { // any falsie should be rejected
			throw new Error(`TimeSeriesModel::writeData Invalid dataPkt. Function recieved ${dataPkt}`);
		}
		if (dataPkt.constructor.name !== "Object") {
			throw new Error(`TimeSeriesModel::writeData Invalid dataPkt. Required Object found ${typeof (dataPkt)} for ${dataPkt}`);
		}

		let time = this.getTimeValue(dataPkt);
		let commonAttributes = this.getCommonAttribute(dataPkt, time);
		let records = this.getRecordsFromDataPkt(dataPkt);

		let query = {
			"DatabaseName": this.database,
			"TableName": this.tablename,
			"Records": records,
			"CommonAttributes": commonAttributes,
		};

		try {
			await writeRecords(query);
		} catch (e) {
			if (e.code === "RejectedRecordsException") {
				throw (`RejectedRecordsException: ${e.message}\n.This can due to\n1: Updating value with same Version.\n2. Trying to add values older than Memory store retention\n${e.stack}`);
			}
			throw (e);

		}

		return true;
	}
	/**
	 * Interface function for TimeSeries DB.
	 * Write data batch to timeseries DB. All keys except the required keys in "dataPkt" will be added as measures in timeseries DB.
	 */
	async writeDataBatch(dataPkts) {
		if (!dataPkts) { // any falsie should be rejected
			throw new Error(`TimeSeriesModel::writeDataBatch Invalid dataPkt. Function recieved ${dataPkts}`);
		}

		if (dataPkts.constructor.name !== "Array") {
			throw new Error(`TimeSeriesModel::writeDataBatch Invalid dataPkt 2. Expected dataPkt to be Array found ${typeof (dataPkt)}`);
		}


		// let records = dataPkts.map(this.getRecordsFromDataPkt);
		throw new Error("TimeSeriesModel::writeDataBatch Not implemented");
	}

	/**
	 * This function should be waterline ORM for timeseries to support basic
	 * queries. It should take input in DAO format and response in the same
	 * 
	 */
	async query(query) {
		// predicate on measure_name and time should be their, its best practice
	}

	/**
	 * This function performs a raw SQL query on timeseries database and return the matched response.
	 * @param {string} query SQL query string
	 * @param {Array} projections Array of columns to return from matched response
	 */
	async rawQuery(query, projections) {
		let q = {
				"QueryString": query
			},
			vals = [],
			data;

		try {
			data = await queryTimeStream(q);
		} catch (e) {
			throw e;
		}

		vals = vals.concat(this.parseRows(data.Rows, projections));
		while (data.NextToken) {
			q.NextToken = data.NextToken;
			data = await queryTimeStream(q);
			vals = vals.concat(this.parseRows(data.Rows, projections));
		}

		return vals;
	}

	// private function
	getRecordsFromDataPkt(dataPkt) {
		let records = [];

		for (let key in dataPkt) {
			if (this.dimensions[key] === undefined) { // this is not a dimension
				let value = dataPkt[key];
				if (utils.isValidTimeSeriesValue(value)) {
					records.push({
						"MeasureName": key,
						"MeasureValue": dataPkt[key].toString()
					});
				} else {
					sails.log(`Dropping Invalid parameter ${key} because of value ${dataPkt[key]}.`);
				}
			}
		}

		return records;
	}

	// private function
	getCommonAttribute(dataPkt, timeInMilliseconds) {
		let dimensions = [];

		for (let key in dataPkt) {
			if (this.dimensions[key] !== undefined && key !== this.TIME_KEY_IN_DATAPKT) { // this is dimension(dont include time here)
				dimensions.push({
					"Name": key,
					"Value": dataPkt[key]
				});
			}
		}

		return {
			"Dimensions": dimensions,
			"Time": timeInMilliseconds.toString()
		};
	}

	// private function
	getTimeValue(dataPkt) {
		let timestamp = dataPkt[this.TIME_KEY_IN_DATAPKT];
		if (timestamp === undefined || utils.isValidTimestamp(timestamp) === false) {
			throw new Error(`Invalid timestamp ${timestamp}`);
		}
		return moment(timestamp).unix() * 1000;
	}

	// private function
	isValidDimension(dimensions) {
		if (!dimensions) {
			throw new Error("Required field Dimensions missing in constructor");
		}
		if (dimensions.constructor.name !== "Object") {
			throw new Error(`Dimensions should be of type Object found ${dimensions.constructor.name}`);
		}

		if (Object.values(dimensions).indexOf(this.TIME_DATA_TYPE) === -1) {
			throw new Error("Dimensions should key with value TIME. The \"key\" with value \"TIME\" will be used to find value from data packet");
		}
		// TODO: also check if other values are VARCHAR, INT etc.
		return true;
	}

	// private functions
	parseRows(Rows, columns) {
		let parsedRows = [];

		if (!Rows || Rows.constructor.name !== "Array") {
			throw new Error(`parseRows:: Invalid rows recieved ${Rows}`);
		}
		Rows.map(row => {
			let data = row.Data || [];
			let temp = {};

			columns.forEach((column, index) => temp[column] = data[index].ScalarValue);
			parsedRows.push(temp);
		});
		return parsedRows;
	}
}

module.exports.TimeSeriesModel = TimeSeriesModel;

const database = "smartjoules";
const tableName = "datadevices";
const schema = {
	"timestamp": "TIME",
	"siteId": "VARCHAR",
	"deviceId": "VARCHAR"
};
const aggregatedParams = new TimeSeriesModel(database, tableName, schema);

module.exports.AggregatedParams = aggregatedParams;