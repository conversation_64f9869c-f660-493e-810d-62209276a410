module.exports = {
	"getCustomProduction": (siteId, start, end) => {
		let consumptionObj = {
			"where": {
				"siteId": siteId,
				"timestamp": {
					"between": [start, end],
				},
				"sort": "1",
			},
		};

		return Production.find(consumptionObj);
	},
	/**
	 * update dynamo for the existing entries.
	 * @param {Object} dateShift formatted object to update daily consumption table.
	 */
	"updateEntry"(dateShift) {
		const { siteId, timestamp } = dateShift;
		return Production.update(
			{
				siteId,
				timestamp,
			},
			dateShift
		);
	},
	/**
	 * Create dynamo with the new entries.
	 * @param {Object} dateShift formatted object to update daily consumption table.
	 */
	"createEntry"(dateShift) {
		return Production.create(dateShift);
	},

};
