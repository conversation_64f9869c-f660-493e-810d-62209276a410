/**
 * @file File exports utility functions for Versions Controllers. To be used specifically only for the APIs that call it to increase testability of the code. Kindly refrain from using them.
 * <AUTHOR>
 */


module.exports = {
	"addVersion": {
		"checkInput": function checkInput(body){
			if (body.deviceType == "controller"){
				if (!body.version || !body.deviceType || !body.gitRepoCICD || !body.gitRepoApplication || !body.gitRepoFirmware || !body.gitRepoHostServices || !body.gitRepoJouleBox || !body.gitRepoRoutingService || !body.dockerApplication || !body.dockerFirmware || !body.dockerJouleBox)
					return false;
			} else { // Incase deviceType is joulesense
				if (!body.deviceType || !body.version)
					return false;
			}

			return true;
		},
		"populateDataObj": function populateDataObj(body){
			let dataObj;
	
			if (body.deviceType == "controller") {
	
				// Populating repoList. To be saved in dynamo.
				let key = Object.keys(body);
				let repoList = []; 
				for (let i in key){
					if (key[i].charAt(0)=="g"){
						if (key[i].substring(0, 3)=="git"){
							repoList.push(key[i]);
						}
					} else if (key[i].charAt(0)=="d"){
						if (key[i].substring(0, 6)=="docker"){
							repoList.push(key[i]);
						}
					}
				}
	
				dataObj = {
					"version": body.version,
					"deviceType": body.deviceType,
					"gitRepoCICD": body.gitRepoCICD,
					"gitRepoApplication": body.gitRepoApplication,
					"gitRepoFirmware": body.gitRepoFirmware, 
					"gitRepoHostServices": body.gitRepoHostServices,
					"gitRepoJouleBox": body.gitRepoJouleBox,
					"gitRepoRoutingService": body.gitRepoRoutingService,
					"dockerApplication": body.dockerApplication,
					"dockerFirmware": body.dockerFirmware,
					"dockerJouleBox": body.dockerJouleBox,
					"repoList": repoList
				};
	
				// Adding d2rs version. TODO: Need generic solution for future repositories.
				if (body.gitRepod2rs != undefined && body.gitRepod2rs != "")
					dataObj["gitRepod2rs"] = body.gitRepod2rs;
				
			} else { // Incase deviceType is joulesense
				dataObj = {
					"version": body.version,
					"deviceType": body.deviceType,
					"gitTag": body.gitTag ? body.gitTag : "null",
					"hardwareVer": body.hardwareVer ? body.hardwareVer : "null"
				};
			}
			return dataObj;
		}
	},
};