/* eslint-disable no-useless-escape */
const moment = require("moment-timezone");
const fs = require("fs");

moment.tz.add("Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6");
moment.tz.setDefault("Asia/Kolkata");
const PF = 0.98;

module.exports = {

	"toArray": function (a) {
		if (!a) {
			return undefined;
		}
		try {
			a = a.constructor.name === "Array" ? a : JSON.parse(a);
			if (a.constructor.name === "Array") {
				return a;
			} else {
				return undefined;
			}
		} catch (e) {
			return undefined;
		}

	},
	/**
	 * @description In case a valid minutes number is passed find the total number of hours/days that minute number consist of. So, 191 minutes = 3 hours 11 min.
	 * @param {number} num Integer number representing number of minutes
	 * @returns {string} "NA" if not valid num passed else string of converted value
	 *
	 */
	"formatMinutesToDays": function (num) {

		if (isNaN(num)) {
			return "NA";
		}
		if (typeof num !== "number") {
			return "NA";
		}
		let day = Math.floor(num / 1440); // 60*24
		let hour = Math.floor((num - (day * 1440)) / 60);
		let min = Math.round(num % 60);

		if (day > 0) {
			return (day + " D, " + hour + " Hr, " + min + " Min");
		} else {
			return (hour + " Hr, " + min + " Min");
		}
	},
	/**
	 * Convert any valid date format to unix timestamp else null
	 * @param {string} ts timestamp in any valid format
	 */
	"tsToUnix": function (ts) {

		parseInt(ts) ? ts = parseInt(ts) : null;
		let momentTs = moment(ts);

		if (momentTs) {
			if (momentTs.isValid()) {
				return momentTs.unix();
			} else {
				return;
			}
		} else {
			return;
		}
	},
	"toString": function (obj) {

		if (!obj) {
			return undefined;
		}
		try {
			obj = typeof (obj) === "string" ? obj : JSON.stringify(obj);
			if (typeof (obj) === "string") {
				return obj;
			} else {
				return undefined;
			}
		} catch (e) {
			return undefined;
		}


	},
	"getRandomInt": function (max) {
		return Math.floor(Math.random() * Math.floor(max));
	},
	"toJson": function (a) {
		if (!a) {
			return undefined;
		}
		try {
			a = typeof (a) === "object" ? a : JSON.parse(a);
			if (a) {
				return a;
			} else {
				return undefined;
			}
		} catch (e) {
			return undefined;
		}

	},
	"getts": function (x) {
		// get timestamp
		if (x) return moment(x).format("YYYY-MM-DD HH:mm");
		return moment().format("YYYY-MM-DD HH:mm");
	},
	"convertStringArrayToJSONArray": function (arr) {
		if (!arr)
			return;
		if (!Array.isArray(arr)) {
			return null;
		}

		for (let i = 0; i < arr.length; i++) {
			try {
				if (typeof arr[i] == "string")
					arr[i] = JSON.parse(arr[i]);

			} catch (e) {
				sails.log.error(e);
			}
		}
		return arr;
	},
	"convertJSONArrayToStringArray": function (arr) {
		if (!arr)
			return;
		if (!Array.isArray(arr))
			return;
		for (let i = 0; i < arr.length; i++) {
			try {
				if (typeof arr[i] == "object") {
					arr[i] = JSON.stringify(arr[i]);
				}

			} catch (e) {
				sails.log.error(e);
			}
		}
		return arr;
	},
	"isEmail": function (email) {
		let re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
		return re.test(String(email).toLowerCase());
	},
	"setDifference": function (setA, setB) {
		let _difference = new Set(setA);
		for (let elem of setB) {
			_difference.delete(elem);
		}
		return _difference;
	},
	"deepClone": function (obj) {
		let clone = Object.assign({}, obj);
		Object.keys(clone).forEach(
			key => (clone[key] = typeof obj[key] === "object" ? this.deepClone(obj[key]) : obj[key])
		);
		return Array.isArray(obj) ? (clone.length = obj.length) && Array.from(clone) : clone;
	},
	"removeDuplicate": function (arr) {
		return arr.filter(function (item, pos) {
			return arr.indexOf(item) == pos;
		});
	},
	"parseFormulaToIds": function (formulaText) {
		let x = formulaText.split("||");
		let devId = new Set();
		for (let i = 1; i < x.length - 1; i++) {
			let expr = x[i];
			let dvParm = expr.split("@");
			if (dvParm.length == 2 && dvParm[0] != "COMMAND") {
				devId.add(dvParm[0]);
			}
		}
		return [...devId];
	},
	"returnFilteredNumber": (num) => {
		//returns a filtered number by stripping and rounding off number as per it's value
		if (num === undefined)
			return null;
		try {
			if (typeof num == "string")
				num = Number(num);

		} catch (e) {
			sails.log.info(e);
			return null;
		}
		if (isNaN(num))
			return null;

		else if (num > 0 && num < 10)
			return Math.round(num * 1000) / 1000;
		else if (num > 10 && num < 100)
			return Math.round(num * 10) / 10;
		else
			return Math.round(num);
	},

	"arrCopy": (arrToCopy, baseArray) => {
		arrToCopy.forEach(item =>
			baseArray.push(item));
		return baseArray;
	},

	"isSubset": (baseArr, arrToCompare) => {
		let result = arrToCompare
			.every(val => baseArr.includes(val));
		return result;
	},

	"validateParams": (parameterArray, errorMsg) => {
		parameterArray.forEach(param => {
			if (!param)
				throw errorMsg;
		});
	},

	// Function to sort a list of objects based on a property
	"sortObjects": (objectList, property) => {
		try {
			helper
				.validateParams(
					[
						objectList,
						property
					],
					undefined);

			objectList.sort((a, b) => {
				return a[property] - b[property];
			});

			return objectList;
		} catch (err) {
			throw err;
		}
	},

	/**
	 * <AUTHOR> <<EMAIL>>
	 * @name addNullGroupBy
	 * @summary Helper function to add null value.
	 * @description Helper function to add null value in the array on the basis of groupBy where data is not present.
	 */
	"addNullGroupBy": (data, groupByChoice) => {
		if (!data || data.length == 0) {
			sails.log.info("No data Present");
			return [];
		}
		if (groupByChoice == undefined) {
			groupByChoice = "minutes";
		}
		let fixedValue = [];
		for (let i = 0; i < data.length - 1; i++) {
			let pastTimeStamp = moment(data[i][0]);
			let nextTimeStamp = moment(data[i + 1][0]);
			//when data is in Increasing Order
			if (nextTimeStamp.diff(pastTimeStamp, groupByChoice) > 0) {
				if (nextTimeStamp.diff(pastTimeStamp, groupByChoice) == 1) {
					fixedValue.push(data[i]);
				} else {
					fixedValue.push(data[i]);
					if (groupByChoice == "minutes" && nextTimeStamp.diff(pastTimeStamp, groupByChoice) > 3) {
						pastTimeStamp = pastTimeStamp.add(3, groupByChoice);
						fixedValue.push([moment(pastTimeStamp, "MM-DD-YYYY").unix() * 1000, null]);
					} else if (groupByChoice != "minutes") {
						pastTimeStamp = pastTimeStamp.add(1, groupByChoice);
						fixedValue.push([moment(pastTimeStamp, "MM-DD-YYYY").unix() * 1000, null]);
					}
				}
			}
		}
		fixedValue.push(data[data.length - 1]);
		return fixedValue;
	},
	/**
	 * @summary Helper function to add Timestamps to time but in chunks. Meaning ts.add(30,minute) for ts=12:23 will give five 12:30 bcz thats whats nearest chunk of 30 will the ts fit in.
	 * so "12:02".add(60,min) will give 13:00
	 *
	 *
	 */
	"addMinuteInChunks": (ts, chunk) => {

		//  n = chunk , ts = 12:33pm
		// minutes  = ts["minutes"] = 33
		//  x = minutes % n
		//  tsDiff = minutes + absolute( n - x  )


		try {
			let intChunk = parseInt(chunk);
			let parsedTs = moment(parseInt(ts));
			let min = parsedTs.minutes();
			let mod = min % intChunk;
			let diff = Math.abs(intChunk - mod);
			return (parsedTs.add(diff, "m").startOf("m").unix());

		} catch (e) {
			sails.log.error("ERRoR ", "Helper.addMinuteInChunks", e);
			return undefined;
		}
	},
	"addNullMinuteForBenchmarking": async (data) => {
		if (!data || data.length == 0) {
			sails.log.info("No data Present");
			return;
		}

		let fixedValue = [];
		for (let i = 0; i < data.length - 1; i++) {
			let pastTimeStamp = moment(data[i][0]);
			let nextTimeStamp = moment(data[i + 1][0]);
			//when data is in Increasing Order
			if (nextTimeStamp.diff(pastTimeStamp, "minutes") > 0) {
				if (nextTimeStamp.diff(pastTimeStamp, "minutes") == 15) {
					fixedValue.push(data[i]);
				} else {
					fixedValue.push(data[i]);
					pastTimeStamp = pastTimeStamp.add(1, "minutes");
					fixedValue.push([moment(pastTimeStamp, "MM-DD-YYYY").unix() * 1000, null]);
				}
			}
		}
		fixedValue.push(data[data.length - 1]);
		return fixedValue;
	},

	/**
	 * @function saveMockData
	 * @summary Generates a file "<fileName>.json" in folder, ie "./mockdata/*" , with the stringified version of the data passed to it. Used to help create mockData for testing purposes.
	 * @param {Any Datatype} data Any data
	 * <AUTHOR> Gupta
	 */
	"saveMockData": function (data, fileName) {
		fs.writeFileSync(`./test/testData/mockdata/${fileName}.json`, JSON.stringify(data));
	},
	"readMockData": function (fileName) {
		let data = fs.readFileSync(`./test/testData/mockdata/${fileName}.json`);
		try {
			data = JSON.parse(data);
		} catch (error) {
			sails.log.error("Error parsing data read from file: ", fileName);
			throw error;
		}
		return data;
	},
	/**
	 * @function createMap
	 * @param {Array} arr
	 * @param {string} key
	 * @return {object} returns an object with key as the value of key in each object of array and value being the object itself
	 * @example for array
	 * [{
	 * 	"name":"amit",
	 * 	"age":10,
	 * },{
	 * 	"name":"rohit",
	 * 	"age": 50
	 * }]
	 * and key as name
	 * it will return
	 * {
	 * 	"amit":{
	 * 	"name":"amit",
	 * 	"age":10
	 * },
	 * "rohit":{
	 * 	"name": "rohit",
	 *  "age": 50
	 * }
	 * }
	 *
	 */
	"createMap": function (arr, key) {
		return arr.reduce((mapObj, param) => {
			mapObj[param[key]] = param;
			return mapObj;
		}, {});
	},

	"kwhToKvah": function (kwh, pf = PF) {
		kwh = parseFloat(kwh);
		pf = parseFloat(pf);
		let kvah = kwh / pf;
		return kvah;
	},
	"kvahToKwh": function (kvah, pf = PF) {
		kvah = parseFloat(kvah);
		pf = parseFloat(pf);
		let kwh = kvah * pf;
		return kwh;
	},
	/**
	 * From a device data packet, retrieve the consumption value(kvah or kwh)
	 * @param {object} data Device data packet
	 * @param {string} userPreferUnit unit of consumption (kvah or kwh)
	 * @returns string("null") or Float(unit)
	 */
	"getConsumptionFromDeviceData": function (data, userPreferUnit) {
		let consdata, kwh, kvah;
		if (!data) {
			return "null";
		}

		consdata = parseFloat(data[userPreferUnit]);
		if (!isNaN(consdata)) {
			return consdata;
		}
		// pf = parseFloat(data["pf"]), kwh = parseFloat(data["kwh"]),
		// kvah = parseFloat(data["kvah"]);
		kwh = parseFloat(data["kwh"]), kvah = parseFloat(data["kvah"]);

		if (isNaN(kwh) && isNaN(kvah)) {
			return "null";
		}
		if (isNaN(kwh)) {
			// user wants kwh and we have kvah
			kwh = helper.kvahToKwh(kvah, PF); // we will have kvah else upper if/else would have already returned
			return kwh;
		} else {
			// user want kvah and we have kwh
			kvah = helper.kwhToKvah(kwh, PF);
			return kvah;
		}
	},

	"isNullish": element => !(typeof element !== "undefined" && !isNaN(element) && element !== null && element !== "null"),

	"padoneZero": function (val) {
		return ("0" + val).slice(-2);
	},

	/**
	 * get actual start date of a group. this is like moment's startOf but instead of startOf group, this checks
	 * if startOf group + 'startTimeOfDayInMinutes' is greater then time right now, then choose immediate previous group
	 * instead of current.
	 * @param {Number} startTimeOfDayInMinutes Start of work time today, i.e some sites start 8AM, so 480 min for that.
	 * @param {string} group group by value. Supports all values of moment's groupBy, i.e day, week, month etc
	 */
	"actualStartOf": function (startTimeOfDayInMinutes, group) {
		let now = moment();
		let groupStartTime = now.clone()
			.startOf(group)
			.add(startTimeOfDayInMinutes, "minutes");

		if (groupStartTime > now) {
			groupStartTime = groupStartTime
				.subtract(1, group);
		}

		return groupStartTime;
	}
};
