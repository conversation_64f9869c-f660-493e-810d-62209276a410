const pgConnection = require('./pgConnection')

module.exports = {
  /**
   *
   * @param deviceId - it can accept deviceId and componentId
   * @param type | device, component
   * @return {Promise<void>}
   */
  async getLinkedConfiguratorSystem(deviceId, type) {
    return await Promise.all([this.getTableLinkedByDeviceId(deviceId, type), this.getSVGTypeLinkedByDeviceId(deviceId, type)])
  },
  async getTableLinkedByDeviceId(deviceId, type) {
    const pgDbCon = new pgConnection()
    const taggedTableResult = await pgDbCon.query(`SELECT DISTINCT ctr.device_id AS "deviceId",ssp.title AS "pageTitle",cs.name AS "subsystemName",csc.name AS "systemName" FROM configurator_table_row ctr JOIN configurator_table ct ON ctr."table_id" = ct.id AND ctr."device_id" = '${deviceId}' AND ct."device_type" = '${type}' JOIN configurator_table_group ctg ON ct."table_group_id" = ctg.id JOIN sub_system_pages ssp ON ctg."pages_ref_id" = ssp.id JOIN configurator_systems cs ON ssp."sub_system_id" = cs.id JOIN configurator_system_category csc ON cs."system_id" = csc.id WHERE ctr.status = 1 AND ct.status = 1 AND ctg.status = 1 AND ssp.status = 1 AND cs.status = 1 AND csc.status = 1`)
    const { rows } = taggedTableResult
    return rows;
  },
  async getSVGTypeLinkedByDeviceId(deviceId, type) {
    const pgDbCon = new pgConnection()
    const taggedSVGResult = await pgDbCon.query(`SELECT DISTINCT ctd.device_id AS "deviceId", ssp.title AS "pageTitle", cs.name AS "subsystemName", csc.name AS "systemName" FROM configurator_tagged_devices ctd JOIN sub_system_pages ssp ON ctd.sub_system_page_id = ssp.id AND ctd.device_id = '${deviceId}' AND ctd."device_Type" = '${type}' JOIN configurator_systems cs ON ssp.sub_system_id = cs.id JOIN configurator_system_category csc ON cs.system_id = csc.id WHERE ctd.status = 1 AND ssp.status = 1 AND cs.status = 1 AND csc.status = 1`)
    const { rows } = taggedSVGResult
    return rows;
  }
}
