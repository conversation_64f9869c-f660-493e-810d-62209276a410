const moment = require("moment-timezone");
const pgConnection = require('./pgConnection');
moment.tz.add(
	"Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6"
);
module.exports = {
    /*returns
    {
        deviceID:<>,
        param:<>,
        obs:<>,
        topic:<>

    }
    */
    "getDevicesInRegion": async function (siteId, region) {
        try {
            let devices = await Devices.find({ "siteId": siteId });
            let filteredDevices = devices.filter((device) => device.regionId == region);
            return filteredDevices;
        } catch (e) {
            sails.log.error(e);
            return e;
        }
    },
    "updateConfigTs": async function ({ siteId, timestamp }) {
        let key = `${siteId}_configTS`;
        let value = timestamp || moment().unix() * 1000;
        try {
            await DyanmoKeyStore.create({ key, value });
            return true;
        } catch (e) {
            sails.log.error(e);
            return false;
        }
    },
    "updateAllConfigTs": async () => {
        try {
            let allSites = await Sites.find({});
            let promiseArr = allSites.map((site) => deviceService.updateConfigTs({ "siteId": site.siteId }));
            await Promise.all(promiseArr);
            return true;
        } catch (error) {
            sails.log.error(error);
            return false;
        }
    },
    /**
     * @function getAvailableSlaveIds
     * @param {object} params contains following parameters
     * devicesList - An array of devices objects (optional)
     * controllerId - DeviceId of controller in which device has to be added
     * siteId - siteId of site
     * @description Gives a list of available slaveIds on that specific controller and
     * a map of slaveIdToDeviceTypes to accomodate for vrfController.
     * @returns {Array} Array of available slave or an empty array in case of error
     */
    "getAvailableSlaveIds": async function (params) {
        let { devicesList, controllerId, siteId } = params;
        try {
            if (!devicesList) devicesList = await Devices.find({ "siteId": siteId });
            let slaveArr = new Array(255); // Modbus slaves addresses can only be from 1-255
            let slaveIdToDeviceTypeMap = {};
            slaveArr.fill(false);
            let availableIndexes = [];
            devicesList.map((device) => {
                if (device.controllerId == controllerId && device.communicationType == "MB") {
                    slaveArr[device.slaveId] = true;
                    slaveIdToDeviceTypeMap[device.slaveId] = device.deviceType;
                }
            });
            for (let i = 1; i < slaveArr.length; i++) {
                if (!slaveArr[i]) {
                    availableIndexes.push(i.toString());
                }
            }
            return { availableIndexes, slaveIdToDeviceTypeMap };
        } catch (e) {
            sails.log.error(e);
            return [];
        }
    },
    /**
     *
     * @param {Set} added
     * @param {Set} removed
     * @param {string} componentId
     * @param {string} siteId
     * @description Updates componentId attribute of devices when the configuration of component is changed i.e either a new device is added or a device is removed from the component configuration
     * @returns {Array} A Promise array
     */
    "updateComponentInfoInDevices": function (added, removed, componentId, siteId) {
        if (added.size == 0 && removed.size == 0) return Promise.resolve([]);
        let promiseArr = [];
        added.forEach((deviceId) => {
            let $update = updateDevices(deviceId, "add");
            promiseArr.push($update);
        });
        removed.forEach((deviceId) => {
            let $update = updateDevices(deviceId, "remove");
            promiseArr.push($update);
        });
        return Promise.all(promiseArr).catch((e) => {
            sails.log.error("unable to update component Ids of controllers", [...added], [...removed], componentId, e);
        });

        //helpers
        /**
         * @function updateDevices
         * @param {string} deviceId
         * @param {string} operation can have two values add or remove
         * @description Finds old componentId of devices and adds/removes componentId based on the operation parameter
         * @returns {Promise} Promise object of device that is to be updated
         */
        function updateDevices(deviceId, operation) {
            let $update = Devices.findOne({ deviceId }).then((dev) => {
                if (!dev) return;
                let oldComponents = dev.componentId;
                componentId = generateCompList(oldComponents, componentId, operation);
                return Devices.update({ deviceId, siteId }, { componentId });
            });
            return $update;
        }
        function generateCompList(oldComponents, componentId, operation) {
            if (!oldComponents) return "NA";
            if (operation === "remove") {
                let cIdSet = new Set(oldComponents.split(","));
                cIdSet.delete(componentId);
                componentId = [...cIdSet].join(",");
                if (componentId === "") componentId = "NA";
            } else if (operation === "add") {
                //edge case for device is attached to component for the first time
                if (typeof oldComponents === "undefined") oldComponents = "";
                if (oldComponents != "NA" && oldComponents !== "null") {
                    let cIdSet = new Set(oldComponents.split(","));
                    cIdSet.add(componentId);
                    componentId = [...cIdSet].join(",");
                }
            }
            return componentId;
        }
    },
    /**
     * @function getDevices
     * @param {string} siteId
     * @param {Boolean} returnMap
     * @description Return all devices for a given siteId in an array
     * If returnMap is true it will return a map of deviceIds
     * @returns {Array} of devices
     * @returns {Object} Map of devices with key as deviceIds
     */
    "getDevices": async function (siteId, returnMap = false) {
        let devices = await Devices.find({ siteId });
        let deviceMap = {};
        if (returnMap) {
            devices.map((device) => {
                deviceMap[device.deviceId] = device;
            });
            return deviceMap;
        }
        return devices;
    },
    /**
     * @param {string} siteId
     * @param {string} deviceType
     * for a given siteId return all the devices with given devicetypes
     */
    "getDeviceTypes": async (siteId, deviceType) => {
        let devices = await deviceService.getDevices(siteId);
        return devices.filter((dev) => dev.deviceType === deviceType);
    },
    /**
     * @function getQueriedParameterConfiguration
     * @description Call back for an MQTT topic. On start up device requests queried parameter configuration related to it. If the device is configured in a
     * process enabled site and has queried parameters configured to it, it responds the configuration to topic: "<siteId>/response/<controllerId>/queriedparameter"
     * @param {string} topic MQTT topic on which this callback is being triggered. Topic: "<siteId>/request/<controllerId>/queriedparameter"
     * @param {object} payload Not actually required for production code, but is being
     * is being provided to uncomment to help debug in case any problem.
     */
    "getQueriedParameterConfiguration": async (topic, payload) => {
        const sitesDeployedWithProcesses = ["sjo-del", "rpml-bij"];

        // Debugging purposes.
        // console.log("Queried Parameter MQTT topic has been procced!!");
        // console.log('topic, packet: ', topic, payload);

        let topicParams = topic.split("/");
        let siteId = topicParams[0];
        let controllerId = topicParams[2];
        let responseTopic = `${siteId}/response/${controllerId}/queriedparameter`;
        let timestamp = moment().format("YYYY-MM-DD HH:mm:ss");
        let responsePayload = {
            "operation": "queriedParam",
        };
        let allProcesses = await processService.find({ siteId });
        let matchingProcesses = allProcesses.filter((process) => process.controllerId == controllerId);

        if (sitesDeployedWithProcesses.includes(siteId)) {
            matchingProcesses.forEach((processInfo) => {
                if (processInfo.queriedParams === undefined || processInfo.queriedParams === null) {
                    return;
                } else {
                    processInfo.queriedParams.forEach((parameter) => {
                        parameter.expression = parameter.expression.replace(/\|\|/g, "");
                        parameter["timestamp"] = timestamp;
                    });
                    responsePayload[processInfo.processId] = processInfo.queriedParams;
                }
            });
            eventService.publish(responseTopic, responsePayload);
        } else {
            // TODO: Configure queried parameters response for those sites which have not been ported to "Processes" yet.
            // let componentInfo = await Component.findOne({ "deviceId": componentId });
            // let parsedDataConfig =
            // let statusConfiguration = componentInfo.data
            // let statusConfiguration = await componentService.queryDataKeys(componentId, { "key": "status" }, ["key", "expression", "deviceId"]);
            // responsePayload.queriedParams = statusConfiguration;
        }
    },
    /**
     * @function saveOffSetData
     * @description When an IOT controller goes offline, it starts calculating the queriedParam data locally.
     * When it reconnects the internet, it informs backend of the offset that needs to be updated, ie, added to the last known values in the "Queriedparameters" database.
     * This functions simply queries the last known value, adds the offset to it and updates the entry.
     * @param {string} topic MQTT Topic which triggers this callback: "<siteId>/queriedParamData/<controllerId>/recent"
     * @param {object } payload Payload of the MQTT topic
     * @param {string} payload.abbr queriedParam "abbr" that needs to be updated.
     * @param {string} payload.queriedParamOffset Offset value that needs to be added to the currently last known value.
     */
    "saveOffSetData": async (topic, payload) => {
        try {
            let topicParams = topic.split("/");
            let siteId = topicParams[0];
            let processId = topicParams[2];
            let { abbr, queriedParamOffset } = payload;

            let lastKnownValues = await Queriedparameters.findOne({
                siteId,
                processId,
            });
            if (!lastKnownValues) {
                sails.log.error(`No last known values found for processId: ${processId}. Stopping execution.`);
                return;
            }
            let { offsetData } = lastKnownValues;
            if (offsetData === undefined) {
                offsetData = {};
                offsetData[abbr] = Number(queriedParamOffset);
            } else if (offsetData[abbr] === undefined) {
                offsetData[abbr] = Number(queriedParamOffset);
            } else {
                let newOffSetValue = Number(offsetData[abbr]) + Number(queriedParamOffset);
                if (isNaN(newOffSetValue)) {
                    sails.log.error("Error! : [saveOffSetData] New offset data being calculated is NaN. Stopping execution.");
                    sails.log.error("Topic:", topic);
                    sails.log.error("Payload:", payload);
                    return;
                }
                offsetData[abbr] = newOffSetValue;
            }
            await Queriedparameters.update({
                siteId,
                processId,
            }).set({
                offsetData,
            });
        } catch (error) {
            sails.log.error("[saveOffSetData] Error! Stopping execution.");
            sails.log.error(error);
        }
    },
    /**
     * @function deleteParentEMFromChildEM
     * @description Check if the device is a parent device and
     * If any of the child device are present
     * Then remove the parenEM from child
     * Means delete child device association from parent device
     * @param {string} siteId
     * @return {boolean}
     */
    "deleteParentEMFromChildEM": async (deviceConfig, resObj) => {
        try {
            const removeParentEmFromChild = await Devices.update({
                deviceId: deviceConfig.childId,
                siteId: deviceConfig.siteId,
            }).set({
                parentEM: null,
            });
            if (removeParentEmFromChild && _.isEmpty(removeParentEmFromChild)) {
                sails.log(`Could not removed parentEM association form child:${deviceConfig.childId}`);
                resObj.notDeleted.push(deviceConfig.deviceId);
                resObj.err.push(`Unable to delete child device for device: ${deviceConfig.deviceId}`);
                return false;
            }
            sails.log(`Removed child:${deviceConfig.childId} from parent: ${deviceConfig.deviceId}`);
            return true;
        } catch (e) {
            sails.log(`Error in deleteParentEMFromChildEM: ${e.message}`);
            sails.log.error(e);
            resObj.err.push(`Error in deleteParentEMFromChildEM: ${e.message}`, `Unable to delete child device: ${deviceConfig.childId} for device: ${deviceConfig.deviceId} at site: ${deviceConfig.siteId}`);
            return false;
        }
    },
    /**
     * @function getComponentConfigurationIfDeviceExist
     * @description This function is used to get the component configuration if the device is present in the 		* database.
     * @param {Object} componentConfigObj
     * @param {Object} resObj
     * @returns boolean
     */
    "getComponentConfigurationIfDeviceExist": async (componentConfigObj, resObj) => {
        let componentConfiguration = await Component.findOne({
            deviceId: componentConfigObj.componentId,
            siteId: componentConfigObj.siteId,
        });
        if (!componentConfiguration) {
            sails.log.error(`No component data found for componentId: ${componentConfigObj.componentId}`);
            return false;
        }
        let isComponentConfigured = false;
        componentConfiguration.controls = componentConfiguration.controls || [];
        componentConfiguration.data = componentConfiguration.data || [];
        let finalComponentData = [...componentConfiguration.controls, ...componentConfiguration.data];
        finalComponentData = finalComponentData.map((item) => {
            item = typeof item != "object" ? JSON.parse(item) : item;
            item.deviceId = (item.deviceId && item.deviceId.split(",")) || [];
            return item;
        });
        for (const item of finalComponentData) {
            if (Array.isArray(item.deviceId) && item.deviceId.length > 0) {
                for (const deviceItem of item.deviceId) {
                    if (deviceItem === componentConfigObj.deviceId) {
                        resObj.notDeleted.push(componentConfigObj.deviceId);
                        resObj.err.push(`DeviceId ${componentConfigObj.deviceId} could not be deleted as it is a part of a component (componentId:
						${componentConfigObj.componentId}, componentName: ${componentConfiguration.name}) and the Parameter: ${item.displayName} for siteId:  ${componentConfiguration.siteId} and controllerId: ${componentConfiguration.controllerId}`);

                        isComponentConfigured = true;
                    }
                }
            }
        }
        return isComponentConfigured;
    },
    /**
     * @function deleteFromEnergyMeterList
     * @description This function is used to delete the device from the energy meter list.
     * @param {String} siteId
     * @param {String} deviceId
     * @returns boolean
     */
    "deleteFromEnergyMeterList": async (deleteDeviceConfig, resObj) => {
        try {
            let energyMeterConfiguredList = await DyanmoKeyStore.findOne({
                key: `${deleteDeviceConfig.siteId}_em`,
            });
            if (!energyMeterConfiguredList) {
                sails.log(`${deleteDeviceConfig.siteId}_em does not exist`);
                return true;
            } else if (energyMeterConfiguredList && !energyMeterConfiguredList.list) {
                sails.log(`${deleteDeviceConfig.siteId}_em list is empty`);
                return true;
            } else if (energyMeterConfiguredList && energyMeterConfiguredList.list.indexOf(deleteDeviceConfig.deviceId) === -1) {
                sails.log(`${deleteDeviceConfig.deviceId} does not exist`);
                return true;
            }
            let energyMeterList = energyMeterConfiguredList.list.filter((listItem) => listItem !== deleteDeviceConfig.deviceId);
            if (energyMeterList && energyMeterList.length === 0) {
                energyMeterList = null;
            }
            const deviceDeleted = await DyanmoKeyStore.update(
                {
                    key: `${deleteDeviceConfig.siteId}_em`,
                },
                {
                    list: energyMeterList,
                }
            );
            if (deviceDeleted && _.isEmpty(deviceDeleted)) {
                sails.log(`${deleteDeviceConfig.deviceId} is not deleted from the energy meter`);
                resObj.notDeleted.push(deleteDeviceConfig.deviceId);
                resObj.err.push(`${deleteDeviceConfig.deviceId} not deleted`);
                return false;
            }
            sails.log(`${deleteDeviceConfig.deviceId} is deleted from the energy meter`);
            return true;
        } catch (e) {
            sails.log(`Error in delete From Energy Meter List: ${e}`);
            resObj.notDeleted.push(deleteDeviceConfig.deviceId);
            resObj.err.push(`Error in delete From Energy Meter List device: ${deleteDeviceConfig.deviceId}`);
            return false;
        }
    },
    /**
     * @function deleteFromEnergyMeterList
     * @description This function is used to delete the device from the main energy meter list.
     * @param {String} siteId
     * @param {String} deviceId
     * @returns boolean
     */
    "deleteFromMainMeterList": async (deleteDeviceConfig, resObj) => {
        try {
            const mainMeterConfiguredList = await DyanmoKeyStore.findOne({
                key: `${deleteDeviceConfig.siteId}_mainMeter`,
            });
            if (!mainMeterConfiguredList) {
                sails.log(`${deleteDeviceConfig.siteId}_mainMeter does not exist`);
                return true;
            } else if (mainMeterConfiguredList && !mainMeterConfiguredList.list) {
                sails.log(`${deleteDeviceConfig.siteId}_mainMeter list is empty`);
                return true;
            } else if (mainMeterConfiguredList && mainMeterConfiguredList.list.indexOf(deleteDeviceConfig.deviceId) === -1) {
                sails.log(`${deleteDeviceConfig.deviceId} does not exist`);
                return true;
            }

            let mainMeterList = mainMeterConfiguredList.list.filter((listItem) => listItem !== deleteDeviceConfig.deviceId);
            if (mainMeterList && mainMeterList.length === 0) {
                mainMeterList = null;
            }
            const deviceDeletedFromMainMeterList = await DyanmoKeyStore.update(
                {
                    key: `${deleteDeviceConfig.siteId}_mainMeter`,
                },
                {
                    list: mainMeterList,
                }
            );
            if (deviceDeletedFromMainMeterList && _.isEmpty(deviceDeletedFromMainMeterList)) {
                sails.log(`${deleteDeviceConfig.deviceId} is not deleted from the mainMeter`);
                resObj.notDeleted.push(deleteDeviceConfig.deviceId);
                resObj.err.push(`${deleteDeviceConfig.deviceId} not deleted`);
                return false;
            }
            sails.log(`${deleteDeviceConfig.deviceId} deleted form mainMeter`);
            return true;
        } catch (e) {
            sails.log.error(e);
            resObj.notDeleted.push(deleteDeviceConfig.deviceId);
            resObj.err.push(`Error in delete From Main Meter List device: ${deleteDeviceConfig.deviceId}`);
            return false;
        }
    },
    getLinkedConfiguratorSystemNameByDeviceId: async function (deviceIds) {
        const pgDbCon = new pgConnection();
        const taggedSystemsResult = await pgDbCon.query(`SELECT
    string_agg(ssp.title,',') AS name,
    ctd.device_id
    FROM
    sub_system_pages ssp
    JOIN
    configurator_tagged_devices ctd
    ON
    ctd.sub_system_page_id = ssp.id
    AND ctd.status = 1
    AND ctd."device_Type" = 'device'
    WHERE
    ctd.device_id IN (${deviceIds.map((it) => `'${it}'`).join(",")})
    AND ssp.status = 1
    GROUP BY
    ctd.device_id`);
        const { rows } = taggedSystemsResult;
        return rows;
    },
    markPrimaryAndSecondaryJouleBox: function (jouleBoxControllerDetail, JouleBoxControllerList) {
        if (jouleBoxControllerDetail.deviceType !== "joulebox") return;
        if (JouleBoxControllerList?.length === 0) {
            jouleBoxControllerDetail.isPrimaryJouleBox = 1;
            return;
        }
        jouleBoxControllerDetail.isPrimaryJouleBox = 0;
        return;
    },
    validateBMSXSlaveControllerAttachment: async function (controllerConfig) {
        const THIRD_PARTY_BMS_SLAVE_CONTROLLERS = ["n3uron"];
        if (!THIRD_PARTY_BMS_SLAVE_CONTROLLERS.includes(controllerConfig.vendorId)) {
            sails.log.warn(`VendorId ${controllerConfig.vendorId} is not a third party BMS slave controller`);
            return { success: false, message: `VendorId ${controllerConfig.vendorId} is not a third party BMS slave controller` };
        }
        const { controllerId: parentControllerId } = controllerConfig;
        const JouleBoxControllerDetail = await Devices.findOne({ siteId: controllerConfig.siteId, deviceId: parentControllerId, deviceType: "joulebox" });
        if (!JouleBoxControllerDetail) {
            sails.log.warn("parent controller does not exist");
            return { success: false, message: `Parent controller does not exist for deviceId: ${parentControllerId}, siteId: ${controllerConfig.siteId}` };
        }
        if (JouleBoxControllerDetail.deviceType !== "joulebox") {
            sails.log.warn(`Parent controller is not a joulebox controller, deviceId: ${parentControllerId}, siteId: ${controllerConfig.siteId}`);
            return { success: false, message: `Parent controller is not a joulebox controller, deviceId: ${parentControllerId}, siteId: ${controllerConfig.siteId}` };
        }
        if (!JouleBoxControllerDetail.hasOwnProperty("isPrimaryJouleBox")) {
            sails.log.warn(`This JouleBox controller doesn't not have isPrimaryJouleBox tagging , deviceId: ${parentControllerId}, siteId: ${controllerConfig.siteId}`);
            return { success: false, message: `This JouleBox controller doesn't not have isPrimaryJouleBox tagging, deviceId: ${parentControllerId}, siteId: ${controllerConfig.siteId}` };
        }
        if (JouleBoxControllerDetail.isPrimaryJouleBox != 1) {
            sails.log.warn(`This JouleBox controller is not a primary controller, deviceId: ${parentControllerId}, siteId: ${controllerConfig.siteId}`);
            return { success: false, message: `This JouleBox controller is not a primary controller, deviceId: ${parentControllerId}, siteId: ${controllerConfig.siteId}` };
        }
        const fetchExistingSlaveControllers = await Devices.find({
            siteId: controllerConfig.siteId,
            deviceType: controllerConfig.deviceType,
            vendorId: controllerConfig.vendorId,
        });
        if (fetchExistingSlaveControllers.length > 0) {
            sails.log.warn(`Slave controller already exists for deviceId: ${parentControllerId}, siteId: ${controllerConfig.siteId}`);
            return { success: false, message: `Slave controller already exists for deviceId: ${parentControllerId}, siteId: ${controllerConfig.siteId}` };
        }
        return { success: true, message: `Device is eligible for BMSX slave attachment for deviceId: ${parentControllerId}, siteId: ${controllerConfig.siteId}` };
    },
    linkBMSXSlaveControllerWithBMSXDatabase: async function (slaveControllerDetail) {
        const { siteId: site_id, communicationCategory: protocol, 
deviceType: bms_connector_class, status, deviceId: slave_controller_id } = slaveControllerDetail;
        const pgDbCon = new pgConnection();
        try {
            await pgDbCon.query("INSERT INTO third_party_bms_connector (site_id,protocol,bms_connector_class,status,slave_controller_id,version) VALUES ($1, $2, $3, $4, $5, $6)", [site_id, protocol, bms_connector_class, 1, slave_controller_id,"v1"]);
            sails.log.info(`BMSX slave controller linked with BMSX database for deviceId: ${slave_controller_id}, siteId: ${site_id}`);
            return { success: true, message: `BMSX slave controller linked with BMSX database for deviceId: ${slave_controller_id}, siteId: ${site_id}` };
        } catch (error) {
            sails.log.error(`Error linking BMSX slave controller with BMSX database for deviceId: ${slave_controller_id}, siteId: ${site_id}`, error);
            return { success: false, message: `Error linking BMSX slave controller with BMSX database for deviceId: ${slave_controller_id}, siteId: ${site_id}`, error };
        }
    },
	isBMSXSlaveController: function(slaveControllerDetail) {
		const BMSXVendorIdControllerDeviceTypeRelationship = {"n3uron":"n3uronbacnetmqtt"}; //vendorId mapped with deviceType of slave controller
		if(BMSXVendorIdControllerDeviceTypeRelationship.hasOwnProperty(slaveControllerDetail?.vendorId) && slaveControllerDetail?.deviceType === BMSXVendorIdControllerDeviceTypeRelationship[slaveControllerDetail?.vendorId]) {
			return true;
		}
		return false;
	}
};
