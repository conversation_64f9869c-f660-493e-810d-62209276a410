const moment = require("moment-timezone");
moment.tz.add("Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6");
moment.tz.setDefault("Asia/Kolkata");


module.exports = {


	"getLastOfAll2": function(promise_arr, compId, type){
		let retObj = {};
		retObj[type]= undefined;
		return new Promise((res, rej)=>{
			Promise.all(promise_arr).then(a=>{
				let ts = 0, maxi = 0, tempTs = 0;
				for (let i=0;i<a.length;i++){
					let tempArr = a[i];
					sails.log(tempArr);
					for (let j=0;j<tempArr.length;j++){
						if (!tempArr[j] || !tempArr[j].timestamp || tempArr[j].componentId !=compId){
							continue;
						}
						if (tempArr[j].type==type){
							tempTs = ((tempArr[j].timestamp.split("_")));
							tempTs = tempTs.length==2?parseInt(tempTs[1]):parseInt(tempTs[0]);
							if (tempTs>ts){
								ts= tempTs;
								maxi = tempArr[j];
							}
							break;
						}
					}
				}
				res(maxi);
			}).catch(e=>{
				sails.log(e);
				rej(e);
			});
		});
	},

	"getLastOfAll": function(promise_arr, compId){
		return new Promise((res, rej)=>{
			Promise.all(promise_arr).then(a=>{
				let ts = 0, maxi = 0;
				sails.log(a);
				for (let i=0;i<a.length;i++){
					if (!a[i] || !a[i].timestamp || a[i].componentId !=compId){
						continue;
					}
					let tempTs = ((a[i].timestamp.split("_")));
					tempTs = tempTs.length==2?parseInt(tempTs[1]):parseInt(tempTs[0]);
					if (isNaN(tempTs)){
						continue;
					}
					if (tempTs>ts){
						ts=tempTs;
						maxi = a[i];
					}
				}
				res(maxi);
			}).catch(e=>{
				sails.log(e);
				rej(e);
			});
		});
	},
	"pidSaveJB": function(sid, fb){
		broadcastService.cast(sid, "okRequest", "PID Successfull Reached JOULE BOX");
	}
};
