module.exports={
	//  setTimer(Recipe,{id:10001010},sails.socket.broadcast(),'ReachedJb','1')
	"setTimer": function(tableName, srcParam, param, value, action, duration=1000*60, endAction=""){
		let functionDetail=`function ${tableName}_${param}(){
            console.log(${tableName}_${param}_interval_param);
            ${tableName}_${param}_interval_param++;
            if(${tableName}_${param}_interval_param>30){
                clearInterval(${tableName}_${param}_interval);    
                return;
            }
            ${tableName}.findOne(${srcParam}).then(data=>{
                if(data){
                    data=typeof(data)=='object'?data:JSON.parse(data);
                    if(data.${param} && data.${param}==${value}){
                        console.log('${tableName}_${param}_interval done');
                        ${endAction}
                        clearInterval(${tableName}_${param}_interval);    
                        return;
                    }else{
                        ${action}
                    }
                }else{
                    console.log('${tableName}_${param}_interval done');
                    
                    ${endAction}
                    clearInterval(${tableName}_${param}_interval);    
                    return;
                }
                


            }).catch(e=>{
                console.log(e);
                clearInterval(${tableName}_${param}_interval);
            })
        }`;

		eval(`var ${tableName}_${param}_interval_param=0`);
		eval(functionDetail);
		let intervalIt=`var ${tableName}_${param}_interval=setInterval(${tableName}_${param},${duration})`;
		eval(intervalIt);


	}

};

// function sendSocket(siteId, data){
// 	sails.sockets.broadcast(siteId, "sendFeedBack", {
// 		"msg": msg,
// 		"data": data
// 	});
// }