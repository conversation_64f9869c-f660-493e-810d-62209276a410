const AWS = require("aws-sdk");
AWS.config.update({ region: process.env?.REGION, logger: console });
const SQS = new AWS.SQS({
  "apiVersion": "2012-11-05",
});
const uuid = require("uuid/v4");
const QTemplateURL = "https://sqs.us-west-2.amazonaws.com/************/recentDataQ_";
module.exports = {
  /**
   * @param {object} data this can be a array or a single object which is to be added to queue and the object must contain siteId
   *
   * @description Add the data packets of the devices in the queue, converts them into batches of messages and then perform batch write in SQS. Function serves the utility of providing device data to the queue which is not sent over mqtt. Currently used for Proto-Convert Controllers
   * Future use can be in the controllers who for some reason are sending data to NodeApp instead of AWS IoT.
   *
   * @returns true if sucessful, false if any error happens
   */
  "addToQueue": async (data) => {
    let messages = [];
    //filtering starts
    if (!data)
      return false;
    else if (Array.isArray(data)) {
      messages = data;
    } else if (typeof data == "object") {
      messages.push(data);
    }
    let siteId = messages[0].siteId;
    if (typeof siteId === "undefined")
      return false;
    //filtering ends

    let QueueUrl = QTemplateURL + siteId;
    let accumulator = [];
    let messagePromise = [];

    for (let index = 0; index < messages.length; index++) {
      //create batch of 10 messages to write to SQS
      let message = messages[index];
      let Id = uuid();
      let MessageBody = helper.toString(message);
      if (MessageBody != undefined)
        accumulator.push({ Id, MessageBody });
      else
        continue;
      if (accumulator.length === 10 || index == messages.length - 1) {
        let Entries = accumulator.map(e => e);
        messagePromise.push(
          SQS.sendMessageBatch({
            QueueUrl,
            Entries,
          }).promise(),
        );
        accumulator = [];
      }
    }
    try {
      await Promise.all(messagePromise);
      return true;
    } catch (e) {
      sails.log.error(e);
      return false;
    }
  },

};
