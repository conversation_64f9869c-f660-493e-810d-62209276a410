module.exports={
	"getParameters": async (deviceType, driverType)=>{
		try {
			const devType = await DeviceType.findOne({deviceType, driverType});
			if (devType){
				let {parameters} = devType;
				const paramObjArr = parameters.map(helper.toJson)
					.filter(Boolean);
				delete devType.params;
				delete devType.parameters;
				devType.parameters = paramObjArr;
				return devType;
			}
			return null;
		} catch (e){
			sails.log.error(e);
			throw Error({"err": "Unable to get devicetype"});
		}
	}
};
