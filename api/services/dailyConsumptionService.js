const moment = require("moment-timezone");
const DEFAULT_CONSUMPTION_UNIT = "kvah";

module.exports = {
	/**
	 * @function getWeekConsumption
	 * @param {string} siteId
	 * @description calculates whole week consumption and and per day consumption for the current week based on the daily consumption values stored in dailyconumption table
	 * @returns {Promise}
	 */
	"getWeekConsumption": siteId => {
		let searchObj = {
			"where": {
				"siteId": siteId,
				"timestamp": {
					"gte": moment()
						.subtract(7, "days")
						.format("YYYY-MM-DD"),
				},
			},
			"limit": 7,
			"sort": "-1",
		};
		return new Promise((resolve, reject) => {
			DailyConsumption.find(searchObj)
				.then(data => {
					if (!data || data.length == 0) {
						sails.log.info("Data not Present for site");
						resolve({ "weekCons": "NA", "weekConsPattern": [] });
					}
					let weekConsPattern = [];
					let totalWeeklyConsumption = 0;
					for (let i = 0; i < data.length; i++) {
						totalWeeklyConsumption += data[i].actual;
						let day = moment(data[i].timestamp).unix() * 1000;
						let cons = data[i].actual;
						let tmp = [day, cons];
						weekConsPattern.push(tmp);
					}
					return resolve({
						"weekCons": totalWeeklyConsumption,
						"weekConsPattern": weekConsPattern,
					});
				})
				.catch(err => {
					sails.log.error(err);
					resolve({ "weekCons": "NA", "weekConsPattern": [] });
				});

			// DailyConsumption.find(searchObj).exec(function (err, data) {
			// 	if (err) {
			// 		sails.log.error(err);
			// 		resolve({ "weekCons": "NA", "weekConsPattern": [] });
			// 	}
			// 	if (!data || data.length == 0) {
			// 		sails.log.info("Data not Present for site");
			// 		resolve({ "weekCons": "NA", "weekConsPattern": [] });
			// 	}
			// 	let weekConsPattern = [];
			// 	let totalWeeklyConsumption = 0;
			// 	for (let i = 0; i < data.length; i++) {
			// 		totalWeeklyConsumption += data[i].actual;
			// 		let day = moment(data[i].timestamp).unix() * 1000;
			// 		let cons = data[i].actual;
			// 		let tmp = [day, cons];
			// 		weekConsPattern.push(tmp);
			// 	}
			// 	return resolve({ "weekCons": totalWeeklyConsumption, "weekConsPattern": weekConsPattern });
			// });
		});
	},
	/**
	 * @function getWeekConsumption
	 * @param {string} siteId
	 * @description calculates whole week consumption and and per day consumption for the current week based on the daily consumption values stored in dailyconumption table
	 * @returns {Promise}
	 */
	"getWeekConsumption_new": async (siteId, consumptionUnitPreference) => {
		let searchObj = {
			"where": {
				"siteId": siteId,
				"timestamp": {
					"gte": moment()
						.subtract(7, "days")
						.format("YYYY-MM-DD"),
				},
			},
			"limit": 7,
			"sort": "-1",
		};

		return new Promise((resolve, reject) => {
			DailyConsumption.find(searchObj)
				.then(data => {
					if (!data || data.length == 0) {
						sails.log.info("Data not Present for site");
						resolve({ "weekCons": "NA", "weekConsPattern": [] });
					}
					let weekConsPattern = [];
					let totalWeeklyConsumption = 0;
					for (let i = 0; i < data.length; i++) {
						let value = dailyConsumptionService.getConsumptionAccordingToUnit(data[i], consumptionUnitPreference);

						totalWeeklyConsumption += value;
						let day = moment(data[i].timestamp).unix() * 1000;
						let cons = value;
						let tmp = [day, cons];
						weekConsPattern.push(tmp);
					}
					return resolve({
						"weekCons": totalWeeklyConsumption,
						"weekConsPattern": weekConsPattern,
					});
				})
				.catch(err => {
					sails.log.error(err);
					resolve({ "weekCons": "NA", "weekConsPattern": [] });
				});

			// DailyConsumption.find(searchObj).exec(function (err, data) {
			// 	if (err) {
			// 		sails.log.error(err);
			// 		resolve({ "weekCons": "NA", "weekConsPattern": [] });
			// 	}
			// 	if (!data || data.length == 0) {
			// 		sails.log.info("Data not Present for site");
			// 		resolve({ "weekCons": "NA", "weekConsPattern": [] });
			// 	}
			// 	let weekConsPattern = [];
			// 	let totalWeeklyConsumption = 0;
			// 	for (let i = 0; i < data.length; i++) {
			// 		totalWeeklyConsumption += data[i].actual;
			// 		let day = moment(data[i].timestamp).unix() * 1000;
			// 		let cons = data[i].actual;
			// 		let tmp = [day, cons];
			// 		weekConsPattern.push(tmp);
			// 	}
			// 	return resolve({ "weekCons": totalWeeklyConsumption, "weekConsPattern": weekConsPattern });
			// });
		});
	},

	/**
	 * @function getCustomConsumption
	 * @description function for getting custom daily consumption.
	 * @param start - start time for the daily consumption.
	 * @param end - end time for the daily consumption.
	 * @param siteId - siteId for a partiular site.
	 */
	"getCustomConsumption": (siteId, start, end) => {
		let consumptionObj = {
			"where": {
				"siteId": siteId,
				"timestamp": {
					"between": [start, end],
				},
				"sort": "1",
			},
		};

		return DailyConsumption.find(consumptionObj);
	},

	"getConsumptionAccordingToUnit": (consumption, unitPreference) => {
		if (unitPreference === "kvah") {
			return parseInt(consumption["actual"]) || 0;
		} else if (unitPreference === "kwh") {
			// we store in kvah and user wants in kwh, we now store in kwh also.
			return parseInt(consumption["actualkwh"]) || 0;
		} else {
			return undefined;
		}

	},
	/**
	 * Get prefernce of consumption in given site Id. If userPreference is given
	 * it will respond with that unit, else defaults to kvah
	 */
	"getUserConsumptionPreference": async (userUnitPrefernce, siteId) => {

		let consUnit = DEFAULT_CONSUMPTION_UNIT;

		if (userUnitPrefernce && userUnitPrefernce.cons) {
			consUnit = userUnitPrefernce.cons;
			return consUnit;
		}
		return consUnit;
	},
	"getSiteBaselineConsumptionUnit": async (siteId) => {
		let consUnit = DEFAULT_CONSUMPTION_UNIT, key;

		key = `${siteId}_basline_consumption_unit`;
		try {
			let consumptionUnit = await DyanmoKeyStore.findOne({ key });
			if (consumptionUnit && consumptionUnit.value) {
				consUnit = consumptionUnit.value;
			}
		} catch (e) {
			sails.log.error(e);
		}
		return consUnit;
	},
	"getSiteBaselineUnit": async (siteId) => {
		try {
			let site = await Sites.findOne({siteId});
			return site.consumptionUnit || DEFAULT_CONSUMPTION_UNIT;
		} catch (e) {
			sails.log.error(e);
		}
		return DEFAULT_CONSUMPTION_UNIT;
	}
};
