const Axios = require('axios');
const Joi = require('joi');
const CSVToJSON = require('csvtojson');
const qs = require('qs');
module.exports =(()=> {
    const config = {
        host: process.env.INFLUX_ENT_URL || 'http://timeseries.smartjoules.org',
        username: process.env.INFLUX_ENT_USERNAME,
        password: process.env.INFLUX_ENT_PASSWORD,
        token: process.env.INFLUX_ENT_TOKEN
    };
    const configSchema = Joi.object()
    .keys({
        host: Joi.string().required(),
        username: Joi.string().required(),
        password: Joi.string().required(),
    })
    .unknown(true);

    const { error } = configSchema.validate(config);
    if (error && process.env.NODE_ENV !== "development") throw new Error(`INFLUX CONFIG FOR ENTERPRISE IS NOT VALID - ${error}`);

    /**
     * @description Get data from influxdb enterprise using a flux query.
     * @param {string} query - The flux query to run for enterprise version.
     * @returns {Object} - The csv data returned from influxdb enterprise filtered to the json format.
    */
    async function runQuery(query,opts = {}) {
        try {

          let _parsedQuery = query;
          if (opts && opts.replacements) {
            let _replacements = opts.replacements;
            for (let key in _replacements) {
              let regEx = `{{${key}}}`
              let re = new RegExp(regEx, "g");
              _parsedQuery = _parsedQuery.replace(re, _replacements[key])
            }
          }
          if (opts && opts.debug) {
            sails.log(_parsedQuery)
          }

        const requestObject = {
          url: `${config.host}/api/v2/query?pretty=true`,
          headers: {
            'Accept': 'application/csv',
            'Content-type': 'application/vnd.flux',
            'Authorization': `Token ${config.username}:${config.password}`
          },
          data : _parsedQuery
        };

        const response = await Axios.post(requestObject.url, requestObject.data, {headers: requestObject.headers});
        const jsonObj = (await CSVToJSON({
         flatKeys: true
        }).fromString(response.data)).map(it => {
          Object.entries(it).forEach(([key, value]) => {
            /**  To remove unnecessary empty string columns added by InfluxDB for a timestamp
             *  when one entry contains a parameter,
             *  but another entry does not.
            */
            if(value == '' || value ==  null || value == undefined) {
              delete it[key]
            }
          })
          return it;
        })

        return jsonObj;
      } catch (error) {
        sails.log.error(error);
           sails.log.error('[Service > influx] Error: ');
           throw new Error(error);
        }
    }

    async function queryUsingInfluxQl(query, databaseName) {
      try {
        const influxQlSchema = Joi.object()
          .keys({
            host: Joi.string().required(),
            database: Joi.string().required(),
            token: Joi.string().required(),
          })
          .unknown(true)
        const influxServerDetail = { ...config, database: databaseName }
        const { error } = influxQlSchema.validate(influxServerDetail)
        if (error) throw new Error(`INVALID INFLUX QL REQUEST -${error}`)
        const { host, database, token } = influxServerDetail
        let data = qs.stringify({
          'q': query
        });
        const requestObject = {
          method: 'get',
          url: `${host}/query?db=${database}&${data}`,
          headers: {
            'Authorization': `Token ${token}`,
            'Content-Type': 'application/json'
          },
        };

        const response = await Axios.request(requestObject);

        let resultObject = []
        if (response.data && response.data.results && response.data.results[0].series && response.data.results[0].series[0]) {
          const seriesData = response.data.results[0].series[0]
          const columns = seriesData.columns
          for (const values of seriesData.values) {
            resultObject.push(_.zipObject(columns, values));
          }
        }
        return resultObject
      } catch (error) {
        sails.log.error(error);
        sails.log.error('[Service > influx OSS] Error: ');
        throw new Error(error);
      }
    }
    return {
        runQuery,
        queryUsingInfluxQl
    }
})()
