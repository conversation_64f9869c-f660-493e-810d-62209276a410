/* eslint-disable no-useless-escape */
const globalUtils = require('../utils/global');
const cacheService = require('./cacheService');

module.exports = {

	"returnSiteId": (siteName, location) => {
		siteName = siteName.replace(/[$-/:-?{-~!"^_`\[\]]/g, "").split(" ");
		location = location.replace(/[$-/:-?{-~!"^_`\[\]]/g, "")
		let nameArr = [];
		siteName.forEach(name => {
			if (name.length >= 1) {
				nameArr.push(name);
			}
		})
		let id1, id2, loc1, loc2;
		switch (nameArr.length) {
			case 1:
				id1 = nameArr[0].substring(0, 2).toLowerCase();
				id2 = nameArr[0].substring(0, 3).toLowerCase();
				break;
			case 2:
				id1 = nameArr[0].substring(0, 2).toLowerCase() + nameArr[1].substring(0, 1).toLowerCase();
				id2 = nameArr[0].substring(0, 1).toLowerCase() + nameArr[1].substring(0, 2).toLowerCase();
				break;
			case 3:
				id1 = nameArr[0].substring(0, 1).toLowerCase() + nameArr[1].substring(0, 1).toLowerCase() + nameArr[2].substring(0, 1).toLowerCase();
				id2 = nameArr[0].substring(0, 1).toLowerCase() + nameArr[1].substring(0, 2).toLowerCase();
				break;
			default: {
				let shortName = "";
				nameArr.map(e => {
					shortName += e && e[0];
				});
				id1 = shortName.toLowerCase();
				id2 = nameArr[0].substring(0, 2).toLowerCase() + nameArr[1].substring(0, 1).toLowerCase();
				break;
			}
		}
		loc1 = location.substring(0, 3).toLowerCase();
		loc2 = location.substring(0, 2).toLowerCase();
		let optionsArr = [id1 + "-" + loc1, id1 + "-" + loc2, id2 + "-" + loc1, id2 + "-" + loc2];
		let probableSiteIds = [...optionsArr];
		return new Promise(async (resolve, reject) => {

			for (let i = 0; i < 20; i++) {
				probableSiteIds.push(id1 + "-" + jwtService.generateRandomString(4));
			}
			let siteId = null;
			for (let i = 0; i < probableSiteIds.length; i++) {
				try {
					let site = await Sites.findOne({ "siteId": probableSiteIds[i] });
					if (site === undefined) {
						siteId = probableSiteIds[i];
						break;
					}
				} catch (e) {
					throw new Error(e);

				}
			}
			if (siteId) {
				resolve(siteId);
			} else {
				reject("Unable to create siteId at moment. Please try later");
			}
		});
	},
	"getSiteInfo": (siteId) => {
		return new Promise((resolve, reject) => {
			Sites.findOne({ "siteId": siteId }).then(data => {
				if (!data)
					return reject(`SiteId ${siteId} not present`);
				resolve(data);
			}).catch(e => {
				return reject(e);
			});
		});

	},
	"updateControllerInRegions": async (oldDeviceInfo, updateParams) => {
		let { req, areaId, regionId } = updateParams;
		let { deviceId } = oldDeviceInfo;
		if (areaId && !regionId) {
			return { "err": "No regionId for updated Region" };
		}
		try {
			if (regionId) {
				let site = await Sites.findOne({ "siteId": oldDeviceInfo.siteId });
				let { regions } = site;
				//remove controller from old region
				let controllers = new Set(regions[oldDeviceInfo.regionId]["controller"]);
				controllers.delete(deviceId);
				regions[oldDeviceInfo.regionId]["controller"] = [...controllers];
				//add to new region
				if (!regions[regionId])
					return { "err": "RegionId does not exist" };
				regions[regionId]["controller"].push(deviceId);
				let newSiteInfo = await Sites.update({ "siteId": site.siteId }, { regions });
				eventService.emmitter.emit("commitChange", req, "sites", site, newSiteInfo);

				return { "event": "update", "data": newSiteInfo };
			}
		} catch (e) {
			sails.log.error(e);
			return Promise.reject({ "err": "Unable to update siteInfo" });
		}

	},
	"removeControllerFromRegion": async (controller) => {
		let { regionId, siteId, deviceId, req } = controller;
		try {
			let site = await Sites.findOne({ siteId });
			if (!site)
				return Promise.reject({
					"err":
						"Site does not exist"
				});
			let { regions } = site;
			let controllers = new Set(regions[regionId]["controller"]);
			controllers.delete(deviceId);
			regions[regionId]["controller"] = [...controllers];
			let newSiteInfo = await Sites.update({ siteId }, { regions });
			eventService.emmitter.emit("commitChange", req, "sites", site, newSiteInfo);
			return { "event": "update", "data": newSiteInfo };
		} catch (e) {
			sails.log.error(e);
			return Promise.reject({ "err": "Unable to update siteInfo" });
		}
	},

	/**
	 * @function giveAccessToDevelopers
	 * @param {string} siteId The new siteId being generated.
	 * @summary A select list of developers / users have been stored in the Dyanmokeystore. It gives admin access to all those users to the new site after fetching the default admin preferences from the Roles table.
	 * @returns {object} A Promise which resolves to : {"status": boolean, "message": "Error message if any."}
	 */
	"giveAccessToDevelopers": async (siteId) => {

		let defaultPreferences, $defaultPreferences, developerList, $developerList, $createUserAccessList;

		try {
			// Fetching default preferences for admin role.
			$defaultPreferences = fetchDefaultPreferences();
			// Fetching list of configured developers in DyanmoKeyStore who should automatically get access.
			$developerList = fetchDeveloperList();
			defaultPreferences = await $defaultPreferences;
			developerList = await $developerList;
			$createUserAccessList = developerList.map(fetchUserDetails).map(giveAccessToUser);
			await Promise.all($createUserAccessList);
			return {
				"status": true
			};
		} catch (error) {
			return {
				"status": false,
				"message": error
			};
		}

		// Helper functions
		/**
		 * @function fetchDefaultPreferences
		 * @summary Fetches default preferences stored in the "defpref" key of the object with "roleName":"admin" in the "Role" Table.
		 */
		async function fetchDefaultPreferences(){
			try {
				let adminRoleResult = await Role.find({
					"roleName": "admin"
				});
				if (adminRoleResult.length == 0){
					const errMessage = "'admin' role not found in 'Roles' Table. Could not allocate permission to admins.";
					sails.log.error(errMessage);
					throw new Error(errMessage);
				} else {
					
					// Storing default preferences if role found.
					let parsedDefaultPreferences = helper.toJson(adminRoleResult[0].defpref);
					if (parsedDefaultPreferences == undefined){
						const errMessage = "Could not parse 'defpref' fetched from 'admin' role";
						sails.log.error(errMessage);
						throw new Error(errMessage);
					} else {
						for (let key in parsedDefaultPreferences){
							parsedDefaultPreferences[key] = JSON.stringify(parsedDefaultPreferences[key]);
						}
						return parsedDefaultPreferences;
					}
				}
			} catch (error) {
				sails.log.error("[giveAccessToDevelopers] Error getting default preferences from 'role' table.");
				throw new Error(error);
			}
		}

		/**
		 * @function fetchDeveloperList
		 * @summary Fetches configured developerList in DyanmoKeyStore Table to who the admin access needs to be given.
		 */
		async function fetchDeveloperList(){
			try {
				let developerListResult = await DyanmoKeyStore.find({
					"key": "developerList"
				});
				if (developerListResult.length === 0){
					const errMessage = "'developerList' not found in DyanmoKeyStore";
					sails.log.error(errMessage);
					throw new Error(errMessage);
				} else 
					return developerListResult[0].list;
			} catch (error) {
				sails.log.error("[giveAccessToDevelopers] Error getting 'developerList' from DyanmoKeyStore table.");
				throw new Error(error);
			}
		}

		/**
		 * @function fetchUserDetails
		 * @summary Fetches user details, ie, is "phone" required for the userSiteMap entry.
		 * @returns {Promise} Return a promise which resolves to the user record in the Users table.
		 */
		async function fetchUserDetails(user){
			try {
				let userResult = await Users.find({
					"userId": user
				});
				if (userResult.length === 0){
					const errMessage = `${user} not found in users tables. Not allocating access to new site.`;
					sails.log.error(errMessage);
					throw new Error(errMessage);
				} else {
					return userResult[0];
				}
			} catch (error) {
				sails.log.error("[giveAccessToDevelopers] Error getting user details.");
				throw new Error(error);
			}
		}

		/**
		 * @function giveAccessToUser
		 * @summary Creates a userSiteMap object required to give one user admin access to a specified site. Uses user details fetched in the previous promise.
		 * @param {promise} $user User search promise
		 * @returns {Promise} Which resolved when the permission record has been succesfully created in userSiteMap table.
		 */
		async function giveAccessToUser($user){
			try {
				let user = await $user;
				let newUserSiteMapsObject = {
					"phone": user.phone,
					"role": "admin",
					"userId": user.userId,
					siteId
				};
				newUserSiteMapsObject = {...defaultPreferences, ...newUserSiteMapsObject};
				return UserSiteMap.create(newUserSiteMapsObject);
			} catch (error) {
				sails.log.error("[giveAccessToDevelopers] Error creating access entry in userSiteMap.");
				throw new Error(error);
			}
		}
	},
	"removeSiteCategoriesFromCache" : (siteId) => {
		try {
			const cacheKey = globalUtils.getSiteDeviceComponentCategoryCacheKey(siteId);
			return cacheService.del(cacheKey)
		} catch(e) {
			sails.log.error("[removeSiteCategoriesFromCache] Error while deleting categories", e);
			return null;
		}
	}
};