const AWS = require("aws-sdk");
const moment = require("moment-timezone");
moment.tz.setDefault("Asia/Kolkata");
AWS.config.update({ region: process.env?.REGION, logger: console });
const athena = new AWS.Athena();
const glue = new AWS.Glue();
const s3 = new AWS.S3();

module.exports = {
  "athena": athena,
  "glue": glue,
  "s3": s3,

  "startQuery": function(query, databaseName = undefined) {
    let params = {
      "QueryString": query,
      "ResultConfiguration": {
        "OutputLocation": "s3://smartjoules-athena/",
      },
      "QueryExecutionContext": {
        "Database": databaseName,
      },
    };
    return new Promise((resolve, reject) => {
      athena.startQueryExecution(params, function(err, data) {
        if (err) {
          sails.log.error("[athenaService.startQuery]", err);
          return reject(err);
        } else {
          // sails.log(data);
          return resolve(data);
        }
      });
    });
  },

  "getQueryExecution": function(QueryExecutionId) {
    let params = {
      // QueryExecutionId: 'c559d935-7060-46b5-a09d-3955fdb23fa3'
      "QueryExecutionId": QueryExecutionId,
    };
    return new Promise((resolve, reject) => {
      athena.getQueryExecution(params, (err, data) => {
        if (err) {
          sails.log.error("[athenaService.getQueryExecution]", err);
          return reject(err);
        } else {
          return resolve(data);
        }
      });
    });
  },

  "stopQueryExecution": function(QueryExecutionId) {
    let params = {
      "QueryExecutionId": QueryExecutionId,
    };
    return new Promise((resolve, reject) => {
      athena.stopQueryExecution(params, (err, data) => {
        if (err) {
          return reject(err);
        } else {
          return resolve(data);
        }
      });
    });
  },

  "listQueryExecutions": function() {
    let params = {};
    return new Promise((resolve, reject) => {
      athena.listQueryExecutions(params, (err, data) => {
        if (err) {
          return reject(err);
        } else {
          return resolve(data);
        }
      });
    });
  },

  "stopAllRunningQueries": async function() {
    try {
      let data = await athenaService.listQueryExecutions();
      // sails.log('All running querries: ', data);
      for (let query in data.QueryExecutionIds) {
        let queryId = data.QueryExecutionIds[query];
        await this.stopQueryExecution(queryId);
        // sails.log('queryId stopped!: ', queryId);
      }
      // sails.log('All queries have been stopped.');
    } catch (error) {
      sails.log.error("[athenaService.stopAllRunningQueries]: ", error);
      throw error;
    }
  },

  // A promise that resolves when a query specified using a queryId is completed.
  "waitForQueryToComplete": async function(QueryExecutionId, ms = 1000, timeout = 3600) {
    let isQueryStillRunning = true;
    let timeoutTime = new Date();
    timeoutTime.setSeconds(timeoutTime.getSeconds() + timeout);
    while (isQueryStillRunning && new Date() < timeoutTime) {
      let response = await this.getQueryExecution(QueryExecutionId);
      // sails.log('Response after calling getQueryExecution:', QueryExecutionId);
      if (response.QueryExecution.Status.State == "SUCCEEDED") {
        return true;
      } else if (response.QueryExecution.Status.State == "RUNNING") {
        await athenaService.sleep(ms);
      } else if (response.QueryExecution.Status.State == "FAILED") {
        /* TODO: Have not tested incase the Query itself fails on athena during execution. */
        throw response;
      }
    }
    try {
      // Stopping query since specified timeout is over:
      await this.stopQueryExecution(QueryExecutionId);
    } catch (error) {
      throw Error({
        "queryId": QueryExecutionId,
        "err": "Query timed out and could not stop query!",
      });
    }
    // sails.log('queryId stopped!: ', QueryExecutionId);
    throw Error({
      "queryId": QueryExecutionId,
      "err": "Query timed out!",
    });
  },

  // Creating multiple partitions.
  "batchCreatePartition": function(params) {
    return new Promise((resolve, reject) => {
      params = {
        "DatabaseName": "athenatest",
        "TableName": "datadevicestemp2",
        "PartitionInputList": [
          {
            "StorageDescriptor": {
              "InputFormat": "org.apache.hadoop.mapred.TextInputFormat",
              "OutputFormat": "org.apache.hadoop.hive.ql.io.HiveIgnoreKeyTextOutputFormat",
              "Location": "s3://datadevicestemp2/ssh/1058/2018-10-17/",
              "SerdeInfo": {
                "Parameters": {
                  "paths": "a_amp,a_vll,a_vln,deviceid,ib,ir,iy,kva,kvah,kvar,kw,pf,siteid,timestamp,xyz",
                  // paths:""
                },
                "SerializationLibrary": "org.openx.data.jsonserde.JsonSerDe",
              },
            },
            "Values": ["ssh", "1058", "2018-10-17"],
          },
        ],
      };
      glue.batchCreatePartition(params, (err, data) => {
        if (err) {
          sails.log.error("[athenaService.batchCreatePartition]:", err);
          throw err;
        } else {
          // sails.log('[athenaService.batchCreatePartition]data: ', data);
          return resolve(data);
        }
      });
    });
  },

  // Deleting a partition.
  "deletePartition": function(dbname, tablename, values) {
    let params = {
      "DatabaseName": dbname, /* required */
      "PartitionValues": values,
      "TableName": tablename,
    };
    return new Promise((resolve, reject) => {
      glue.deletePartition(params, function(err, data) {
        if (err) {
          sails.log.error("[athenaService.deletePartition]", err, err.stack); // an error occurred
          return reject(err);
        } else {
          // sails.log('[athenaService.deletePartition]');
          // sails.log(data);           // successful response
          return resolve(data);
        }
      });
    });
  },

  "getQueryResultsParts": function(queryId, NextToken = undefined) {
    let params = {
      "QueryExecutionId": queryId,
      "NextToken": NextToken,
    };
    return new Promise((resolve, reject) => {
      athena.getQueryResults(params, (err, data) => {
        if (err) {
          sails.log.error("[athenaService.getQueryResultsParts]: ", err);
          return reject(err);
        } else {
          if (data.NextToken != undefined) {
            // sails.log('Fetched query result part: ', data);
            return resolve({ "data": data, "completed": false, "NextToken": data.NextToken });
          } else {
            // sails.log('Fetched last query result part: ', data);
            return resolve({ "data": data, "completed": true });
          }
          // return resolve(data);
        }
      });
    });
  },

  // Fetching query results of a completed query using its queryid.
  "getQueryResults": async function(queryId, NextToken = undefined) {
    let res = { "completed": false }, allData = { "Rows": [] };
    while (!res.completed) {
      try {
        res = await this.getQueryResultsParts(queryId, NextToken);
      } catch (error) {
        sails.log.error("[athenaService.getQueryResults]", error);
        return;
      }
      allData.Rows = [...allData.Rows, ...res.data.ResultSet.Rows];
      NextToken = res.NextToken;
    }
    allData["ResultSetMetadata"] = res.data.ResultSet.ResultSetMetadata;
    // sails.log('All results:', allData);
    allData = athenaService.csvToJson(allData);
    return allData;
  },

  // Get meta data of a table
  "getTable": async function(databaseName, tableName) {
    let params = {
      "DatabaseName": databaseName, /* required */
      "Name": tableName, /* required */
      // CatalogId: 'STRING_VALUE'
    };
    return new Promise((resolve, reject) => {
      glue.getTable(params, (err, data) => {
        if (err) {
          throw err;
        } else {
          return resolve(data);
        }
      });
    });
  },

  // Get all partitions info of a table
  "getPartitions": async function(databaseName, tableName) {
    let params = {
      "DatabaseName": databaseName, /* required */
      "TableName": tableName, /* required */
      // CatalogId: 'STRING_VALUE'
    };
    return new Promise((resolve, reject) => {
      glue.getPartitions(params, (err, data) => {
        if (err) {
          throw err;
        } else {
          return resolve(data);
        }
      });
    });
  },

  // Hard coded function to put an object in an s3 bucket.
  "putOject": function(obj = undefined) {
    obj = { "timestamp": "2018-10-15 00:00:00", "kw": 0, "ib": 0, "pf": 1, "ir": 0, "kvar": 0, "a_vll": 449.03, "kvah": 4832.34, "kva": 0, "a_vln": 259.25, "iy": 0, "a_amp": 0, "deviceid": "1058", "siteid": "ssh", "xyz": "123maybe!" };
    obj = JSON.stringify(obj);
    let params = {
      "Body": obj,
      "Bucket": "datadevicestemp2",
      "Key": "ssh/1058/2018-10-15/00:00:00",
    };
    return new Promise((resolve, reject) => {
      s3.putObject(params, function(err, data) {
        if (err) sails.log(err, err.stack); // an error occurred
        else {
          sails.log("[athenaService.putObject]data: ", data);
        }
        /*
                data = {
                ETag: "\"6805f2cfc46c0f04559748bb039d69ae\"",
                VersionId: "Bvq0EDKxOcXLJXNo_Lkz37eM3R4pfzyQ"
                }
                */
      });
    });

  },

  // Get info about a single partition.
  "getPartition": function(databaseName, tableName, values) {
    let params = {
      "DatabaseName": databaseName, /* required */
      "PartitionValues": values,
      "TableName": tableName,
    };
    return new Promise((resolve, reject) => {
      glue.getPartition(params, (err, data) => {
        if (err) {
          sails.log.error("[athenaService.getPartition]: ", err);
          return reject(err);
        } else {
          return resolve(data);
        }
      });
    });
  },

  // Create Batch Partitions using an AthenaQuery
  "batchCreatePartitionsDataDevicesAthenaQuery": async function(siteId, deviceId, startDate, endDate, databaseName, tableName) {
    // Hardcoded variables
    // let databaseName = 'athenatest',
    // tableName = 'datadevicestemp2';

    try {
      startDate = new moment(startDate);
      endDate = new moment(endDate);
    } catch (error) {
      sails.log.error("[batchCreatePartitionsDataDevicesAthenaQuery] Error parsing StartDate or endDate!", error);
      return;
    }
    // let queryStringInit = `ALTER TABLE "${databaseName}"."${tableName}" ADD \n`;
    let queryStringInit = `ALTER TABLE ${tableName} ADD   `;
    let loopLength = Number(endDate.diff(startDate, "days"));
    sails.log(`SiteId: ${siteId}. Number of partitions to add: ${loopLength} for device: ${deviceId} `);
    let currentDate = moment(startDate);
    let queryString = queryStringInit;
    let queryResponse,
      counter = 1,
      queryExecuted = false,
      log = [];

    // Helper functions
    function logging(log, partition, queryId, status) {
      log.push({
        "partition": partition,
        "queryId": queryId,
        "status": status,
      });
    }

    let i;
    for (i = 0; i <= loopLength; i++) {
      queryExecuted = false;
      let currentDateString = currentDate.format("YYYY-MM-DD");
      queryString += `PARTITION (partition_0 = '${siteId}', partition_1 = '${deviceId}', partition_2 = '${currentDateString}') LOCATION 's3://${tableName}/${siteId}/${deviceId}/${currentDateString}' \n`;
      currentDate.add(1, "day");
      counter++;
      if (counter == 100) {
        queryExecuted = true;
        // Query athena and wait for results.
        sails.log("Adding 100 partitions till date: ", currentDate.format("YYYY-MM-DD"));
        try {
          queryResponse = await this.startQuery(queryString, databaseName);
          await this.waitForQueryToComplete(queryResponse.QueryExecutionId, 1, 12);
        } catch (error) {
          sails.log("[batchCreatePartitionsDataDevicesAthenaQuery] Error!:", error);
          logging(log, i, queryResponse.QueryExecutionId, false);
          return Promise.reject(log);
        }
        sails.log("Query completed.");
        logging(log, i, queryResponse.QueryExecutionId, true);
        counter = 1;
        queryString = queryStringInit;
      }
    }
    if (!queryExecuted) {
      sails.log("Adding remaining partitions till date: ", currentDate.format("YYYY-MM-DD"));
      try {
        queryResponse = await this.startQuery(queryString, databaseName);
        await this.waitForQueryToComplete(queryResponse.QueryExecutionId, 1, 12);
        logging(log, i, queryResponse.QueryExecutionId, true);

      } catch (error) {
        sails.log("[batchCreatePartitionsDataDevicesAthenaQuery] Error!:", error);
        logging(log, i, queryResponse.QueryExecutionId, false);
        return Promise.reject(log);
      }
      sails.log("Query completed.");
    }
    log.push(`Partitions of Device:${deviceId} of Site:${siteId} finished adding.`);
    sails.log(`Partitions of Device:${deviceId} of Site:${siteId} finished adding.`);
    return Promise.resolve(log);
  },
  "sleep": (ms) => {

    /* Makes function sleep for specified milliseconds */

    return new Promise((resolve, reject) => {
      setTimeout(() => {
        return resolve();
      }, ms);
    });
  },
  /* Converts the queryResult to JSON format. Also Type casts the values based on column MetaData as stored in Athena. */
  "csvToJson": (queryResult) => {
    // sails.log('Converting to JSON.');
    let jsonMap, finalObject = [], rows = queryResult.Rows;
    jsonMap = athenaService.initJsonMapFromMetadata(queryResult);
    for (let i = 1; i < rows.length; i++) {
      finalObject[i - 1] = {};
      let row = rows[i].Data;
      for (let cell in row) {
        if (row[cell].VarCharValue != undefined) {
          let key = jsonMap[cell].name;
          let dataType = jsonMap[cell].dataType;
          let value = row[cell].VarCharValue;
          switch (dataType) {
            case "varchar":
              value = String(value);
              break;
            case "tinyint":
            case "smallint":
            case "integer":
            case "bigint":
            case "double":
            case "boolean":
              value = Number(value);
              break;
            case "date":
              break;
            case "timestamp":
              break;
          }
          finalObject[i - 1][key] = value;
        }
      }
    }
    // sails.log(finalObject);
    return finalObject;
  },

  "initJsonMapFromMetadata": (queryResult) => {
    /* Initializes jsonMap object using Column Meta data. Returns label of column as well as data type. Example :
    { '0': { name: 'timestamp', dataType: 'varchar' },
      '1': { name: 'inputterminalcurrent', dataType: 'double' } }
*/
    let retObj = {};
    let arrayOfColumns = queryResult.ResultSetMetadata.ColumnInfo;
    for (let i in arrayOfColumns) {
      retObj[i] = {
        "name": arrayOfColumns[i].Label,
        "dataType": arrayOfColumns[i].Type,
      };
    }
    // sails.log(retObj);
    return retObj;
  },

};


