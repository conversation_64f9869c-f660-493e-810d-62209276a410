const moment = require("moment-timezone");
moment.tz.setDefault("Asia/Kolkata");
const TS_FORMAT = "YYYY-MM-DD HH:mm:ss";
moment.defaultFormat = "YYYY-MM-DD HH:mm:ss";


/**
 *
 * @param {String} ts timestamp of format YYYY-MM-DD HH:mm:ss
 * @description returns the timestamps relative to the timestamp passed
 * @returns {Object}
 */
function requiredTimestamps(ts, addMinutes) {
	const recentData = moment(ts).format(TS_FORMAT);
	const tsMoment = moment(ts);

	const last24HrsData = helper
		.actualStartOf(addMinutes, "day")
		.subtract(1, "day")
		.format(TS_FORMAT);
	const monthStartData = helper
		.actualStartOf(addMinutes, "month")
		.format(TS_FORMAT);
	const sameDayLastMonthData = tsMoment.clone()
		.subtract(1, "month")
		.format(TS_FORMAT);
	const lastMonthStartData = helper
		.actualStartOf(addMinutes, "month")
		.subtract(1, "month")
		.format(TS_FORMAT);
	return {
		recentData,
		last24HrsData,
		monthStartData,
		sameDayLastMonthData,
		lastMonthStartData,
	};
}

/**
 * Get devices's parameter data between 2 timestamp and sum the parameter value
 * @param {string} plantId Plant id to uniquely identify a plant
 * @param {string} param Parameter of plant to get data of
 * @param {string} startTime Start time of query
 * @param {string} endTime End time of query
 */
async function aggreagateBetween2TS(plantId, param, startTime, endTime) {
	try {

		let query = `select sum(measure_value::double) as ${param} from smartjoules.aggregatedParams
			 where 
				measure_name='${param}' 
				and deviceId='${plantId}' 
				and time between '${startTime}'-330m and '${endTime}'-330m`;
		// The query is something like select sum(param) from table where conidtions.
		// Get data between 2 timestamp and sum the param value. We subtract 330m from time 
		// bcz timeseries database stores values in UTC so subtracting 330m or 5hours:30min 
		// gives actual value

		let data = await timeSeries.AggregatedParams.rawQuery(query, [param]);

		return { "data": data[0] };

	} catch (e) {
		sails.log.error("dashboardservice::aggreagateBetween2TS ", e);
		throw (e);
	}
}


async function getDayWiseWeekConsumption(plantId, param) {
	const $consArr = [];
	const dateArr = [];
	let startOfToday = helper.actualStartOf(480, "day");

	for (let index = 0; index < 7; index++) {
		let start = startOfToday.clone().subtract(1, "day");
		let end = startOfToday.clone().subtract(1, 'minute');

		$consArr.push(
			dataDeviceService.customConsumption(
				plantId,
				[param],
				start,
				end
			)
		);

		let currStartDate = (start.unix() * 1000) - (480 * 1000 * 60);
		dateArr.push(currStartDate);
		startOfToday = startOfToday.subtract(1, "day");

	}
	const consArr = await Promise.all($consArr);
	const tsConsArr = consArr.map((cons, index) => {
		const val = getValue(cons.param);
		return [dateArr[index], val];
	});
	return tsConsArr;
}
/**
 *
 * @param {stiring} siteId
 * @param {string} plantId [all|<plantId>] if plantId is "all", all plants will be queried
 * @param {Array} select fields to return
 * @description calls processService to query for plants for a given site
 * @returns {Promise}
 */
function getPlantInfo(siteId, plantId, select) {
	let queryObj = {
		"where": { siteId, "type": "plant" },
	};
	if (plantId !== "all" && plantId) {
		queryObj.where.processId = plantId;
		delete queryObj.where.siteId;
		delete queryObj.where.type;
	}
	if (select) {
		queryObj.select = select;
	}
	// console.log(queryObj);
	return processService.find(queryObj);
}
/**
 *
 * @param {Array} deviceData raw deviceData queried from db
 * @description in case of multiple plants the value of same timestamps are to be aggregated.
 * This function removes the values which don't have all the data points. This is done to remove wrong values which may arise due to incompleteness.
 */
function removeIncompleteParams(deviceData) {
	// contains array of data device objects
	// check if param is in all the packets
	// then only add it
	const totalPackets = deviceData.length;
	const paramData = {};
	deviceData.map(dataPacket => {
		if (!dataPacket) return;
		const { data } = dataPacket;
		for (let param in data) {
			if (!paramData[param]) paramData[param] = [];
			paramData[param].push(Number(data[param]));
		}
	});
	// remove incomplete parameters
	for (let param in paramData) {
		if (paramData[param].length !== totalPackets) delete paramData[param];
	}
	return paramData;
}
/**
 *
 * @param {Object} dataObject
 * Adds all the values for a particular id and timestamp.
 */
function addAllParameters(dataObject) {
	const dataMap = {};
	for (let param in dataObject) {
		dataMap[param] = dataObject[param].reduce((acc, val) => acc + val, 0);
	}
	return dataMap;
}
/**
 * @module calculator
 * @description contains calculator functions which are called when all the data is available
 */
const calculator = {
	elecLiveInfo(data) {
		const { recentData } = data;
		const val = recentData ? recentData.instelec : null;
		if (!helper.isNullish(val)) {
			return `${helper.returnFilteredNumber(val)} kW`;
		}
		return val;
	},
	currentSteamVal(data) {
		const { recentData } = data;
		const val = recentData ? recentData.inststeam : null;
		if (!helper.isNullish(val)) {
			return `${helper.returnFilteredNumber(val)} m³/s`;
		}
		return val;
	},
	totalMonthsElectricityConsumption(data) {
		const { recentData, monthStartData, unitPref } = data;
		try {
			let preferredUnit = unitPref["cons"]; // conselec is in dau , i.e kwh for energy
			let recentConsumption, monthConsumption, unit;

			if (preferredUnit === "kvah") {
				recentConsumption = helper.kwhToKvah(recentData.conselec);
				monthConsumption = helper.kwhToKvah(monthStartData.conselec);
				unit = "kVAh";
			} else {
				recentConsumption = recentData.conselec;
				monthConsumption = monthStartData.conselec;
				unit = "kWh";
			}

			const val = (recentConsumption - monthConsumption);

			if (!helper.isNullish(val)) {
				return `${helper.returnFilteredNumber(val)} ${unit}`;
			}
			return val;
		} catch (error) {
			sails.log.error(error);
			return null;
		}
	},
	totalMonthsSteamConsumption(data) {
		const { aggreagateBetween2TSMonthSteam } = data;
		try {
			const val = helper.returnFilteredNumber(aggreagateBetween2TSMonthSteam.conssteam);

			if (!helper.isNullish(val)) {
				return `${helper.returnFilteredNumber(val)} kg`;
			}
			return val;
		} catch (error) {
			sails.log.error(error);
			return null;
		}
	},
	elecMonthInfo(data) {
		const { recentData, monthStartData, billRate, unitPref } = data;
		try {

			let recentConsumption, monthConsumption;
			let preferredUnit = unitPref["cons"]; // conselec is in dau , i.e kwh for energy

			if (preferredUnit === "kvah") {
				recentConsumption = helper.kwhToKvah(recentData.conselec);
				monthConsumption = helper.kwhToKvah(monthStartData.conselec);
			} else {
				recentConsumption = recentData.conselec;
				monthConsumption = monthStartData.conselec;
			}

			const val =
				(recentConsumption - monthConsumption) * billRate.elecRate;

			if (!helper.isNullish(val)) {
				return `₹ ${helper.returnFilteredNumber(val)}`;
			}
			return val;
		} catch (error) {
			sails.log.error(error);
			return null;
		}
	},
	weekElecChart(data) {
		const { dayWiseConsumption, unitPref } = data;
		let preferredUnit = unitPref["cons"]; // conselec is in dau , i.e kwh for energy
		let weekConsumption;

		if (preferredUnit === "kvah") {
			weekConsumption = dayWiseConsumption.map(consumption => {
				return [
					consumption[0],
					helper.returnFilteredNumber(helper.kwhToKvah(consumption[1]))
				];
			});
		} else {
			weekConsumption = dayWiseConsumption.map(consumption =>
				[consumption[0], helper.returnFilteredNumber(consumption[1])]
			);
		}

		return weekConsumption;
	},
	powerCurrentVsLast(data) {
		const {
			recentData,
			monthStartData,
			sameDayLastMonthData,
			lastMonthStartData,
		} = data;
		try {
			const thisMonthConsumption =
				recentData.conselec - monthStartData.conselec;
			const lastMonthConsumption =
				sameDayLastMonthData.conselec - lastMonthStartData.conselec;
			const delta = thisMonthConsumption - lastMonthConsumption;
			const change = (delta / lastMonthConsumption) * 100;
			if (!helper.isNullish(change)) {
				return `${Math.round(change)} %`;
			}
			return change;
		} catch (error) {
			sails.log.error(error);
			return null;
		}
	},
	steamLiveInfo(data) {
		const { recentData } = data;
		const val = recentData ? recentData.inststeam : null;
		if (!helper.isNullish(val)) {
			return `${helper.returnFilteredNumber(val)} kg/hour`;
		}
		return val;
	},
	steamMonthInfo(data) {
		const { aggreagateBetween2TSMonthSteam, billRate } = data;
		try {
			const val =
				(aggreagateBetween2TSMonthSteam.conssteam) *
				billRate.steamRate;

			if (!helper.isNullish(val)) {
				return `₹ ${helper.returnFilteredNumber(val)}`;
			}
			return val;
		} catch (error) {
			sails.log.error(error);
			return null;
		}
	},
	weekSteamChart(data) {
		const { dayWiseConsumptionSteam } = data;
		return dayWiseConsumptionSteam.map(consumption => [Number(consumption[0]), consumption[1]]);
	},
	steamCurrentVsLast(data) {
		const {
			aggreagateBetween2TSLastMonthSteam,
			aggreagateBetween2TSMonthSteam
		} = data;
		try {
			const thisMonthConsumption = aggreagateBetween2TSMonthSteam.conssteam;
			const lastMonthConsumption = aggreagateBetween2TSLastMonthSteam.conssteam;
			const delta = thisMonthConsumption - lastMonthConsumption;
			const change = (delta / thisMonthConsumption) * 100;
			if (!helper.isNullish(change)) {
				return `${Math.round(change)} %`;
			}
			return change;
		} catch (error) {
			sails.log.error(error);
			return null;
		}
	},
};
function getValue(paramObj) {
	if (!paramObj) return null;
	let val = null;
	Object.keys(paramObj).forEach(param => {
		if (!helper.isNullish(paramObj[param])) val = paramObj[param];
	});
	return val;
}
function addWeekConsumption(consArr) {
	const addedConsumption = [];
	if (!consArr || consArr.length <= 0)
		return addedConsumption;
	for (let day = 0; day < consArr[0].length; day++) {
		let consumption = 0;
		let ts = consArr[0][day][0];
		for (let index = 0; index < consArr.length; index++) {
			consumption += consArr[index][day][1];
		}

		addedConsumption.push([
			ts,
			helper.returnFilteredNumber(consumption)
		]); // its better to have a NaN if value doesnt exist for ts

	}
	return addedConsumption;
}

/**
 * Given array of data of multiple plants, this functions aggregate data on timestamp
 * @param {Array} consArr Array of data between 2 timestamps for different plants
 * @param {string} parameter Parameter inside data to aggregate
 */
function addWeekConsumption2(consArr, parameter) {

	let consumptionGroupbyTime = consArr
		.flat()
		.reduce((acc, elem) => {
			let paramValue = helper.returnFilteredNumber(Number(elem[parameter])) || NaN;
			let ts = moment(elem.timestamp).unix() * 1000;

			if (acc[ts] === undefined) acc[ts] = 0;
			acc[ts] += paramValue;

			return acc;
		}, {});

	return Object.keys(consumptionGroupbyTime).map(timestamp =>
		[timestamp, consumptionGroupbyTime[timestamp]]
	);
}

/**
 *
 * @param {Array} deviceIds
 * @param {Object} timeObj
 * @param {string} category
 * @param {string} display
 * @deprecated prepares parameters to be passed to datadevices service to calculate consuption and returns an array of promises.
 * @returns {Promise}
 */
function getConsumption(deviceIds, timeObj, category, display) {
	let params = ["kvah", "kwh", "conselec"];
	if (category === "steam") {
		params = ["conssteam"]; // fill parameters for steam
	}
	let $consArr = [];
	if (display !== "table") {
		deviceIds.map(deviceId => {
			let $consumptionVal = dataDeviceService.customConsumption(
				deviceId,
				params,
				timeObj.defaultStart, // default start and end is last hour
				timeObj.defaultEnd
			);
			$consArr.push($consumptionVal);
		});
	} else {
		deviceIds.map(deviceId => {
			let { hour, day, yesterday, lastWeek } = timeObj;
			[hour, day, yesterday, lastWeek].forEach(timeSlot => {
				$consArr.push(
					dataDeviceService.customConsumption(
						deviceId,
						params,
						...timeSlot
					)
				);
			});
			// $consArr.push(
			// 	dataDeviceService.customConsumption(deviceId, params, ...tillNow)
			// );
		});
	}
	return $consArr;
}

module.exports = {
	removeIncompleteParams,
	addAllParameters,
	getPlantInfo,
	getConsumption,
};
