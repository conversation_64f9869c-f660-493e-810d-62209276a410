const events = require("events");
const axios = require("axios");
const mqttHost = process.env.MQTT_HOST;
const helper = require("./helper");

const logObj = {};

const emitter = new events.EventEmitter();
let globalWorkers = 0;

// Event listeners starts
emitter.on("enqueue", (msg) => {
  globalWorkers += 1;
  sails.log(msg, "from the msg", globalWorkers);
});
emitter.on("dequeue", (msg) => {
  if (globalWorkers < 5) {

    sails.log(msg, "from the msg", globalWorkers);

    globalWorkers -= 1;
  }
});
emitter.on("initChange", req => {
  //when any modications are started, then initChange should be emitted, it will register the reqId so that changes can be appended to it.
  if (!req || !req._logMeta) {
    sails.log.warn("No req Object in append");
    return;
  }
  let _logMeta = helper.deepClone(req._logMeta);
  logObj[_logMeta.id] = { ..._logMeta };
});
emitter.on("commitChange", (req, table, oldItem, newItem) => {
  //when a table is updated, commitChange event should be emitted with tablename and items
  //it will append the changes in table to id registered in initchange

  if (!req || !req._logMeta) {
    sails.log.warn("No req Object in append");
    return;
  }
  let { _logMeta } = req;
  let { id } = _logMeta;

  if (!table) {
    sails.log.warn("No table specified", req.allParams(), _logMeta);
    return;
  }
  let oldObj = [],
    newObj = [];

  if (typeof oldItem == "string") {
    try {
      oldObj.push(JSON.parse(oldItem));
    } catch (e) {
      sails.log.error(e);
    }
  } else if (Array.isArray(oldItem))
    oldObj = oldItem;
  else if (typeof oldItem == "object" && Object.keys(oldItem).length > 0)
    oldObj.push(oldItem);
  if (typeof newItem == "string") {
    try {
      newObj.push(JSON.parse(newItem));
    } catch (e) {
      sails.log.error(e);
    }
  } else if (Array.isArray(newItem))
    newObj = newItem;
  else if (typeof newItem == "object" && Object.keys(newItem).length > 0)
    newObj.push(newItem);


  if (!logObj[id]) {
    sails.log.warn("log event not registered", req.allParams(), _logMeta);
  }

  logObj[id][table] = { oldObj, newObj };
});
emitter.on("pushChange", async (req) => {
  //When the event is complete then emit this event with req object it will put the object in s3 and dynamo
  if (!req || !req._logMeta) {
    sails.log.warn("No req Object in append");
    return;
  }
  let { _logMeta } = req;
  let { id } = _logMeta;
  let change = logObj[id];
  let { table, siteId, ts, userId } = change;
  let updates = change[table];
  let action = "";
  // let key;
  let oldLen = updates.oldObj.length;
  let newLen = updates.newObj.length;
  if (oldLen != 0 && newLen != 0) {
    //case for update
    action = "update";
    // key = updates.oldObj[0][pkName];

  } else if (newLen != 0) {
    //case for create
    action = "create";
    // key = updates.newObj[0][pkName];

  } else {
    //case for delete
    action = "delete";
    // key = updates.oldObj[0][pkName];
  }
  change.action = action;
  let yearMonth = ts.slice(0, 7);
  change.yearMonth = yearMonth;


  try {
    let s3Keys = await sails.config.helper.syncToS3(change);
    if (s3Keys == 0)
      return;
    let dynamoObj = {
      userId,
      ts,
      s3Keys,
      siteId,
      table,
    };
    let status = await ChangeLog.create(dynamoObj);
    sails.log(status);
  } catch (e) {
    sails.log.error(e);
  }
  delete logObj[id];
});
emitter.on("stashChange", (req) => {
  //in case of any exception call stashChange {generally in catch blocks}
  if (!req || !req._logMeta) {
    sails.log.warn("No req Object in append");
    return;
  }
  let { _logMeta } = req;
  sails.log.warn("Event errored ", _logMeta, req.allParams());
  delete logObj[_logMeta.id];

});
emitter.on("error", (msg) => {
  sails.log.error(msg);
});

async function getOldPoliciesByRoleName(roleName) {
  let role;
  try {
    role = await Role.findOne({ roleName });
  } catch (e) {
    sails.log.error("getOldPoliciesByRoleName", e);
    return e;
  }
  if (!role) {
    return null;
  }
  let policies = helper.toJson(role.policies);
  let flattenedPolicies = {};
  for (let eachPolicy in policies) {
    let subHeadings = policies[eachPolicy]["subHeadings"];
    let pageViewKey = `${eachPolicy}_View`;
    let pageViewValue = (policies[eachPolicy]["pageView"] === true) ? "1" : "0";
    flattenedPolicies[pageViewKey] = pageViewValue;
    for (let eachSubHead in subHeadings) {
      let innerPolicies = subHeadings[eachSubHead]["policies"];
      let camelCaseSubHead = eachSubHead.charAt(0).toUpperCase() + eachSubHead.slice(1);
      for (let eachInnerPolicy in innerPolicies) {
        let innerPolicyCameCase = eachInnerPolicy.charAt(0).toUpperCase() + eachInnerPolicy.slice(1);
        let key = `${camelCaseSubHead}_${innerPolicyCameCase}`;
        let value = (innerPolicies[eachInnerPolicy]["hasAccess"] === true) ? "1" : "0";
        flattenedPolicies[key] = value;
      }
    }
  }
  return flattenedPolicies;
}

// Event listeners end
module.exports = {
  //change name of emmitter
  "emmitter": emitter,
  "joinJouleTrackList": async (socketId, siteId, role) => {
    if (!role) {
      sails.log.warn("A user without role logged In");
      return;
    }

    let policies = await getOldPoliciesByRoleName(role);

    if (!policies) {
      sails.log.warn(`Unregistered role ${role} logged In`);
      return;
    }
    let subscribeRooms = new Set();
    for (let policy in policies) {
      if (policies[policy] == 1 || policies[policy] == "1") {
        if (sails.policyRoomMap[policy]) {
          let room = sails.policyRoomMap[policy];
          subscribeRooms.add(room);
        } else {
          //sails.log.warn("Not defined policy " + policy+ "in join jouletracklist");
        }
      }

    }
    subscribeRooms.add("public");
    subscribeRooms.forEach((room, roomvalue) => {
      sails.sockets.join(socketId, `jt_${siteId}_${room}`);
    });

  },
  "notifyJouleTrack": (siteId, room, topic, data) => {
    //Room values:
    /**
     * diagnotstics
     * config
     * recipies
     * **********
     * public : for all the connected users on that site
     *
     * data => should contain two keys "event" and "data"
     * and data should be in form of array of objects with primary key present
     */
    let eventRoom = `jt_${siteId}_${room}`;
    if (!data || !data.event && !data.data) {
      sails.log.error("Event or data not present in packet!!");
      sails.log.error("Not publishing event on topic " + topic);
      return { "err": "Invalid data object." };
    }
    sails.sockets.broadcast(eventRoom, topic, data);
    sails.log.info("[notifyJouleTrack]-" + eventRoom + " " + JSON.stringify(data));
    return { "status": "ok" };
  },
  "notifyUser": async (hash, topic, data) => {
    try {
      let socketId = await cacheService.smembers(hash);
      for (let i in socketId) {
        sails.sockets.broadcast(socketId[i], topic, data);
      }
    } catch (e) {
      sails.log.error(e);
    }
  },
  //topic can be a string or an array of strings
  "subscribe": async (topic) => {
    if (!topic)
      return;
    try {
      let data = await axios.post(mqttHost + "/v1/event/subscribe", { topic });
      return data;
    } catch (err) {
      return Promise.reject(err);
    }

  },
  //topic is string, msg can be string or JSON
  "publish": async (topic, msg) => {
    if (!topic || !msg)
      return;
    try {
      let data = await axios.post(mqttHost + "/v1/event/publish", { topic, msg });
      return data;
    } catch (err) {
      sails.log.error(err);
      return (undefined);
    }
  },
  //topic can be a string or an array of strings
  "unsubscribe": async (topic) => {
    if (!topic)
      return;
    try {
      let api = mqttHost + "/v1/event/unsubscribe";
      let data = await axios.post(api, { topic });
      return data;
    } catch (err) {
      return Promise.reject(err);
    }
  },

};
