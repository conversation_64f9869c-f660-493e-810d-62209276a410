const Joi = require("joi");
const _ = require("lodash");
const {
  InfluxDB,
  Point,
  HttpError,
  fluxDuration,
  DEFAULT_WriteOptions,
  FluxTableMetaData
} = require("@influxdata/influxdb-client");
const { default: axios } = require("axios");

module.exports = (() => {
  const config = {
    host: process.env.INFLUX_URL,
    token: process.env.INFLUX_TOKEN,
    org: process.env.INFLUX_ORG,
    orgID: process.env.INFLUX_ORG_ID,
  };
  const configSchema = Joi.object()
    .keys({
      host: Joi.string().required(),
      token: Joi.string().required(),
      org: Joi.string().required(),
      orgID: Joi.string().required(),
    })
    .unknown(true);

  const { error } = configSchema.validate(config);
  // if (error && process.env.NODE_ENV == "production") throw new Error(`INFLUX CONFIG IS NOT VALID - ${error}`);
  function query( query,opts ) {
    return new Promise((resolve,reject)=> {
        const queryResult = []
        let _parsedQuery = query;
        if(opts.replacements) {
            let _replacements = opts.replacements;
            for( let key in _replacements ) {
              let regEx = `{{${key}}}`
              let re = new RegExp(regEx,"g");
              _parsedQuery = _parsedQuery.replace(re,_replacements[key])
            }
        }
        if(opts.debug){
          console.log(_parsedQuery)
        }
        const queryApi = new InfluxDB({ url: config.host, token: config.token }).getQueryApi(config.org);
        try {
          queryApi.queryRows(_parsedQuery, {
            next(row, tableMeta) {
              // console.log(JSON.stringify(o, null, 2))
              const rows = tableMeta.toObject(row)
              queryResult.push(rows);
            },
            error(error) {
              sails.log.error(`INFLUX ERROR: ${error.message}`);
              sails.log.error(error)
              return reject(error)
            },
            complete() {
              return resolve(queryResult);
            },
          })
        } catch (e){
          return reject(e);
        }
    })


  }
  return {
    query
}
})();
