let moment = require("moment-timezone");
const DEFAULT_ELECTRICITY_PRICE = 9;


module.exports = {
	"getCurrentBaseline1": (siteId, data) => {
		let searchObj = {
			"where": {
				"siteId": siteId,
				"startDate": {
					"beginsWith": data.month,
				},
				"sort": "-1",
				"limit": 1
			}
		};
		// if(parseInt(data.month))
		sails.log.info(searchObj, "search obj");
		return new Promise((resolve, reject) => {
			Baseline.find(searchObj).exec(function (err, data) {
				if (err) {
					sails.log.error(err);
					return reject({ "err": "Unable to fetch data at present" });
				}
				if (!data || data.length == 0) {
					if (moment().month() == 0) {
						let updatedObj = {
							"where": {
								"siteId": siteId,
								"startDate": {
									"beginsWith": "12"
								},
								"sort": "-1",
								"limit": 1
							}
						};
						Baseline.find(updatedObj).exec((err, data) => {
							if (err) {
								return reject({ "err": "Unable to fetch data at present" });
							}
							if (data.length == 0)
								return reject({ "err": "No data present for current site", "data": null });
							let tmpDate = data[0].startDate.split("-");
							let month = tmpDate[0];
							let day = tmpDate[1];
							let year = tmpDate[2];
							tmpDate[0] = year;
							tmpDate[1] = month;
							tmpDate[2] = day;
							data[0].startDate = tmpDate.join("-");
							return resolve(data);
						});
					} else {
						return reject({ "err": "No data present for current site", "data": null });
					}
				} else {
					let tmpDate = data[0].startDate.split("-");
					let month = tmpDate[0];
					let day = tmpDate[1];
					let year = tmpDate[2];
					tmpDate[0] = year;
					tmpDate[1] = month;
					tmpDate[2] = day;
					data[0].startDate = tmpDate.join("-");
					return resolve(data);
				}
			});
		});

	},
	"getCurrentBaseline1_new": async (siteId, data, userConsumptionUnit, siteConsumptionUnit) => {

		let searchObj = {
			"where": {
				"siteId": siteId,
				"startDate": {
					"beginsWith": data.month,
				},
				"sort": "-1",
				"limit": 1
			}
		};

		return new Promise((resolve, reject) => {

			Baseline.find(searchObj).exec(async function (err, data) {
				if (err) {
					sails.log.error(err);
					return reject({ "err": "Unable to fetch data at present" });
				}
				if (!data || data.length == 0) {
					/**
					 * I dont know how will we ever come here
					 */
					if (moment().month() == 0) {
						let updatedObj = {
							"where": {
								"siteId": siteId,
								"startDate": {
									"beginsWith": "12"
								},
								"sort": "-1",
								"limit": 1
							}
						};
						Baseline.find(updatedObj).exec(async (err, data) => {
							if (err) {
								return reject({ "err": "Unable to fetch data at present" });
							}
							if (data.length == 0)
								return reject({ "err": "No data present for current site", "data": null });
							/*
							Code to make sure we get the right month
							*/
							let startdate = moment(data[0]["startDate"], "MM-DD-YYYY");
							startdate = startdate.year(moment().year());

							if (startdate > moment()) {
								let newmonth = helper.padoneZero(startdate.subtract(1, "month").month() + 1);
								searchObj["where"]["startDate"]["beginsWith"] = String(newmonth);
								data = await Baseline.find(searchObj);
								if (!data || data.length === 0) {
									return reject({ "err": "Unable to fetch data at present" });
								}
							}
							/*
							End of code to make sure we get right month
							*/
							let tmpDate = data[0].startDate.split("-");
							let month = tmpDate[0];
							let day = tmpDate[1];
							let year = tmpDate[2];
							tmpDate[0] = year;
							tmpDate[1] = month;
							tmpDate[2] = day;
							data[0].startDate = tmpDate.join("-");
							if (siteConsumptionUnit === userConsumptionUnit) {
								// no thing
							} else if (siteConsumptionUnit === "kvah") {
								// user wants consumption in kwh
								data[0]["baselineValue"] = helper.kvahToKwh(data[0]["baselineValue"]);
								data[0]["consumptionValue"] = helper.kvahToKwh(data[0]["consumptionValue"]);
							} else {
								// user wants consumption in kvah
								data[0]["baselineValue"] = helper.kwhToKvah(data[0]["baselineValue"]);
								data[0]["consumptionValue"] = helper.kwhToKvah(data[0]["consumptionValue"]);
							}
							return resolve(data);
						});
					} else {
						return reject({ "err": "No data present for current site", "data": null });
					}
				} else {
					/*
					Code to make sure we get the right month
					*/
					let startdate = moment(data[0]["startDate"], "MM-DD-YYYY");
					startdate = startdate.year(moment().year());

					if (startdate > moment()) {
						let newmonth = helper.padoneZero(startdate.subtract(1, "month").month() + 1);
						searchObj["where"]["startDate"]["beginsWith"] = String(newmonth);
						data = await Baseline.find(searchObj);
						if (!data || data.length === 0) {
							return reject({ "err": "Unable to fetch data at present" });
						}
					}
					/*
					End of code to make sure we get right month
					*/
					let tmpDate = data[0].startDate.split("-");
					let month = tmpDate[0];
					let day = tmpDate[1];
					let year = tmpDate[2];
					tmpDate[0] = year;
					tmpDate[1] = month;
					tmpDate[2] = day;
					data[0].startDate = tmpDate.join("-");

					if (siteConsumptionUnit === userConsumptionUnit) {
						// no thing
					} else if (siteConsumptionUnit === "kvah") {
						// user wants consumption in kwh
						data[0]["baselineValue"] = helper.kvahToKwh(data[0]["baselineValue"]);
						data[0]["consumptionValue"] = helper.kvahToKwh(data[0]["consumptionValue"]);
					} else {
						// user wants consumption in kvah
						data[0]["baselineValue"] = helper.kwhToKvah(data[0]["baselineValue"]);
						data[0]["consumptionValue"] = helper.kwhToKvah(data[0]["consumptionValue"]);
					}

					return resolve(data);
				}
			});
		});

	},
	"getCurrentBaseline": async (siteId, normalize = false) => {
		let searchObj = {
			"where": {
				"siteId": siteId,
				"startDate": {
					"lte": moment().format("MM-DD-YYYY"),
				},
				"sort": "-1"
			}
		};
		try {
			let baseline = await Baseline.findOne(searchObj);
			if (typeof baseline === "undefined" && moment().month() === 0) {
				let updatedObj = {
					"where": {
						"siteId": siteId,
						"startDate": {
							"beginsWith": "12"
						},
						"sort": "-1",
						"limit": 1
					}
				};
				baseline = await Baseline.find(updatedObj);
			}
			if (!baseline) {
				return Promise.reject({ "err": "Baseline not configured" });
			}
			let { startDate } = baseline;
			let tmpDate = startDate.split("-");
			let month = tmpDate[0];
			let day = tmpDate[1];
			let year = normalize ? moment().year() : tmpDate[2];
			baseline.startDate = `${year}-${month}-${day}`;
			return [baseline];

		} catch (e) {
			sails.log.error(e);
			return Promise.reject("Unable to get baseline");
		}


	},
	"getCurrentBaseline_new": async (siteId, siteConsumptionUnit, userConsumptionUnit, normalize = false) => {
		let searchObj = {
			"where": {
				"siteId": siteId,
				"startDate": {
					"lte": moment().format("MM-DD-YYYY"),
				},
				"sort": "-1"
			}
		};
		try {
			let baseline = await Baseline.findOne(searchObj);
			if (typeof baseline === "undefined" && moment().month() === 0) {
				let updatedObj = {
					"where": {
						"siteId": siteId,
						"startDate": {
							"beginsWith": "12"
						},
						"sort": "-1",
						"limit": 1
					}
				};
				baseline = await Baseline.findOne(updatedObj);
			}
			if (!baseline) {
				return Promise.reject({ "err": "Baseline not configured" });
			}
			let { startDate } = baseline;
			let tmpDate = startDate.split("-");
			let month = tmpDate[0];
			let day = tmpDate[1];
			let year = normalize ? moment().year() : tmpDate[2];
			baseline.startDate = `${year}-${month}-${day}`;
			if (siteConsumptionUnit === userConsumptionUnit) {
				// no thing
			} else if (siteConsumptionUnit === "kvah") {
				// user wants consumption in kwh, site have it in kvah
				baseline["baselineValue"] = helper.kvahToKwh(baseline["baselineValue"]);
				baseline["consumptionValue"] = helper.kvahToKwh(baseline["consumptionValue"]);
			} else {
				// user wants consumption in kvah
				baseline["baselineValue"] = helper.kwhToKvah(baseline["baselineValue"]);
				baseline["consumptionValue"] = helper.kwhToKvah(baseline["consumptionValue"]);
			}
			return [baseline];

		} catch (e) {
			sails.log.error(e);
			return Promise.reject("Unable to get baseline");
		}


	},
	/**
	 * update dynamo for the existing entries.
	 * @param {Object} dateShift formatted object to update daily consumption table.
	 */
	"updateEntry"(dateShift) {
		const { siteId, timestamp } = dateShift;
		return DailyConsumption.update(
			{
				siteId,
				timestamp,
			},
			dateShift
		);
	},
	/**
	 * Create dynamo with the new entries.
	 * @param {Object} dateShift formatted object to update daily consumption table.
	 */
	"createEntry"(dateShift) {
		return DailyConsumption.create(dateShift);
	},
	"getSiteElectricityPrice": async (siteId) => {
		let price = DEFAULT_ELECTRICITY_PRICE;
		let site = await Sites.findOne({ "siteId": siteId });
		if (site.hasOwnProperty("unitCost") && site.unitCost != 0) {
			price = Number(site.unitCost)
		}
		return price;
	}
};
