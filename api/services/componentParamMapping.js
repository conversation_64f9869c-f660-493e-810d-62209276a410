module.exports.mapping = {

	"ahu": {
		"CWIT": {
			"param": "TMP", //to be returned as param
			"key": "CWIT" //to be used in Components which <PERSON><PERSON> does this param belong
		},
		"Status": {
			"param": "FEEDBACK",
			"key": "Status"
		},
		"CWOT": {
			"param": "TMP",
			"key": "CWOT"
		},
		"SA": {
			"param": "TMP",
			"key": "SA"
		},
		"RA": {
			"param": "TMP",
			"key": "RA"
		},
		"Actuator": {
			"param": "ACTUATOR FEEDBACK",
			"key": "Actuator"
		},
		"KVA": {
			"param": "KVA",
			"key": "EM"
		},
		"KVAH": {
			"param": "KVAH",
			"key": "EM"
		},
		"KW": {
			"param": "KW",
			"key": "EM"
		},
		"OutputFrequency": {
			"param": "OutputFrequency",
			"key": "VFD"
		}
	},
	"chiller": {
		"KVA": {
			"param": "KVA",
			"key": "EM"
		},
		"KVAH": {
			"param": "KVAH",
			"key": "EM"
		},
		"KW": {
			"param": "KW",
			"key": "EM"
		},
		"WaterFlow": {
			"param": "WaterFlow",
			"key": "VFD"
		},
		"Status": {
			"param": "Status",
			"key": "CHL"
		},
		"ControlType": {
			"param": "ControlType",
			"key": "CHL"
		}, "SetPoint": {
			"param": "SetPoint",
			"key": "CHL"
		},
		"CoolEwt": {
			"param": "CoolEwt",
			"key": "CHL"
		},
		"CoolLwt": {
			"param": "CoolLwt",
			"key": "CHL"
		},
		"CondEwt": {
			"param": "CondEwt",
			"key": "CHL"
		},
		"CondLwt": {
			"param": "CondLwt",
			"key": "CHL"
		},
		"SctA": {
			"param": "SctA",
			"key": "CHL"
		},
		"SstA": {
			"param": "SstA",
			"key": "CHL"
		},
		"hrs": {
			"param": "HrMach",
			"key": "CHL"
		}
	},
	"chilledWaterPump": {
		"OutputFrequency": {
			"param": "OutputFrequency",
			"key": "VFD"
		},
		"Status": {
			"param": "Status",
			"key": "Status"
		},
		"KVA": {
			"param": "KVA",
			"key": "EM"
		},
		"KVAH": {
			"param": "KVAH",
			"key": "EM"
		},
		"KW": {
			"param": "KW",
			"key": "EM"
		},
		"WaterFlow": {
			"param": "WaterFlow",
			"key": "VFD"
		}
	},
	"condenserWaterPump": {
		"OutputFrequency": {
			"param": "OutputFrequency",
			"key": "VFD"
		},
		"Status": {
			"param": "Status",
			"key": "Status"
		},
		"KVA": {
			"param": "KVA",
			"key": "EM"
		},
		"KVAH": {
			"param": "KVAH",
			"key": "EM"
		},
		"KW": {
			"param": "KW",
			"key": "EM"
		},
		"WaterFlow": {
			"param": "WaterFlow",
			"key": "VFD"
		}
	},
	"coolingTower": {
		"OutputFrequency": {
			"param": "OutputFrequency",
			"key": "VFD"
		},
		"Status": {
			"param": "FEEDBACK",
			"key": "Status"
		},
		"KVA": {
			"param": "KVA",
			"key": "EM"
		},
		"KVAH": {
			"param": "KVAH",
			"key": "EM"
		},
		"InWaterTemp": {
			"param": "CondEwt",
			"key": "CHL"
		},
		"OutWaterTemp": {
			"param": "CondLwt",
			"key": "CHL"
		}
	}

};