let uuid = require("uuid/v4");
const moment = require("moment-timezone");
moment.tz.add("Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6");
moment.tz.setDefault("Asia/Kolkata");
module.exports = function (req, res, next) {
	let id = uuid();
	let ts = moment().format("YYYY-MM-DD HH:mm:ss");
	req._logMeta={
		"id": id,
		"table": "component",
		"siteId": req._userMeta._site,
		"userId": req._userMeta.id,
		"pkName": "deviceId",
		"ts": ts
	};
	next();
};