/*
ToDO : implement login system
At login hit an api and check if user auth token is valid/invalid or expired
if valid => generate new token and return;
if invalid => return false and log user out of system.
if expired =>
  Check for expiry if its older than 2 days then return false and log user out of system.
else
  generate new token and return token.
*/

// how is policy.json arranged

// {
//     "controllerName" :{
//         "recipe_READ":{
//             "action" : [""]  ,// actions that this policy will block,
//             "req_type": [""] // req type get/post etc
//         },
//         "_description" :{
//             "displayname" : "Name" // Name of legend it lies in
//         }
//     }

//     // so above POlicy will disply in front end like

//     Name:
//         recipe     []READ

// }

// [TODO] fix storing whole policies in redis instead just save relevant once and not '0
//  once
const policies = require("../../config/policy.json");

module.exports = function (req, res, next) {
	if (sails.config.environment === "testing") return next();
	let userInfo = req._userMeta;
	if (!userInfo) {
		return res.json(401, { "err": "Unable to fetch User" });
	}
	if (userInfo["_role"] == "admin") {
		return next();
	}
	// let currsite = userInfo["_site"],
	let	currRole = userInfo["_role"];
	let { controller, action } = req.options;
	action = action.toLowerCase();
	controller = controller.toLowerCase();
	let requestMethod = String(req.method);

	let topPolicyController = policies[controller];
	topPolicyController = topPolicyController ? topPolicyController : {};
	let policy;
	for (let i in topPolicyController) {
		if (i[0] == "_") {
			// skipping metadata
			continue;
		}
		let tpc = topPolicyController[i];
		let t1 = helper.toArray(tpc["action"]);

		let findi = indexOfAll(t1, action);
		if (findi.length >= 0) {
			// checking if this route needs access if yes than
			// what is policy attached
			findi = findi[0];

			findi = indexOfAll(t1, action);
			if (tpc["req_type"][findi] == requestMethod) {
				policy = i;
				break;
			}

		}
		if (!policy) {
			return next();
		} else {
			// first try hit cache
			// if not find insert into cache
			// cache me like manager : ["policy"]
			let inCache = true;
			cacheService.getKey(currRole).then(role => {
				if (role) {
					return (role);
				} else {
					inCache = false;
					return Role.findOne({ "roleName": currRole });
				}
			}).then(role => {
				let _policies = [];
				if (!role) {
					res.badRequest({ "err": "Role foesnt exist" });
					return res.end();
				} else if (!inCache) {
					_policies = helper.toJson(role["policies"]);
					if (_policies) {
						cacheService.setExpiryKey(currRole, JSON.stringify(_policies));
						sails.log("Was not in cache , but now is");
					} else {
						res.badRequest({ "err": "Error, no policy attached" });
						return res.end();
					}
				} else {
					_policies = helper.toJson(role);
				}
				let accessPoliciy = String(_policies[policy]);

				if (accessPoliciy === "1") {
					sails.log("wohoo");
					return next();
				} else if (accessPoliciy === "0") {
					res.notFound();
				}
				else {
					return res.badRequest({ "err": "Unauthorized" });
				}
			})
				.catch(e => {
					sails.log(e);
					return res.serverError({ "err": "Unauthorized and Error" });
				});
			if (tpc["req_type"][findi] == requestMethod) {
				policy = i;
				break;
			}
		}
	}
	if (!policy) {
		return next();
	} else {
		// first try hit cache
		// if not find insert into cache
		// cache me like manager : ["policy"]
		let inCache = true;
		cacheService
			.getKey(currRole)
			.then(role => {
				if (role) {
					return role;
				} else {
					inCache = false;
					return Role.findOne({ "roleName": currRole });
				}
			})
			.then(role => {
				let _policies = [];
				if (!role) {
					res.badRequest({ "err": "Role foesnt exist" });
					return res.end();
				} else if (!inCache) {
					_policies = helper.toJson(role["policies"]);
					if (_policies) {
						cacheService.setExpiryKey(currRole, JSON.stringify(_policies));
						sails.log("Was not in cache , but now is");
					} else {
						res.badRequest({ "err": "Error, no policy attached" });
						return res.end();
					}
				} else {
					_policies = helper.toJson(role);
				}
				let accessPoliciy = String(_policies[policy]);
				if (accessPoliciy === "1") {
					return next();
				} else if (accessPoliciy === "0") {
					res.notFound();
				} else {
					return res.badRequest({ "err": "Unauthorized" });
				}
			})
			.catch(e => {
				sails.log(e);
				return res.serverError({ "err": "Unauthorized and Error" });
			});
	}
};
function indexOfAll(arr, val){
	return arr.reduce((acc, el, i) => (el === val ? [...acc, i] : acc), []);
}
