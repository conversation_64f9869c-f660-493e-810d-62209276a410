/*
ToDO : implement login system
At login hit an api and check if user auth token is valid/invalid or expired
if valid => generate new token and return;
if invalid => return false and log user out of system.
if expired =>
  Check for expiry if its older than 1 day then return false and log user out of system.
else
  generate new token and return token.
*/
const jwt = require("jsonwebtoken");
module.exports = function (req, res, next) {
	let token;
	if (req.headers && req.headers.authorization) {
		let parts = req.headers.authorization.split(" ");
		if (parts.length == 2) {
			let scheme = parts[0],
				credentials = parts[1];

			if (/^Bearer$/i.test(scheme)) {
				token = credentials;
			}
		} else {
			return res.json(401, { "err": "Format is Authorization: Bearer [token]" });
		}
	} else if (req.param("token")) {
		token = req.param("token");
		// We delete the token from param to not mess with blueprints
		let parts = req.headers.authorization.split(" ");
		if (parts.length == 2) {
			let scheme = parts[0],
				credentials = parts[1];

			if (/^Bearer$/i.test(scheme)) {
				token = credentials;
			}
		} else {
			return res.json(401, { "err": "Format is Authorization: Bearer [token]" });
		}
		delete req.query.token;
	} else {
		return res.json(401, { "err": "No Authorization header was found" });
	}

	jwtService.verify(token, function (err, verifiedToken) {
		if (err) {
			let decoded = jwt.decode(token);
			sails.log.error(err, req.method, req.path, decoded);
			if (err.name == "TokenExpiredError") {
				//Todo : Remove this when new login mech implemented on frontend
				let decode = jwt.decode(token);
				req.user = decode.id;
				return res.json(401, { "err": "Token Expired" });
			} else {
				
				return res.json(401, { "err": "Invalid Token!" });
			}
		}

		let user = verifiedToken.id;
		req.user = user;
		req._userMeta = verifiedToken;
		next();
	});
};
