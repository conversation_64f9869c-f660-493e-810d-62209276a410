//this policies use for user authentication and check his permission

module.exports = function(req, res, next) {

	// User is allowed, proceed to the next policy,
	// or if this is the last policy, the controller

	//userId = req.param("accessId"); //userId is stored value when user login
	if (req.token.role=="smartjoules employee"){
		return next();
	}
	else {
		return res.forbidden(403, "You are not permitted to access");
	}};


// User is not allowed
// (default res.forbidden() behavior can be overridden in `config/403.js`)


