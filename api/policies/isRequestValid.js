module.exports = function (req, res, next) {
	if (sails.config.environment === "development") return next();
	let token;
	if (req.headers && req.headers.authorization) {
		let parts = req.headers.authorization.split(" ");
		if (parts.length == 2) {
			let scheme = parts[0],
				credentials = parts[1];

			if (/^Bearer$/i.test(scheme)) {
				token = credentials;
			}
		} else {
			return res.json(401, { "err": "Format is Authorization: Bearer [token]" });
		}
	}

	jwtService.verify(token, (err, verifiedToken) => {
		if (err) {
			if (err.name == "TokenExpiredError") {
				//Todo : Remove this when new login mech implemented on frontend
				res.status = 401;
				return res.send({"err": "Token Expired"});
			} else {
				sails.log.error(err);
				return res.json(401, { "err": "Invalid Token!" });
			}
		}
		next();
	});
};
