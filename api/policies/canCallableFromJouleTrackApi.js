const authService = require('../services/auth/auth.public');
module.exports = function (req, res, next) {
  const authToken = req.query.authToken;
  if (!authToken) {
    return res.forbidden({ problems: ['Unauthorized Authorization'] });
  }
  // This secret key is being used in jouleTrackAPI while network call
  if (authToken === 'G0y2PqIAr0AZRMgYiUBQLmGxV5LIPUVlfs7f0uuvtsqgoBB3fO1KzB5vSUPj1D2P') {
    return next()
  } 
  return res.badRequest({ problems: ['Invalid Auth token'] });
};
