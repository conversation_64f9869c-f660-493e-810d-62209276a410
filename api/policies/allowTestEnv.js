
module.exports = function (req, res, next) {
	if (process.env.enviornment === "testing" || process.env.env === "testing" || process.env.NODE_ENV === "testing") {
		let { userId, password } = req.allParams();
		let { authorization } = req.headers;
		if (!userId || !password || !authorization)
			return res.badRequest();
		if (userId === "test" && password === "smartjoules") {
			let tokenObj = {
				"id": "test",
				"_role": "admin",
				"name": "testUser",
				"_site": "smt-del",
				"_h_": "smt-del" + "_" + authorization,
				"env": "testing"
			};
			let Ntoken = jwtService.issue(tokenObj);
			let resObj = {
				"token": Ntoken
			};
			//req._userMeta.
			//setHeader('authoriztion':"Bearer ")
			return res.json(resObj);
		}
	} else {
		next();
	}

};
