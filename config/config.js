const nodemailer = require("nodemailer");
const AWS = require("aws-sdk");
const vogels = require("vogels");


AWS.config.update({ region: process.env?.REGION, logger: console });
vogels.AWS.config.update(AWS.config);


AWS.config.getCredentials(function(err) {
  if (err) console.error("[AWS Config] Error loading credentials:", err.stack);
  else console.log("[AWS Config] Credentials loaded:", AWS.config.credentials);
});

let transporter =
  {
    "alert": nodemailer.createTransport({
      "SES": new AWS.SES({ "apiVersion": "2010-12-01" }),
    }),
    "forgot-password": nodemailer.createTransport({
      "SES": new AWS.SES({ "apiVersion": "2010-12-01" }),
    }),
    "benchmarking": nodemailer.createTransport({
      "SES": new AWS.SES({ "apiVersion": "2010-12-01" }),
    }),
    "maintenance": nodemailer.createTransport({
      "SES": new AWS.SES({ "apiVersion": "2010-12-01" }),
    }),
  };

const SERVICE_JT_API_URL = "https://services.smartjoules.org";
// const SERVICE_JT_API_URL = 'https://zeta2.free.beeceptor.com';

module.exports = {
  SERVICE_JT_API_URL,
  "transporter": transporter,
  "tokenSecret": "b52831dd10cbdff819e6635c82c9dd83",
  "s3Url": "https://s3-us-west-2.amazonAWS.com/assets.smartjoules",
};
