const merge = require("lodash.merge");
const appConfig = require("./config.json");

const defaultConfig = appConfig.development;
const environment = process.env.NODE_ENV || "development";
const environmentConfig = appConfig[environment];
const subscribeTopics = [
  ...new Set([
    "+/feedback/+/+", // Partly Shifted to mqtt-microservice
    "feedback/+/cicd",
    "local/req/+/cicd",
    "global/status/+/diagnostics",
    "+/response/+/diagnostic",
    "+/sendalerts/0001/+",
    "jouletrack/+/+/+", // this group of topics will be used to display realtime data on jouletrack
  ]),
];

const subscribeTopics2 = [
  ...new Set([
    "+/request/+/queriedparameter", // Topic for controller to request queried parameter configuration for that controller.
    "+/queriedParamData/+/recent", // Topic where the controller sends queried parameter offset data after sending uploading QOS data.,
    "+/qos/trigger", // Controller sends trigger that it has finished uploading QOS data.
    "+/componentdata/all/recent", // For batched Component Data packet socket broadcasts
    "+/request/+/recentData",
    "+/data/+/recent",
    "+/response/bmsx/connector-tags" // BMSX discovery response topic
  ]),
];
const config = merge({}, defaultConfig, environmentConfig, { environment, subscribeTopics, subscribeTopics2 });

module.exports = config;
