const policy = require("../api/data/deJoulePolicy.json");
module.exports.bootstrap = function(cb) {

  // It's very important to trigger this callback method when you are finished
  // with the bootstrap!  (otherwise your server will never lift, since it's waiting on the bootstrap)

  sails.on("lifted", () => {
    //A map for policies and chatrooms that will be used while publishing on sockets
    let policyRoomMap = {};
    for (let controller in policy) {
      let roles = policy[controller];
      let { _description } = roles;
      let room = _description.room;

      for (let role in roles) {
        if (role != "_description")
          policyRoomMap[role] = room;
      }
    }
    sails.policyRoomMap = policyRoomMap;
  });
  cb();
};
