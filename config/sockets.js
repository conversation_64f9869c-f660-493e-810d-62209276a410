/**
 * WebSocket Server Settings
 * (sails.config.sockets)
 *
 * These settings provide transparent access to the options for Sails'
 * encapsulated WebSocket server, as well as some additional Sails-specific
 * configuration layered on top.
 *
 * For more information on sockets configuration, including advanced config options, see:
 * http://sailsjs.org/#!/documentation/reference/sails.config/sails.config.sockets.html
 */

module.exports.sockets = {


	/***************************************************************************
     *                                                                          *
     * Node.js (and consequently Sails.js) apps scale horizontally. It's a      *
     * powerful, efficient approach, but it involves a tiny bit of planning. At *
     * scale, you'll want to be able to copy your app onto multiple Sails.js    *
     * servers and throw them behind a load balancer.                           *
     *                                                                          *
     * One of the big challenges of scaling an application is that these sorts  *
     * of clustered deployments cannot share memory, since they are on          *
     * physically different machines. On top of that, there is no guarantee     *
     * that a user will "stick" with the same server between requests (whether  *
     * HTTP or sockets), since the load balancer will route each request to the *
     * Sails server with the most available resources. However that means that  *
     * all room/pubsub/socket processing and shared memory has to be offloaded  *
     * to a shared, remote messaging queue (usually Redis)                      *
     *                                                                          *
     * Luckily, Socket.io (and consequently Sails.js) apps support Redis for    *
     * sockets by default. To enable a remote redis pubsub server, uncomment    *
     * the config below.                                                        *
     *                                                                          *
     * Worth mentioning is that, if `adapter` config is `redis`, but host/port  *
     * is left unset, Sails will try to connect to redis running on localhost   *
     * via port 6379                                                            *
     *                                                                          *
     ***************************************************************************/
	//

	//
	// -OR-
	//
	// "adapter": "memory",
	adapter: 'socket.io-redis',
	"port": 6379,
	//host: '************', //host:'**********',
	// pass: '<redis auth password>',


	/***************************************************************************
     *                                                                          *
     * Whether to expose a 'get /__getcookie' route with CORS support that sets *
     * a cookie (this is used by the sails.io.js socket client to get access to *
     * a 3rd party cookie and to enable sessions).                              *
     *                                                                          *
     * Warning: Currently in this scenario, CORS settings apply to interpreted  *
     * requests sent via a socket.io connection that used this cookie to        *
     * connect, even for non-browser clients! (e.g. iOS apps, toasters, node.js *
     * unit tests)                                                              *
     *                                                                          *
     ***************************************************************************/

	// grant3rdPartyCookie: true,


	/***************************************************************************
     *                                                                          *
     * `beforeConnect`                                                          *
     *                                                                          *
     * This custom beforeConnect function will be run each time BEFORE a new    *
     * socket is allowed to connect, when the initial socket.io handshake is    *
     * performed with the server.                                               *
     *                                                                          *
     * By default, when a socket tries to connect, Sails allows it, every time. *
     * (much in the same way any HTTP request is allowed to reach your routes.  *
     * If no valid cookie was sent, a temporary session will be created for the *
     * connecting socket.                                                       *
     *                                                                          *
     * If the cookie sent as part of the connection request doesn't match any   *
     * known user session, a new user session is created for it.                *
     *                                                                          *
     * In most cases, the user would already have a cookie since they loaded    *
     * the socket.io client and the initial HTML page you're building.         *
     *                                                                          *
     * However, in the case of cross-domain requests, it is possible to receive *
     * a connection upgrade request WITHOUT A COOKIE (for certain transports)   *
     * In this case, there is no way to keep track of the requesting user       *
     * between requests, since there is no identifying information to link      *
     * him/her with a session. The sails.io.js client solves this by connecting *
     * to a CORS/jsonp endpoint first to get a 3rd party cookie(fortunately this*
     * works, even in Safari), then opening the connection.                     *
        if (!req.isSocket) {                                                                          *
        if (!req.isSocket) { You can also pass along a ?cookie query parameter to the upgrade url,    *
        if (!req.isSocket) { which Sails will use in the absence of a proper cookie e.g. (when        *
        if (!req.isSocket) { connecting from the client):                                             *
        if (!req.isSocket) { io.sails.connect('http://localhost:1337?cookie=smokeybear')              *
     *                                                                          *
     * Finally note that the user's cookie is NOT (and will never be) accessible*
     * from client-side javascript. Using HTTP-only cookies is crucial for your *
     * app's security.                                                          *
     *                                                                          *
     ***************************************************************************/
	"beforeConnect": function (handshake, cb) {
		// console.log(handshake)

		// console.log(sails.sockets.getId(handshake), 'socketId');
		let authHeader = handshake.headers.authorization || handshake._query.token;
		if (!authHeader)
			return cb("undefined", false);
		let tokenArr = authHeader.split(" ");
		if (tokenArr.length != 2)
			return cb("undefined", false);
		let token = tokenArr[1];
		jwtService.verify(token, (err, data) => {
			if (err) {
				cb("undefined", false);
			}
			else
				cb(undefined, true);
		});
	},


	/***************************************************************************
     *                                                                          *
     * `afterDisconnect`                                                        *
     *                                                                          *
     * This custom afterDisconnect function will be run each time a socket      *
     * disconnects                                                              *
     *                                                                          *
     ***************************************************************************/
	"afterDisconnect": async function (session, socket, cb) {
		//Remove socket Id From cache
		// list becomes empty delete it and the user
		// let authHeader = socket.handshake.headers.authorization || socket.handshake.query.token;
		// let socketId = socket.id;
		// if (!authHeader)
		//     return cb(Error("Auth not present in disconnecting " + socket));
		// let tokenArr = authHeader.split(' ');
		// if (tokenArr.length != 2)
		//     return cb("undefined", false);
		// let token = tokenArr[1];
		// let payload = jwtService.decode(token);
		// let { _h_, id } = payload;
		// if (!_h_ || !id)
		//     return cb(Error("Invalid Auth Header", socket));
		// try {
		//     let remInfo = await cacheService.srem(_h_, socketId);
		//     let number = await cacheService.scard(_h_);
		//     if (number < 1) {
		//         let delInfo =await cacheService.del(_h_);
		//         let delU = await cacheService.del('u_' + _h_);
		//     }
		//     cb();
		// } catch (e) {
		//     sails.log.error(e);
		//     cb(Error(e));
		// }

		cb();
	},

	/***************************************************************************
     *                                                                          *
     * `transports`                                                             *
     *                                                                          *
     * A array of allowed transport methods which the clients will try to use.  *
     * On server environments that don't support sticky sessions, the "polling" *
     * transport should be disabled.                                            *
     *                                                                          *
     ***************************************************************************/
	"transports": ["websocket"],
	"_hookTimeout": 60000


};
if (process.env.NODE_ENV === "production" || process.env.NODE_ENV === "prod") {
	module.exports.sockets.adapter = "socket.io-redis";
	module.exports.sockets.host = process.env.CACHE_HOST;
}
