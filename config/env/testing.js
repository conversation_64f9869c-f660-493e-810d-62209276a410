/*
	Environment for Testing
*/
let keys = null;
try {
  keys = require("./keys");

} catch (e) {
  keys = {
    // "ACCESS_KEY": process.env.ACCESS_KEY,
    // "SECRET_KEY": process.env.SECRET_KEY,
    // "REGION": process.env.REGION,
  };
}

module.exports = {
  // configuration for testing purposes
  "log": {
    "level": "silent",
  },
  "hooks": {
    "grunt": false,
  },
  "models": {
    "connection": "dynamoDBAsiaPacific",
    "migrate": "safe",
  },
  "connections": {
    "dynamoDBAsiaPacific": {
      "adapter": "sails-dynamodb",
    },
  },
  "sockets": {
    "adapter": "memory",
  },
  "cachePort": 6379,
  "cacheHost": (process.env.TEST_ENV === "server" ? "redis" : "localhost"), //redis is used while CICD pipeline execution
};
