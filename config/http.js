/**
 * HTTP Server Settings
 * (sails.config.http)
 *
 * Configuration for the underlying HTTP server in Sails.
 * Only applies to HTTP requests (not WebSockets)
 *
 * For more information on configuration, check out:
 * http://sailsjs.org/#!/documentation/reference/sails.config/sails.config.http.html
 */
// eslint-disable-next-line
const uuid = require('uuid');
const compression = require('compression');
const globals = require('../api/utils/global');

module.exports.http = {

  /** **************************************************************************
     *                                                                           *
     * Express middleware to use for every Sails request. To add custom          *
     * middleware to the mix, add a function to the middleware config object and *
     * add its key to the "order" array. The $custom key is reserved for         *
     * backwards-compatibility with Sails v0.9.x apps that use the               *
     * `customMiddleware` config option.                                         *
     *                                                                           *
     *************************************************************************** */

  middleware: {

    /** *************************************************************************
     *                                                                          *
     * The order in which middleware should be run for HTTP request. (the Sails *
     * router is invoked by the "router" middleware below.)                     *
     *                                                                          *
     ************************************************************************** */

    order: [
      'requestLogger',
      'startRequestTimer',
      'bodyParser',
      'compression',
      'handleBodyParserError',
      'methodOverride',
      'router',
      '404',
      '500',
    ],
    compression: compression(),
    requestLogger(req, res, next) {
      res.on('finish', () => {
        globals.requestLogger(req, res);
      });
      next();
    },

    /** *************************************************************************
     *                                                                          *
     * The body parser that will handle incoming multipart HTTP requests. By    *
     * default as of v0.10, Sails uses                                          *
     * [skipper](http://github.com/balderdashy/skipper). See                    *
     * http://www.senchalabs.org/connect/multipart.html for other options.      *
     *                                                                          *
     * Note that Sails uses an internal instance of Skipper by default; to      *
     * override it and specify more options, make sure to "npm install skipper" *
     * in your project first.  You can also specify a different body parser or  *
     * a custom function with req, res and next parameters (just like any other *
     * middleware function).                                                    *
     *                                                                          *
     ************************************************************************** */

    // bodyParser: require('skipper')({strict: true})

  },
};
