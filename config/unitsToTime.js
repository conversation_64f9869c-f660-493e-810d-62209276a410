/**
 * Created by abhinav on 6/23/17.
 */
module.exports.inJson = {

	"Amps":
	{
		"minutes": "RAW",
		"hours": "AVG",
		"days": "AVG",
		"months": "AVG"
	}
	,
	"Volt":
	{
		"minutes": "RAW",
		"hours": "AVG",
		"days": "AVG",
		"months": "AVG"
	}
	,
	"KvAh":
	{
		"minutes": "NA",
		"hours": "DIFF",
		"days": "DIFF",
		"months": "DIFF"
	}
	,
	"KW":
	{
		"minutes": "RAW",
		"hours": "AVG",
		"days": "AVG",
		"months": "AVG"
	}
	,
	"KvA":
	{
		"minutes": "RAW",
		"hours": "AVG",
		"days": "AVG",
		"months": "AVG"
	}
	,
	"F":
	{
		"minutes": "RAW",
		"hours": "AVG",
		"days": "AVG",
		"months": "AVG"
	}
	,
	"C":
	{
		"minutes": "RAW",
		"hours": "AVG",
		"days": "AVG",
		"months": "AVG"
	},
	"TR":
	{
		"minutes": "RAW",
		"hours": "AVG",
		"days": "AVG",
		"months": "AVG"
	}
	,
	"Psi":
	{
		"minutes": "RAW",
		"hours": "AVG",
		"days": "AVG",
		"months": "AVG"
	}
	,
	"Hz":
	{
		"minutes": "RAW",
		"hours": "NA",
		"days": "NA",
		"months": "NA"
	}
	,
	"GPM":
	{
		"minutes": "RAW",
		"hours": "AVG",
		"days": "AVG",
		"months": "AVG"
	}
	,
	"kVAr":
	{
		"minutes": "RAW",
		"hours": "AVG",
		"days": "AVG",
		"months": "AVG"
	}
	,
	"On/Off":
	{
		"minutes": "RAW",
		"hours": "NA",
		"days": "NA",
		"months": "NA"
	}
	,
	"ft":
	{
		"minutes": "RAW",
		"hours": "AVG",
		"days": "AVG",
		"months": "AVG"
	}
	,
	"kW/TR":
	{
		"minutes": "RAW",
		"hours": "AVG",
		"days": "AVG",
		"months": "AVG"
	}
	,
	"Hrs":
	{
		"minutes": "NA",
		"hours": "DIFF",
		"days": "DIFF",
		"months": "DIFF"
	}
	,
	"%AGE":
	{
		"minutes": "RAW",
		"hours": "AVG",
		"days": "AVG",
		"months": "AVG"
	}
	,
	"SETPOINT":
	{
		"minutes": "RAW",
		"hours": "NA",
		"days": "NA",
		"months": "NA"
	},
	"PF": {
		"minutes": "RAW",
		"hours": "AVG",
		"days": "AVG",
		"months": "AVG"
	}

};