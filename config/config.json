{"production": {"API_SERVICE_URL": "http://prod-controller-feedback:1337", "API_V2_SERVICE_URL": "http://prod-jt-api-v2:1337", "API_STAGING_SERVICE_URL": "http://stg-jouletrack-api.stg:1337", "CACHE_HOST": "redis-master.core", "CACHE_PORT": "6379", "BROKER_HOST": "a1hz6xr4hc68uh.iot.us-west-2.amazonaws.com", "BROKER_KEY": "/certs/private.key", "BROKER_CERT": "/certs/cert.crt", "BROKER_TRUSTED_CA": "/certs/rootCA.pem", "BROKER_PORT": "8883", "QOS_CONSUMER_SERVICE_URL": "http://prod-qos-consumer", "SENTRY_DSN": "https://<EMAIL>/55", "SENTRY_TRACE_SAMPLE_RATE": 0.5, "SENTRY_DEBUG": false}, "development": {"API_SERVICE_URL": "http://localhost:1336", "API_V2_SERVICE_URL": "http://localhost:1337", "API_STAGING_SERVICE_URL": "http://stg-jouletrack-api.stg:1337", "CACHE_HOST": "127.0.0.1", "CACHE_PORT": "6379", "BROKER_HOST": "a1hz6xr4hc68uh-ats.iot.ap-south-1.amazonaws.com", "BROKER_KEY": "/certs-dev/private.key", "BROKER_CERT": "/certs-dev/cert.crt", "BROKER_TRUSTED_CA": "/certs-dev/rootCA.pem", "BROKER_PORT": "8883", "QOS_CONSUMER_SERVICE_URL": "http://localhost:3000", "SENTRY_DSN": "https://<EMAIL>/55", "SENTRY_TRACE_SAMPLE_RATE": 0.5, "SENTRY_DEBUG": false}}