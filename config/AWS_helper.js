const moment = require("moment-timezone");
const AWS = require("aws-sdk");
const zlib = require("zlib");

// var AWS = require('aws-sdk');
const DataDeviceService = require("../api/services/dataDeviceService");
AWS.config.update({ region: process.env?.REGION, logger: console });
const s3 = new AWS.S3();
const docClient = new AWS.DynamoDB.DocumentClient();
const influxClient = require("../api/services/influxEnterPriseService");
// AWS.config.loadFromPath(__dirname + '/aws-config.json');
const { DATA_SOURCE } = process.env;

module.exports.helper = {
  "dynamo": docClient,
  "queryData": function(start, end, deviceId, callback, finalCallback) {
    let complete_data = { "Count": 0, "Items": [] };
    const startTime = moment(start, "YYYY-MM-DD HH:mm:ss").utcOffset("330m").format("YYYY-MM-DDTHH:mm:ssZ");
    const endTime = moment(end, "YYYY-MM-DD HH:mm:ss").utcOffset("330m").add("1", "seconds").format("YYYY-MM-DDTHH:mm:ssZ");
    DataDeviceService.getDataBetweenTwoTimeStampsFromInfluxdb({ deviceId, startTime, endTime })
      .then((data) => {
        let _formattedResult = {
          Count: 0,
          Items: [],
        };
        _formattedResult.Items = data.map((it) => {
          return {
            data: JSON.stringify(it.data),
            timestamp: it.timestamp,
          };
        });
        _formattedResult.Count = _formattedResult.Items.length;
        return finalCallback(_formattedResult);
      })
      .catch((err) => {
        console.error(err);
        return finalCallback(err);
      });
  },
  "checkExist": function(arr) {
    try {
      let err = false;
      if (typeof arr != "undefined" && arr.constructor.name == "Array") {
        arr.forEach(function(v, i, a) {
          if (typeof v == "undefined" || v == "") {
            err = true;
          }
        });
      } else {
        err = true;
      }
      if (err) return true;
      else return false;
    } catch (e) {
      return true;
    }
  },
  "checkTime": function(arr) {
    try {
      let err = false;
      if (typeof arr != "undefined" && arr.constructor.name == "Array") {
        arr.forEach(function(v, i, a) {
          if (v.includes("Invalid")) {
            err = true;
          }
        });
      } else {
        err = true;
      }
      if (err) return true;
      else return false;
    } catch (e) {
      return true;
    }
  },
  "getData": function(data_obj) {
    return new Promise((resolve, reject) => {
      try {
        let startTime = data_obj.startTime;
        let endTime = data_obj.endTime;
        let deviceId = data_obj.deviceId;
        let reqst = data_obj.reqst;
        // let type = data_obj.type || null;
        let groupBy = data_obj.groupBy;
        let result = [];

        if (this.checkExist([deviceId])) {
          reject(new Error("Required deviceId"));
          return;
        }
        // if (type) { } else
        // if (this.checkExist(reqst)) {
        // 	reject(new Error("Required parameters"));
        // 	return;
        // }
        if (this.checkTime([startTime])) {
          reject(new Error("Invalid start and end time"));
          return;
        }

        this.queryData(
          startTime,
          endTime,
          deviceId,
          function(partial_data) {
            //    res.send(result);
          },
          (complete_data) => {
            console.log(complete_data);
            if (data_obj.type == "custom") {
              result.push(complete_data.Items);
            }
            if (data_obj.type == "candle") result.push(this.filterDataCandel(complete_data.Items, groupBy, reqst));
            else if (data_obj.type == "line") result.push(this.filterData(complete_data.Items, groupBy, reqst));
            else if (data_obj.type == "spec") result.push(this.filterDataspec(complete_data.Items, groupBy, reqst));
            else if (data_obj.type == "box") result.push(this.filterDatabox(complete_data.Items, reqst));
            else if (data_obj.type == "thermalIndex") result.push(complete_data.Items);
            else if (data_obj.type == "heatmap") result.push(this.filterDataHeat(complete_data.Items, startTime, reqst, endTime));
            else result.push(complete_data.Items);
            resolve(result);
          },
        );
      } catch (e) {
        reject(new Error(e));
      }
    });
  },
  "getDataFromInflux": function(data_obj) {
    return new Promise((resolve, reject) => {
      try {
        let startTime = data_obj.startTime;
        let endTime = data_obj.endTime;
        let deviceId = data_obj.deviceId;
        let reqst = data_obj.reqst;
        // let type = data_obj.type || null;
        let groupBy = data_obj.groupBy;
        let result = [];

        if (this.checkExist([deviceId])) {
          reject(new Error("Required deviceId"));
          return;
        }
        // if (type) { } else
        // if (this.checkExist(reqst)) {
        // 	reject(new Error("Required parameters"));
        // 	return;
        // }
        if (this.checkTime([startTime])) {
          reject(new Error("Invalid start and end time"));
          return;
        }

        this.queryData(
          startTime,
          endTime,
          deviceId,
          function(partial_data) {
            //    res.send(result);
          },
          (complete_data) => {
            if (data_obj.type == "custom") {
              result.push(complete_data.Items);
            }
            if (data_obj.type == "candle") result.push(this.filterDataCandel(complete_data.Items, groupBy, reqst));
            else if (data_obj.type == "line") result.push(this.filterData(complete_data.Items, groupBy, reqst));
            else if (data_obj.type == "spec") result.push(this.filterDataspec(complete_data.Items, groupBy, reqst));
            else if (data_obj.type == "box") result.push(this.filterDatabox(complete_data.Items, reqst));
            else if (data_obj.type == "thermalIndex") result.push(complete_data.Items);
            else if (data_obj.type == "heatmap") result.push(this.filterDataHeat(complete_data.Items, startTime, reqst, endTime));
            else result.push(complete_data.Items);
            resolve(result);
          },
        );
      } catch (e) {
        reject(new Error(e));
      }
    });
  },
  "filterParam": function(data, paramsArr, groupBy) {
    if (!groupBy) {
      groupBy = "minute";
    }

    let returner = {};
    paramsArr.forEach((p) => {
      returner[p] = [];
    });
    data.forEach((indata) => {
      let paramData = typeof indata.data == "object" ? indata.data : helper.toJson(indata.data);
      paramsArr.forEach((v) => {
        if (paramData[v]) {
          returner[v].push(paramData[v]);
        }
      });
    });
    return returner;
  },
  "filterDataHeat": function(data, startTime, paramsArr, endTime) {
    let returner = {};
    paramsArr.forEach((p) => {
      returner[p] = {};
    });

    try {
      // group by hour params data, so
      // returner[param][hour-0] = [data..in..0th..hour]
      // returner[param][hour-1] = [data..in..1st..hour]
      data.forEach((indata) => {
        let paramData = helper.toJson(indata.data);
        let beginTime = moment(indata.timestamp).startOf("hours").format("YYYY-MM-DD HH:mm");
        paramsArr.forEach((v) => {
          if (paramData[v]) {
            if (returner[v][beginTime]) {
              returner[v][beginTime].push(paramData[v]);
            } else {
              returner[v][beginTime] = [paramData[v]];
            }
          }
        });
      });
      let min, max, now_check;

      for (let i in returner) {
        Object.keys(returner[i]).forEach((v) => {
          returner[i][v] = this.avg(returner[i][v]);
          now_check = parseFloat(returner[i][v]);
          min = min < now_check ? min : now_check;
          max = max > now_check ? max : now_check;
        });
      }

      let difference = moment(endTime).diff(moment(startTime), "days");
      let hawk = [];
      for (let i = 0; i < difference; i++) {
        // going with days
        for (let j = 0; j < 24; j++) {
          // going with hours
          hawk.push([i, j, 0]);
        }
      }
      let st = moment(startTime);
      Object.keys(returner).forEach((v) => {
        Object.keys(returner[v]).forEach((v1) => {
          let d = moment(v1).diff(st, "days");
          let h = moment(v1).hours();
          let exct = d * 24 + h;
          hawk[exct][2] = returner[v][v1];
        });
        returner[v] = hawk;
      });
      returner["minimum"] = min;
      returner["maximum"] = max;
      return returner;
    } catch (e) {
      sails.log.error(e);
    }
  },
  "avg": function(data) {
    data = this.removeNan(data);
    let leng = data.length;
    let total = 0;
    data.forEach((v) => {
      total += parseFloat(v);
    });
    let avgFloatVal = helper.returnFilteredNumber(total / leng);

    return String(avgFloatVal);
  },
  "avg2": function(data) {
    data = this.removeNan(data);
    let leng = data.length;
    let total = 0;
    data.forEach((v) => {
      total += parseInt(v);
    });
    return total / leng;
  },

  "filterData": function(data, groupBy, paramsArr) {
    try {
      if (!groupBy) groupBy = "minutes";
      let resObj = {};
      if (paramsArr.length == 0) {
        return data;
      }
      // let minuteFinalObj = {};
      // let tmp = 0;
      let startTs = null;
      let tmpObj = {};
      data.forEach((ele, index) => {
        let ts = moment(ele.timestamp).startOf(groupBy).format("YYYY-MM-DD HH:mm");
        let devData = helper.toJson(ele.data);
        for (let key in devData) {
          devData[key.toLowerCase()] = devData[key];
        }
        if (!startTs) startTs = ts;
        if (groupBy === "minutes" || groupBy === "m" || groupBy === "minute") {
          paramsArr.forEach((param) => {
            if (devData[param] != undefined && devData[param] != null && devData[param] !== "") {
              if (!resObj[param]) {
                //case for first value in each group by
                resObj[param] = [];
              }
              resObj[param].push([moment(ts).unix() * 1000, parseFloat(devData[param])]);
            }
          });
        } else {
          paramsArr.forEach((param) => {
            if (devData[param] != undefined && devData[param] != null) {
              if (!tmpObj[param]) {
                //case for first value in each group by
                tmpObj[param] = parseFloat(devData[param]);
                tmpObj[param + "counter"] = 1;
              } else {
                tmpObj[param] += parseFloat(devData[param]);
                tmpObj[param + "counter"] += 1;
              }
            }
          });
          if (ts != startTs || index == data.length - 1) {
            //push calculated data for hour
            paramsArr.forEach((param) => {
              if (tmpObj[param] === undefined || tmpObj[param] === null) {
                return;
              }
              if (!resObj[param]) {
                resObj[param] = [];
              }
              let calcValue = tmpObj[param] / tmpObj[param + "counter"];
              if (!calcValue || !isNaN(calcValue)) resObj[param].push([moment(startTs).unix() * 1000, Math.round(calcValue * 100) / 100]);
            });
            tmpObj = {};
            startTs = ts;
          }
        }
      });
      return resObj;
    } catch (e) {
      throw new Error(e);
    }
  },
  "maxmin": function(arr, param) {
    let maximum = 0;
    let minimum = 10000000;
    arr.forEach((i) => {
      if (i[param] && i[param] > maximum) {
        maximum = i[param];
      }
      if (i[param] && i[param] < minimum) {
        minimum = i[param];
      }
    });
    if (maximum == 0 && minimum == 10000000) {
      throw new Error();
    }
    return [parseFloat(maximum), parseFloat(minimum)];
  },
  "start": function(arr, param) {
    for (let i = 0; i < arr.length; i++) {
      if (arr[i][param]) {
        return arr[i][param];
      }
    }
  },
  "end": function(arr, param) {
    for (let i = arr.length - 1; i >= 0; i--) {
      if (arr[i][param]) {
        return arr[i][param];
      }
    }
  },
  "filterDataCandel": function(data, groupBy, params) {
    try {
      if (!groupBy) {
        groupBy = "hours";
      }
      if (!params || typeof params == "undefined") {
        return "Params are required";
      }
      let observer = {};
      let returner = {};

      params.forEach((p) => {
        returner[p] = [];
      });
      data.forEach((indata) => {
        let paramData = helper.toJson(indata.data);
        let ts = indata.timestamp;
        for (let key in paramData) {
          paramData[key.toLowerCase()] = paramData[key];
        }
        ts = moment(ts).startOf(groupBy).format("YYYY-MM-DD HH:mm");
        let unix_tm = moment(ts).unix() * 1000;
        let temp = {};

        params.forEach((v) => {
          if (paramData[v]) {
            temp[v] = paramData[v];
          }
        });
        if (!observer[unix_tm]) {
          observer[unix_tm] = [temp];
        } else {
          observer[unix_tm].push(temp);
        }
      });

      Object.keys(observer).forEach((v, i) => {
        let now_data = observer[v];
        now_data = this.removeNan(now_data);
        Object.keys(returner).forEach((v1) => {
          try {
            returner[v1].push([parseInt(v), parseFloat(this.start(now_data, v1)), ...this.maxmin(now_data, v1), parseFloat(this.end(now_data, v1))]);
          } catch (e) {
            returner[v1].push();
          }
        });
      });

      return returner;
    } catch (e) {
      return "server error";
    }
  },
  "filterDataspec": function(data, groupBy, params) {
    try {
      if (!groupBy) {
        groupBy = "minutes";
      }
      if (!params || typeof params == "undefined") {
        return "Params are required";
      }
      // let observer = {};
      let returner = {};

      params.forEach((p) => {
        returner[p] = {};
      });

      data.forEach((indata) => {
        let paramData = helper.toJson(indata.data);
        for (let key in paramData) {
          paramData[key.toLowerCase()] = paramData[key];
        }
        params.forEach((v) => {
          if (paramData[v]) {
            let temp_val = helper.returnFilteredNumber(paramData[v]);
            if (returner[v][temp_val]) {
              returner[v][temp_val] += 1;
            } else {
              returner[v][temp_val] = 1;
            }
          }
        });
      });
      // let to_return = [];
      let final_ret = {};
      Object.keys(returner).map((d) => {
        final_ret[d] = [];
        Object.keys(returner[d]).map((d1) => {
          final_ret[d].push([d1, returner[d][d1]]);
        });
      });

      return final_ret;
    } catch (e) {
      sails.log.error(e);
      return "server error";
    }
  },
  "removeNan": function(data) {
    return data.filter((val) => !isNaN(Number(val)));
  },
  "boxplotter": function(data) {
    data = this.removeNan(data);
    let leng = data.length;
    if (leng == 0) {
      return [0, 0, 0, 0, 0];
    }
    if (leng == 1) {
      let d = data[0];
      return [d, d, d, d, d];
    }
    let max = data[leng - 1];
    leng -= 1;
    let min = data[0];
    //  lq =lower quatile and uq is upper quatile
    let med = parseInt(leng / 2);
    if (Number.isInteger(med)) {
      med = data[med];
    } else {
      med = (data[med] + data[med + 1]) / 2;
    }
    let uq = (3 / 4) * leng;
    if (Number.isInteger(uq)) {
      uq = Math.floor(uq);
      uq = data[uq];
    } else {
      uq = Math.floor(uq);
      uq = (data[uq] + data[uq + 1]) / 2;
    }
    let lq = (1 / 4) * leng;
    if (Number.isInteger(lq)) {
      lq = Math.floor(lq);
      lq = data[lq];
    } else {
      lq = Math.floor(lq);
      lq = (data[lq] + data[lq + 1]) / 2;
    }

    return [min, lq, med, uq, max];
  },
  "partition": function(items, left, right) {
    let pivot = items[Math.floor((right + left) / 2)],
      i = left,
      j = right;

    while (i <= j) {
      while (items[i] < pivot) {
        i++;
      }

      while (items[j] > pivot) {
        j--;
      }

      if (i <= j) {
        this.swap(items, i, j);
        i++;
        j--;
      }
    }

    return i;
  },
  "swap": function(items, firstIndex, secondIndex) {
    let temp = items[firstIndex];
    items[firstIndex] = items[secondIndex];
    items[secondIndex] = temp;
  },
  "quickSort": function(items, left, right) {
    let index;

    if (items.length > 1) {
      left = typeof left != "number" ? 0 : left;
      right = typeof right != "number" ? items.length - 1 : right;

      index = this.partition(items, left, right);

      if (left < index - 1) {
        this.quickSort(items, left, index - 1);
      }

      if (index < right) {
        this.quickSort(items, index, right);
      }
    }

    return items;
  },
  "filterDatabox": function(data, params) {
    try {
      let returner = {};
      params.forEach((p) => {
        returner[p] = [];
      });
      data.forEach((indata) => {
        let paramData = helper.toJson(indata.data);
        for (let key in paramData) {
          paramData[key.toLowerCase()] = paramData[key];
        }
        params.forEach((v) => {
          returner[v].push(paramData[v]);
        });
      });
      let final_ret = {};
      Object.keys(returner).map((d) => {
        let vals = returner[d];
        for (let i = 0; i < vals.length; i++) {
          vals[i] = parseFloat(vals[i]);
        }
        vals = this.quickSort(vals);

        final_ret[d] = [...this.boxplotter(vals)];
      });
      return final_ret;
    } catch (e) {
      sails.log.error(e);
      return "server error";
    }
  },
  // uploadFile: function(req, res, userId) {
  //     // return new Promise((resolve, reject) => {

  //     //         req.file('avatar').upload({
  //     //             adapter: require('skipper-s3'),
  //     //             bucket: 'assets.smartjoules',
  //     //             key: "AKIAIUYZ5CGDQXTWS4NA",
  //     //             secret: "IKmuo2LCQDnXS8sH,3/wvZ/RnWn2cXxc5s7TD6B1V",
  //     //             ACL: 'public-read',
  //     //             endpoint: 'assets.smartjoules.s3.amazonaws.com'
  //     //         }, function(err, filesUploaded) {
  //     //             if (err) return reject(err);
  //     //             return resolve({
  //     //                 files: filesUploaded,
  //     //                 textParams: req.params.all()
  //     //             });
  //     //         });

  //     //     })
  //     return new Promise((resolve, reject) => {
  //         s3Bucket.upload(req.file('avatar'), function(err, data) {
  //             console.log(err)
  //             if (err) {
  //                 console.log('Error uploading data: ', data);
  //                 // fs.rmdirSync(__dirname + '/assets')
  //                 rmDir(__dirname + '/assets')
  //                     // fs.unlink(__dirname + '/assets/resized/' + userId + '.jpeg')
  //                     // fs.unlink(__dirname + '/assets/' + userId + '.jpeg')
  //                 reject(err)

  //             } else {
  //                 console.log('succesfully uploaded the image!', data);
  //                 // fs.rmdirSync(__dirname + '/assets')
  //                 rmDir(__dirname + '/assets')
  //                     // fs.unlink(__dirname + '/assets/resized/' + userId + '.jpeg')
  //                     // fs.unlink(__dirname + '/assets/' + userId + '.jpeg')
  //                 resolve({ data: data, filename: userId + '.jpeg' });
  //             }
  //         });
  //         // req.file('avatar').upload({
  //         //     dirname: __dirname + "/assets"
  //         // }, (er, files) => {
  //         //     if (er) return res.negotiaite(er);
  //         //     console.log(files)
  //         //         // var file = files[0].filename;
  //         //         // fileType = file.split('.').pop();
  //         //     var filename = __dirname + '/assets/resized/' + userId + '.jpeg';
  //         //     console.log(name, 'namemem')
  //         //     var name = files[0].fd;
  //         //     Jimp.read(name, function(err, lenna) {
  //         //         if (err) {
  //         //             console.log(err, 'error')
  //         //             throw err
  //         //         };
  //         //         lenna.resize(200, 200) // resize
  //         //             .quality(100) // set JPEG quality
  //         //             // .greyscale() // set greyscale
  //         //             .write(filename); // save

  //         //         fs.readFile(filename, (er, fileData) => {
  //         //             try {
  //         //                 var base64data = new Buffer(fileData, 'binary');
  //         //             } catch (err) {
  //         //                 return reject({ message: 'Cannot upload... Try again...', err: err })
  //         //             }

  //         //             var data = { Key: userId + '.jpeg', Body: base64data, ACL: 'public-read' };

  //         //             s3Bucket.putObject(data, function(err, data) {
  //         //                 console.log(err)
  //         //                 if (err) {
  //         //                     console.log('Error uploading data: ', data);
  //         //                     // fs.rmdirSync(__dirname + '/assets')
  //         //                     rmDir(__dirname + '/assets')
  //         //                         // fs.unlink(__dirname + '/assets/resized/' + userId + '.jpeg')
  //         //                         // fs.unlink(__dirname + '/assets/' + userId + '.jpeg')
  //         //                     reject(err)

  //         //                 } else {
  //         //                     console.log('succesfully uploaded the image!', data);
  //         //                     // fs.rmdirSync(__dirname + '/assets')
  //         //                     rmDir(__dirname + '/assets')
  //         //                         // fs.unlink(__dirname + '/assets/resized/' + userId + '.jpeg')
  //         //                         // fs.unlink(__dirname + '/assets/' + userId + '.jpeg')
  //         //                     resolve({ data: data, filename: userId + '.jpeg' });
  //         //                 }
  //         //             });

  //         //         })
  //         //     });
  //         // })
  //     })

  // },
  "basicFilter": function(allData) {
    let returner = [];
    let temp = {};
    allData.forEach((indata) => {
      let paramData = typeof indata.data == "object" ? indata.data : helper.toJson(indata.data);
      let KW = paramData.KW;
      let ts = moment(indata["timestamp"]).startOf("minute").format("YYYY-MM-DD HH:mm");
      if (KW) {
        temp[ts] = KW;
      }
    });
    Object.keys(temp).forEach((d) => {
      returner.push({
        "time": d,
        "kw": parseInt(temp[d]),
      });
    });

    return returner;
  },
  "filterBuilding": function(alldata, comparer) {
    let returner = {};
    // let leng = [];
    let ts, kw; //starthour;
    alldata.forEach((data) => {
      data.forEach((inside) => {
        ts = inside.time;
        kw = inside.kw;
        if (returner[ts]) {
          returner[ts].push(kw);
        } else {
          returner[ts] = [kw];
        }
      });
    });
    let buildingLoad = {};
    let shour;
    let sums;
    // let counter;
    Object.keys(returner).forEach((nts) => {
      if (returner[nts].length == comparer) {
        sums = returner[nts].reduce((a, b) => a + b, 0);
        shour = moment(nts).startOf("hour").format("YYYY-MM-DD HH:mm");
        if (buildingLoad[shour]) {
          buildingLoad[shour].push(sums);
        } else {
          buildingLoad[shour] = [sums];
        }
      }
    });
    let totalLoad = {};
    let tmp1, tmp2, tmp3;
    Object.keys(buildingLoad).forEach((nts) => {
      tmp1 = moment(nts).unix() * 1000;
      tmp2 = Math.round(this.avg2(buildingLoad[nts]) * 100) / 100;
      tmp3 = moment(nts).startOf("day").format("YYYY-MM-DD HH:mm");
      if (totalLoad[tmp3]) {
        totalLoad[tmp3].push([tmp1, tmp2]);
      } else {
        totalLoad[tmp3] = [[tmp1, tmp2]];
      }
    });

    return totalLoad;
  },
  "syncToS3": async (params) => {
    try {
      let tableObj = {};
      let oldArr = params[params.table]["oldObj"];
      let newArr = params[params.table]["newObj"];
      let { table, pkName, siteId, ts, yearMonth } = params;
      oldArr.map((d) => {
        tableObj[d[pkName]] = {
          "oldObj": [d],
          "newObj": [],
        };
      });
      newArr.map((d) => {
        if (!tableObj[d[pkName]]) {
          tableObj[d[pkName]] = {
            "oldObj": [],
            "newObj": [],
          };
        }
        tableObj[d[pkName]]["newObj"] = [d];
      });
      let s3KeysArr = [];
      for (let key in tableObj) {
        let obj = { ...params };
        obj[table] = tableObj[key];
        let s3Key = `${siteId}/${table}/${key}/${yearMonth}/${ts}.log.gz`;
        delete obj.yearMonth;
        delete obj.pkName;
        let dataObj = zlib.gzipSync(JSON.stringify(params));

        let s3params = {
          "Bucket": "dejoule.changelogs",
          "Key": s3Key,
          "Body": dataObj,
          "ContentType": "application/json",
          "ContentEncoding": "gzip",
        };
        await s3.putObject(s3params).promise();
        s3KeysArr.push(s3Key);
      }
      return s3KeysArr;
    } catch (err) {
      sails.log.error(err);
      return 0;
    }
  },
};
