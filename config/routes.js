/* eslint-disable sort-keys */
module.exports.routes = {
	//alert controller
	"POST /notification": {
		"controller": "Alert",
		"action": "fetchAlerts",
	},

	"GET /latestNotifications": {
		"controller": "Alert",
		"action": "latestNotifications",
	},

	"GET /lastHourNotifications": {
		"controller": "Alert",
		"action": "lastHourNotifications",
	},

	//auth controller

	"POST /v1/auth/logout": {
		"controller": "Auth",
		"action": "logout",
	},
	"GET /v1/hello": {
		"controller": "Auth",
		"action": "hello",
	},
	"POST /v1/auth/login": {
		"controller": "Auth",
		"action": "login",
	},
	"POST /v1/app/grantToken": {
		"controller": "Auth",
		"action": "grantToken",
	},
	"POST /v1/auth/registerSocket": {
		"controller": "Auth",
		"action": "registerSocket",
	},
	"POST /v1/auth/socketBroadcast": {
		"controller": "Auth",
		"action": "socketBroadcast",
	},
	"POST /v1/auth/issueToken": {
		"controller": "auth",
		"action": "issueToken",
	},
	"POST /v1/verifyToken": {
		"controller": "auth",
		"action": "verifyToken",
	},
	"/": {
		"controller": "Auth",
		"action": "homepage",
	},
	"POST  /v1/getToken": {
		"controller": "Auth",
		"action": "getToken",
	},
	"GET  /auth": {
		"controller": "Auth",
		"action": "invalid",
	},
	"POST  /users/forgotpassword": {
		"controller": "Auth",
		"action": "forgotPassword",
	},
	"POST  /users/forgotpasswordverify": {
		"controller": "Auth",
		"action": "forgotPasswordVerify",
	},

	//baseline controller
	"GET /site/:siteId/getDataFromPreviousBaseline": {
		"controller": "Baseline",
		"action": "getDataFromPreviousBaseline",
	},
	"GET /m1/site/:siteId/getDataFromPreviousBaseline": {
		"controller": "Baseline",
		"action": "getDataFromPreviousBaseline_new",
	},
	"POST  /site/:siteId/baseline/add": {
		"controller": "Baseline",
		"action": "addBaseline",
	},
	"DELETE  /baseline/:siteId/:startDate": {
		"controller": "Baseline",
		"action": "deleteBaseline",
	},
	"PUT  /baseline/:siteId/:startDate": {
		"controller": "Baseline",
		"action": "updateBaseline",
	},
	"GET  /site/:siteId/baseline": {
		"controller": "Baseline",
		"action": "getBaseline",
	},
	"GET /site/:siteId/currentBaseline": {
		"controller": "Baseline",
		"action": "currentBaseline",
	},
	"GET /:siteId/savings": {
		"controller": "Baseline",
		"action": "amountSaved",
	},
	"GET /m1/:siteId/savings": {
		"controller": "Baseline",
		"action": "amountSaved_new",
	},
	"POST /production": {
		"controller": "Baseline",
		"action": "productionData"
	},
	"POST /production/add": {
		"controller": "Baseline",
		"action": "addProductionData"
	},

	//command controller
	"POST /v1/command": {
		"controller": "command",
		"action": "create",
	},
	"POST /v1/command/fixJBinController": {
		"controller": "command",
		"action": "fixJBCommand",
	},
	"POST /v1/command/configUpdate": {
		"controller": "command",
		"action": "updateDevices",
	},

	"POST /latestCommand": {
		"controller": "command",
		"action": "latestCommand",
	},
	//protoconvert apis
	"POST /v1/proto/command/reached": {
		"controller": "command",
		"action": "updateProtoCommandStatus",
	},
	"POST /v1/proto/command/executed": {
		"controller": "command",
		"action": "updateProtoCommandStatus",
	},
	//Component controller
	"GET /components/:siteId": {
		"controller": "component",
		"action": "getAllComponents",
	},
	"GET /:siteId/components/status": {
		"controller": "component",
		"action": "getAllComponentsStatus",
	},
	"DELETE /component/:siteId": {
		"controller": "component",
		"action": "deleteComponent",
	},
	"POST /v1/component": {
		"controller": "component",
		"action": "addComponent",
	},
	"GET /v1/component/:cid": {
		"controller": "component",
		"action": "getComponent",
	},
	"DELETE /v1/component/:deviceId": {
		"controller": "component",
		"action": "deleteComponent",
	},
	"GET /v1/component/:cid/config": {
		"controller": "component",
		"action": "getComponentConfig",
	},
	"PUT /v1/component/:cid": {
		"controller": "component",
		"action": "editComponent",
	},
	"PUT /m1/component/v1/chemicalprice": {
		"controller": "component",
		"action": "editComponentChemicalPrice",
	},
	// dashboards api
	"GET /v2/:siteId/consumption/:plantId": {
		"controller": "dashboard",
		"action": "consumption",
	},
	"GET /v2/:siteId/consumptionExcel/:plantId": {
		"controller": "dashboard",
		"action": "consumptionExcel",
	},
	//data device controller
	"GET  /site/:siteId/getMonthlyConsumption": {
		"controller": "Datadevice",
		"action": "getMonthlyConsumption",
	},
	"GET  /m1/site/:siteId/getMonthlyConsumption": {
		"controller": "Datadevice",
		"action": "getMonthlyConsumption_new",
	},
	"POST /analyzeCustomAxis": {
		"controller": "Datadevice",
		"action": "analyzeCustomAxis",
	},
	"GET /v1/dashboard/:siteId": {
		"controller": "Datadevice",
		"action": "dashBoardV1",
	},
	"GET  /site/:siteId/getLastDayLoad": {
		"controller": "Datadevice",
		"action": "getLastDayLoad",
	},
	"GET  /site/:siteId/getWeeklyConsumption": {
		"controller": "Datadevice",
		"action": "getWeeklyConsumption",
	},
	"GET  /m1/site/:siteId/getWeeklyConsumption": {
		"controller": "Datadevice",
		"action": "getWeeklyConsumption_new",
	},
	"GET  /site/:siteId/devices/:type": {
		"controller": "Datadevice",
		"action": "getDevices",
	},
	"GET /:siteId/consumptionFromBaseline": {
		"controller": "Datadevice",
		"action": "getCusmptionFromCurrentBaseline",
	},
	"GET  /device/:deviceId/data": {
		"controller": "Datadevice",
		"action": "getData",
	},
	"POST /m1/analytics/rama": {
		"controller": "Datadevice",
		"action": "presetsRama"
	},
	// ToDo(April 2020): Following route needs to be deprecated after FrontEnd Merge.
	"GET /:siteId/:group/consumption": {
		"controller": "Datadevice",
		"action": "dashboardConsumptionData",
	},
	"GET /:siteId/:group/dashboardConsumptionData": {
		"controller": "Datadevice",
		"action": "dashboardConsumptionData",
	},
	"GET /m1/:siteId/:group/consumption": {
		"controller": "Datadevice",
		"action": "dashboardConsumptionData_new",
	},
	"GET /m1/:siteId/:group/dashboardConsumptionData": {
		"controller": "Datadevice",
		"action": "dashboardConsumptionData_new",
	},
	"GET /consumptionPageData/:group": {
		"controller": "Datadevice",
		"action": "consumptionPageData",
	},
	"GET  /device/:deviceId/CurrentValue": {
		"controller": "Datadevice",
		"action": "getADeviceLatestData",
	},
	"GET /component/:id/data": {
		"controller": "dataDevice",
		"action": "getComponentData",
	},
	"POST /component/filteredData": {
		"controller": "dataDevice",
		"action": "getComponentDataByParam",
	},
	"POST /analytics/:type": {
		"controller": "dataDevice",
		"action": "deviceInfo",
	},
	"GET /test/getData/:siteId/:device": {
		"controller": "dataDevice",
		"action": "getData",
	},
	"GET /v1/analytics/paramDiff": {
		"controller": "Datadevice",
		"action": "analyseParamDiff",
	},

	"GET /dashboard/getTonnageDelivery": {
		"controller": "Datadevice",
		"action": "tonnageDeliveryLoading",
	},
	"POST /deviceData/save": {
		"controller": "Datadevice",
		"action": "saveData",
	},
	"POST /v1/proto/saveData": {
		"controller": "Datadevice",
		"action": "saveProtoConvertData",
	},
	"GET /v1/getMainConsumption/:siteId": {
		"controller": "Datadevice",
		"action": "mainMeterConsumption",
	},
	"GET /v1/getCustomDailyConsumption/:siteId": {
		"controller": "Datadevice",
		"action": "getCustomDailyConsumption",
	},
	"POST /m1/analytics/v2/site-consumption": {
		"controller": "Datadevice",
		"action": "getSiteConsumption"
	},

	//Device Controller
	"POST /devices/setMode": {
		"controller": "Devices",
		"action": "setMode",
	},
	"POST /pid/config": {
		"controller": "Devices",
		"action": "setPidConfig",
	},
	"POST /pid/list": {
		"controller": "Devices",
		"action": "pidList",
	},
	"POST /pid/getConfig": {
		"controller": "Devices",
		"action": "getPidConfig",
	},
	"POST  /devices/pid": {
		"controller": "Devices",
		"action": "setPidConfig",
	},
	"DELETE  /:siteId/device/:deviceId/delete": {
		"controller": "Devices",
		"action": "deleteDevice",
	},
	// ToDo(April 2020): Following route needs to be deprecated after FrontEnd Merge.
	"POST /:siteId/updateEMList": {
		"controller": "Devices",
		"action": "updateDashboardEMList",
	},
	"POST /:siteId/updateDashboardEMList": {
		"controller": "Devices",
		"action": "updateDashboardEMList",
	},
	"PUT /ConsumptionPageEMList": {
		"controller": "Devices",
		"action": "updateConsumptionPageEMList",
	},
	// ToDo(April 2020): Following route needs to be deprecated after FrontEnd Merge.
	"GET /:siteId/getEMList": {
		"controller": "Devices",
		"action": "dashboardEMList",
	},
	"GET /:siteId/dashboardEMList": {
		"controller": "Devices",
		"action": "dashboardEMList",
	},
	"GET /consumptionPageEMList": {
		"controller": "Devices",
		"action": "consumptionPageEMList",
	},
	"PUT  /device/updatedevice": {
		"controller": "Devices",
		"action": "update",
	},
	"POST /v1/devices": {
		"controller": "Devices",
		"action": "addDevice",
	},
	"PUT /v1/devices": {
		"controller": "Devices",
		"action": "editDevice",
	},
	"DELETE /v1/devices": {
		"controller": "Devices",
		"action": "deleteDevice",
	},
	"POST /v1/devices/controller": {
		"controller": "Devices",
		"action": "addController",
	},
	"PUT /v1/devices/controller": {
		"controller": "Devices",
		"action": "editController",
	},
	"DELETE /v1/devices/controller": {
		"controller": "Devices",
		"action": "deleteController",
	},
	"GET /v1/devices/configureController": {
		"controller": "devices",
		"action": "initController",
	},
	"GET /v1.5/devices/configureController": {
		"controller": "devices",
		"action": "generateControllerConfig",
	},
	"POST /v2/diagnostics/insertCurrentVersion": {
		"controller": "Devices",
		"action": "insertCurrentVersion",
	},

	//DeviceType Controller
	"POST  /deviceType/add": {
		"controller": "DeviceType",
		"action": "add",
	},
	"PUT  /deviceType/update": {
		"controller": "DeviceType",
		"action": "update",
	},
	"POST /deviceType": {
		"controller": "deviceType",
		"action": "add",
	},
	"POST /deviceType/update": {
		"controller": "deviceType",
		"action": "update",
	},
	"GET /params": {
		"controller": "deviceType",
		"action": "listParams",
	},
	"POST /devicetype/create": {
		"controller": "DeviceType",
		"action": "create",
	},
	"POST /devicetype/update": {
		"controller": "DeviceType",
		"action": "update",
	},
	"POST /devicetype/updateparams": {
		"controller": "DeviceType",
		"action": "updateParams",
	},
	"DELETE /devicetype/del/:DeviceType/:DriverType": {
		"controller": "DeviceType",
		"action": "delete",
	},
	"GET /devicetype/listAll": {
		"controller": "DeviceType",
		"action": "listAll",
	},
	"GET /devicetype/findParams/:DeviceType/:DriverType": {
		"controller": "DeviceType",
		"action": "findParams",
	},
	"GET /devicetype/findDevice/:DeviceType": {
		"controller": "DeviceType",
		"action": "findDevice",
	},
	"GET /devicetype/findone/:DeviceType/:DriverType": {
		"controller": "DeviceType",
		"action": "findOne",
	},
	"GET /v1/config/vendors": {
		"controller": "devicetype",
		"action": "getVendors",
	},
	"GET /v1/config/controllerMapping": {
		"controller": "devicetype",
		"action": "getDeviceMapping",
	},

	//Diagnostics Controller
	"POST /v2/diagnostics/populate": {
		"controller": "Diagnostics",
		"action": "populateDiagnostics",
	},
	"POST /v2/diagnostics/updateToNewVersion": {
		"controller": "Diagnostics",
		"action": "updateToNewVersion",
	},
	"POST /v2/diagnostics/cicd/update": {
		"controller": "Diagnostics",
		"action": "updateFirmware",
	},
	"POST /v2/diagnostics/cicd/updateMany": {
		"controller": "Diagnostics",
		"action": "updateManyFirmware",
	},
	"POST /v2/diagnostics/nsip": {
		"controller": "Diagnostics",
		"action": "reqInfo",
	},
	"POST /v2/diagnostics/restart": {
		"controller": "Diagnostics",
		"action": "restartDevice",
	},
	"POST /v2/diagnostics/recalibrate": {
		"controller": "Diagnostics",
		"action": "recalibrateSensor",
	},
	"POST /v1/diagnostics/maintenance": {
		"controller": "Diagnostics",
		"action": "maintenanceMode",
	},
	"POST /v2/diagnostics/remoteaccess": {
		"controller": "Diagnostics",
		"action": "remoteAccess",
	},
	"POST /v1/diagnostics/general/fb": {
		"controller": "Diagnostics",
		"action": "diagnosticFeedback",
	},
	"POST /v1/diagnostics/test": {
		"controller": "Diagnostics",
		"action": "testDiagnostics",
	},
	"POST /v1/diagnostics/test2": {
		"controller": "Diagnostics",
		"action": "testDiagnostics2",
	},
	"POST /v1/diagnostics/taglist": {
		"controller": "Diagnostics",
		"action": "gitFetchTags",
	},
	"POST /v1/diagnostics/populate": {
		"controller": "Diagnostics",
		"action": "_populateDiagnostics",
	},
	"POST /v1/diagnostics/giveMaintenanceList": {
		"controller": "Diagnostics",
		"action": "giveMaintenanceList",
	},
	"POST /v1/diagnostics/needsMaintenanceCircle": {
		"controller": "Diagnostics",
		"action": "needsMaintenanceCircle",
	},
	"POST /v1/diagnostics/showMaintenanceInfo": {
		"controller": "Diagnostics",
		"action": "showMaintenanceInfo",
	},
	"POST /v1/diagnostics/storeMaintenanceInfo": {
		"controller": "Diagnostics",
		"action": "storeMaintenanceInfo",
	},
	"POST /v1/diagnostics/updateMaintenanceInfo": {
		"controller": "Diagnostics",
		"action": "updateMaintenanceInfo",
	},
	"POST /v1/diagnostics/deleteMaintenanceInfo": {
		"controller": "Diagnostics",
		"action": "deleteMaintenanceInfo",
	},
	"POST /v1/diagnostics/resolveFromMaintenance": {
		"controller": "Diagnostics",
		"action": "resolveFromMaintenance",
	},
	"POST /v1/diagnostics/checkRealOrVirtualMaintenance": {
		"controller": "Diagnostics",
		"action": "checkRealOrVirtualMaintenance",
	},
	"POST /v1/diagnostics/dejouleGraphBenchmarking": {
		"controller": "Diagnostics",
		"action": "diagnosticBenchmarkingGraph",
	},
	"POST /v1/diagnostics/underMaintenanceGraph": {
		"controller": "Diagnostics",
		"action": "underMaintenanceGraph",
	},
	"POST /v1/diagnostics/needsMaintenanceGraph": {
		"controller": "Diagnostics",
		"action": "needsMaintenanceGraph",
	},
	"POST /v1/diagnostics/maintenanceHistory": {
		"controller": "Diagnostics",
		"action": "maintenanceHistory",
	},
	"POST /v1/diagnostics/setJouleBox": {
		"controller": "Diagnostics",
		"action": "setJouleBox",
	},
	"POST /v1/diagnostics/calculateDQI": {
		"controller": "Diagnostics",
		"action": "calculateDQI",
	},
	"POST /v1/diagnostics/getDQIGraph": {
		"controller": "Diagnostics",
		"action": "getDQIGraph",
	},
	"POST /v1/diagnostics/storeAlertsAccountables": {
		"controller": "Diagnostics",
		"action": "storeAlertsAccountables",
	},
	"POST /v1/diagnostics/giveAccountables": {
		"controller": "Diagnostics",
		"action": "giveAccountables",
	},
	"POST /v1/diagnostics/storeStrictness": {
		"controller": "Diagnostics",
		"action": "storeStrictness",
	},
	"POST /v1/diagnostics/setDefaultStrictness": {
		"controller": "Diagnostics",
		"action": "setDefaultStrictness",
	},
	"POST /v1/diagnostics/saveAlertRecipe": {
		"controller": "Diagnostics",
		"action": "saveAlertRecipe",
	},
	"POST /v1/diagnostics/deleteAlertRecipes": {
		"controller": "Diagnostics",
		"action": "deleteAlertRecipes",
	},
	"POST /v1/diagnostics/getBenchmarkingRecipes": {
		"controller": "Diagnostics",
		"action": "getBenchmarkingRecipes",
	},
	"POST /v1/diagnostics/editAlerts": {
		"controller": "Diagnostics",
		"action": "editAlerts",
	},

	//Events Controller
	"POST /v1/event/recieve": {
		"controller": "events",
		"action": "recieve",
	},

	//Mode controller
	"POST /devices/getMode": {
		"controller": "Mode",
		"action": "getMode",
	},

	//Parameter controller
	"POST /test/thermalindex": {
		"controller": "parameter",
		"action": "thermalComfortIndex",
	},
	"POST /test/CWSP": {
		"controller": "parameter",
		"action": "chilledWaterSP",
	},
	"POST /test/best": {
		"controller": "parameter",
		"action": "bestComponent",
	},
	"POST /test/buildingLoad": {
		"controller": "parameter",
		"action": "buildingLoad",
	},
	"GET /lastCommand2/:siteId/:id": {
		"controller": "parameter",
		"action": "lastCommandForSameId",
	},
	"GET /lastCommand/:siteId/:id": {
		"controller": "parameter",
		"action": "lastCommand",
	},
	"POST /pid/getGraph": {
		"controller": "Parameter",
		"action": "getPidGraph",
	},

	//Parameters Controller | new one
	"GET /v1.5/parameters": {
		"controller": "Parameters",
		"action": "find",
	},
	"PUT /v1.5/parameters": {
		"controller": "Parameters",
		"action": "update",
	},

	// Process Controller

	"POST /v1/config/sites/:siteId/processes": {
		"controller": "Process",
		"action": "create",
	},
	"PUT /v1/config/sites/:siteId/processes/:processId/params": {
		"controller": "Process",
		"action": "updateParams",
	},
	"PUT /v1/config/sites/:siteId/processes/:processId": {
		"controller": "Process",
		"action": "updateProcessInfo",
	},
	"DELETE /v1/config/processes/:processId": {
		"controller": "Process",
		"action": "deleteProcess"
	},
	"GET /v1/config/sites/:siteId/processes": {
		"controller": "Process",
		"action": "findAll",
	},
	"GET /v1/config/process/:siteId/:processId": {
		"controller": "Process",
		"action": "getProcessConfig",
	},
	//Recipe Controller
	"POST /v1/recepie/save": {
		"controller": "recipe",
		"action": "saveRecipev2",
	},
	"GET /v1/recepie/get/:siteId": {
		"controller": "recipe",
		"action": "getRecipeOf",
	},
	"POST /v1/recepie/saveabstract": {
		"controller": "recipe",
		"action": "saveAbstract",
	},
	"POST /v1/recepie/saveSchedule": {
		"controller": "recipe",
		"action": "saveSchedule",
	},
	"POST /v1/recepie/deploy": {
		"controller": "recipe",
		"action": "deploy",
	},
	"POST /v1/recepie/schedules": {
		"controller": "recipe",
		"action": "getSiteSchedule",
	},
	"POST /v1/recepie/deploySchedule": {
		"controller": "recipe",
		"action": "deploySingleSchedule",
	},
	"POST /v1/recipe/pause": {
		"controller": "recipe",
		"action": "pauseRecipe",
	},
	"POST /v1/recipe/pausedRecipeplay": {
		"controller": "recipe",
		"action": "pausedRecipeplay",
	},
	"POST /v1/recipe/startStopRecipe": {
		"controller": "recipe",
		"action": "startStopRecipe",
	},
	"POST /v1/recipe/delete": {
		"controller": "recipe",
		"action": "deleteRecipe",
	},
	"POST /v1/schedules/delete": {
		"controller": "recipe",
		"action": "deleteSchedule",
	},
	"POST /setActive": {
		"controller": "recipe",
		"action": "setActive",
	},
	"POST /v1/recipe/update": {
		"controller": "recipe",
		"action": "updateRecipe",
	},
	"POST /v1/recipe/test": {
		"controller": "recipe",
		"action": "test",
	},
	//Repaired Controller

	"POST /maintainance/add": {
		"controller": "repaired",
		"action": "addToMaintainance",
	},
	"POST /v1/maintainance/ticker": {
		"controller": "repaired",
		"action": "getTick",
	},
	"POST /maintainance/done": {
		"controller": "repaired",
		"action": "removeFromMaintainance",
	},
	"POST /maintainance/list": {
		"controller": "repaired",
		"action": "showAllInSite",
	},
	"POST /maintainance/list2": {
		"controller": "repaired",
		"action": "showAllInSite2",
	},
	"POST /maintainance/show": {
		"controller": "repaired",
		"action": "showOne",
	},
	"POST /maintainance/doneof": {
		"controller": "repaired",
		"action": "getrepaired",
	},
	"POST /maintainance/revive": {
		"controller": "repaired",
		"action": "revive",
	},
	"POST /v1/maintainance/update": {
		"controller": "repaired",
		"action": "updateMode",
	},

	// Sites Controller
	"GET /v1/init": {
		"controller": "Sites",
		"action": "initJouleTrack",
	},
	"GET /v2/:siteId/initJouletrack": {
		"controller": "Sites",
		"action": "initJouleTrackV2",
	},
	"POST /v1/site": {
		"controller": "Sites",
		"action": "addNewSite",
	},
	"PUT /v1/site/:siteId": {
		"controller": "Sites",
		"action": "editSiteInfo",
	},
	"GET /v1/site/:siteId": {
		"controller": "Sites",
		"action": "getSiteInfo",
	},
	"POST /v1/site/:siteId/region": {
		"controller": "Sites",
		"action": "addRegion",
	},
	"PUT /v1/site/:siteId/region": {
		"controller": "Sites",
		"action": "editRegion",
	},
	"POST /v1/site/:siteId/area": {
		"controller": "Sites",
		"action": "addArea",
	},
	"PUT /v1/site/:siteId/area": {
		"controller": "Sites",
		"action": "editArea",
	},
	"POST /v1/site/:siteId/network": {
		"controller": "Sites",
		"action": "addNetwork",
	},

	// User Controller
	"GET  /users": {
		"controller": "Users",
		"action": "getUser",
	},
	"GET /v1/users/defaultPref": {
		"controller": "Users",
		"action": "defPref",
	},
	"POST  /users": {
		"controller": "Users",
		"action": "create",
	},
	"POST /users/photo": {
		"controller": "Users",
		"action": "uploadphoto",
	},
	"POST /user/getUserPolicy": {
		"controller": "Users",
		"action": "getUserPolicies",
	},
	"POST /users/changeSetting": {
		"controller": "Users",
		"action": "changeSetting",
	},
	"GET  /:siteId/users": {
		"controller": "Users",
		"action": "getUsers",
	},
	"POST  /users/available": {
		"controller": "Users",
		"action": "available",
	},
	"POST  /users/avatar": {
		"controller": "Users",
		"action": "uploadAvatar",
	},
	"PUT  /users": {
		"controller": "Users",
		"action": "Update",
	},
	"POST /user/createRole": {
		"controller": "Users",
		"action": "createRole",
	},
	"POST /user/deleteRole": {
		"controller": "Users",
		"action": "deleteRole",
	},
	"POST /user/changepwd": {
		"controller": "Users",
		"action": "editPassword",
	},
	"POST /user/updateRole": {
		"controller": "Users",
		"action": "updateRole",
	},
	"GET /user/getPolicies": {
		"controller": "Users",
		"action": "getPolicies",
	},
	"PUT /user/pref": {
		"controller": "Users",
		"action": "userPref",
	},
	"GET /user/pref": {
		"controller": "Users",
		"action": "getPref",
	},
	"GET /getRoles": {
		"controller": "Users",
		"action": "getRole",
	},
	//Version Controller
	"GET /version/getAll": {
		"controller": "Version",
		"action": "sendAllVersion",
	},
	"POST /version/create": {
		"controller": "Version",
		"action": "addVersion",
	},
	"POST /version/read": {
		"controller": "Version",
		"action": "getParticularVersion",
	},
	"POST /version/update": {
		"controller": "Version",
		"action": "updateVersion",
	},
	"POST /version/delete": {
		"controller": "Version",
		"action": "deleteVersion",
	},
	"GET /version/getLatest": {
		"controller": "Version",
		"action": "getLatestVersion",
	},
	"POST /version/checkVersionNotExist": {
		"controller": "Version",
		"action": "checkVersionNotExist",
	},
	"POST /version/checkVersionExist": {
		"controller": "Version",
		"action": "checkVersionExist",
	},
	"POST /user/resetPassword": {
		"controller": "Users",
		"action": "resetPassword",
	},

	// Reportinfo Controller
	"POST /reports/details": {
		"controller": "Reportinfo",
		"action": "getReportDetails",
	},

	//Mock API
	"POST /v1/socket/publish-to-socket": {
		"controller": "sitesController",
		"action": "publishMockSocketEvents"
	},

	"POST /v1/site/:siteId/component/mode/publish-to-socket": {
		"controller": "Mode",
		"action": "publishComponentModeDetailsToSocket"
	},
	"GET /v1/health": {
		"controller": "HealthCheck",
		"action": "health"
	},
	"GET /health": {
		"controller": "HealthCheck",
		"action": "health"
	}
};

/***************************************************************************
 *                                                                          *
 * Custom routes here...                                                    *
 *                                                                          *
 * If a request to a URL doesn't match any of the custom routes above, it   *
 * is matched against Sails route blueprints. See `config/blueprints.js`    *
 * for configuration options and examples.                                  *
 *                                                                          *
 ***************************************************************************/
