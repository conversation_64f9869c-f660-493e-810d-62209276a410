/**
 * Policy Mappings
 * (sails.config.policies)
 *
 * Policies are simple functions which run **before** your controllers.
 * You can apply one or more policies to a given controller, or protect
 * its actions individually.
 *
 * Any policy file (e.g. `api/policies/authenticated.js`) can be accessed
 * below by its filename, minus the extension, (e.g. "authenticated")
 *
 * For more information on how policies work, see:
 * http://sailsjs.org/#!/documentation/concepts/Policies
 *
 * For more information on configuring policies, check out:
 * http://sailsjs.org/#!/documentation/reference/sails.config/sails.config.policies.html
 */

module.exports.policies = {
	"*": ["isAuthorized"],
	"HealthCheckController": {
		"health": true
	},
	"UsersController": {
		"create": true, // We dont need authorization here, allowing public access,
		"resetPassword": true, // We dont need authorization here, allowing public access,
		"getUsers": ["isAuthorized", "checkAccess"],
		"createRole": ["isAuthorized", "checkAccess"],
	},
	"DatadeviceController": {
		"tonnageDeliveryLoading": ["isAuthorized"],
		"saveProtoConvertData": ["isRequestValid"],
		"deviceInfo": ["isAuthorized"],
		"mainMeterConsumption": ["isAuthorized"],
	},
	"EventsController": {
		"recieve": true,
	},
	"DevicesController": {
		"addDevice": ["isAuthorized", "deviceLogPolicy"],
		"addController": ["isAuthorized", "deviceLogPolicy"],
		"initController": ["initReqController"],
		"generateControllerConfig": ["initReqController"],
		"deleteDevice": ["isAuthorized", "deviceLogPolicy"],
		"deleteController": ["isAuthorized", "deviceLogPolicy"],
		"editDevice": ["isAuthorized", "deviceLogPolicy"],
		"editController": ["isAuthorized", "deviceLogPolicy"],
		"setPidConfig": ["isAuthorized", "deviceLogPolicy"],
		"setMode": ["isAuthorized", "deviceLogPolicy"],
	},
	"ComponentController": {
		"deleteComponent": ["isAuthorized", "componentLogPolicy"],
		"addComponent": ["isAuthorized", "componentLogPolicy"],
		"editComponent": ["isAuthorized", "componentLogPolicy"],
	},
	"AuthController": {
		"registerSocket": ["isAuthorized"],
		"verifyToken": ["isAuthorized"],
		"login": ["allowTestEnv"],
		"*": true, // We dont need authorization here, allowing public access
	},
	"SitesController": {
		"listAllSites": ["isAuthorized", "checkAccess"],
	},
	"RecipeController": {
		"saverecipev2": ["isAuthorized", "checkAccess"],
		"getrecipeof": ["isAuthorized", "checkAccess"],
	},
	// Currently this api is calling from jt-api-v2. This api will be completely deprecated once consumption calculation shifted to jt-api-v2
	"Datadevice":{
		analyseParamDiff:true
	}
};
