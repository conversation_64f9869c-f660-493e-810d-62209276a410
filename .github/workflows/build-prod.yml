name: Tag Release and Deploy Production

on:
  pull_request:
    branches:
      - master
    types:
    - closed

env:
  DEFAULT_BUMP: patch
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  IMAGE_REPOSITORY: mqtt
  SERVICE_NAME: mqtt

jobs:
  tag-prod:
    if: github.event.pull_request.merged
    runs-on: ubuntu-latest
    outputs:
      TAG_NAME: ${{ steps.determineTag.outputs.TAG_NAME }}
    steps:
    - name: Check out master branch
      uses: actions/checkout@v2
    - name: Determine BUMP
      id: determineTag
      run: |
        sudo npm install -g semver
        git fetch "https://$GITHUB_ACTOR:$<EMAIL>/$GITHUB_REPOSITORY.git" --tags
        tag=$(git describe --tags `git rev-list --tags --max-count=1`)
        log=$(git log $tag..HEAD --pretty=oneline)
        commit=$(git log -1 --pretty=%B)
        case $log in
          *#major* ) new=$(semver -i major $tag);;
          *#minor* ) new=$(semver -i minor $tag);;
          *#patch* ) new=$(semver -i patch $tag);;
          *enhancement*) new=$(semver -i minor $tag);;
          *feature*) new=$(semver -i minor $tag);;
          *bug*) new=$(semver -i patch $tag);;
          *hotfix*) new=$(semver -i patch $tag);;
          *patch*) new=$(semver -i patch $tag);;
          *) new=$(semver -i patch $tag);;
        esac
        echo "::set-output name=TAG_NAME::$new"
        echo "::set-output name=TAG::v$new"
        echo "::set-output name=COMMIT::$commit"
        echo "::set-output name=LOG::$log"
    - name: Create New Release
      id: release
      uses: actions/create-release@v1
      with:
        tag_name: ${{ steps.determineTag.outputs.TAG }}
        release_name: ${{ steps.determineTag.outputs.COMMIT }}
        body: ${{ steps.determineTag.outputs.LOG }}
        draft: false
        prerelease: false
  build-doc:
    runs-on: ubuntu-latest
    needs: tag-prod
    steps:
    - name: Check out master branch
      uses: actions/checkout@v2
    - name: Configure AWS Credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-west-2
    - name: Setup Node.js for use with actions
      uses: actions/setup-node@v2
    - name: Install Dependencies
      run: npm install -g jsdoc
    - name: Build Documentation
      run: npm run build:docs
    - name: Deploy to S3
      run: |
        aws s3 sync docs s3://code-documentation/$SERVICE_NAME --delete
  build-prod:
    runs-on: ubuntu-latest
    needs: tag-prod
    steps:
    - name: Check out master branch
      uses: actions/checkout@v2
    - name: Configure AWS Credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-west-2
    - name: Login to Amazon ECR
      uses: aws-actions/amazon-ecr-login@v1
    - name: Build MQTT
      run: docker build -t 878252606197.dkr.ecr.us-west-2.amazonaws.com/$IMAGE_REPOSITORY:${{ needs.tag-prod.outputs.TAG_NAME }} .
    - name: Push Production Image to AWS ECR
      run: docker push 878252606197.dkr.ecr.us-west-2.amazonaws.com/$IMAGE_REPOSITORY:${{ needs.tag-prod.outputs.TAG_NAME }}
