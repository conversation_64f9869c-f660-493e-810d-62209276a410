name: Docker Image Deployment

on:
  release:
    types: [published]

env:
  TAG_NAME: ${{ github.event.release.tag_name }}

jobs:
  build-dev:
    if: github.event.release.prerelease
    runs-on: ubuntu-latest
    env:
      IMAGE_REPOSITORY: jouletrack-api-dev
    steps:
      - uses: actions/checkout@v2
        with:
          ref: "development"
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2
      - name: Setup Node.js for use with actions
        uses: actions/setup-node@v2
      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v1
      - name: Build JouleTrack-API
        run: docker build -t 878252606197.dkr.ecr.us-west-2.amazonaws.com/$IMAGE_REPOSITORY:`sed 's/development-//g' <<< "$TAG_NAME"` .
      - name: Push Development Image to AWS ECR
        run: docker push 878252606197.dkr.ecr.us-west-2.amazonaws.com/$IMAGE_REPOSITORY:`sed 's/development-//g' <<< "$TAG_NAME"`
  build-prod:
    if: github.event.release.prerelease != true
    runs-on: ubuntu-latest
    env:
      IMAGE_REPOSITORY: jouletrack-api
    steps:
      - uses: actions/checkout@v2
        with:
          ref: "master"
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2
      - name: Setup Node.js for use with actions
        uses: actions/setup-node@v2
      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v1
      - name: Build JouleTrack-API
        run: docker build -t 878252606197.dkr.ecr.us-west-2.amazonaws.com/$IMAGE_REPOSITORY:`sed 's/prod-//g' <<< "$TAG_NAME"` .
      - name: Push Development Image to AWS ECR
        run: docker push 878252606197.dkr.ecr.us-west-2.amazonaws.com/$IMAGE_REPOSITORY:`sed 's/prod-//g' <<< "$TAG_NAME"`
