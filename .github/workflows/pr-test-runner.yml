name: PR Test Runner

on:
  pull_request:
    branches: development
    types: [opened, reopened]

jobs:
  unit-tests:

    runs-on: ubuntu-latest
    env:
      AWS_ACCESS_KEY_ID: ${{ secrets. AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets. AWS_SECRET_ACCESS_KEY }}

    steps:
    - uses: actions/checkout@v2
    - name: Setup Node.js for use with actions
      uses: actions/setup-node@v2
    - name: Install Dependencies
      run: |
        npm i
        docker pull redis
    - name: Run DB and Cache servers
      run: docker run -p 6379:6379 -d redis
    - name: Run Unit Tests
      run: npm run unittest

  integration-tests:

    runs-on: ubuntu-latest
    env:
      AWS_ACCESS_KEY_ID: ${{ secrets. AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets. AWS_SECRET_ACCESS_KEY }}

    steps:
    - uses: actions/checkout@v2
    - name: Setup Node.js for use with actions
      uses: actions/setup-node@v2
    - name: Install Dependencies
      run: |
        pip3 install setuptools
        npm i
        docker pull amazon/dynamodb-local
        docker pull redis
        pip3 install boto3
    - name: Run DB and Cache servers
      env:
        AWS_ACCESS_KEY_ID: ${{ secrets. AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets. AWS_SECRET_ACCESS_KEY }}
      run: |
        docker run --rm -p 8000:8000 amazon/dynamodb-local -Djava.library.path=./DynamoDBLocal_lib -jar DynamoDBLocal.jar -sharedDb &
        docker run -p 6379:6379 -d redis
        python3 .build/pythonscripts/mock-tables.py
    - name: Run Integration Tests
      run: npm run test
