name: Create Release Tag

on:
  pull_request:
    branches:
      - master
      - development
    types:
      - closed

env:
  DEFAULT_BUMP: patch
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

jobs:
  tag-dev:
    if: github.event.pull_request.merged && github.event.pull_request.base.ref == 'development'
    runs-on: ubuntu-latest

    steps:
    - name: Check out development branch
      uses: actions/checkout@v2
      with:
        ref: development
    - name: Determine BUMP
      env:
        HEAD: ${{ github.event.pull_request.head.ref }}
      id: determineTag
      run: |
        sudo npm install -g semver
        git fetch "https://$GITHUB_ACTOR:$<EMAIL>/$GITHUB_REPOSITORY.git" --tags
        tag=$(git tag | grep development- | cut -d "-" -f2 | sort -V | tail -n 1)
        log="$HEAD\n$(git log development-$tag..HEAD --pretty='format:%s')"
        commit=$(git log -1 --pretty=%B)
        case $log in
          *#major* ) new=$(semver -i major $tag);;
          *#minor* ) new=$(semver -i minor $tag);;
          *#patch* ) new=$(semver -i patch $tag);;
          *enhancement*) new=$(semver -i minor $tag);;
          *feature*) new=$(semver -i minor $tag);;
          *bug*) new=$(semver -i patch $tag);;
          *hotfix*) new=$(semver -i patch $tag);;
          *patch*) new=$(semver -i patch $tag);;
          *) new=$(semver -i $DEFAULT_BUMP $tag);;
        esac
        echo "::set-output name=RELEASE_TAG::development-$new"
        echo "::set-output name=RELEASE_NAME::$commit"
        echo "::set-output name=LOG::$log"
    - name: DeJoule CI Access Token
      env:
         GITHUB_APP_API_KEY: ${{ secrets.GITHUB_APP_API_KEY }}
      id: getToken
      run: |
        token=$(curl -H "x-api-key: $GITHUB_APP_API_KEY" https://serverless.smartjoules.org/sj-helper/dejouleCI/token)
        echo "::set-output name=ACCESS_TOKEN::$token"
    - name: Create New Pre-Release
      uses: meeDamian/github-release@2.0
      with:
        token: ${{ steps.getToken.outputs.ACCESS_TOKEN }}
        tag: ${{ steps.determineTag.outputs.RELEASE_TAG }}
        name: ${{ steps.determineTag.outputs.RELEASE_NAME }}
        commitish: development
        body: ${{ steps.determineTag.outputs.LOG }}
        prerelease: true
  tag-prod:
    if: github.event.pull_request.merged && github.event.pull_request.base.ref == 'master'
    runs-on: ubuntu-latest
    env:
      DEFAULT_BUMP: patch
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    steps:
    - name: Check out master branch
      uses: actions/checkout@v2
      with:
        ref: master
    - name: Determine BUMP
      id: determineTag
      run: |
        sudo npm install -g semver
        git fetch "https://$GITHUB_ACTOR:$<EMAIL>/$GITHUB_REPOSITORY.git" --tags
        tag=$(git tag | grep prod- | cut -d "-" -f2 | sort -V | tail -n 1)
        log=$(git log prod-$tag..HEAD --pretty='format:%s')
        commit=$(git log -1 --pretty=%B)
        case $log in
          *#major* ) new=$(semver -i major $tag);;
          *#minor* ) new=$(semver -i minor $tag);;
          *#patch* ) new=$(semver -i patch $tag);;
          *enhancement*) new=$(semver -i minor $tag);;
          *feature*) new=$(semver -i minor $tag);;
          *bug*) new=$(semver -i patch $tag);;
          *hotfix*) new=$(semver -i patch $tag);;
          *patch*) new=$(semver -i patch $tag);;
          *) new=$(semver -i $DEFAULT_BUMP $tag);;
        esac
        echo "::set-output name=RELEASE_TAG::prod-$new"
        echo "::set-output name=RELEASE_NAME::$commit"
        echo "::set-output name=LOG::$log"
    - name: DeJoule CI Access Token
      env:
         GITHUB_APP_API_KEY: ${{ secrets.GITHUB_APP_API_KEY }}
      id: getToken
      run: |
        token=$(curl -H "x-api-key: $GITHUB_APP_API_KEY" https://serverless.smartjoules.org/sj-helper/dejouleCI/token)
        echo "::set-output name=ACCESS_TOKEN::$token"
    - name: Create New Release
      uses: meeDamian/github-release@1.0
      with:
        token: ${{ steps.getToken.outputs.ACCESS_TOKEN }}
        tag: ${{ steps.determineTag.outputs.RELEASE_TAG }}
        name: ${{ steps.determineTag.outputs.RELEASE_NAME }}
        body: ${{ steps.determineTag.outputs.LOG }}
