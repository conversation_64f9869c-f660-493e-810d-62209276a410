branch-defaults:
  master:
    environment: sj-api-prod 
  development:
    environment: development-sj
global:
  application_name: smartjoulesapi
  default_ec2_keyname: AWS_KEY
  default_platform: arn:aws:elasticbeanstalk:us-west-2::platform/Docker running on
    64bit Amazon Linux/2.7.4
  default_region: us-west-2
  include_git_submodules: true
  instance_profile: null
  platform_name: null
  platform_version: null
  profile: null
  sc: git
  workspace_type: Application
deploy:
  artifact: Dockerrun.aws.json
