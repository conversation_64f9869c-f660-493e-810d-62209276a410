{"name": "JoulesTrack-API", "private": true, "version": "0.0.0", "description": "a Sails application", "keywords": [], "dependencies": {"@influxdata/influxdb-client": "^1.22.0", "aws-sdk": "^2.1692.0", "axios": "^0.18.1", "bcryptjs": "^2.4.3", "chai-http": "^3.0.0", "compression": "^1.7.1", "csvtojson": "^2.0.10", "dotenv": "^8.2.0", "dynamodb-wrapper": "^1.3.0", "elasticsearch": "^15.4.1", "exceljs": "^4.1.0", "express": "^4.16.4", "express-handlebars": "^3.0.0", "express-http-context": "^1.2.0", "http-aws-es": "^6.0.0", "include-all": "^1.0.0", "influx": "^5.9.3", "jimp": "^0.14.0", "joi": "^17.6.0", "jsdoc": "^3.6.3", "json-to-csv": "^1.0.0", "json2csv": "^3.11.0", "jsonwebtoken": "^7.4.0", "lodash": "^4.17.21", "mathjs": "^6.0.2", "md5": "^2.2.1", "moment-timezone": "^0.5.13", "mqtt": "^2.18.8", "newrelic": "latest", "node-cron": "^1.1.3", "node-schedule": "^1.2.3", "nodemailer": "^4.1.0", "nodemailer-express-handlebars": "^3.0.0", "nodemon": "^1.19.4", "pg": "^8.11.3", "rc": "^1.2.8", "s3-stream-upload": "^2.0.2", "s3-uploader": "^2.0.3", "sails": "~0.12.13", "sails-disk": "~0.10.9", "sails-dynamodb": "^0.12.4", "sails-redis": "^0.10.7", "sails.io.js": "^1.1.12", "skipper-s3": "^0.6.0", "socket.io-client": "^2.0.1", "socket.io-redis": "1.0.0", "uuid": "^3.3.2"}, "scripts": {"postinstall": "husky install", "debug": "node debug app.js", "start": "node app.js --prod", "test": "NODE_ENV=testing node ./node_modules/mocha/bin/mocha test/bootstrap.test.js --exit test/integration/controllers/**/*.test.js", "unittest": "NODE_ENV=testing node ./node_modules/mocha/bin/mocha test/bootstrap.test.js --exit test/unit/services/**/*.test.js", "unittestserver": "NODE_ENV=testing node ./node_modules/mocha/bin/mocha test/bootstrap.test.js --exit test/unit/services/**/*.test.js --reporter=json >testresult/testresult.json", "onetest": "NODE_ENV=testing node ./node_modules/mocha/bin/mocha test/bootstrap.test.js --exit", "lint": "eslint ."}, "main": "app.js", "repository": {"type": "git", "url": "git://github.com/amit/JoulesTrack-API.git"}, "author": "<PERSON><PERSON>", "license": "", "devDependencies": {"chai": "^4.2.0", "eslint": "^7.19.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-plugin-import": "^2.22.1", "husky": "^5.0.9", "istanbul": "^0.4.5", "mocha": "^5.2.0", "nyc": "^14.1.1", "sinon": "^4.5.0", "supertest": "^3.0.0"}}