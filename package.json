{"name": "jouletrack-api-mqtt", "version": "1.0.0", "description": "MQTT Service for JouleTrack-API", "main": "index.js", "scripts": {"postinstall": "husky install", "start": "NODE_ENV=production node index.js", "lint": "eslint ."}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"@sentry/node": "^8.25.0", "@sentry/profiling-node": "^8.25.0", "axios": "^1.7.4", "body-parser": "^1.19.0", "express": "^4.19.2", "lodash.merge": "^4.6.2", "mqtt": "^3.0.0", "newrelic": "latest", "pino": "^5.16.0", "pino-pretty": "^11.2.2", "redis": "3.1.1", "uuid": "^10.0.0"}, "devDependencies": {"eslint": "^7.20.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-plugin-import": "^2.22.1", "husky": "^5.1.1"}}