const express = require("express");
const eventRouter = require("./routes/eventRouter");
const bodyParser = require("body-parser");
const app = express();
const logger = require("./services/logger");
const PORT = 5555;

app.use(bodyParser.urlencoded({ extended: false }));
app.use(bodyParser.json());
app.use(eventRouter);

const startServer = () => {
  app.listen(PORT, () => {
    logger.info("Endpoints Exposed:");
    logger.info("/v1/event/publish");
    logger.info(`Port: ${PORT}`);
  });
};

module.exports = startServer;
