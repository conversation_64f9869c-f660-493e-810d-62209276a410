import boto3
import logging
import time
from pprint import pprint
client = boto3.client('dynamodb', region_name="us-west-2")
client_sydney = boto3.client('dynamodb', region_name="ap-southeast-2")


def list_tables():
    """
    This function all the tables in us-west-2 region
    """
    response = client.list_tables(
    )
    data = response
    TableNames = data['TableNames']
    for i in TableNames:
        print(i)
        describeTable(i)


def worksOnLOC(loc):
    """
    This function takes loc as input which is response['Table']['LocalSecondaryIndexes'] coming from decribing a table.
    Further this function works to remove not required keys from the dict and returns the clean dict
    which can straight be used to create a table in case of local secondary indexes in a table.
    """
    if len(loc) is 1:
        del loc[0]['IndexSizeBytes']
        del loc[0]['ItemCount']
        del loc[0]['IndexArn']
        return loc
    elif len(loc) > 1:
        for i in range(len(loc)):
            del loc[i]['IndexSizeBytes']
            del loc[i]['ItemCount']
            del loc[i]['IndexArn']
        return loc


def worksOnGOC(goc):
    """
    This function takes goc as input which is response['Table']['GlobalSecondaryIndexes'] coming from decribing a table.
    Further this function works to remove not required keys from the dict and returns the clean dict
    which can straight be used to create a table in case of local secondary indexes in a table.
    """
    if len(goc) is 1:
        try:
            del goc[0]['IndexArn']
        except Exception as identifier:
            logging.exception(identifier)
        try:
            del goc[0]['ItemCount']
        except Exception as identifier:
            logging.exception(identifier)
        try:
            del goc[0]['IndexSizeBytes']
        except Exception as identifier:
            logging.exception(identifier)
        try:
            del goc[0]['Backfilling']
        except Exception as identifier:
            logging.exception(identifier)
        try:
            del goc[0]['IndexStatus']
        except Exception as identifier:
            logging.exception(identifier)
        try:
            del goc[0]['ProvisionedThroughput']['NumberOfDecreasesToday']
        except Exception as identifier:
            logging.exception(identifier)
        try:
            del goc[0]['ProvisionedThroughput']['LastDecreaseDateTime']
        except Exception as identifier:
            logging.exception(identifier)
        return goc
    elif len(goc) > 1:
        for i in range(len(goc)):
            try:
                del goc[i]['IndexArn']
            except Exception as identifier:
                logging.exception(identifier)
            try:
                del goc[i]['ItemCount']
            except Exception as identifier:
                logging.exception(identifier)
            try:
                del goc[i]['IndexSizeBytes']
            except Exception as identifier:
                logging.exception(identifier)
            try:
                del goc[i]['Backfilling']
            except Exception as identifier:
                logging.exception(identifier)
            try:
                del goc[i]['IndexStatus']
            except Exception as identifier:
                logging.exception(identifier)
            try:
                del goc[0]['ProvisionedThroughput']['NumberOfDecreasesToday']
            except Exception as identifier:
                logging.exception(identifier)
            try:
                del goc[0]['ProvisionedThroughput']['LastDecreaseDateTime']
            except Exception as identifier:
                logging.exception(identifier)
        return goc


def describeTable(name):
    """
    This function takes tableName as input, Example : actions,
    Hits the describeTable api and checks for 'LocalSecondaryIndexes' and/or 'GlobalSecondaryIndexes' to exist.
    There are two flags being used, Flag and flag2:
        flag = True means  LocalSecondaryIndexes exists in the response
        and similarly,
        flag2 = True would mean, GlobalSecondaryIndexes exists in the process.
        Further calls the worksonLOC or worksonGOC function to get clean data as it is needed to create tables in sydney region.
    """

    TableName = name
    response = client.describe_table(
        TableName=TableName
    )
    flag = False
    flag2 = False
    try:
        loc = response['Table']['LocalSecondaryIndexes']
        flag = True

    except Exception as identifier:
        flag = False

    try:
        goc = response['Table']['GlobalSecondaryIndexes']
        flag2 = True

    except Exception as identifier:
        flag2 = False
    AttributeDefinitions = response['Table']['AttributeDefinitions']
    KeySchema = response['Table']['KeySchema']
    if (flag is False and flag2 is False):
        createTable(KeySchema, AttributeDefinitions, TableName)
    elif(flag is True and flag2 is not True):
        loc = worksOnLOC(loc)
        createTableloc(KeySchema, AttributeDefinitions,
                       TableName, loc)
    elif(flag2 is True and flag is not True):
        goc = worksOnGOC(goc)
        createTablelocglob(KeySchema, AttributeDefinitions,
                           TableName, goc)
    elif(flag is True and flag2 is True):
        loc = worksOnLOC(loc)
        goc = worksOnGOC(goc)
        createTableinCasebothLocalandGlobalIndex(
            KeySchema, AttributeDefinitions, TableName, loc, goc)
    else:
        print("WTF")


def createTable(KeySchema, AttributeDefinitions, TableName):
    """
    This function is called to create the table of tableName when:
        flag = False
        flag2 = False
    """
    try:
        print("Creating table of the name : "+TableName)
        _ = client_sydney.create_table(
            AttributeDefinitions=AttributeDefinitions,
            TableName=TableName,
            KeySchema=KeySchema,
            BillingMode='PROVISIONED',
            ProvisionedThroughput={
                'ReadCapacityUnits': 1,
                'WriteCapacityUnits': 1
            },
        )

    except Exception as identifier:
        print(identifier)
        pass


def createTableloc(KeySchema, AttributeDefinitions, TableName, loc):
    """
    This function is called to create the table of tableName when:
        flag = True
        flag2 = False
    """
    try:
        print("Creating table of the name : "+TableName)
        response = client_sydney.create_table(
            AttributeDefinitions=AttributeDefinitions,
            TableName=TableName,
            KeySchema=KeySchema,
            BillingMode='PROVISIONED',
            ProvisionedThroughput={
                'ReadCapacityUnits': 1,
                'WriteCapacityUnits': 1
            },
            LocalSecondaryIndexes=loc
        )

    except Exception as identifier:
        print(identifier)
        pass


def createTablelocglob(KeySchema, AttributeDefinitions, TableName, goc):
    """
    This function is called to create the table of tableName when:
        flag = False
        flag2 = True
    """
    try:

        print("Creating table of the name : "+TableName)
        response = client_sydney.create_table(
            AttributeDefinitions=AttributeDefinitions,
            TableName=TableName,
            KeySchema=KeySchema,
            BillingMode='PROVISIONED',
            ProvisionedThroughput={
                'ReadCapacityUnits': 1,
                'WriteCapacityUnits': 1
            },
            GlobalSecondaryIndexes=goc
        )
    except Exception as identifier:
        print(identifier)


def createTableinCasebothLocalandGlobalIndex(KeySchema, AttributeDefinitions, TableName, loc, goc):
    """
    This function is called to create the table of tableName when:
        flag = True
        flag2 = True
    """
    try:

        response = client_sydney.create_table(
            AttributeDefinitions=AttributeDefinitions,
            TableName=TableName,
            KeySchema=KeySchema,
            BillingMode='PROVISIONED',
            ProvisionedThroughput={
                'ReadCapacityUnits': 1,
                'WriteCapacityUnits': 1

            },
            LocalSecondaryIndexes=loc,
            GlobalSecondaryIndexes=goc
        )
        print("Did it for both global and local case")
        print(response)
    except Exception as identifier:
        logging.exception(identifier)
        return False


if __name__ == "__main__":
    list_tables()