import boto3

client_sydney = boto3.client('dynamodb', region_name="ap-southeast-2")
# Deletes all the tables present in Apsoutheast-2 region 
def deleteTable():
    response = client_sydney.list_tables(
    )
    TableNames = response['TableNames']
    for i in TableNames:
        response = client_sydney.delete_table(
        TableName=i
        ) 
        print("Deleting the Table : "+i)      
        print(response)



if __name__ == "__main__":
    deleteTable()

