import logging
import boto3
import sys
client = boto3.client('elasticbeanstalk', region_name='us-west-2')


def describe_dev_env():
    """
    Describes the dev env ebs and returns the current versionlabel on the env
    """
    # For development environment
    try:
        response = client.describe_environments(
            EnvironmentIds=[
                'e-yrw5ahtigx',
            ],
            EnvironmentNames=[
                'development-sj',
            ]
        )
        VersionLabel = response['Environments'][0]['VersionLabel']
        return VersionLabel
    except Exception as identifier:
        logging.exception(identifier)
        return False


def describe_prod_env():
    """
    Describes the prod env ebs and returns the current versionlabel on the env
    """
    # For prod environment
    try:
        response = client.describe_environments(
            EnvironmentIds=[
                'e-z5yprhnmck',
            ],
            EnvironmentNames=[
                'sj-api-prod',
            ]
        )
        VersionLabel = response['Environments'][0]['VersionLabel']
        return VersionLabel
    except Exception as identifier:
        logging.exception(identifier)
        return False


def updateDescription(label, tag):
    """
    This function takes label as input which is version label of currently deployed image and the tag to update the description of the deployed image for easy deployement. 
    """
    try:
        _ = client.update_application_version(
            ApplicationName='smartjoulesapi',
            VersionLabel=label,
            Description=tag
        )
        print('updated')
    except Exception as identifier:
        logging.exception(identifier)
        return False


if __name__ == "__main__":
    variable = sys.argv[1]
    tag = sys.argv[2]
    print("updating for the tag", tag)
    if variable == '-D':
        print("dev env")
        label = describe_dev_env()
        updateDescription(label, tag)
    elif variable == '-P':
        print("prod env")
        label = describe_prod_env()
        updateDescription(label, tag)