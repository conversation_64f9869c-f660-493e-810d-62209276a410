provider "aws" {
  region = "${var.region}"
}

terraform {
  # Defining the stage file location for dynamic use. 
  backend "s3" {
    bucket = "testing-random-123"
    key    = "terraform.tfstate"
    region = "ap-southeast-2"
  }
}
# Creates the VPC and other related resources as mentioned in the VPC module. 
module "my_vpc" {
  source      = "./modules/vpc"
  vpc_cidr    = "192.168.0.0/16"
  tenancy     = "default"
  subnet_cidr = "192.168.1.0/24"
  vpc_id      = "${module.my_vpc.vpc_id}"
  subnet_id   = "${module.my_vpc.subnet_id}"
  ig_id       = "${module.my_vpc.ig_id}"
  rt_id       = "${module.my_vpc.rt_id}"
  sg_id       = "${module.my_vpc.sg_id}"
}
# Creates the EC2 instance with predefined AMI 
module "my_ec2" {
  source    = "./modules/ec2"
  subnet_id = "${module.my_vpc.subnet_id}"
  ami       = "ami-067500ba2035f2940"     
}
