resource "aws_vpc" "vpc" {
  cidr_block           = "${var.vpc_cidr}"
  instance_tenancy     = "${var.tenancy}"
  enable_dns_hostnames = "true"

  tags = {
    Name = "main"
  }
}

resource "aws_subnet" "main" {
  vpc_id     = "${var.vpc_id}"
  cidr_block = "${var.subnet_cidr}"
}

resource "aws_internet_gateway" "gw" {
  vpc_id = "${var.vpc_id}"
}

# resource "aws_route_table" "r" {
#   vpc_id = "${var.vpc_id}"

#   route {
#     cidr_block = "${var.route_cidr_destination}"
#     gateway_id = "${var.ig_id}"
#   }
# }
resource "aws_route" "r" {
  route_table_id         = "${var.rt_id}"
  destination_cidr_block = "${var.route_cidr_destination}"
  gateway_id             = "${var.ig_id}"
}

resource "aws_security_group_rule" "allow_ssh" {
  type              = "ingress"
  from_port         = 22
  to_port           = 22
  protocol          = "tcp"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = "${var.sg_id}"
}

resource "aws_security_group_rule" "allow_icmp" {
  type              = "ingress"
  from_port         = 8
  to_port           = 0
  protocol          = "icmp"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = "${var.sg_id}"
}

# resource "aws_security_group_rule" "allow_allout" {
#   type              = "egress"
#   from_port         = 0
#   to_port           = 65535
#   protocol          = "-1"
#   cidr_blocks       = ["0.0.0.0/0"]
#   security_group_id = "${var.sg_id}"

#   //define all rules already defined in security groups below here and delete that part. 
# }

//
//

# resource "aws_security_group" "allow_all" {
#   name        = "allow_all"
#   description = "Allow all inbound traffic"
#   vpc_id      = "${aws_vpc.vpc.id}"

#   ingress {
#     from_port   = 0
#     to_port     = 0
#     protocol    = "-1"
#     cidr_blocks = ["0.0.0.0/0"]
#   }
#   ingress {
#     from_port = 0  
#     to_port = 65535
#     protocol = "icmp"
#     cidr_blocks = ["0.0.0.0/0"]
#   }

#   ingress {
#     from_port   = 22
#     to_port     = 22
#     protocol    = "tcp"
#     cidr_blocks = ["0.0.0.0/0"]
#   } 
#    egress {
#     from_port       = 0
#     to_port         = 0
#     protocol        = "-1"
#     cidr_blocks     = ["0.0.0.0/0"]
#   }
# }

//

output "ig_id" {
  value = "${aws_internet_gateway.gw.id}"
}

output "vpc_id" {
  value = "${aws_vpc.vpc.id}"
}

output "subnet_id" {
  value = "${aws_subnet.main.id}"
}

output "rt_id" {
  value = "${aws_vpc.vpc.main_route_table_id}"
}

output "sg_id" {
  value = "${aws_vpc.vpc.default_security_group_id}"
}
