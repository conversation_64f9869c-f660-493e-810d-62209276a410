version: 0.2
# This buildspec.yml is called at the second stage of DeploytoProd pipeline.  
phases:
  pre_build:
    commands:
      # Defining REPO here as this is used in shell scripts to tag the repo.  
      - REPO=$(echo "**************:SmartJoules/JouleTrack-API.git")
      # Logging into ECR in region us-west-2 as the ECR repo is in oregon, and is used to pull and push images to. 
      - echo Logging in to Amazon ECR...
      - $(aws ecr get-login --no-include-email --region us-west-2)
      - apt-get update -y && apt-get install -y git
      - cd $CODEBUILD_SRC_DIR
      # ssh keys of smartjoule-dev account. 
      - cp $CODEBUILD_SRC_DIR/.build/result ~/.ssh/id_rsa
      - chmod 600 ~/.ssh/id_rsa
      - aws s3 cp s3://testing-random-123/data/prodtag.txt prodtag.txt
      - aws s3 cp s3://testing-random-123/data/devtag.txt devtag.txt
      - prodtag=$(cat prodtag.txt)
      - devtag=$(cat devtag.txt)
      # Tagging of repo has started.  
      - git clone $REPO
      - cd JouleTrack-API
      - git init
      - git checkout development  
      # Script that takes the prodtag and further pushed this tag to the REPO.
      - bash .build/scripts/tagtoproddeploy.sh

  build:
    commands:
      - cd ..
      - rm -r JouleTrack-API
      # Pulling the Docker image from ECR
      - docker pull ************.dkr.ecr.us-west-2.amazonaws.com/dejoule:$devtag
      # Tagging of image to give it ProdTag. 
      - docker tag  ************.dkr.ecr.us-west-2.amazonaws.com/dejoule:$devtag ************.dkr.ecr.us-west-2.amazonaws.com/dejoule:$prodtag 
      # Pushing prod docker image. 
      - docker push ************.dkr.ecr.us-west-2.amazonaws.com/dejoule:$prodtag
      # Replacing for prod tag in order to deploy at production.
      - sed -i -- "s/version/$prodtag/g" Dockerrun.aws.json
      - cat Dockerrun.aws.json

  post_build:
    commands:
      - echo "done...... "
artifacts:
  files:
    - Dockerrun.aws.json
  name: $prodtag