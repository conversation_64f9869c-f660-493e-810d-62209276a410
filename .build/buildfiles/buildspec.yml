version: 0.2
# This buildspec.yml is called at the second stage of Dejoule-Tag-Test pipeline.  
phases:
  pre_build:
    commands:
      # Linting 
      - npm i
      - npm run eslint
      # Defining REPO here as this is used in shell scripts to tag the repo.  
      - REPO=$(echo "**************:SmartJoules/JouleTrack-API.git")
      - echo Logging in to Amazon ECR...
      # Installing git 
      - apt-get update && apt-get install jq -y && apt-get install -y git
      # Logging into ECR in region us-west-2 as the ECR repo is in oregon, and is used to pull and push images to. 
      - $(aws ecr get-login --no-include-email --region us-west-2)
      # This command stores the ACCESS KEY and SECRET KEY at tmp/aws_cred_export.txt for terraform authentication 
      - curl *************$AWS_CONTAINER_CREDENTIALS_RELATIVE_URI | jq 'to_entries | [ .[] | select(.key | (contains("Expiration") or contains("RoleArn"))  | not) ] |  map(if .key == "AccessKeyId" then . + {"key":"AWS_ACCESS_KEY_ID"} else . end) | map(if .key == "SecretAccessKey" then . + {"key":"AWS_SECRET_ACCESS_KEY"} else . end) | map(if .key == "Token" then . + {"key":"AWS_SESSION_TOKEN"} else . end) | map("export \(.key)=\(.value)") | .[]' -r > /tmp/aws_cred_export.txt # work around https://github.com/hashicorp/terraform/issues/8746
      - cd /tmp && curl -o terraform.zip https://releases.hashicorp.com/terraform/0.11.11/terraform_0.11.11_linux_amd64.zip && echo "terraform.zip" |  unzip terraform.zip && mv terraform /usr/bin
      - cd $CODEBUILD_SRC_DIR
      # ssh keys of smartjoule-dev account. 
      - cp $CODEBUILD_SRC_DIR/.build/result ~/.ssh/id_rsa
      - chmod 600 ~/.ssh/id_rsa
      # Tagging of repo has started.  
      - git clone $REPO
      - cd JouleTrack-API
      - git init
      - git checkout development  
      # Tagdev.sh is the script that tags the repo for development tag as well as produces the prod tag that might be used in later stage if the image is pushed to Production, Both the tags are stored in S3 (Testing-random-123 bucket) 
      - bash .build/scripts/tagdev.sh
      - aws s3 cp $CODEBUILD_SRC_DIR/JouleTrack-API/devtag.txt s3://testing-random-123/data/devtag.txt
      - aws s3 cp $CODEBUILD_SRC_DIR/JouleTrack-API/prodtag.txt s3://testing-random-123/data/prodtag.txt
      - cd ..
      # Removing the cloned repo from container before image is build. 
      - rm -r JouleTrack-API

  build:
    commands:
      #Getting the Devtag to build the image here.
      - aws s3 cp s3://testing-random-123/data/devtag.txt devtag.txt
      - devtag=$(cat devtag.txt)
      # Editing the Docker.aws.json for deploying to dev env purpose  
      - sed -i -- "s/version/$devtag/g" Dockerrun.aws.json
      - cat Dockerrun.aws.json
      - echo Build started on `date`
      - echo Building the Docker image...
      # Building the docker image
      - docker build -t 878252606197.dkr.ecr.us-west-2.amazonaws.com/dejoule:$devtag .
      - echo Build completed on `date`
      - echo Pushing the Docker image...
      # Pushing the docker image 
      - docker push 878252606197.dkr.ecr.us-west-2.amazonaws.com/dejoule:$devtag
      # Copying the docker-compose.yml to s3 and is used at Infrastructure 
      - aws s3 cp docker-compose.yml s3://testing-random-123/data/docker-compose.yml
      # test.txt is also used in infrastructure that is spawned
      - echo "878252606197.dkr.ecr.us-west-2.amazonaws.com/dejoule:$devtag" > $CODEBUILD_SRC_DIR/.build/buildfiles/test.txt
      - aws s3 cp $CODEBUILD_SRC_DIR/.build/buildfiles/test.txt s3://testing-random-123/data/test.txt
      # Creating tables at ap-southeast-2 
      - apt install python-pip -y 
      - pip install boto3 
      - python .build/pythonscripts/createtables.py
      # Going into terraform folder to spawn the infra.  
      - cd .build/terraform/
      - . /tmp/aws_cred_export.txt && terraform init
      - cat /tmp/aws_cred_export.txt
      - . /tmp/aws_cred_export.txt && terraform apply -auto-approve
      - cd ../../
      
  post_build:
    commands:
    # Done. 
      - echo "done...... "
# Producing artifact for Deploy stage. 
artifacts:
  files:
    - Dockerrun.aws.json
  name: $devtag
