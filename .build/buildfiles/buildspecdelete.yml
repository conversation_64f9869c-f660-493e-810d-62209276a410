version: 0.2
# This buildspec.yml is called at the Fourth stage of Dejoule-Tag-Test pipeline. 
phases:
  pre_build:
    commands:
    
      - apt-get update && apt-get install jq -y
      # This command stores the ACCESS KEY and SECRET KEY at tmp/aws_cred_export.txt for terraform authentication 
      - curl *************$AWS_CONTAINER_CREDENTIALS_RELATIVE_URI | jq 'to_entries | [ .[] | select(.key | (contains("Expiration") or contains("RoleArn"))  | not) ] |  map(if .key == "AccessKeyId" then . + {"key":"AWS_ACCESS_KEY_ID"} else . end) | map(if .key == "SecretAccessKey" then . + {"key":"AWS_SECRET_ACCESS_KEY"} else . end) | map(if .key == "Token" then . + {"key":"AWS_SESSION_TOKEN"} else . end) | map("export \(.key)=\(.value)") | .[]' -r > /tmp/aws_cred_export.txt # work around https://github.com/hashicorp/terraform/issues/8746
      # Installing terraform in the codebuild container
      - cd /tmp && curl -o terraform.zip https://releases.hashicorp.com/terraform/0.11.11/terraform_0.11.11_linux_amd64.zip && echo "terraform.zip" |  unzip terraform.zip && mv terraform /usr/bin
      # Going into terraform folder to spawn the infra.  
      - cd $CODEBUILD_SRC_DIR
      - cd .build/terraform/
      - . /tmp/aws_cred_export.txt && terraform init
      - . /tmp/aws_cred_export.txt && terraform destroy -auto-approve
  build:
    commands:
      # $Check is the variable that is updated as per the test results, If all pass then value is yes. and even if one fails it updates No here and hence the pipeline fails at this stage of deleteinfra.  
      - echo $check
      - cd $CODEBUILD_SRC_DIR
      - apt-get install python3-pip -y 
      - pip3 install boto3
      # Deletes all the tables created at build stage at ap-southeast-2
      - python3 .build/pythonscripts/deletetables.py
      # Final check if the $check variable is yes or no and further stop or continue the pipeline. 
      - bash .build/scripts/deploy.sh 
      
      
  post_build:
    commands:
      - echo "done...... "
