version: 0.2
# This buildspec.yml is called at the second stage of DeploytoProd pipeline.  
phases:
  pre_build:
    commands:
      - aws s3 cp s3://testing-random-123/data/devtag.txt devtag.txt
      - devtag=$(cat devtag.txt)
      - apt-get update
      - apt install python-pip -y 
      - pip install boto3
      - python $CODEBUILD_SRC_DIR/.build/pythonscripts/updateDescriptionOnEbs.py -D $devtag
      - apt-get install curl -y 
      - curl -X POST --data '{"text":"The Pipeline to deploy at Dev environment has been successful","channel":"#backend-pipeline"}' https://d6qibje7eg.execute-api.ap-southeast-2.amazonaws.com/dev/users/MsgToSlack
  post_build:
    commands:
      - echo "done...... "
