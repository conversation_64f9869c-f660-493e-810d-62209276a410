#!/bin/bash
sleep 400
set -e 
cd /home/<USER>/scripts/
wget https://s3-ap-southeast-2.amazonaws.com/testing-random-123/data/docker-compose.yml
wget https://s3-ap-southeast-2.amazonaws.com/testing-random-123/data/test.txt
value=$(cat test.txt)
echo "$value"
echo "Initializing ....";
echo "Login ..."
sudo apt-get update -y
aws ecr get-login --no-include-email --region us-west-2 > /home/<USER>/scripts/login.sh
bash /home/<USER>/scripts/login.sh
echo "login complete.."
t=$(cat /home/<USER>/.aws/credentials)
echo $t
val=$(echo $t | cut -d " " -f 4)
some=$(echo $t | cut -d " " -f 7)
echo "----"
sed  -i "s@val@$val@g" docker-compose.yml
sed  -i "s@some@$some@g" docker-compose.yml
sed  -i "s@blabla@$value@g" docker-compose.yml
echo "Updating of docker-compose file done."
docker-compose up --abort-on-container-exit
sed  -i "s@Tables have been created@@g" /home/<USER>/testresult/testresult.json
sed  -i "s@waiting for table actions to become ACTIVE@@g" /home/<USER>/testresult/testresult.json
sed  -i "s@creating table actions@@g" /home/<USER>/testresult/testresult.json
python3 /home/<USER>/scripts/sendmessagetoslack.py
aws s3 cp /home/<USER>/testresult/testresult.json s3://testing-random-123/testresult/testresult.json
aws lambda invoke \
        --function-name arn:aws:lambda:ap-southeast-2:878252606197:function:CodePipelinetrigger-dev-SetTrigger \
    --invocation-type RequestResponse \
    outfile.txt
exit