#!/bin/bash
#This function pushes the tag to the branch. 
lastfunc()
{
    $(git tag $branch-$major.$minor.$patch $branch)
    echo " new tag is $branch-$major.$minor.$patch $branch"
    echo "pushing new tag now .... "
    $(git push origin $branch-$major.$minor.$patch $branch)
}

# Used to find major minor and patch values from the Development tags. 

findtagvalues()
{
    if [ $last_tag ]; then
        echo "Last Tag: $last_tag"
        version=$(echo $last_tag | grep -o '[^-]*$')
        major=$(echo $version | cut -d. -f1)
        minor=$(echo $version | cut -d. -f2)
        patch=$(echo $version | cut -d. -f3)
    fi
}

# Used to find Major, minor and patch values from production tags. 

findtagprod()
{
    if [ $last_tag_prod ]; then
        version1=$(echo $last_tag_prod | grep -o '[^-]*$')
        major1=$(echo $version1 | cut -d. -f1)
        minor1=$(echo $version1 | cut -d. -f2)
        patch1=$(echo $version1 | cut -d. -f3)
    fi

}
# finding the last prod tag. 
last_tag_prod=$(git tag -l "prod-*"  | sort -V | tail -n 1)
echo $last_tag_prod
#Finding the last dev tag.
last_tag=$(git tag -l "development-*"  | sort -V | tail -n 1)
# Finding the branch and further is used if it's development.
branch=$(git symbolic-ref HEAD | cut -d/ -f3)
#echo $branch
# check="minor"
#Increamenting the values in case of minor update
if [ "$check" = "minor" ];
then 
    findtagvalues
    findtagprod
    minor=$((minor+1))
    minor1=$((minor1+1))
    echo "this is minor";
    lastfunc
#Increamenting the values in case of major update
elif [ "$check" = "major" ];
then
    findtagvalues
    findtagprod
    major=$((major+1))
    major1=$((major1+1))
    minor1=0
    minor=0
    patch=0
    patch1=0
    echo " this is major ";
    lastfunc
#Increamenting the values in case of Bug update
elif [ "$check" = "bug" ];
then
    findtagvalues
    findtagprod
    patch=$((patch+1))
    patch1=$((patch1+1))
    echo " this is a bug ";
    lastfunc
fi

#echoing the values into a file and these files are further uploaded to s3 for further use in the pipeline. 
echo "prod-$major1.$minor1.$patch1" > $CODEBUILD_SRC_DIR/JouleTrack-API/prodtag.txt
echo "$branch-$major.$minor.$patch" > $CODEBUILD_SRC_DIR/JouleTrack-API/devtag.txt
