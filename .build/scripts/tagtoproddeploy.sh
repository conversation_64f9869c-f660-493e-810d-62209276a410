#!/bin/bash
#This script runs in deploytoprod pipeline. It gets the prodtag from s3 and further pushes it to repo and once done it merges the dev branch with master and pushes the change to master. Set -e is used here as the script needs to terminate if any process exists with a nonzerop value. 

set -e
git config --global user.email "<EMAIL>" 
git config --global user.name "Smartjoules Dev" 
echo $prodtag
git tag $prodtag
echo " new tag is $prodtag"
echo "pushing new tag now .... "
git push origin $prodtag
git checkout master
git merge -s recursive -X theirs development -m "merged for tag $prodtag"
git push origin master
