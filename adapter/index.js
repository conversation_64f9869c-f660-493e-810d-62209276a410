const AWS = require("aws-sdk");
const util = require("./utils/app.util");

let dynamoDb, client;
AWS.config.update({ region: process.env?.REGION, logger: console });
dynamoDb = new AWS.DynamoDB();
client = new AWS.DynamoDB.DocumentClient({ "service": dynamoDb });
let registeredDatastores = {};
module.exports = {
	"adapterApiVersion": 1,
	"defaults": {},
	"updateCredentials": function(datastoreConfig) {
    dynamoDb = new AWS.DynamoDB();
		client = new AWS.DynamoDB.DocumentClient({ "service": dynamoDb });
	},
	"addSchema": async function(schema) {
		try {
			const { tableName } = schema;
			registeredDatastores[tableName] = schema;
			const dynamoSchema = util.getDynamoConfig(
				registeredDatastores,
				tableName
			);
			util.populateDataStore(
				registeredDatastores[tableName],
				dynamoSchema
			);
			return true;
		} catch (err) {
			throw Error(err);
		}
	},
	"datastores": registeredDatastores,

	//////////////////////////////////////////////////////////////////////////////////////////////////
	//  ██████╗ ███╗   ███╗██╗                                                                      //
	//  ██╔══██╗████╗ ████║██║                                                                      //
	//  ██║  ██║██╔████╔██║██║                                                                      //
	//  ██║  ██║██║╚██╔╝██║██║                                                                      //
	//  ██████╔╝██║ ╚═╝ ██║███████╗                                                                 //
	//  ╚═════╝ ╚═╝     ╚═╝╚══════╝                                                                 //
	// (D)ata (M)anipulation (L)anguage                                                             //
	//                                                                                              //
	// DML adapter methods:                                                                         //
	// Methods related to manipulating records stored in the database.                              //
	//////////////////////////////////////////////////////////////////////////////////////////////////

	/**
	 *  ╔═╗╦═╗╔═╗╔═╗╔╦╗╔═╗
	 *  ║  ╠╦╝║╣ ╠═╣ ║ ║╣
	 *  ╚═╝╩╚═╚═╝╩ ╩ ╩ ╚═╝
	 * Create a new record.
	 *
	 * (e.g. add a new row to a SQL table, or a new document to a MongoDB collection.)
	 *
	 * > Note that depending on the value of `query.meta.fetch`,
	 * > you may be expected to return the physical record that was
	 * > created (a dictionary) as the second argument to the callback.
	 * > (Otherwise, exclude the 2nd argument or send back `undefined`.)
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 * @param  {String}       datastoreName The name of the datastore to perform the query on.
	 * @param  {Dictionary}   query         The stage-3 query to perform.
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 * @param  {Function}     done          Callback
	 *               @param {Error?}
	 *               @param {Dictionary?}
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 */
	"create": async function(TableName, query) {
		const record = query;
		const schema = registeredDatastores[TableName];
		if (!schema) {
			throw new Error({
				"err": `No table registered in models with ${TableName}`,
			});
		}
		try {
			const Item = util.createDynamoItem(record, schema);
			await client
				.put({
					TableName,
					Item,
				})
				.promise();
			return true;
		} catch (error) {
			throw Error(error);
		}
	},

	/**
	 *  ╔═╗╦═╗╔═╗╔═╗╔╦╗╔═╗  ╔═╗╔═╗╔═╗╦ ╦
	 *  ║  ╠╦╝║╣ ╠═╣ ║ ║╣   ║╣ ╠═╣║  ╠═╣
	 *  ╚═╝╩╚═╚═╝╩ ╩ ╩ ╚═╝  ╚═╝╩ ╩╚═╝╩ ╩
	 * Create multiple new records.
	 *
	 * > Note that depending on the value of `query.meta.fetch`,
	 * > you may be expected to return the array of physical records
	 * > that were created as the second argument to the callback.
	 * > (Otherwise, exclude the 2nd argument or send back `undefined`.)
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 * @param  {String}       datastoreName The name of the datastore to perform the query on.
	 * @param  {Dictionary}   query         The stage-3 query to perform.
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 * @param  {Function}     done            Callback
	 *               @param {Error?}
	 *               @param {Array?}
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 */
	"createEach": async function(datastoreName, query, done) {
		const TableName = query.using;
		const records = query.newRecords;
		const schema = registeredDatastores[datastoreName][TableName];
		if (!schema) {
			throw new Error({
				"err": `No table registered in models with ${TableName}`,
			});
		}
		const Items = records.map(record =>
			util.createDynamoItem(record, schema)
		);
		const batchedItems = util.createBatch(Items);
		let $query = batchedItems.map(dataArr => {
			const dQuery = { "RequestItems": {} };
			dQuery.RequestItems[TableName] = dataArr;
			return client.batchWrite(dQuery).promise();
		});
		try {
			await Promise.all($query);
			return done();
		} catch (err) {
			return done(new Error(err));
		}
	},

	/**
	 *  ╦ ╦╔═╗╔╦╗╔═╗╔╦╗╔═╗
	 *  ║ ║╠═╝ ║║╠═╣ ║ ║╣
	 *  ╚═╝╩  ═╩╝╩ ╩ ╩ ╚═╝
	 * Update matching records.
	 *
	 * > Note that depending on the value of `query.meta.fetch`,
	 * > you may be expected to return the array of physical records
	 * > that were updated as the second argument to the callback.
	 * > (Otherwise, exclude the 2nd argument or send back `undefined`.)
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 * @param  {String}       datastoreName The name of the datastore to perform the query on.
	 * @param  {Dictionary}   query         The stage-3 query to perform.
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 * @param  {Function}     done            Callback
	 *               @param {Error?}
	 *               @param {Array?}
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 */
	"update": async function(TableName, Key, updates) {
		// Look up the datastore entry (manager/driver/config).
		const schema = registeredDatastores[TableName];
		if (!schema) {
			throw new Error({
				"err": `No table registered in models with ${TableName}`,
			});
		}
		const record = updates;
		const Item = util.createDynamoItem(record, schema);
		// Here an assumption is made that update will always have keys in 0th index of where
		// This will be true in every case but I am not sure
		// Can be updated in future
		let AttributeUpdates = util.createUpdateObject(Item);
		const queryObj = {
			TableName,
			Key,
			AttributeUpdates,
		};
		try {
			await client.update(queryObj).promise();
		} catch (err) {
			throw Error(err);
		}
		return true;
	},

	/**
	 *  ╔╦╗╔═╗╔═╗╔╦╗╦═╗╔═╗╦ ╦
	 *   ║║║╣ ╚═╗ ║ ╠╦╝║ ║╚╦╝
	 *  ═╩╝╚═╝╚═╝ ╩ ╩╚═╚═╝ ╩
	 * Destroy one or more records.
	 *
	 * > Note that depending on the value of `query.meta.fetch`,
	 * > you may be expected to return the array of physical records
	 * > that were destroyed as the second argument to the callback.
	 * > (Otherwise, exclude the 2nd argument or send back `undefined`.)
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 * @param  {String}       datastoreName The name of the datastore to perform the query on.
	 * @param  {Dictionary}   query         The stage-3 query to perform.
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 * @param  {Function}     done            Callback
	 *               @param {Error?}
	 *               @param {Array?}
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 */
	"destroy": async function(TableName, Key) {
		// Look up the datastore entry (manager/driver/config).
		const schema = registeredDatastores[TableName];
		if (!schema) {
			throw new Error({
				"err": `No table registered in models with ${TableName}`,
			});
		}
		const queryObj = {
			TableName,
			Key,
		};
		try {
			await client.delete(queryObj).promise();
		} catch (err) {
			throw Error(err);
		}
		return true;
	},

	//////////////////////////////////////////////////////////////////////////////////////////////////
	//  ██████╗  ██████╗ ██╗                                                                        //
	//  ██╔══██╗██╔═══██╗██║                                                                        //
	//  ██║  ██║██║   ██║██║                                                                        //
	//  ██║  ██║██║▄▄ ██║██║                                                                        //
	//  ██████╔╝╚██████╔╝███████╗                                                                   //
	//  ╚═════╝  ╚══▀▀═╝ ╚══════╝                                                                   //
	// (D)ata (Q)uery (L)anguage                                                                    //
	//                                                                                              //
	// DQL adapter methods:                                                                         //
	// Methods related to fetching information from the database (e.g. finding stored records).     //
	//////////////////////////////////////////////////////////////////////////////////////////////////

	/**
	 *  ╔═╗╦╔╗╔╔╦╗
	 *  ╠╣ ║║║║ ║║
	 *  ╚  ╩╝╚╝═╩╝
	 * Find matching records.
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 * @param  {String}       datastoreName The name of the datastore to perform the query on.
	 * @param  {Dictionary}   query         The stage-3 query to perform.
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 * @param  {Function}     done            Callback
	 *               @param {Error?}
	 *               @param {Array}  [matching physical records]
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 */
	"find": async function(TableName, query) {
		// Look up the datastore entry (manager/driver/config).
		const schema = registeredDatastores[TableName];
		const { where, limit, sort, select } = query;
		if (sort && sort.length > 1) {
			throw Error({ "err": "Cannot sort on more than one field" });
		}
		let ScanIndexForward = true;
		if (sort === "DESC") {
			ScanIndexForward = false;
		}
		const normalizedQuery = where || query;
		const indexes = util.getIndexes(schema, normalizedQuery);
		const conditions = util.prepareQueryConditions(
			normalizedQuery,
			indexes
		);
		let LastEvaluatedKey = true;
		const dynamoQuery = {
			TableName,
			ScanIndexForward,
			"Limit": limit,
			...conditions,
		};
		if (select) {
			dynamoQuery.AttributesToGet = select;
		}
		let data;
		if (indexes.type === "localIndex") {
			console.log("Local Index Query");
			let IndexName = `${indexes.keys.hash}_local_index`;
			dynamoQuery.IndexName = IndexName;
		} else if (indexes.type === "globalIndex") {
			console.log("global index query");
			let { hash } = indexes.keys;
			let range = schema[hash].rangeKey;
			if (!range) {
				dynamoQuery.IndexName = `${hash}_global_index`;
			} else {
				dynamoQuery.IndexName = `${hash}_${range}_global_index`;
			}
		}
		let finalData = [];
		while (true) {
			try {
				if (indexes.type === "scan") {
					data = await client.scan(dynamoQuery).promise();
				}
				data = await client.query(dynamoQuery).promise();
			} catch (err) {
				throw Error(err);
			}
			finalData = [...finalData, ...data.Items];
			({ LastEvaluatedKey } = data);
			if (!LastEvaluatedKey) {
				break;
			} else {
				dynamoQuery.ExclusiveStartKey = LastEvaluatedKey;
			}
		}
		const normalizedData = finalData.map(entry =>
			util.normalizeData(entry, schema)
		);
		return normalizedData;
	},

	/**
	 *   ╦╔═╗╦╔╗╔
	 *   ║║ ║║║║║
	 *  ╚╝╚═╝╩╝╚╝
	 *  ┌─    ┌─┐┌─┐┬─┐  ┌┐┌┌─┐┌┬┐┬┬  ┬┌─┐  ┌─┐┌─┐┌─┐┬ ┬┬  ┌─┐┌┬┐┌─┐    ─┐
	 *  │───  ├┤ │ │├┬┘  │││├─┤ │ │└┐┌┘├┤   ├─┘│ │├─┘│ ││  ├─┤ │ ├┤   ───│
	 *  └─    └  └─┘┴└─  ┘└┘┴ ┴ ┴ ┴ └┘ └─┘  ┴  └─┘┴  └─┘┴─┘┴ ┴ ┴ └─┘    ─┘
	 * Perform a "find" query with one or more native joins.
	 *
	 * > NOTE: If you don't want to support native joins (or if your database does not
	 * > support native joins, e.g. Mongo) remove this method completely!  Without this method,
	 * > Waterline will handle `.populate()` using its built-in join polyfill (aka "polypopulate"),
	 * > which sends multiple queries to the adapter and joins the results in-memory.
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 * @param  {String}       datastoreName The name of the datastore to perform the query on.
	 * @param  {Dictionary}   query         The stage-3 query to perform.
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 * @param  {Function}     done          Callback
	 *               @param {Error?}
	 *               @param {Array}  [matching physical records, populated according to the join instructions]
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 */
	"join": function(datastoreName, query, done) {
		// Look up the datastore entry (manager/driver/config).
		let dsEntry = registeredDatastores[datastoreName];

		// Sanity check:
		if (_.isUndefined(dsEntry)) {
			return done(
				new Error(
					"Consistency violation: Cannot do that with datastore (`" +
						datastoreName +
						"`) because no matching datastore entry is registered in this adapter!  This is usually due to a race condition (e.g. a lifecycle callback still running after the ORM has been torn down), or it could be due to a bug in this adapter.  (If you get stumped, reach out at https://sailsjs.com/support.)"
				)
			);
		}

		// Perform the query and send back a result.
		//
		// > TODO: Replace this setTimeout with real logic that calls
		// > `done()` when finished. (Or remove this method from the
		// > adapter altogether
		setTimeout(() => {
			return done(
				new Error("Adapter method (`join`) not implemented yet.")
			);
		}, 16);
	},

	/**
	 *  ╔═╗╔═╗╦ ╦╔╗╔╔╦╗
	 *  ║  ║ ║║ ║║║║ ║
	 *  ╚═╝╚═╝╚═╝╝╚╝ ╩
	 * Get the number of matching records.
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 * @param  {String}       datastoreName The name of the datastore to perform the query on.
	 * @param  {Dictionary}   query         The stage-3 query to perform.
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 * @param  {Function}     done          Callback
	 *               @param {Error?}
	 *               @param {Number}  [the number of matching records]
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 */
	"count": function(datastoreName, query, done) {
		// Look up the datastore entry (manager/driver/config).
		let dsEntry = registeredDatastores[datastoreName];

		// Sanity check:
		if (_.isUndefined(dsEntry)) {
			return done(
				new Error(
					"Consistency violation: Cannot do that with datastore (`" +
						datastoreName +
						"`) because no matching datastore entry is registered in this adapter!  This is usually due to a race condition (e.g. a lifecycle callback still running after the ORM has been torn down), or it could be due to a bug in this adapter.  (If you get stumped, reach out at https://sailsjs.com/support.)"
				)
			);
		}

		// Perform the query and send back a result.
		//
		// > TODO: Replace this setTimeout with real logic that calls
		// > `done()` when finished. (Or remove this method from the
		// > adapter altogether
		setTimeout(() => {
			return done(
				new Error("Adapter method (`count`) not implemented yet.")
			);
		}, 16);
	},

	/**
	 *  ╔═╗╦ ╦╔╦╗
	 *  ╚═╗║ ║║║║
	 *  ╚═╝╚═╝╩ ╩
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 * @param  {String}       datastoreName The name of the datastore to perform the query on.
	 * @param  {Dictionary}   query         The stage-3 query to perform.
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 * @param  {Function}     done          Callback
	 *               @param {Error?}
	 *               @param {Number}  [the sum]
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 */
	"sum": function(datastoreName, query, done) {
		// Look up the datastore entry (manager/driver/config).
		let dsEntry = registeredDatastores[datastoreName];

		// Sanity check:
		if (_.isUndefined(dsEntry)) {
			return done(
				new Error(
					"Consistency violation: Cannot do that with datastore (`" +
						datastoreName +
						"`) because no matching datastore entry is registered in this adapter!  This is usually due to a race condition (e.g. a lifecycle callback still running after the ORM has been torn down), or it could be due to a bug in this adapter.  (If you get stumped, reach out at https://sailsjs.com/support.)"
				)
			);
		}

		// Perform the query and send back a result.
		//
		// > TODO: Replace this setTimeout with real logic that calls
		// > `done()` when finished. (Or remove this method from the
		// > adapter altogether
		setTimeout(() => {
			return done(
				new Error("Adapter method (`sum`) not implemented yet.")
			);
		}, 16);
	},

	/**
	 *  ╔═╗╦  ╦╔═╗
	 *  ╠═╣╚╗╔╝║ ╦
	 *  ╩ ╩ ╚╝ ╚═╝
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 * @param  {String}       datastoreName The name of the datastore to perform the query on.
	 * @param  {Dictionary}   query         The stage-3 query to perform.
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 * @param  {Function}     done          Callback
	 *               @param {Error?}
	 *               @param {Number}  [the average ("mean")]
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 */
	"avg": function(datastoreName, query, done) {
		// Look up the datastore entry (manager/driver/config).
		let dsEntry = registeredDatastores[datastoreName];

		// Sanity check:
		if (_.isUndefined(dsEntry)) {
			return done(
				new Error(
					"Consistency violation: Cannot do that with datastore (`" +
						datastoreName +
						"`) because no matching datastore entry is registered in this adapter!  This is usually due to a race condition (e.g. a lifecycle callback still running after the ORM has been torn down), or it could be due to a bug in this adapter.  (If you get stumped, reach out at https://sailsjs.com/support.)"
				)
			);
		}

		// Perform the query and send back a result.
		//
		// > TODO: Replace this setTimeout with real logic that calls
		// > `done()` when finished. (Or remove this method from the
		// > adapter altogether
		setTimeout(() => {
			return done(
				new Error("Adapter method (`avg`) not implemented yet.")
			);
		}, 16);
	},

	//////////////////////////////////////////////////////////////////////////////////////////////////
	//  ██████╗ ██████╗ ██╗                                                                         //
	//  ██╔══██╗██╔══██╗██║                                                                         //
	//  ██║  ██║██║  ██║██║                                                                         //
	//  ██║  ██║██║  ██║██║                                                                         //
	//  ██████╔╝██████╔╝███████╗                                                                    //
	//  ╚═════╝ ╚═════╝ ╚══════╝                                                                    //
	// (D)ata (D)efinition (L)anguage                                                               //
	//                                                                                              //
	// DDL adapter methods:                                                                         //
	// Methods related to modifying the underlying structure of physical models in the database.    //
	//////////////////////////////////////////////////////////////////////////////////////////////////

	/**
	 *  ╔╦╗╔═╗╔═╗╦╔╗╔╔═╗
	 *   ║║║╣ ╠╣ ║║║║║╣
	 *  ═╩╝╚═╝╚  ╩╝╚╝╚═╝
	 * Build a new physical model (e.g. table/etc) to use for storing records in the database.
	 *
	 * (This is used for schema migrations.)
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 * @param  {String}       datastoreName The name of the datastore containing the table to define.
	 * @param  {String}       tableName     The name of the table to define.
	 * @param  {Dictionary}   definition    The physical model definition (not a normal Sails/Waterline model-- log this for details.)
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 * @param  {Function}     done           Callback
	 *               @param {Error?}
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 */
	"define": function(datastoreName, tableName, definition, done) {
		// Look up the datastore entry (manager/driver/config).
		let dsEntry = registeredDatastores[datastoreName];

		// Sanity check:
		if (_.isUndefined(dsEntry)) {
			return done(
				new Error(
					"Consistency violation: Cannot do that with datastore (`" +
						datastoreName +
						"`) because no matching datastore entry is registered in this adapter!  This is usually due to a race condition (e.g. a lifecycle callback still running after the ORM has been torn down), or it could be due to a bug in this adapter.  (If you get stumped, reach out at https://sailsjs.com/support.)"
				)
			);
		}

		// Define the physical model (e.g. table/etc.)
		//
		// > TODO: Replace this setTimeout with real logic that calls
		// > `done()` when finished. (Or remove this method from the
		// > adapter altogether
		setTimeout(() => {
			return done(
				new Error("Adapter method (`define`) not implemented yet.")
			);
		}, 16);
	},

	/**
	 *  ╔╦╗╦═╗╔═╗╔═╗
	 *   ║║╠╦╝║ ║╠═╝
	 *  ═╩╝╩╚═╚═╝╩
	 * Drop a physical model (table/etc.) from the database, including all of its records.
	 *
	 * (This is used for schema migrations.)
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 * @param  {String}       datastoreName The name of the datastore containing the table to drop.
	 * @param  {String}       tableName     The name of the table to drop.
	 * @param  {Ref}          unused        Currently unused (do not use this argument.)
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 * @param  {Function}     done          Callback
	 *               @param {Error?}
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 */
	"drop": function(datastoreName, tableName, unused, done) {
		// Look up the datastore entry (manager/driver/config).
		let dsEntry = registeredDatastores[datastoreName];

		// Sanity check:
		if (_.isUndefined(dsEntry)) {
			return done(
				new Error(
					"Consistency violation: Cannot do that with datastore (`" +
						datastoreName +
						"`) because no matching datastore entry is registered in this adapter!  This is usually due to a race condition (e.g. a lifecycle callback still running after the ORM has been torn down), or it could be due to a bug in this adapter.  (If you get stumped, reach out at https://sailsjs.com/support.)"
				)
			);
		}

		// Drop the physical model (e.g. table/etc.)
		//
		// > TODO: Replace this setTimeout with real logic that calls
		// > `done()` when finished. (Or remove this method from the
		// > adapter altogether
		setTimeout(() => {
			return done(
				new Error("Adapter method (`drop`) not implemented yet.")
			);
		}, 16);
	},

	/**
	 *  ╔═╗╔═╗╔╦╗  ┌─┐┌─┐┌─┐ ┬ ┬┌─┐┌┐┌┌─┐┌─┐
	 *  ╚═╗║╣  ║   └─┐├┤ │─┼┐│ │├┤ ││││  ├┤
	 *  ╚═╝╚═╝ ╩   └─┘└─┘└─┘└└─┘└─┘┘└┘└─┘└─┘
	 * Set a sequence in a physical model (specifically, the auto-incrementing
	 * counter for the primary key) to the specified value.
	 *
	 * (This is used for schema migrations.)
	 *
	 * > NOTE - If your adapter doesn't support sequence entities (like PostgreSQL),
	 * > you should remove this method.
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 * @param  {String}       datastoreName   The name of the datastore containing the table/etc.
	 * @param  {String}       sequenceName    The name of the sequence to update.
	 * @param  {Number}       sequenceValue   The new value for the sequence (e.g. 1)
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 * @param  {Function}     done
	 *               @param {Error?}
	 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
	 */
	"setSequence": function(datastoreName, sequenceName, sequenceValue, done) {
		// Look up the datastore entry (manager/driver/config).
		let dsEntry = registeredDatastores[datastoreName];

		// Sanity check:
		if (_.isUndefined(dsEntry)) {
			return done(
				new Error(
					"Consistency violation: Cannot do that with datastore (`" +
						datastoreName +
						"`) because no matching datastore entry is registered in this adapter!  This is usually due to a race condition (e.g. a lifecycle callback still running after the ORM has been torn down), or it could be due to a bug in this adapter.  (If you get stumped, reach out at https://sailsjs.com/support.)"
				)
			);
		}

		// Update the sequence.
		//
		// > TODO: Replace this setTimeout with real logic that calls
		// > `done()` when finished. (Or remove this method from the
		// > adapter altogether
		setTimeout(() => {
			return done(
				new Error("Adapter method (`setSequence`) not implemented yet.")
			);
		}, 16);
	},
};
