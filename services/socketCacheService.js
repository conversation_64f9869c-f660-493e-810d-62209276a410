const redisClient = require("../connection/redisClient");
const logger = require("../services/logger");
const cacheTTL = 60 * 60;
/**
 * @description Stores filtered recent data in cache with a 3-minute TTL.
 * Filters out invalid values and excludes objects with no valid data.
 * @param {Array} componentData - Array of data objects (each containing deviceId, siteId, timestamp, data).
 */
const setRecentComponentCache = async (componentData) => {
  try {
    const cacheData = componentData.reduce((acc, {deviceId, siteId, timestamp, data}) => {
      if (isValidData(data)) {
        const filteredData = {
          siteId,
          data: filterValidData(data),
          timestamp,
          deviceId,
        };
        acc.push(assetDataCacheKey(deviceId), JSON.stringify(filteredData));
      }
      return acc;
    }, []);

    if (cacheData.length > 0) {
      const msetResponse = await redisClient.mset(cacheData);
      if (msetResponse === -111) {
        logger.error(`Redis client disconnected errorCode is -111`);
        return false;
      }
      await setTTL(componentData);
      return true;
    }
    return false;
  } catch (err) {
    logger.error("Error storing recent data in cache:", err);
  }
};

/**
 * Checks if the provided data object is valid.
 * @param {Object} data - The data object to validate.
 * @returns {Boolean} - Returns true if data is valid, otherwise false.
 */
const isValidData = (data) => data && typeof data === "object";

/**
 * Filters out key-value pairs from an object where the value is null, undefined, or a string.
 * @param {Object} data - The object to be filtered.
 * @returns {Object} - A new object with only the valid key-value pairs.
 */
const filterValidData = (data) => Object.fromEntries(Object.entries(data).filter(([_, value]) => value != null && typeof value !== "string"));

/**
 * @description Sets 7 days TTL for each cache key.
 * @param {Array} componentData - Array of data objects (each containing deviceId).
 */
const setTTL = async (componentData) => {
  await Promise.all(componentData.map(({deviceId}) => redisClient.expire(assetDataCacheKey(deviceId), cacheTTL)));
};

/**
 * @description Generates a cache key based on the deviceId.
 * @param {String} deviceId - The ID of the device.
 * @returns {String} - The generated cache key.
 */
const assetDataCacheKey = (deviceId) => `asset:${deviceId}:recent-data`;

const setRecentDeviceCache = async (dataObj) => {
  if (!isValidData(dataObj)) {
    logger.error(`Invalid data provided for device ID: ${dataObj?.deviceId}`);
    return false;
  }
  const {siteId, timestamp, data, deviceId} = dataObj;
  let parsedData;
  if (typeof data == "string") {
    try {
      parsedData = JSON.parse(data);
    } catch (error) {
      logger.error(`Failed to parse JSON data for device ID: ${deviceId}`, error);
      return false;
    }
  } else {
    parsedData = data;
  }
  const filteredData = filterValidData(parsedData);

  if (Object.keys(filteredData).length > 0) {
    const cacheKey = assetDataCacheKey(deviceId);
    const cacheValue = JSON.stringify({
      siteId,
      deviceId,
      data: filteredData,
      timestamp,
    });
    const setResponse = await redisClient.set(cacheKey, cacheValue);
    if (setResponse !== "OK") {
      logger.error(`Failed to set cache for device ID: ${deviceId}`);
      return false;
    }
    const ttlResponse = await redisClient.expire(cacheKey, cacheTTL);
    if (ttlResponse === -111) {
      logger.error(`Redis client disconnected errorCode is -111`);
      return false;
    }
    return true;
  } else {
    logger.warn(`No valid data for device ID: ${deviceId}`);
    return false;
  }
};

module.exports = {setRecentComponentCache, setRecentDeviceCache};
