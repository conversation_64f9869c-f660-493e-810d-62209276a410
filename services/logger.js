const pino = require("pino");
const pretty = require("pino-pretty");
const Sentry = require("./sentryHandler");

const stream = pretty({
  colorize: true, // Disable colors for plain text
  translateTime: "yyyy-mm-dd HH:MM:ss.l", // Optional: format the timestamp
  messageFormat: "[{level}] {msg}", // Custom format
  levelFirst: true, // Ensure the level appears first
  ignore: "pid,hostname,v",
});

const logger = pino(
  {
    level: process.env.LOG_LEVEL || "info",
  },
  stream,
);

/**Custom logger function to send logs to Sentry*/
const customLogger = {
  trace: (msg, ...args) => {
    logger.trace(msg, ...args);
  },
  debug: (msg, ...args) => {
    logger.debug(msg, ...args);
  },
  info: (msg, ...args) => {
    logger.info(msg, ...args);
  },
  warn: (msg, ...args) => {
    logger.warn(msg, ...args);
  },
  error: (msg, ...args) => {
    logger.error(msg, ...args);
  },
  fatal: (msg, ...args) => {
    logger.fatal(msg, ...args);
  },
  /**Custom logging method for critical issues*/
  critical: (msg, ...args) => {
    logger.fatal(msg, ...args);
    Sentry.captureException(new Error(msg), { level: "fatal" });
  },
};

module.exports = customLogger;
