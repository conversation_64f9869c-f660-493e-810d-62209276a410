const axios = require("axios");
const redisClient = require("../connection/redisClient");
const config = require("../config/config");
const logger = require("../services/logger");

module.exports = {
  async doHandleJouletrackCommandFeedback(message) {
    const { uniqId } = message;
    const commandUidINCache = await redisClient.exists(`command:${uniqId}`);
    if (!commandUidINCache) {
      logger.info(`command uid is not registered ${uniqId}`);
      return;
    }
    if (commandUidINCache === -111) {
      logger.info(`[Redis] unable to retrieve commandUid:${uniqId} and errorCode is -111`);
    }
    try {
      await axios.post(`${config.API_V2_SERVICE_URL}/m2/control/jouletrack/feedback-webhook`, message);
    } catch (e) {
      logger.error(e);
    }
  },
  async doHandleUserConfigDataParam(payload) {
    const { requestId, siteId, componentId, status, error } = payload;
    return axios.put(`${config.API_V2_SERVICE_URL}/m2/site/${siteId}/component/${componentId}/user-config-data-param?requestId=${requestId}&status=${Number(status)}&error=${error}`);
  },
  async doHandleComponentRecentDataRequest(message) {
    const { uuid, siteId, componentId, paramList, lastValidDays } = message;
    const payload = { uuid, paramList, lastValidDays };
    await axios.post(`${config.API_V2_SERVICE_URL}/m2/site/${siteId}/component/${componentId}/recent-data-by-params`, payload);
  },
  async doHandleRecipeFeedback(payload) {
    const { siteId, func, operation } = payload;
    const rid = payload?.recipeInfo?.rid || payload?.data?.[0]?.rid || payload?.data?.rid;

    try {
      const response = await axios.post(`${config.API_V2_SERVICE_URL}/m2/v2/site/${siteId}/super-recipe/feedback`, payload);
      logger.info(`Recipe logic feedback sent for site=${siteId}, rid=${rid}, func=${func}, operation=${operation}`);
      return response.data;
    } catch (error) {
      logger.error(`Failed to send recipe logic feedback for site=${siteId}, rid=${rid}: ${error.message}`);
      throw error;
    }
  },
};
