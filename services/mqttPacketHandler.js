const Sentry = require("./sentryHandler");
const axios = require("axios");
const IoTTopicHandler = require("./iotTopicHandler");
const { setRecentDeviceCache, setRecentComponentCache } = require("./socketCacheService");
const mqttClient = require("../connection/mqttClient");
const { Readable } = require("stream");
const config = require("../config/config");
const logger = require("./logger");

const objectToStream = (obj) =>
  new Readable({
    read() {
      this.push(JSON.stringify(obj));
      this.push(null);
    },
  });

/** BETA SITES Commenting this line as QOS data pipeline is not running for beta*/
// const sitesSyncedOnBeta = ["mgch", "aph-ahm", "asm-kot", "chn-hyd", "frtn", "amh-cal"];

const triggerPacketHandler = async (topic, data, packetUuid) => {
  const siteId = topic.split("/")[0];
  let dataObj = Buffer.isBuffer(data) ? data.toString() : data;

  try {
    dataObj = JSON.parse(dataObj);
    if (siteId !== "jouletrack") {
      dataObj.siteId = siteId;
    }
  } catch (e) {
    dataObj = data.toString();
  }

  try {
    if (/.*\/qos\/trigger$/.test(topic)) {
      await handleQosTrigger(topic, dataObj, packetUuid, siteId);
    } else if (/.*\/feedback\/\d+\/jouletrack$/.test(topic)) {
      await handleJouleTrackFeedback(topic, dataObj, packetUuid);
    } else if (/[^\/]+\/request\/[^\/]+\/recentData$/.test(topic)) {
      await handleRecentDataRequest(topic, dataObj, siteId, packetUuid);
    } else if (/.*\/feedback\/\d+\/recipelogicconfig$/.test(topic)) {
      await handleRecipeLogicConfig(topic, dataObj, packetUuid);
    } else if (/.*\/feedback\/\d+\/recipeControl$/.test(topic)) {
      await handleRecipeControl(topic, dataObj, packetUuid);
    } else if (/.*\/request\/\d+\/recipesync$/.test(topic)) {
      await handleRecipeSync(topic, dataObj, packetUuid);
    } else if (/^[^\/]+\/data\/[^\/]+\/recent$/.test(topic)) {
      await handleDeviceDataRequest(topic, dataObj, packetUuid);
    } else if (/^[a-zA-Z0-9_-]+\/feedback\/[a-zA-Z0-9\-_]+\/componentsync$/.test(topic)) {
      await handleComponentDataRequest(topic, dataObj, packetUuid);
    } else if (/^[^\/]+\/response\/bmsx\/connector-tags$/.test(topic)) {
      await handleBMSXDiscoveryResponse(topic, dataObj, packetUuid);
    } else {
      await handleControllerFeedback(topic, dataObj, packetUuid);
    }
  } catch (error) {
    const errorMessage = `txnId=${packetUuid} topic=${topic} error="${error.message || "Unexpected error occurred"}"`;
    logger.error(errorMessage);
    logger.error(error);
    Sentry.setTag("topic", topic);
    Sentry.setTag("uuid", packetUuid);
    Sentry.setTag("siteId", siteId);
    Sentry.captureMessage(errorMessage);
    Sentry.captureException(error);
  }
};

const handleQosTrigger = async (topic, dataObj, packetUuid, siteId) => {
  logger.debug(`txnId=${packetUuid} handler=qosTrigger topic=${topic} message="packet received"`);

  try {
    await axios.post(`${config.QOS_CONSUMER_SERVICE_URL}/storetrigger`, { dataObj });
    logger.debug(`txnId=${packetUuid} handler=qosTrigger topic=${topic} message="packet sent to destination" route="${config.QOS_CONSUMER_SERVICE_URL}/storetrigger"`);
  } catch (error) {
    logger.error(`txnId=${packetUuid} handler=qosTrigger topic=${topic} error="Prod QoS consumer service: ${error.message || "Cannot connect to QoS consumer service"}" route="${config.QOS_CONSUMER_SERVICE_URL}/storetrigger"`);
  }
};

const handleJouleTrackFeedback = async (topic, dataObj, packetUuid) => {
  logger.debug(`txnId=${packetUuid} handler=JouleTrackCommandFeedback topic=${topic} message="packet received" packet=${JSON.stringify(dataObj, null, 2)}`);
  try {
    await IoTTopicHandler.doHandleJouletrackCommandFeedback(dataObj);
    logger.debug(`txnId=${packetUuid} handler=JouleTrackCommandFeedback topic=${topic} message="packet processed successfully"`);
  } catch (error) {
    logger.error(`txnId=${packetUuid} handler=JouleTrackCommandFeedback topic=${topic} error="${error.message || "Error while processing doHandleJouletrackCommandFeedback function"}" route="${config.API_V2_SERVICE_URL}/m2/control/jouletrack/feedback-webhook"`);
  }
};

const handleRecentDataRequest = async (topic, dataObj, siteId, packetUuid) => {
  const componentId = topic.split("/")[2];
  const requestObject = { ...dataObj, siteId, componentId };

  try {
    await IoTTopicHandler.doHandleComponentRecentDataRequest(requestObject);
  } catch (e) {
    const errorObject = {
      uuid: dataObj.uuid,
      statusCode: 0,
      reqObj: dataObj,
      errMessage: `${e.response?.data?.code || "Unknown Error Code"} - ${e.message}`,
    };
    mqttClient.publish(`${siteId}/response/${componentId}/recentData`, JSON.stringify(errorObject));
  }
};

const handleControllerFeedback = async (topic, dataObj, packetUuid) => {
  logger.info(`txnId=${packetUuid} handler=ComponentSocketDataHandler topic=${topic} message="packet received"`);
  const isComponentSocketDataTopic = /[^\/]+\/componentdata\/all\/recent$/.test(topic);
  if (isComponentSocketDataTopic) {
    const response = await setRecentComponentCache(dataObj);
    if (response) {
      logger.info(`txnId=${packetUuid} handler=ComponentSocketDataHandler topic=${topic} message="Component data stored in cache"`);
    }
  }
  try {
    const stream = objectToStream({ topic, dataObj });
    const headers = {
      "Content-Type": "application/json",
      "Transfer-Encoding": "chunked",
      "X-Request-Id": packetUuid,
    };
    await axios.post(`${config.API_SERVICE_URL}/v1/event/recieve`, stream, {
      headers,
    });
    await notifyStagingService(topic, packetUuid, headers, stream);
    if (isComponentSocketDataTopic) {
      logger.debug(`txnId=${packetUuid} handler=ControllerFeedback topic=${topic} message="packet processed successfully" route=${config.API_SERVICE_URL}/v1/event/recieve}`);
    }
  } catch (error) {
    logger.critical(`txnId=${packetUuid} handler=ControllerFeedback topic=${topic} error="${error.message || "Error while processing Controller Feedback function"}" route="${config.API_SERVICE_URL}/v1/event/recieve"`);
  }
};

/**Handler for Component Data Pipeline for Staging Service*/
const notifyStagingService = async (topic, packetUuid, headers, stream) => {
  try {
    await axios.post(`${config.API_STAGING_SERVICE_URL}/v1/event/recieve`, stream, {
      headers,
    });
  } catch (error) {
    logger.critical(`txnId=${packetUuid} handler=ControllerFeedback topic=${topic} error="${error.message || "Error while processing Controller Feedback function"}" route="${config.API_STAGING_SERVICE_URL}/v1/event/recieve"`);
  }
};

const handleComponentDataRequest = async (topic, dataObj, packetUuid) => {
  logger.info(`txnId=${packetUuid} handler=handleComponentDataRequest topic=${topic} message="Component user config data param received for sync"`);
  try {
    const response = await IoTTopicHandler.doHandleUserConfigDataParam(dataObj);
    if (response) {
      logger.info(`txnId=${packetUuid} handler=handleComponentDataRequest topic=${topic} message="Component user config data param successfully updated"`);
    }
  } catch (err) {
    logger.critical(`txnId=${packetUuid} handler=handleComponentDataRequest topic=${topic} error="${err.message || "Error while updating user config data param to component"}"`);
  }
};

const handleRecipeLogicConfig = async (topic, dataObj, packetUuid) => {
  const { func, operation, siteId, data, recipeInfo } = dataObj;
  const rid = data?.rid || recipeInfo?.rid || data?.[0]?.rid;

  logger.info(`txnId=${packetUuid} handler=handleRecipeLogicConfig topic=${topic} message="Recipe Logic Config Received"`);

  try {
    const response = await IoTTopicHandler.doHandleRecipeFeedback(dataObj);
    if (response) {
      logger.info(JSON.stringify(response, null, 2));

      if (func === "deleteRecipe") {
        logger.info(`txnId=${packetUuid} handler=handleRecipeLogicConfig topic=${topic} message="Recipe deleted for site=${siteId}, rid=${rid}"`);
      } else if (operation === "recipeInit") {
        logger.info(`txnId=${packetUuid} handler=handleRecipeLogicConfig topic=${topic} message="Recipe deployed for site=${siteId}, rid=${rid}"`);
      } else {
        logger.info(`txnId=${packetUuid} handler=handleRecipeLogicConfig topic=${topic} message="Unhandled recipe logic config event"`);
      }
    }
  } catch (err) {
    logger.critical(`txnId=${packetUuid} handler=handleRecipeLogicConfig topic=${topic} error="${err.message}"`);
    logger.critical(JSON.stringify(err));
  }
};

const handleRecipeControl = async (topic, dataObj, packetUuid) => {
  const { func, siteId, data, recipeInfo } = dataObj;
  const rid = data?.rid || recipeInfo?.rid || data?.[0]?.rid;

  logger.info(`txnId=${packetUuid} handler=handleRecipeControl topic=${topic} message="Recipe Control Received"`);

  try {
    const response = await IoTTopicHandler.doHandleRecipeFeedback(dataObj);
    if (response) {
      logger.info(JSON.stringify(response, null, 2));

      if (func === "startStopRecipe") {
        const switchOff = data?.[0]?.switchOff === "1";
        const status = switchOff ? "paused" : "start";
        logger.info(`txnId=${packetUuid} handler=handleRecipeControl topic=${topic} message="Recipe ${status} for site=${siteId}, rid=${rid}"`);
      } else {
        logger.info(`txnId=${packetUuid} handler=handleRecipeControl topic=${topic} message="Unhandled recipe control event"`);
      }
    }
  } catch (err) {
    logger.critical(`txnId=${packetUuid} handler=handleRecipeControl topic=${topic} error="${err.message}"`);
    logger.critical(JSON.stringify(err));
  }
};

const handleRecipeSync = async (topic, dataObj, packetUuid) => {
  const { operation, siteId, data, recipeInfo } = dataObj;
  const rid = data?.rid || recipeInfo?.rid || data?.[0]?.rid;

  logger.info(`txnId=${packetUuid} handler=handleRecipeSync topic=${topic} message="Recipe Sync Received"`);

  try {
    const response = await IoTTopicHandler.doHandleRecipeFeedback(dataObj);
    if (response) {
      logger.info(JSON.stringify(response, null, 2));

      if (operation === "recipeInit") {
        logger.info(`txnId=${packetUuid} handler=handleRecipeSync topic=${topic} message="Recipe deployed for site=${siteId}, rid=${rid}"`);
      } else {
        logger.info(`txnId=${packetUuid} handler=handleRecipeSync topic=${topic} message="Unhandled recipe sync event"`);
      }
    }
  } catch (err) {
    logger.critical(`txnId=${packetUuid} handler=handleRecipeSync topic=${topic} error="${err.message}"`);
    logger.critical(JSON.stringify(err));
  }
};

const handleDeviceDataRequest = async (topic, dataObj, packetUuid) => {
  logger.info(`txnId=${packetUuid} handler=DeviceSocketDataHandler topic=${topic} message="Device socket data received"`);
  try {
    const response = await setRecentDeviceCache(dataObj);
    if (response) {
      logger.info(`txnId=${packetUuid} handler=DeviceSocketDataHandler topic=${topic} message="Device data stored in cache"`);
    }
  } catch (error) {
    logger.critical(`txnId=${packetUuid} handler=DeviceSocketDataHandler topic=${topic} error="${error.message || "Error while storing device data in cache"}"`);
  }
};

const handleBMSXDiscoveryResponse = async (topic, dataObj, packetUuid) => {
  logger.info(`txnId=${packetUuid} handler=BMSXDiscoveryResponse topic=${topic} message="BMSX discovery response received"`);

  try {
    const response = await IoTTopicHandler.doHandleBMSXDiscoveryResponse(dataObj);
    if (response) {
      logger.info(`txnId=${packetUuid} handler=BMSXDiscoveryResponse topic=${topic} message="BMSX discovery response processed successfully"`);
    }
  } catch (error) {
    logger.critical(`txnId=${packetUuid} handler=BMSXDiscoveryResponse topic=${topic} error="${error.message || "Error while processing BMSX discovery response"}"`);
  }
};

module.exports = { triggerPacketHandler };
