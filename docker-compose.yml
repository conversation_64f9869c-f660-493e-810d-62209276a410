# used in the spawned infrastructure to create a replica of prod env. 
version: "2"
services:
  redis:
    image: redis
    ports:
      - 6379:6379
    container_name: redis
  broker:
    image: toke/mosquitto
    ports:
      - 1883:1883
      - 9001:9001
    container_name: broker
  node:
    image: blabla
    depends_on:
      - redis
      - broker
    container_name: node
    ports:
      - 1337:1337
    entrypoint: bash /usr/smartjoules/api/.build/docker-entrypoint.sh
    volumes:
      - /home/<USER>/testresult:/usr/smartjoules/api/testresult
    environment:
    # Values added in the infrastructure. 
      - ACCESS_KEY=val
      - SECRET_KEY=some
      - REGION=ap-southeast-2
      - NODE_ENV=testing
      - TEST_ENV=server
