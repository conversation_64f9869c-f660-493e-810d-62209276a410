const Sentry = require("./services/sentryHandler");
const uuid = require("uuid");
const config = require("./config/config");
const server = require("./server");
const logger = require("./services/logger");
const redisClient = require("./connection/redisClient");
const mqttClientConnector = require("./connection/mqttClient");
const { triggerPacketHandler } = require("./services/mqttPacketHandler");
const mqttClient = mqttClientConnector.getConnection();

mqttClient.on("connect", () => {
  subscribeToTopics();
});

mqttClient.on("message", (topic, message) => {
  triggerPacketHandler(topic, message, uuid.v4());
});

server();

function subscribeToTopics() {
  mqttClient.subscribe(config.subscribeTopics, (error) => _handleSubscriptionError(error));
  mqttClient.subscribe(config.subscribeTopics2, (error) => _handleSubscriptionError(error));
  logger.info("Subscribed to topics.");
}

function _handleSubscriptionError(error) {
  if (error) {
    Sentry.captureException(error);
    this.logger.error("Error subscribing to topic:", error);
  }
}

/**Process handling for graceful shutdown*/
process.on("uncaughtException", async (error) => {
  logger.error("unhandledRejection", error);
  Sentry.captureException(error);
  await _handleShutdown();
});
process.on("unhandledRejection", async (error) => {
  logger.error("unhandledRejection", error);
  Sentry.captureException(error);
  await _handleShutdown();
});

async function _handleShutdown() {
  logger.error("Gracefully shutting down...");
  await Sentry.flush();
  /**Close MQTT client connection*/
  if (mqttClient) {
    mqttClient.end(false, () => {
      logger.error("MQTT client disconnected");
    });
  }

  /**Close Redis client connection*/
  if (redisClient) {
    redisClient.quit(() => {
      logger.error("Redis client disconnected");
    });
  }

  process.exit(0);
}
