module.exports = {
	"env": {
		"node": true,
		"es6": true,
		"mocha":true,
	},
	"globals": {
		"Recipe": true,
		"helper": true,
		"athenaService": true,
		"Alert": true,
		"sails": true,
		"jwtService": true,
		"Users": true,
		"cacheService": true,
		"eventService": true,
		"UserSiteMap": true,
		"DyanmoKeyStore": true,
		"Baseline": true,
		"baselineService": true,
		"DailyConsumption": true,
		"diagnosticService": true,
		"Command": true,
		"commandService": true,
		"Sites": true,
		"siteService": true,
		"Component": true,
		"componentService": true,
		"DataDevice": true,
		"dataDeviceService": true,
		"Devices": true,
		"helper": true,
		"deviceService": true,
		"DeviceType": true,
		"Recipe": true,
		"Datadevice": true,
		"dailyConsumptionService": true,
		"Mode": true,
		"dbHelper": true,
		"RecipeService": true,
		"modeService": true,
		"recipeService": true,
		"alertService": true,
		"Schedules": true,
		"IFTTT_helper": true,
		"Repaired": true,
		"Unrepaired": true,
		"Role": true,
		"repairedService": true,
		"userService": true,
		"sendsms": true,
		"snsHelper": true,
		"ChangeLog": true,
		"broadcastService": true,
		"parameterService": true

	},
	"extends": "eslint:recommended",
	"parserOptions": {
		"sourceType": "module",
		"ecmaVersion": 9
	},
	"rules": {
		"indent": [
			"error",
			"tab"
		],
		"linebreak-style": [
			"error",
			"unix"
		],
		"quotes": [
			"error",
			"double"
		],
		"semi": [
			"error",
			"always"
		],
		"no-use-before-define": [
			"error",
			{ "classes": true, "functions": false }
		],
		"no-irregular-whitespace": [
			"error",
			{
				"skipStrings": true,
				"skipComments": true,
				"skipRegExps": true,
				"skipTemplates": true
			}
		],
		"no-constant-condition": [
			"error",
			{ "checkLoops": false }
		],
		"global-require": "error",
		"no-ex-assign": "error",
		"no-ex-assign": "error",
		"strict": 2,
		"camelcase": 1,
		"comma-spacing": [
			2,
			{
				"before": false,
				"after": true
			}
		],
		"key-spacing": 1,
		"keyword-spacing": [1, {
			"before": true,
			"after": true
		}],
		"no-unused-vars": ["error", { "args": "none" }],
		"no-mixed-spaces-and-tabs": 2,
		"no-multiple-empty-lines": 2,
		"quote-props": ["error", "always"],
		"no-var": "error"
	}
};
