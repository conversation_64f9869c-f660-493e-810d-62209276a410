// const moment = require("moment-timezone");
// const assert = require("chai").assert;
// const should = require("chai").should();
const supertest = require("supertest")("http://localhost:1337");

let token;
describe("Devices Controller Test Suite Starts", () => {
	before(function () {
		token = global.token;
	});

	describe("POST /:siteId/updateEMList => fn UpdateEMList ", () => {
		it("No params send 400 status code", async () => {
			await supertest
				.post("/testSite/updateEMList")
				.set("authorization", global.token)
				.expect(400);

		});
		it("Invalid params send 400 status code", async () => {
			await supertest
				.post("/testSite/updateEMList")
				.set("authorization", global.token)
				.send({ "emList": "[1, 2]" })
				.expect(400);

		});
		it("A list of devices of type energy meters", async () => {
			await supertest
				.post("/testSite/updateEMList")
				.set("authorization", global.token)
				.send({ "emList": [1, 2] })
				.expect(200);

		});
		after(async () => {
			await DyanmoKeyStore.destroy({ "key": "testSite_em" });
		});
	});

	describe("GET /:siteId/getEMList => fn getEMList ", () => {
		before(async () => {
			await DyanmoKeyStore.create({ "key": "testSite_em", "list": ["1", "2", "3"] });
		});
		let testUrl = "/testSite/getEMList";
		it("A list of devices of type energy meters", async () => {
			await supertest
				.get(testUrl)
				.set("authorization", token)
				.expect(200, {
					"err": null,
					"data": ["1", "2", "3"]
				});

		});
		after(async () => {
			await DyanmoKeyStore.destroy({ "key": "testSite_em" });
		});
	});

});


