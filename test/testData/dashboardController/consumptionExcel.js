module.exports = {
	"consumptionData": {
		"4715": {
			"selectedTime": {
				"deviceId": "4715",
				"param": {
					"kwh": 50.66000000000349
				}
			},
			"yesterday": {
				"deviceId": "4715",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4715",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4715",
				"param": {}
			}
		},
		"4716": {
			"selectedTime": {
				"deviceId": "4716",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4716",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4716",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4716",
				"param": {}
			}
		},
		"4717": {
			"selectedTime": {
				"deviceId": "4717",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4717",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4717",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4717",
				"param": {}
			}
		},
		"4718": {
			"selectedTime": {
				"deviceId": "4718",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4718",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4718",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4718",
				"param": {}
			}
		},
		"4720": {
			"selectedTime": {
				"deviceId": "4720",
				"param": {
					"kvah": 1042.8199999993667,
					"kwh": 1023.0999999996275
				}
			},
			"yesterday": {
				"deviceId": "4720",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4720",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4720",
				"param": {}
			}
		},
		"4721": {
			"selectedTime": {
				"deviceId": "4721",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4721",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4721",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4721",
				"param": {}
			}
		},
		"4722": {
			"selectedTime": {
				"deviceId": "4722",
				"param": {
					"kvah": 391.6399999996647,
					"kwh": 377.2399999999907
				}
			},
			"yesterday": {
				"deviceId": "4722",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4722",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4722",
				"param": {}
			}
		},
		"4723": {
			"selectedTime": {
				"deviceId": "4723",
				"param": {
					"kvah": 96.4100000000326,
					"kwh": 81.14000000001397
				}
			},
			"yesterday": {
				"deviceId": "4723",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4723",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4723",
				"param": {}
			}
		},
		"4724": {
			"selectedTime": {
				"deviceId": "4724",
				"param": {
					"kvah": 122.47999999998137,
					"kwh": 82.89999999996508
				}
			},
			"yesterday": {
				"deviceId": "4724",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4724",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4724",
				"param": {}
			}
		},
		"4725": {
			"selectedTime": {
				"deviceId": "4725",
				"param": {
					"kvah": 152.21999999997206,
					"kwh": 140.03000000002794
				}
			},
			"yesterday": {
				"deviceId": "4725",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4725",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4725",
				"param": {}
			}
		},
		"4726": {
			"selectedTime": {
				"deviceId": "4726",
				"param": {
					"kvah": 84.5800000000163,
					"kwh": 50.170000000012806
				}
			},
			"yesterday": {
				"deviceId": "4726",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4726",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4726",
				"param": {}
			}
		},
		"4727": {
			"selectedTime": {
				"deviceId": "4727",
				"param": {
					"kvah": 115.34999999997672,
					"kwh": 104.02999999991152
				}
			},
			"yesterday": {
				"deviceId": "4727",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4727",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4727",
				"param": {}
			}
		},
		"4728": {
			"selectedTime": {
				"deviceId": "4728",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4728",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4728",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4728",
				"param": {}
			}
		},
		"4729": {
			"selectedTime": {
				"deviceId": "4729",
				"param": {
					"kwh": 14.069999999999709
				}
			},
			"yesterday": {
				"deviceId": "4729",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4729",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4729",
				"param": {}
			}
		},
		"4730": {
			"selectedTime": {
				"deviceId": "4730",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4730",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4730",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4730",
				"param": {}
			}
		},
		"4731": {
			"selectedTime": {
				"deviceId": "4731",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4731",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4731",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4731",
				"param": {}
			}
		},
		"4732": {
			"selectedTime": {
				"deviceId": "4732",
				"param": {
					"kwh": 167.7600000000093
				}
			},
			"yesterday": {
				"deviceId": "4732",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4732",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4732",
				"param": {}
			}
		},
		"4733": {
			"selectedTime": {
				"deviceId": "4733",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4733",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4733",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4733",
				"param": {}
			}
		},
		"4734": {
			"selectedTime": {
				"deviceId": "4734",
				"param": {
					"kwh": 17.610000000000582
				}
			},
			"yesterday": {
				"deviceId": "4734",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4734",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4734",
				"param": {}
			}
		},
		"4735": {
			"selectedTime": {
				"deviceId": "4735",
				"param": {
					"kwh": 8.93999999999869
				}
			},
			"yesterday": {
				"deviceId": "4735",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4735",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4735",
				"param": {}
			}
		},
		"4737": {
			"selectedTime": {
				"deviceId": "4737",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4737",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4737",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4737",
				"param": {}
			}
		},
		"4738": {
			"selectedTime": {
				"deviceId": "4738",
				"param": {
					"kwh": 0
				}
			},
			"yesterday": {
				"deviceId": "4738",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4738",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4738",
				"param": {}
			}
		},
		"4739": {
			"selectedTime": {
				"deviceId": "4739",
				"param": {
					"kwh": 7.3400000000001455
				}
			},
			"yesterday": {
				"deviceId": "4739",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4739",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4739",
				"param": {}
			}
		},
		"4740": {
			"selectedTime": {
				"deviceId": "4740",
				"param": {
					"kwh": 18.830000000001746
				}
			},
			"yesterday": {
				"deviceId": "4740",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4740",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4740",
				"param": {}
			}
		},
		"4741": {
			"selectedTime": {
				"deviceId": "4741",
				"param": {
					"kwh": 6.680000000000291
				}
			},
			"yesterday": {
				"deviceId": "4741",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4741",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4741",
				"param": {}
			}
		},
		"4742": {
			"selectedTime": {
				"deviceId": "4742",
				"param": {
					"kwh": 3.529999999998836
				}
			},
			"yesterday": {
				"deviceId": "4742",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4742",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4742",
				"param": {}
			}
		},
		"4743": {
			"selectedTime": {
				"deviceId": "4743",
				"param": {
					"kwh": 3.470000000001164
				}
			},
			"yesterday": {
				"deviceId": "4743",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4743",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4743",
				"param": {}
			}
		},
		"4744": {
			"selectedTime": {
				"deviceId": "4744",
				"param": {
					"kwh": 3.8000000000029104
				}
			},
			"yesterday": {
				"deviceId": "4744",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4744",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4744",
				"param": {}
			}
		},
		"4745": {
			"selectedTime": {
				"deviceId": "4745",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4745",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4745",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4745",
				"param": {}
			}
		},
		"4746": {
			"selectedTime": {
				"deviceId": "4746",
				"param": {
					"kwh": 16.94999999999709
				}
			},
			"yesterday": {
				"deviceId": "4746",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4746",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4746",
				"param": {}
			}
		},
		"4747": {
			"selectedTime": {
				"deviceId": "4747",
				"param": {
					"kwh": 4.4099999999998545
				}
			},
			"yesterday": {
				"deviceId": "4747",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4747",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4747",
				"param": {}
			}
		},
		"4748": {
			"selectedTime": {
				"deviceId": "4748",
				"param": {
					"kwh": 6.989999999997963
				}
			},
			"yesterday": {
				"deviceId": "4748",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4748",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4748",
				"param": {}
			}
		},
		"4749": {
			"selectedTime": {
				"deviceId": "4749",
				"param": {
					"kwh": 0
				}
			},
			"yesterday": {
				"deviceId": "4749",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4749",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4749",
				"param": {}
			}
		},
		"4750": {
			"selectedTime": {
				"deviceId": "4750",
				"param": {
					"kwh": 20.06999999999971
				}
			},
			"yesterday": {
				"deviceId": "4750",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4750",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4750",
				"param": {}
			}
		},
		"4751": {
			"selectedTime": {
				"deviceId": "4751",
				"param": {
					"kwh": 19.60000000000582
				}
			},
			"yesterday": {
				"deviceId": "4751",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4751",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4751",
				"param": {}
			}
		},
		"4752": {
			"selectedTime": {
				"deviceId": "4752",
				"param": {
					"kwh": 2.0200000000004366
				}
			},
			"yesterday": {
				"deviceId": "4752",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4752",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4752",
				"param": {}
			}
		},
		"4753": {
			"selectedTime": {
				"deviceId": "4753",
				"param": {
					"kwh": 2.6300000000010186
				}
			},
			"yesterday": {
				"deviceId": "4753",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4753",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4753",
				"param": {}
			}
		},
		"4754": {
			"selectedTime": {
				"deviceId": "4754",
				"param": {
					"kwh": 1.2100000000000364
				}
			},
			"yesterday": {
				"deviceId": "4754",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4754",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4754",
				"param": {}
			}
		},
		"4755": {
			"selectedTime": {
				"deviceId": "4755",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4755",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4755",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4755",
				"param": {}
			}
		},
		"4756": {
			"selectedTime": {
				"deviceId": "4756",
				"param": {
					"kwh": 9.909999999999854
				}
			},
			"yesterday": {
				"deviceId": "4756",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4756",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4756",
				"param": {}
			}
		},
		"4757": {
			"selectedTime": {
				"deviceId": "4757",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4757",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4757",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4757",
				"param": {}
			}
		},
		"4758": {
			"selectedTime": {
				"deviceId": "4758",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4758",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4758",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4758",
				"param": {}
			}
		},
		"4759": {
			"selectedTime": {
				"deviceId": "4759",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4759",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4759",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4759",
				"param": {}
			}
		},
		"4760": {
			"selectedTime": {
				"deviceId": "4760",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4760",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4760",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4760",
				"param": {}
			}
		},
		"4761": {
			"selectedTime": {
				"deviceId": "4761",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4761",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4761",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4761",
				"param": {}
			}
		},
		"4762": {
			"selectedTime": {
				"deviceId": "4762",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4762",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4762",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4762",
				"param": {}
			}
		},
		"4764": {
			"selectedTime": {
				"deviceId": "4764",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4764",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4764",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4764",
				"param": {}
			}
		},
		"4765": {
			"selectedTime": {
				"deviceId": "4765",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4765",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4765",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4765",
				"param": {}
			}
		},
		"4766": {
			"selectedTime": {
				"deviceId": "4766",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4766",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4766",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4766",
				"param": {}
			}
		},
		"4767": {
			"selectedTime": {
				"deviceId": "4767",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4767",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4767",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4767",
				"param": {}
			}
		},
		"4768": {
			"selectedTime": {
				"deviceId": "4768",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4768",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4768",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4768",
				"param": {}
			}
		},
		"4769": {
			"selectedTime": {
				"deviceId": "4769",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4769",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4769",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4769",
				"param": {}
			}
		},
		"4770": {
			"selectedTime": {
				"deviceId": "4770",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4770",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4770",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4770",
				"param": {}
			}
		},
		"4772": {
			"selectedTime": {
				"deviceId": "4772",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4772",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4772",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4772",
				"param": {}
			}
		},
		"4773": {
			"selectedTime": {
				"deviceId": "4773",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4773",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4773",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4773",
				"param": {}
			}
		},
		"4774": {
			"selectedTime": {
				"deviceId": "4774",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4774",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4774",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4774",
				"param": {}
			}
		},
		"4775": {
			"selectedTime": {
				"deviceId": "4775",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4775",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4775",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4775",
				"param": {}
			}
		},
		"4776": {
			"selectedTime": {
				"deviceId": "4776",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4776",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4776",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4776",
				"param": {}
			}
		},
		"4777": {
			"selectedTime": {
				"deviceId": "4777",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4777",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4777",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4777",
				"param": {}
			}
		},
		"4778": {
			"selectedTime": {
				"deviceId": "4778",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4778",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4778",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4778",
				"param": {}
			}
		},
		"4779": {
			"selectedTime": {
				"deviceId": "4779",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4779",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4779",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4779",
				"param": {}
			}
		},
		"4780": {
			"selectedTime": {
				"deviceId": "4780",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4780",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4780",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4780",
				"param": {}
			}
		},
		"4781": {
			"selectedTime": {
				"deviceId": "4781",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4781",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4781",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4781",
				"param": {}
			}
		},
		"4782": {
			"selectedTime": {
				"deviceId": "4782",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4782",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4782",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4782",
				"param": {}
			}
		},
		"4783": {
			"selectedTime": {
				"deviceId": "4783",
				"param": {}
			},
			"yesterday": {
				"deviceId": "4783",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "4783",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "4783",
				"param": {}
			}
		},
		"5138": {
			"selectedTime": {
				"deviceId": "5138",
				"param": {}
			},
			"yesterday": {
				"deviceId": "5138",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "5138",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "5138",
				"param": {}
			}
		},
		"5139": {
			"selectedTime": {
				"deviceId": "5139",
				"param": {}
			},
			"yesterday": {
				"deviceId": "5139",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "5139",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "5139",
				"param": {}
			}
		},
		"5148": {
			"selectedTime": {
				"deviceId": "5148",
				"param": {}
			},
			"yesterday": {
				"deviceId": "5148",
				"param": {}
			},
			"lastWeek": {
				"deviceId": "5148",
				"param": {}
			},
			"lastMonth": {
				"deviceId": "5148",
				"param": {}
			}
		}
	},
	"deviceTree": [
		{
			"name": "Transformer No 2 (Plant-2 Main Supply Panel) EM  NF-29",
			"deviceId": "4721",
			"childNodes": []
		},
		{
			"name": "Transformer No 1 (Plant-1 Main Supply Panel)  EM  NF-29",
			"deviceId": "4720",
			"childNodes": [
				{
					"name": "Paper Machine & New Vacuum Main Line EM  NF-29",
					"deviceId": "4722",
					"parentEM": "4720",
					"childNodes": [
						{
							"name": "Back Water Pump  KwhMeter",
							"deviceId": "4778",
							"parentEM": "4722",
							"childNodes": []
						},
						{
							"name": "Forth  Layer Paper Machine  Pump  KwhMeter",
							"deviceId": "4743",
							"parentEM": "4722",
							"childNodes": []
						},
						{
							"name": "Fan Pump 6 KwhMeter",
							"deviceId": "4751",
							"parentEM": "4722",
							"childNodes": []
						},
						{
							"name": "Middle Machine Agitator KwhMeter",
							"deviceId": "4752",
							"parentEM": "4722",
							"childNodes": []
						},
						{
							"name": "High Pressure Pump (Paper Machine) KwhMeter",
							"deviceId": "4746",
							"parentEM": "4722",
							"childNodes": []
						},
						{
							"name": "Return Roll Drive KwhMeter",
							"deviceId": "4733",
							"parentEM": "4722",
							"childNodes": []
						},
						{
							"name": "Mold Nip Blower KwhMeter",
							"deviceId": "4737",
							"parentEM": "4722",
							"childNodes": []
						},
						{
							"name": "Main Motor Drive KwhMeter",
							"deviceId": "4728",
							"parentEM": "4722",
							"childNodes": []
						},
						{
							"name": "Forth Layer Agitator KwhMeter",
							"deviceId": "4744",
							"parentEM": "4722",
							"childNodes": []
						},
						{
							"name": "Fan Pump 5 KwhMeter",
							"deviceId": "4750",
							"parentEM": "4722",
							"childNodes": []
						},
						{
							"name": "Mold Blower 2 KwhMeter",
							"deviceId": "4739",
							"parentEM": "4722",
							"childNodes": []
						},
						{
							"name": "Bottom Refiller Pump KwhMeter",
							"deviceId": "5139",
							"parentEM": "4722",
							"childNodes": []
						},
						{
							"name": "Machine Pressure Screen  KwhMeter",
							"deviceId": "4738",
							"parentEM": "4722",
							"childNodes": []
						},
						{
							"name": "MG Blower (near return roll)  KwhMeter",
							"deviceId": "4745",
							"parentEM": "4722",
							"childNodes": []
						},
						{
							"name": "Fresh Water Pump KwhMeter",
							"deviceId": "4749",
							"parentEM": "4722",
							"childNodes": []
						},
						{
							"name": "Top Paper Machine Pump  KwhMeter",
							"deviceId": "4748",
							"parentEM": "4722",
							"childNodes": []
						},
						{
							"name": "Fan Pump 2 KwhMeter",
							"deviceId": "4740",
							"parentEM": "4722",
							"childNodes": []
						},
						{
							"name": "Fan Pump 1 KwhMeter",
							"deviceId": "5138",
							"parentEM": "4722",
							"childNodes": []
						},
						{
							"name": "Fan Pump 3 KwhMeter",
							"deviceId": "4755",
							"parentEM": "4722",
							"childNodes": []
						},
						{
							"name": "Bottom Agitator  KwhMeter",
							"deviceId": "4741",
							"parentEM": "4722",
							"childNodes": []
						},
						{
							"name": "Fan Pump 4 KwhMeter",
							"deviceId": "4756",
							"parentEM": "4722",
							"childNodes": []
						},
						{
							"name": "MG Blower KwhMeter",
							"deviceId": "4735",
							"parentEM": "4722",
							"childNodes": []
						},
						{
							"name": "Kuchpit Agitator KwhMeter",
							"deviceId": "4754",
							"parentEM": "4722",
							"childNodes": []
						},
						{
							"name": "Machine Top Mal Agiator  KwhMeter",
							"deviceId": "4747",
							"parentEM": "4722",
							"childNodes": []
						},
						{
							"name": "Middle Machine Mal Pump  KwhMeter",
							"deviceId": "4753",
							"parentEM": "4722",
							"childNodes": []
						},
						{
							"name": "Bottom Paper machine  Pump  KwhMeter",
							"deviceId": "4742",
							"parentEM": "4722",
							"childNodes": []
						}
					]
				},
				{
					"name": "Pulp Mill First Floor Panel EM  NF-29",
					"deviceId": "4725",
					"parentEM": "4720",
					"childNodes": [
						{
							"name": "Bottom Centri Cleaner-primary  KwhMeter",
							"deviceId": "4767",
							"parentEM": "4725",
							"childNodes": []
						},
						{
							"name": "High Pressure Pump test KwhMeter",
							"deviceId": "4729",
							"parentEM": "4725",
							"childNodes": []
						},
						{
							"name": "Bottom tower Pump KwhMeter",
							"deviceId": "4765",
							"parentEM": "4725",
							"childNodes": []
						},
						{
							"name": "Centri Cleaner- Secondary  KwhMeter",
							"deviceId": "4764",
							"parentEM": "4725",
							"childNodes": []
						},
						{
							"name": "Feed Pump KwhMeter",
							"deviceId": "4718",
							"parentEM": "4725",
							"childNodes": []
						},
						{
							"name": "Rejector Back water KwhMeter",
							"deviceId": "4766",
							"parentEM": "4725",
							"childNodes": []
						},
						{
							"name": "Pressure Screen (CH 7) KwhMeter",
							"deviceId": "4770",
							"parentEM": "4725",
							"childNodes": []
						},
						{
							"name": "Stock Chest 7 Pump KwhMeter",
							"deviceId": "4772",
							"parentEM": "4725",
							"childNodes": []
						},
						{
							"name": "CH 5 KwhMeter",
							"deviceId": "4768",
							"parentEM": "4725",
							"childNodes": []
						},
						{
							"name": "Rejector KwhMeter",
							"deviceId": "4769",
							"parentEM": "4725",
							"childNodes": []
						}
					]
				},
				{
					"name": "Mold Blower 1 KwhMeter",
					"deviceId": "4734",
					"parentEM": "4720",
					"childNodes": []
				},
				{
					"name": "Top & Bottom Side Panel EM  NF-29",
					"deviceId": "4724",
					"parentEM": "4720",
					"childNodes": [
						{
							"name": "Top Mal Pump 3 KwhMeter",
							"deviceId": "4762",
							"parentEM": "4724",
							"childNodes": []
						},
						{
							"name": "Top Refiner Chest Pump No 2  KwhMeter",
							"deviceId": "4759",
							"parentEM": "4724",
							"childNodes": []
						},
						{
							"name": "Condenced Water Pump  KwhMeter",
							"deviceId": "4760",
							"parentEM": "4724",
							"childNodes": []
						},
						{
							"name": "Refiller Pump Bottom Side  KwhMeter",
							"deviceId": "5148",
							"parentEM": "4724",
							"childNodes": []
						},
						{
							"name": "Top Agitator 3 KwhMeter",
							"deviceId": "4761",
							"parentEM": "4724",
							"childNodes": []
						}
					]
				},
				{
					"name": "Coating Plant Panel EM NF-29",
					"deviceId": "4726",
					"parentEM": "4720",
					"childNodes": [
						{
							"name": "Coating Blower KwhMeter",
							"deviceId": "4782",
							"parentEM": "4726",
							"childNodes": []
						},
						{
							"name": "Overflow 1 KwhMeter",
							"deviceId": "4780",
							"parentEM": "4726",
							"childNodes": []
						},
						{
							"name": "OverFlow 2 KwhMeter",
							"deviceId": "4781",
							"parentEM": "4726",
							"childNodes": []
						},
						{
							"name": "Service Tank Agitator KwhMeter",
							"deviceId": "4783",
							"parentEM": "4726",
							"childNodes": []
						}
					]
				},
				{
					"name": "Vaccum 1 KwhMeter",
					"deviceId": "4732",
					"parentEM": "4720",
					"childNodes": []
				},
				{
					"name": "HD Pulper Panel EM NF-29",
					"deviceId": "4727",
					"parentEM": "4720",
					"childNodes": [
						{
							"name": "Poer KwhMeter",
							"deviceId": "4716",
							"parentEM": "4727",
							"childNodes": []
						},
						{
							"name": "Tertiary  Top KwhMeter",
							"deviceId": "4779",
							"parentEM": "4727",
							"childNodes": []
						},
						{
							"name": "Broke Pulper KwhMeter",
							"deviceId": "4731",
							"parentEM": "4727",
							"childNodes": []
						},
						{
							"name": "Middle Pressure Screen  KwhMeter",
							"deviceId": "4775",
							"parentEM": "4727",
							"childNodes": []
						},
						{
							"name": "Secondary Top  KwhMeter",
							"deviceId": "4776",
							"parentEM": "4727",
							"childNodes": []
						},
						{
							"name": "Agitator  KwhMeter",
							"deviceId": "4773",
							"parentEM": "4727",
							"childNodes": []
						},
						{
							"name": "Broke Pump KwhMeter",
							"deviceId": "4730",
							"parentEM": "4727",
							"childNodes": []
						},
						{
							"name": "Top Layer Panel EM NF-29",
							"deviceId": "4777",
							"parentEM": "4727",
							"childNodes": []
						},
						{
							"name": "CH 3 KwhMeter",
							"deviceId": "4774",
							"parentEM": "4727",
							"childNodes": []
						},
						{
							"name": "Poer Pump KwhMeter",
							"deviceId": "4717",
							"parentEM": "4727",
							"childNodes": []
						},
						{
							"name": "HD Pulper Drive Motor ( Bottom)  KwhMeter",
							"deviceId": "4715",
							"parentEM": "4727",
							"childNodes": []
						}
					]
				},
				{
					"name": "Top Refine , Lighting & F/N Screen EM  NF-29",
					"deviceId": "4723",
					"parentEM": "4720",
					"childNodes": [
						{
							"name": "F/N Screen Turbo 600 KwhMeter",
							"deviceId": "4758",
							"parentEM": "4723",
							"childNodes": []
						},
						{
							"name": "Top Refiner KwhMeter",
							"deviceId": "4757",
							"parentEM": "4723",
							"childNodes": []
						}
					]
				}
			]
		}
	],
	"emList": [
		"4762", "4722", "4758", "4778", "4743", "4721", "4716", "4779", "4751", "4767", "4725", "4729", "4752", "4759", "4765", "4757", "4734", "4724", "4746", "4733", "4764", "4731", "4737", "4782", "4718", "4728", "4744", "4726", "4760", "4766", "4732", "4775", "4770", "4750", "4772", "4768", "4769", "4739", "5139", "4776", "4738", "4745", "4749", "4727", "4773", "4748", "4720", "4780", "4740", "4781", "5138", "4755", "4741", "4730", "4756", "5148", "4777", "4735", "4723", "4754", "4774", "4747", "4753", "4742", "4717", "4783", "4715", "4761"
	],
	"emObjects": [
		{
			"deviceId": "4762",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Top Mal Pump 3 KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "4",
			"controllerId": "4170",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4724",
			"createdAt": "2019-09-23T10:26:44.728Z",
			"updatedAt": "2020-03-23T05:29:38.897Z"
		},
		{
			"deviceId": "4722",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Paper Machine & New Vacuum Main Line EM  NF-29",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "0",
			"slaveId": "7",
			"controllerId": "4168",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4720",
			"createdAt": "2019-09-23T09:11:21.355Z",
			"updatedAt": "2019-10-03T06:40:18.685Z"
		},
		{
			"deviceId": "4758",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "F/N Screen Turbo 600 KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "24",
			"controllerId": "4170",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4723",
			"createdAt": "2019-09-23T10:19:30.590Z",
			"updatedAt": "2019-10-03T07:06:59.664Z"
		},
		{
			"deviceId": "4778",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Back Water Pump  KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "23",
			"controllerId": "4170",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4722",
			"createdAt": "2019-09-23T10:46:45.302Z",
			"updatedAt": "2020-01-03T07:20:10.175Z"
		},
		{
			"deviceId": "4743",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Forth  Layer Paper Machine  Pump  KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "6",
			"controllerId": "4169",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4722",
			"createdAt": "2019-09-23T09:58:27.314Z",
			"updatedAt": "2019-10-03T06:46:37.663Z"
		},
		{
			"deviceId": "4721",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Transformer No 2 (Plant-2 Main Supply Panel) EM  NF-29",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "0",
			"slaveId": "6",
			"controllerId": "4168",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "true",
			"createdAt": "2019-09-23T09:10:07.690Z",
			"updatedAt": "2019-11-11T13:00:59.563Z"
		},
		{
			"deviceId": "4716",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Poer KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "2",
			"controllerId": "4168",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4727",
			"createdAt": "2019-09-23T08:40:12.565Z",
			"updatedAt": "2019-10-03T08:55:55.872Z"
		},
		{
			"deviceId": "4779",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Tertiary  Top KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "22",
			"controllerId": "4170",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4727",
			"createdAt": "2019-09-23T10:47:35.348Z",
			"updatedAt": "2020-01-04T09:12:27.305Z"
		},
		{
			"deviceId": "4751",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Fan Pump 6 KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "14",
			"controllerId": "4169",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4722",
			"createdAt": "2019-09-23T10:02:51.624Z",
			"updatedAt": "2020-03-23T05:28:41.505Z"
		},
		{
			"deviceId": "4767",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Bottom Centri Cleaner-primary  KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "9",
			"controllerId": "4170",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4725",
			"createdAt": "2019-09-23T10:35:45.609Z",
			"updatedAt": "2019-10-03T08:43:31.397Z"
		},
		{
			"deviceId": "4725",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Pulp Mill First Floor Panel EM  NF-29",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "0",
			"slaveId": "10",
			"controllerId": "4168",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4720",
			"createdAt": "2019-09-23T09:20:53.992Z",
			"updatedAt": "2019-10-03T08:41:48.466Z"
		},
		{
			"deviceId": "4729",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "High Pressure Pump test KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "16",
			"controllerId": "4168",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4725",
			"createdAt": "2019-09-23T09:26:44.450Z",
			"updatedAt": "2020-03-19T11:58:39.998Z"
		},
		{
			"deviceId": "4752",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Middle Machine Agitator KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "15",
			"controllerId": "4169",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4722",
			"createdAt": "2019-09-23T10:03:40.733Z",
			"updatedAt": "2019-10-03T06:50:38.961Z"
		},
		{
			"deviceId": "4759",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Top Refiner Chest Pump No 2  KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "1",
			"controllerId": "4170",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4724",
			"createdAt": "2019-09-23T10:24:21.441Z",
			"updatedAt": "2019-10-03T07:11:27.122Z"
		},
		{
			"deviceId": "4765",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Bottom tower Pump KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "7",
			"controllerId": "4170",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4725",
			"createdAt": "2019-09-23T10:34:28.324Z",
			"updatedAt": "2019-10-03T08:42:57.070Z"
		},
		{
			"deviceId": "4757",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Top Refiner KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "16",
			"controllerId": "4170",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4723",
			"createdAt": "2019-09-23T10:18:31.378Z",
			"updatedAt": "2019-10-03T07:06:43.885Z"
		},
		{
			"deviceId": "4734",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Mold Blower 1 KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "22",
			"controllerId": "4168",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4720",
			"createdAt": "2019-09-23T09:33:35.977Z",
			"updatedAt": "2020-06-15T08:18:34.684Z"
		},
		{
			"deviceId": "4724",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Top & Bottom Side Panel EM  NF-29",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "0",
			"slaveId": "9",
			"controllerId": "4168",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4720",
			"createdAt": "2019-09-23T09:19:31.662Z",
			"updatedAt": "2019-10-03T07:09:36.890Z"
		},
		{
			"deviceId": "4746",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "High Pressure Pump (Paper Machine) KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "9",
			"controllerId": "4169",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4722",
			"createdAt": "2019-09-23T10:00:17.575Z",
			"updatedAt": "2020-06-09T08:24:45.544Z"
		},
		{
			"deviceId": "4733",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Return Roll Drive KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "21",
			"controllerId": "4168",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4722",
			"createdAt": "2019-09-23T09:32:44.525Z",
			"updatedAt": "2020-01-04T09:17:00.179Z"
		},
		{
			"deviceId": "4764",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Centri Cleaner- Secondary  KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "6",
			"controllerId": "4170",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4725",
			"createdAt": "2019-09-23T10:32:46.447Z",
			"updatedAt": "2019-10-03T08:42:36.024Z"
		},
		{
			"deviceId": "4731",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Broke Pulper KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "18",
			"controllerId": "4168",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4727",
			"createdAt": "2019-09-23T09:29:47.283Z",
			"updatedAt": "2020-01-04T09:13:48.679Z"
		},
		{
			"deviceId": "4737",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Mold Nip Blower KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "23",
			"controllerId": "4168",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4722",
			"createdAt": "2019-09-23T09:38:53.386Z",
			"updatedAt": "2020-06-15T08:19:11.509Z"
		},
		{
			"deviceId": "4782",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "vghp",
			"name": "Coating Blower KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "3",
			"controllerId": "4171",
			"portNumber": "7",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4726",
			"createdAt": "2019-09-23T10:50:28.775Z",
			"updatedAt": "2020-01-04T09:05:51.921Z"
		},
		{
			"deviceId": "4718",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Feed Pump KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "15",
			"controllerId": "4168",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4725",
			"createdAt": "2019-09-23T08:41:51.980Z",
			"updatedAt": "2020-01-04T09:03:12.013Z"
		},
		{
			"deviceId": "4728",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Main Motor Drive KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "20",
			"controllerId": "4168",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4722",
			"createdAt": "2019-09-23T09:25:41.855Z",
			"updatedAt": "2020-01-04T09:16:43.494Z"
		},
		{
			"deviceId": "4744",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Forth Layer Agitator KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "7",
			"controllerId": "4169",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4722",
			"createdAt": "2019-09-23T09:59:05.382Z",
			"updatedAt": "2020-01-03T07:18:01.602Z"
		},
		{
			"deviceId": "4726",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Coating Plant Panel EM NF-29",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "0",
			"slaveId": "11",
			"controllerId": "4168",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4720",
			"createdAt": "2019-09-23T09:22:19.109Z",
			"updatedAt": "2019-10-03T08:48:45.365Z"
		},
		{
			"deviceId": "4760",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Condenced Water Pump  KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "2",
			"controllerId": "4170",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4724",
			"createdAt": "2019-09-23T10:25:14.604Z",
			"updatedAt": "2019-10-03T07:16:52.884Z"
		},
		{
			"deviceId": "4766",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Rejector Back water KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "8",
			"controllerId": "4170",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4725",
			"createdAt": "2019-09-23T10:35:06.617Z",
			"updatedAt": "2019-10-03T08:43:11.400Z"
		},
		{
			"deviceId": "4732",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Vaccum 1 KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "19",
			"controllerId": "4168",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4720",
			"createdAt": "2019-09-23T09:30:54.247Z",
			"updatedAt": "2020-01-03T07:11:37.304Z"
		},
		{
			"deviceId": "4775",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Middle Pressure Screen  KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "20",
			"controllerId": "4170",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4727",
			"createdAt": "2019-09-23T10:41:39.316Z",
			"updatedAt": "2020-01-04T09:11:53.080Z"
		},
		{
			"deviceId": "4770",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Pressure Screen (CH 7) KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "12",
			"controllerId": "4170",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4725",
			"createdAt": "2019-09-23T10:37:24.476Z",
			"updatedAt": "2020-03-23T05:30:07.589Z"
		},
		{
			"deviceId": "4750",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Fan Pump 5 KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "13",
			"controllerId": "4169",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4722",
			"createdAt": "2019-09-23T10:02:23.776Z",
			"updatedAt": "2020-03-23T05:28:33.322Z"
		},
		{
			"deviceId": "4772",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Stock Chest 7 Pump KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "15",
			"controllerId": "4170",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4725",
			"createdAt": "2019-09-23T10:39:23.934Z",
			"updatedAt": "2020-03-23T05:30:29.520Z"
		},
		{
			"deviceId": "4768",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "CH 5 KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "10",
			"controllerId": "4170",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4725",
			"createdAt": "2019-09-23T10:36:17.870Z",
			"updatedAt": "2020-03-23T05:29:50.760Z"
		},
		{
			"deviceId": "4769",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Rejector KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "11",
			"controllerId": "4170",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4725",
			"createdAt": "2019-09-23T10:37:00.329Z",
			"updatedAt": "2019-10-03T08:44:02.669Z"
		},
		{
			"deviceId": "4739",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Mold Blower 2 KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "2",
			"controllerId": "4169",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4722",
			"createdAt": "2019-09-23T09:55:51.437Z",
			"updatedAt": "2020-06-15T08:19:50.537Z"
		},
		{
			"deviceId": "5139",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Bottom Refiller Pump KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "29",
			"controllerId": "4170",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4722",
			"createdAt": "2019-12-23T10:29:49.071Z",
			"updatedAt": "2020-06-15T08:20:48.159Z"
		},
		{
			"deviceId": "4776",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Secondary Top  KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "21",
			"controllerId": "4170",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4727",
			"createdAt": "2019-09-23T10:44:54.284Z",
			"updatedAt": "2020-01-04T09:12:10.885Z"
		},
		{
			"deviceId": "4738",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Machine Pressure Screen  KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "1",
			"controllerId": "4169",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4722",
			"createdAt": "2019-09-23T09:55:11.881Z",
			"updatedAt": "2019-10-03T06:45:16.116Z"
		},
		{
			"deviceId": "4745",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "MG Blower (near return roll)  KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "8",
			"controllerId": "4169",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4722",
			"createdAt": "2019-09-23T09:59:42.169Z",
			"updatedAt": "2020-01-04T09:17:46.581Z"
		},
		{
			"deviceId": "4749",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Fresh Water Pump KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "12",
			"controllerId": "4169",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4722",
			"createdAt": "2019-09-23T10:01:57.538Z",
			"updatedAt": "2019-10-03T06:48:43.679Z"
		},
		{
			"deviceId": "4727",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "HD Pulper Panel EM NF-29",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "0",
			"slaveId": "12",
			"controllerId": "4168",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4720",
			"createdAt": "2019-09-23T09:23:47.182Z",
			"updatedAt": "2019-10-03T08:55:12.349Z"
		},
		{
			"deviceId": "4773",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Agitator  KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "18",
			"controllerId": "4170",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4727",
			"createdAt": "2019-09-23T10:40:11.587Z",
			"updatedAt": "2020-01-04T09:08:08.482Z"
		},
		{
			"deviceId": "4748",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Top Paper Machine Pump  KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "11",
			"controllerId": "4169",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4722",
			"createdAt": "2019-09-23T10:01:28.700Z",
			"updatedAt": "2019-10-03T06:48:27.713Z"
		},
		{
			"deviceId": "4720",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Transformer No 1 (Plant-1 Main Supply Panel)  EM  NF-29",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "0",
			"slaveId": "5",
			"controllerId": "4168",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "true",
			"createdAt": "2019-09-23T09:09:14.437Z",
			"updatedAt": "2019-11-11T13:00:36.055Z"
		},
		{
			"deviceId": "4780",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "vghp",
			"name": "Overflow 1 KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "1",
			"controllerId": "4171",
			"portNumber": "7",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4726",
			"createdAt": "2019-09-23T10:49:40.597Z",
			"updatedAt": "2020-03-23T05:31:53.116Z"
		},
		{
			"deviceId": "4740",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Fan Pump 2 KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "3",
			"controllerId": "4169",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4722",
			"createdAt": "2019-09-23T09:56:39.920Z",
			"updatedAt": "2020-03-23T05:28:20.575Z"
		},
		{
			"deviceId": "4781",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "vghp",
			"name": "OverFlow 2 KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "2",
			"controllerId": "4171",
			"portNumber": "7",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4726",
			"createdAt": "2019-09-23T10:50:06.257Z",
			"updatedAt": "2020-03-23T05:32:03.958Z"
		},
		{
			"deviceId": "5138",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Fan Pump 1 KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "30",
			"controllerId": "4170",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4722",
			"createdAt": "2019-12-23T09:56:07.353Z",
			"updatedAt": "2020-06-15T08:20:25.793Z"
		},
		{
			"deviceId": "4755",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Fan Pump 3 KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "18",
			"controllerId": "4169",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4722",
			"createdAt": "2019-09-23T10:05:05.285Z",
			"updatedAt": "2020-03-23T05:28:52.171Z"
		},
		{
			"deviceId": "4741",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Bottom Agitator  KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "4",
			"controllerId": "4169",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4722",
			"createdAt": "2019-09-23T09:57:12.206Z",
			"updatedAt": "2019-10-03T06:46:09.310Z"
		},
		{
			"deviceId": "4730",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Broke Pump KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "17",
			"controllerId": "4168",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4727",
			"createdAt": "2019-09-23T09:28:44.168Z",
			"updatedAt": "2020-01-04T09:13:33.512Z"
		},
		{
			"deviceId": "4756",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Fan Pump 4 KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "19",
			"controllerId": "4169",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4722",
			"createdAt": "2019-09-23T10:05:35.039Z",
			"updatedAt": "2020-03-23T05:29:00.009Z"
		},
		{
			"deviceId": "5148",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Refiller Pump Bottom Side  KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "29",
			"controllerId": "4169",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4724",
			"createdAt": "2019-12-26T06:40:13.387Z",
			"updatedAt": "2020-01-04T09:44:10.733Z"
		},
		{
			"deviceId": "4777",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Top Layer Panel EM NF-29",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "0",
			"slaveId": "14",
			"controllerId": "4170",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4727",
			"createdAt": "2019-09-23T10:45:53.072Z",
			"updatedAt": "2020-03-23T05:31:00.236Z"
		},
		{
			"deviceId": "4735",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "MG Blower KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "24",
			"controllerId": "4168",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4722",
			"createdAt": "2019-09-23T09:34:27.017Z",
			"updatedAt": "2020-01-04T09:17:14.119Z"
		},
		{
			"deviceId": "4723",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Top Refine , Lighting & F/N Screen EM  NF-29",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "0",
			"slaveId": "8",
			"controllerId": "4168",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4720",
			"createdAt": "2019-09-23T09:18:33.237Z",
			"updatedAt": "2019-10-03T07:05:22.239Z"
		},
		{
			"deviceId": "4754",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Kuchpit Agitator KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "17",
			"controllerId": "4169",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4722",
			"createdAt": "2019-09-23T10:04:36.968Z",
			"updatedAt": "2019-10-03T06:55:19.514Z"
		},
		{
			"deviceId": "4774",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "CH 3 KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "19",
			"controllerId": "4170",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4727",
			"createdAt": "2019-09-23T10:40:54.189Z",
			"updatedAt": "2020-03-23T05:30:46.962Z"
		},
		{
			"deviceId": "4747",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Machine Top Mal Agiator  KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "10",
			"controllerId": "4169",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4722",
			"createdAt": "2019-09-23T10:00:47.751Z",
			"updatedAt": "2019-10-03T06:47:08.567Z"
		},
		{
			"deviceId": "4753",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Middle Machine Mal Pump  KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "16",
			"controllerId": "4169",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4722",
			"createdAt": "2019-09-23T10:04:09.954Z",
			"updatedAt": "2019-10-03T06:50:58.409Z"
		},
		{
			"deviceId": "4742",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Bottom Paper machine  Pump  KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "5",
			"controllerId": "4169",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4722",
			"createdAt": "2019-09-23T09:57:44.501Z",
			"updatedAt": "2019-10-03T06:46:23.889Z"
		},
		{
			"deviceId": "4717",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Poer Pump KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "3",
			"controllerId": "4168",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4727",
			"createdAt": "2019-09-23T08:40:37.540Z",
			"updatedAt": "2019-10-03T08:56:15.452Z"
		},
		{
			"deviceId": "4783",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "vghp",
			"name": "Service Tank Agitator KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "4",
			"controllerId": "4171",
			"portNumber": "7",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4726",
			"createdAt": "2019-09-23T10:50:50.627Z",
			"updatedAt": "2020-01-04T09:05:09.237Z"
		},
		{
			"deviceId": "4715",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "HD Pulper Drive Motor ( Bottom)  KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "1",
			"controllerId": "4168",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4727",
			"createdAt": "2019-09-23T08:38:39.340Z",
			"updatedAt": "2019-10-03T08:55:40.493Z"
		},
		{
			"deviceId": "4761",
			"siteId": "rpml-bij",
			"networkId": "smartjoules-network-0",
			"regionId": "wtkw",
			"name": "Top Agitator 3 KwhMeter",
			"deviceType": "em",
			"areaId": "lpfe",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "8",
			"slaveId": "3",
			"controllerId": "4170",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"parentEM": "4724",
			"createdAt": "2019-09-23T10:26:01.768Z",
			"updatedAt": "2020-03-23T05:29:26.223Z"
		}
	],
	"query": {
		"start": "2020-07-06 11:56:32",
		"end": "2020-07-06 12:56:32"
	},
	"simplifiedConsumption": {
		"4715": {
			"selectedTime": 50.66,
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": "4.95%",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4716": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4717": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4718": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4720": {
			"selectedTime": 1023.1,
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": "100%",
			"selectedTimePercentage": "100%",
			"lastWeekPercentage": "100%",
			"lastMonthPercentage": "100%"
		},
		"4721": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": "100%",
			"selectedTimePercentage": "100%",
			"lastWeekPercentage": "100%",
			"lastMonthPercentage": "100%"
		},
		"4722": {
			"selectedTime": 377.24,
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": "36.87%",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4723": {
			"selectedTime": 81.14,
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": "7.93%",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4724": {
			"selectedTime": 82.9,
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": "8.1%",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4725": {
			"selectedTime": 140.03,
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": "13.69%",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4726": {
			"selectedTime": 50.17,
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": "4.9%",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4727": {
			"selectedTime": 104.03,
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": "10.17%",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4728": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4729": {
			"selectedTime": 14.07,
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": "1.38%",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4730": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4731": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4732": {
			"selectedTime": 167.76,
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": "16.4%",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4733": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4734": {
			"selectedTime": 17.61,
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": "1.72%",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4735": {
			"selectedTime": 8.94,
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": "0.87%",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4737": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4738": {
			"selectedTime": 0,
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4739": {
			"selectedTime": 7.34,
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": "0.72%",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4740": {
			"selectedTime": 18.83,
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": "1.84%",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4741": {
			"selectedTime": 6.68,
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": "0.65%",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4742": {
			"selectedTime": 3.53,
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": "0.35%",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4743": {
			"selectedTime": 3.47,
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": "0.34%",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4744": {
			"selectedTime": 3.8,
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": "0.37%",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4745": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4746": {
			"selectedTime": 16.95,
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": "1.66%",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4747": {
			"selectedTime": 4.41,
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": "0.43%",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4748": {
			"selectedTime": 6.99,
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": "0.68%",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4749": {
			"selectedTime": 0,
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4750": {
			"selectedTime": 20.07,
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": "1.96%",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4751": {
			"selectedTime": 19.6,
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": "1.92%",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4752": {
			"selectedTime": 2.02,
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": "0.2%",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4753": {
			"selectedTime": 2.63,
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": "0.26%",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4754": {
			"selectedTime": 1.21,
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": "0.12%",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4755": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4756": {
			"selectedTime": 9.91,
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": "0.97%",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4757": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4758": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4759": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4760": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4761": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4762": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4764": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4765": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4766": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4767": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4768": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4769": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4770": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4772": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4773": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4774": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4775": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4776": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4777": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4778": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4779": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4780": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4781": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4782": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"4783": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"5138": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"5139": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		},
		"5148": {
			"selectedTime": " ",
			"yesterday": " ",
			"lastWeek": " ",
			"lastMonth": " ",
			"yesterdayPercentage": " ",
			"selectedTimePercentage": " ",
			"lastWeekPercentage": " ",
			"lastMonthPercentage": " "
		}
	},
	"timeObj": {
		"selectedTime": ["2020-07-06 11:56:32", "2020-07-06 12:56:32"],
		"yesterday": ["2020-07-21 00:00:00", "2020-07-21 23:59:59"],
		"lastWeek": ["2020-07-14 00:00:00", "2020-07-21 23:59:59"],
		"lastMonth": ["2020-06-21 00:00:00", "2020-07-21 23:59:59"]
	},	
};