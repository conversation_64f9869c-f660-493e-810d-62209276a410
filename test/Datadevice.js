

module.exports = {

	// connection : "dynamoDB",
	"attributes": {
		"timestamp": {
			"type": "string",
			"primaryKey": "range"
		},
		"deviceId": {
			"type": "string",
			"primaryKey": "hash",
		},
		"siteId": {
			"type": "string",
			"index": "secondary"
		},
		"data": {
			"type": "json",
			"required": true
		}
		/* Format of JSON
            {   pid:{
                    unit:"some unit",
                    value:"some value"
                }
                pid:{
                    unit:"some unit",
                    value:"some value"
                }

            }
            example :
            {
                kwah:{
                    unit:"total energy",
                    value: 305
                }
            }
        */
	}
};
