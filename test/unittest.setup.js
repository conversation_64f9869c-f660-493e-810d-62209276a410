// global setup for unittesting

const sails = require("sails");
const keys = require("../config/env/keys");

global.sails = sails;

global.sails.config = {
  // configuration for testing purposes
  "log": {
    "level": "info",
  },
  "hooks": {
    "grunt": false,
  },
  "models": {
    "connection": "dynamoDBAsiaPacific",
    "migrate": "safe",
  },
  "connections": {
    "dynamoDBAsiaPacific": {
      "adapter": "sails-dynamodb",
    },
  },
  "sockets": {
    "adapter": "memory",
  },
  "cachePort": 6379,
  // "cacheHost": '*************'
  "cacheHost": "localhost",
};
