/* eslint-disable no-console */
// Global Setup File for Mocha
const supertest = require("supertest");
const sails = require("sails");

before(done => {
	sails.log("lifting sails now");
	sails.lift({}, err => {
		if(process.env.TEST_ENV === "server")
			console.log = function () { };
		if (err) {
			return done(err);
		}
		supertest("localhost:1337/")
			.post("v1/auth/login")
			.set("authorization", "12345")
			.send({
				"userId": "test",
				"password": "smartjoules"
			})
			.expect(200)
			.then(val => {
				global.token = val.body.token;
				done(err, sails);
			});
	});
});


after(done => {
	// process.exit();
	sails.log("lowering sails now");
	sails.lower(done);
});
