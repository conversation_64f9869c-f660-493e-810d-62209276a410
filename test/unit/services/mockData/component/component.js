const find = [
	{
		"siteId": "ssh",
		"deviceId": "ssh_28",
		"driverType": "0",
		"deviceType": "ahu",
		"data":
			"[\"{\\\"key\\\":\\\"status\\\",\\\"expression\\\":\\\"||4187@feedback||\\\",\\\"deviceId\\\":\\\"4187\\\",\\\"displayName\\\":\\\"Status\\\",\\\"unit\\\":\\\"On/Off\\\",\\\"min\\\":null,\\\"max\\\":null,\\\"paramGroup\\\":\\\"unitless\\\",\\\"dau\\\":\\\"NA\\\"}\",\"{\\\"key\\\":\\\"kva\\\",\\\"expression\\\":\\\"||2958@inputpower||\\\",\\\"deviceId\\\":\\\"2958\\\",\\\"displayName\\\":\\\"Average Apparent Power\\\",\\\"unit\\\":\\\"kVA\\\",\\\"min\\\":\\\"\\\",\\\"max\\\":\\\"\\\",\\\"paramGroup\\\":\\\"voltamps\\\",\\\"dau\\\":\\\"kV*A\\\"}\"]",
		"updatedAt": "2019-07-09T11:30:35.834Z",
		"createdAt": "2018-12-13T06:46:41.431Z",
		"name": "OT - 3 AHU",
		"controls":
			"[\"{\\\"key\\\":\\\"stop\\\",\\\"expression\\\":\\\"||4187@feedback||==||0||\\\",\\\"deviceId\\\":\\\"2957\\\",\\\"min\\\":\\\"0\\\",\\\"max\\\":1,\\\"timeout\\\":120,\\\"displayName\\\":\\\"Stop\\\",\\\"paramGroup\\\":\\\"unitless\\\",\\\"dau\\\":\\\"NA\\\"}\",\"{\\\"key\\\":\\\"start\\\",\\\"expression\\\":\\\"||4187@feedback||==||1||\\\",\\\"deviceId\\\":\\\"2957\\\",\\\"min\\\":0,\\\"max\\\":\\\"1\\\",\\\"timeout\\\":120,\\\"displayName\\\":\\\"Start\\\",\\\"paramGroup\\\":\\\"unitless\\\",\\\"dau\\\":\\\"NA\\\"}\"]",
		"controllerId": "2850",
		"regionId": "xsxr"
	},
	{
		"siteId": "ssh",
		"deviceId": "ssh_26",
		"driverType": "0",
		"deviceType": "ahu",
		"data":
			"[\"{\\\"key\\\":\\\"cwit\\\",\\\"expression\\\":\\\"||1167@tmp||\\\",\\\"deviceId\\\":\\\"1167\\\",\\\"displayName\\\":\\\"Chilled Water Inlet Temperature\\\",\\\"unit\\\":\\\"°C\\\",\\\"min\\\":\\\"\\\",\\\"max\\\":\\\"\\\",\\\"paramGroup\\\":\\\"temperature\\\",\\\"dau\\\":\\\"degC\\\"}\",\"{\\\"key\\\":\\\"cwot\\\",\\\"expression\\\":\\\"||1166@tmp||\\\",\\\"deviceId\\\":\\\"1166\\\",\\\"displayName\\\":\\\"Chilled Water Outlet Temperature\\\",\\\"unit\\\":\\\"°C\\\",\\\"min\\\":\\\"\\\",\\\"max\\\":\\\"\\\",\\\"paramGroup\\\":\\\"temperature\\\",\\\"dau\\\":\\\"degC\\\"}\",\"{\\\"key\\\":\\\"sa\\\",\\\"expression\\\":\\\"||1165@tmp||\\\",\\\"deviceId\\\":\\\"1165\\\",\\\"displayName\\\":\\\"Supply Air Temperature\\\",\\\"unit\\\":\\\"°C\\\",\\\"min\\\":\\\"\\\",\\\"max\\\":\\\"\\\",\\\"paramGroup\\\":\\\"temperature\\\",\\\"dau\\\":\\\"degC\\\"}\",\"{\\\"key\\\":\\\"kva\\\",\\\"expression\\\":\\\"||1157@kva||\\\",\\\"deviceId\\\":\\\"1157\\\",\\\"displayName\\\":\\\"Average Apparent Power\\\",\\\"unit\\\":\\\"kVA\\\",\\\"min\\\":\\\"\\\",\\\"max\\\":\\\"\\\",\\\"paramGroup\\\":\\\"voltamps\\\",\\\"dau\\\":\\\"kV*A\\\"}\",\"{\\\"key\\\":\\\"kvah\\\",\\\"expression\\\":\\\"||1157@kvah||\\\",\\\"deviceId\\\":\\\"1157\\\",\\\"displayName\\\":\\\"Active Energy\\\",\\\"unit\\\":\\\"kVAh\\\",\\\"min\\\":\\\"\\\",\\\"max\\\":\\\"\\\",\\\"paramGroup\\\":\\\"voltampshrs\\\",\\\"dau\\\":\\\"kV*A*hr\\\"}\",\"{\\\"key\\\":\\\"kw\\\",\\\"expression\\\":\\\"||1157@kw||\\\",\\\"deviceId\\\":\\\"1157\\\",\\\"displayName\\\":\\\"Average Active Power\\\",\\\"unit\\\":\\\"kW\\\",\\\"min\\\":\\\"\\\",\\\"max\\\":\\\"\\\",\\\"paramGroup\\\":\\\"power\\\",\\\"dau\\\":\\\"kW\\\"}\",\"{\\\"key\\\":\\\"actuator\\\",\\\"expression\\\":\\\"||1169@actuator_feedback||\\\",\\\"deviceId\\\":\\\"1169\\\",\\\"displayName\\\":\\\"Two Way Valve Position\\\",\\\"unit\\\":\\\"%\\\",\\\"min\\\":\\\"\\\",\\\"max\\\":\\\"\\\",\\\"paramGroup\\\":\\\"unitless\\\",\\\"dau\\\":\\\"percent\\\"}\",\"{\\\"key\\\":\\\"status\\\",\\\"expression\\\":\\\"||3122@inputpower||>||0.5||\\\",\\\"deviceId\\\":\\\"3122,\\\",\\\"displayName\\\":\\\"Status\\\",\\\"unit\\\":\\\"NA\\\",\\\"min\\\":\\\"\\\",\\\"max\\\":\\\"\\\",\\\"paramGroup\\\":\\\"unitless\\\",\\\"dau\\\":\\\"NA\\\"}\",\"{\\\"key\\\":\\\"outputfrequency\\\",\\\"expression\\\":\\\"||3122@outputfrequency||\\\",\\\"deviceId\\\":\\\"3122\\\",\\\"displayName\\\":\\\"Output Frequency\\\",\\\"unit\\\":\\\"Hz\\\",\\\"min\\\":\\\"\\\",\\\"max\\\":\\\"\\\",\\\"paramGroup\\\":\\\"frequency\\\",\\\"dau\\\":\\\"Hz\\\"}\"]",
		"updatedAt": "2019-07-18T09:44:19.704Z",
		"createdAt": "2018-12-12T08:52:33.168Z",
		"pidOn": "2709",
		"name": "Nursery AHU",
		"controls":
			"[\"{\\\"key\\\":\\\"stop\\\",\\\"expression\\\":\\\"||1157@kva||<||0.3||\\\",\\\"deviceId\\\":\\\"3122\\\",\\\"min\\\":\\\"0\\\",\\\"max\\\":\\\"1\\\",\\\"timeout\\\":\\\"60\\\",\\\"displayName\\\":\\\"Stop\\\",\\\"paramGroup\\\":\\\"unitless\\\",\\\"dau\\\":\\\"NA\\\"}\",\"{\\\"key\\\":\\\"start\\\",\\\"expression\\\":\\\"||1157@kva||>||0.3||\\\",\\\"deviceId\\\":\\\"3122\\\",\\\"min\\\":\\\"0\\\",\\\"max\\\":\\\"1\\\",\\\"timeout\\\":\\\"60\\\",\\\"displayName\\\":\\\"Start\\\",\\\"paramGroup\\\":\\\"unitless\\\",\\\"dau\\\":\\\"NA\\\"}\",\"{\\\"key\\\":\\\"setfrequency\\\",\\\"expression\\\":\\\"||3122@outputfrequency||==||COMMAND||\\\",\\\"deviceId\\\":\\\"3122\\\",\\\"min\\\":\\\"25\\\",\\\"max\\\":\\\"55\\\",\\\"timeout\\\":\\\"60\\\",\\\"displayName\\\":\\\"Output Frequency\\\",\\\"paramGroup\\\":\\\"frequency\\\",\\\"dau\\\":\\\"Hz\\\"}\",\"{\\\"key\\\":\\\"changesetpoint\\\",\\\"expression\\\":\\\"||(||(||(||COMMAND||-||2||)||<=||(||1169@actuator_feedback||)||)||and||(||(||1169@actuator_feedback||)||<=||(||COMMAND||+||2||)||)||)||\\\",\\\"deviceId\\\":\\\"1168\\\",\\\"min\\\":\\\"0\\\",\\\"max\\\":\\\"100\\\",\\\"timeout\\\":\\\"120\\\",\\\"displayName\\\":\\\"Two Way Valve Position\\\",\\\"paramGroup\\\":\\\"unitless\\\",\\\"dau\\\":\\\"percent\\\"}\"]",
		"controllerId": "2722",
		"regionId": "jzvm"
	}
];
const filteredArr = [
	{
		"siteId": "ssh",
		"deviceId": "ssh_1",
		"driverType": "0",
		"deviceType": "ahu",
		"data": [
			{
				"key": "cwit",
				"expression": "||130@tmp||",
				"deviceId": "130",
				"displayName": "Chilled Water Inlet Temperature",
				"unit": "°C",
				"min": "",
				"max": "",
				"paramGroup": "temperature",
				"dau": "degC"
			},
			{
				"key": "cwot",
				"expression": "||131@tmp||",
				"deviceId": "131",
				"displayName": "Chilled Water Outlet Temperature",
				"unit": "°C",
				"min": "",
				"max": "",
				"paramGroup": "temperature",
				"dau": "degC"
			},
			{
				"key": "sa",
				"expression": "||134@tmp||",
				"deviceId": "134",
				"displayName": "Supply Air Temperature",
				"unit": "°C",
				"min": "",
				"max": "",
				"paramGroup": "temperature",
				"dau": "degC"
			},
			{
				"key": "kva",
				"expression": "||135@kva||",
				"deviceId": "135",
				"displayName": "Average Apparent Power",
				"unit": "kVA",
				"min": "",
				"max": "",
				"paramGroup": "voltamps",
				"dau": "kV*A"
			},
			{
				"key": "kw",
				"expression": "||135@kw||",
				"deviceId": "135",
				"displayName": "Average Active Power",
				"unit": "kW",
				"min": "",
				"max": "",
				"paramGroup": "power",
				"dau": "kW"
			},
			{
				"key": "actuator",
				"expression": "||340@actuator_feedback||",
				"deviceId": "340",
				"displayName": "Two Way Valve Position",
				"unit": "%",
				"min": "",
				"max": "",
				"paramGroup": "unitless",
				"dau": "percent"
			},
			{
				"key": "status",
				"expression": "||135@kva||>||0.3||",
				"deviceId": "135,",
				"displayName": "Status",
				"unit": "NA",
				"min": "",
				"max": "",
				"paramGroup": "unitless",
				"dau": "NA"
			}
		],
		"updatedAt": "2019-07-05T10:18:17.440Z",
		"createdAt": "2018-12-12T09:19:18.225Z",
		"pidOn": "2704",
		"name": " Library AHU",
		"controls": [
			{
				"key": "stop",
				"expression": "||135@kva||<||0.3||",
				"deviceId": "137",
				"min": "0",
				"max": "1",
				"timeout": "60",
				"displayName": "Stop",
				"paramGroup": "unitless",
				"dau": "NA"
			},
			{
				"key": "start",
				"expression": "||135@kva||>||0.3||",
				"deviceId": "137",
				"min": "0",
				"max": "1",
				"timeout": "60",
				"displayName": "Start",
				"paramGroup": "unitless",
				"dau": "NA"
			},
			{
				"key": "changesetpoint",
				"expression":
					"||(||(||(||COMMAND||+||2||)||<=||(||340@actuator_feedback||)||)||and||(||(||340@actuator_feedback||)||<=||(||COMMAND||-||2||)||)||)||",
				"deviceId": "159",
				"min": "0",
				"max": "100",
				"timeout": 60,
				"displayName": "Two Way Valve Position",
				"paramGroup": "unitless",
				"dau": "percent"
			}
		],
		"controllerId": "2703",
		"regionId": "suby"
	},
	{
		"siteId": "ssh",
		"deviceId": "ssh_2",
		"driverType": "0",
		"deviceType": "ahu",
		"data": [
			{
				"key": "kva",
				"expression": "||178@kva||",
				"deviceId": "178",
				"displayName": "Average Apparent Power",
				"unit": "kVA",
				"min": "",
				"max": "",
				"paramGroup": "voltamps",
				"dau": "kV*A"
			},
			{
				"key": "kw",
				"expression": "||178@kw||",
				"deviceId": "178",
				"displayName": "Average Active Power",
				"unit": "kW",
				"min": "",
				"max": "",
				"paramGroup": "power",
				"dau": "kW"
			},
			{
				"key": "status",
				"expression": "||178@kva||>||0.3||",
				"deviceId": "178,",
				"displayName": "Status",
				"unit": "NA",
				"min": "",
				"max": "",
				"paramGroup": "unitless",
				"dau": "NA"
			},
			{
				"key": "outputfrequency",
				"expression": "||3119@outputfrequency||",
				"deviceId": "3119",
				"displayName": "Output Frequency",
				"unit": "Hz",
				"min": "",
				"max": "",
				"paramGroup": "frequency",
				"dau": "Hz"
			}
		],
		"updatedAt": "2019-07-12T09:31:50.310Z",
		"createdAt": "2018-12-12T09:19:53.455Z",
		"name": "Blood Bank AHU",
		"controls": [
			{
				"key": "stop",
				"expression": "||178@kva||<||0.3||",
				"deviceId": "3119",
				"min": "0",
				"max": "1",
				"timeout": "60",
				"displayName": "Stop",
				"paramGroup": "unitless",
				"dau": "NA"
			},
			{
				"key": "start",
				"expression": "||178@kva||>||0.3||",
				"deviceId": "3119",
				"min": "0",
				"max": "1",
				"timeout": "60",
				"displayName": "Start",
				"paramGroup": "unitless",
				"dau": "NA"
			},
			{
				"key": "setfrequency",
				"expression": "||3119@outputfrequency||==||COMMAND||",
				"deviceId": "3119",
				"min": "25",
				"max": "55",
				"timeout": "60",
				"displayName": "Output Frequency",
				"paramGroup": "frequency",
				"dau": "Hz"
			}
		],
		"controllerId": "2705",
		"regionId": "xvsy"
	},
	{
		"siteId": "ssh",
		"deviceId": "ssh_12",
		"driverType": "0",
		"deviceType": "chiller",
		"data": [
			{
				"key": "status",
				"expression": "||67@kva||>||0.5||",
				"deviceId": "67,",
				"displayName": "Status",
				"unit": "On/Off",
				"min": "",
				"max": "",
				"paramGroup": "unitless",
				"dau": "NA"
			},
			{
				"key": "pertotcap",
				"expression": "||65@pertotcap||",
				"deviceId": "65",
				"displayName": "Chiller Loading Capacity",
				"unit": "%",
				"min": "",
				"max": "",
				"paramGroup": "unitless",
				"dau": "percent"
			},
			{
				"key": "controltype",
				"expression": "||65@controltype||",
				"deviceId": "65",
				"displayName": "Control Type",
				"unit": "NA",
				"min": "",
				"max": "",
				"paramGroup": "unitless",
				"dau": "NA"
			},
			{
				"key": "setpoint",
				"expression": "||65@setpoint||",
				"deviceId": "65",
				"displayName": "Set Point",
				"unit": "°F",
				"min": "",
				"max": "",
				"paramGroup": "temperature",
				"dau": "degC"
			},
			{
				"key": "coolewt",
				"expression": "||65@coolewt||",
				"deviceId": "65",
				"displayName": "Chilled Water in Temperature",
				"unit": "°F",
				"min": "",
				"max": "",
				"paramGroup": "temperature",
				"dau": "degC"
			},
			{
				"key": "coollwt",
				"expression": "||65@coollwt||",
				"deviceId": "65",
				"displayName": "Chilled Water out Temperature",
				"unit": "°F",
				"min": "",
				"max": "",
				"paramGroup": "temperature",
				"dau": "degC"
			},
			{
				"key": "condewt",
				"expression": "||65@condewt||",
				"deviceId": "65",
				"displayName": "Condenser in Temperature",
				"unit": "°F",
				"min": "",
				"max": "",
				"paramGroup": "temperature",
				"dau": "degC"
			},
			{
				"key": "condlwt",
				"expression": "||65@condlwt||",
				"deviceId": "65",
				"displayName": "Condenser out Temperature",
				"unit": "°F",
				"min": "",
				"max": "",
				"paramGroup": "temperature",
				"dau": "degC"
			},
			{
				"key": "scta",
				"expression": "||65@scta||",
				"deviceId": "65",
				"displayName": "Refrigerant Condenser Temperature",
				"unit": "°F",
				"min": "",
				"max": "",
				"paramGroup": "temperature",
				"dau": "degC"
			},
			{
				"key": "ssta",
				"expression": "||65@ssta||",
				"deviceId": "65",
				"displayName": "Refrigerant Evaporator Temperature",
				"unit": "°F",
				"min": "",
				"max": "",
				"paramGroup": "temperature",
				"dau": "degC"
			},
			{
				"key": "hrs",
				"expression": "||65@hrmach||",
				"deviceId": "65",
				"displayName": "Running Hours",
				"unit": "Hours",
				"min": "",
				"max": "",
				"paramGroup": "time",
				"dau": "hr"
			},
			{
				"key": "condapproach",
				"expression": "||(||(||65@scta||)||-||(||65@condlwt||)||)||",
				"deviceId": "65,65,",
				"displayName": "Condenser Approach",
				"unit": "°F",
				"min": "",
				"max": "",
				"paramGroup": "temperature",
				"dau": "degC"
			},
			{
				"key": "evapproach",
				"expression": "||65@coollwt||-||65@ssta||",
				"deviceId": "65,65",
				"displayName": "Evaporator Approach",
				"unit": "°F",
				"min": "",
				"max": "",
				"paramGroup": "temperature",
				"dau": "degC"
			},
			{
				"key": "kva",
				"expression": "||67@kva||",
				"deviceId": "67",
				"displayName": "Average Apparent Power",
				"unit": "kVA",
				"min": "",
				"max": "",
				"paramGroup": "voltamps",
				"dau": "kV*A"
			},
			{
				"key": "kvah",
				"expression": "||67@kvah||",
				"deviceId": "67",
				"displayName": "Active Energy",
				"unit": "kVAh",
				"min": "",
				"max": "",
				"paramGroup": "voltampshrs",
				"dau": "kV*A*hr"
			},
			{
				"key": "kw",
				"expression": "||67@kw||",
				"deviceId": "67",
				"displayName": "Average Active Power",
				"unit": "kW",
				"min": "",
				"max": "",
				"paramGroup": "power",
				"dau": "kW"
			},
			{
				"key": "tr",
				"expression":
					"||(||69@waterflow||*||(||65@coolewt||-||65@coollwt||)||/||24||)||",
				"deviceId": "69,65,65,",
				"displayName": "Tonnage",
				"unit": "TR",
				"min": "",
				"max": "",
				"paramGroup": "tonnage",
				"dau": "refrigeration_ton"
			},
			{
				"key": "eff",
				"expression":
					"||67@kw||/||(||69@waterflow||*||(||65@coolewt||-||65@coollwt||)||/||24||)||",
				"deviceId": "67,69,65,65,",
				"displayName": "Chiller Efficiency",
				"unit": "kW/TR",
				"min": "",
				"max": "",
				"paramGroup": "unitless",
				"dau": "percent"
			},
			{
				"key": "chleff",
				"expression":
					"||(||(||(||67@kva||)||+||(||146@kva||)||+||(||147@kva||)||+||(||2604@kva||)||+||(||155@kva||)||)||/||(||(||(||(||65@coolewt||)||-||(||65@coollwt||)||)||*||(||69@waterflow||)||)||/||24||)||)||",
				"deviceId": "67,146,147,2604,155,65,65,69,",
				"displayName": "Chiller Plant Efficiency",
				"unit": "kW/TR",
				"min": "",
				"max": "",
				"paramGroup": "unitless",
				"dau": "percent"
			},
			{
				"key": "dispressure",
				"expression": "||65@dispressure||",
				"deviceId": "65",
				"unit": "kPa",
				"displayName": "Discharge Pressure",
				"min": "",
				"max": "",
				"paramGroup": "pressure",
				"dau": "kPa"
			},
			{
				"key": "sucpressure",
				"expression": "||65@sucpressure||",
				"deviceId": "65",
				"unit": "kPa",
				"displayName": "Suction Pressure",
				"min": "",
				"max": "",
				"paramGroup": "pressure",
				"dau": "kPa"
			}
		],
		"updatedAt": "2019-04-15T05:35:29.038Z",
		"createdAt": "2018-12-12T09:39:58.080Z",
		"name": "New Chiller Carrier 350 TR Chiller",
		"controls": [
			{
				"key": "stop",
				"expression": "||65@status||==||0||",
				"deviceId": "65",
				"min": "0",
				"max": "1",
				"timeout": "60",
				"displayName": "Stop",
				"paramGroup": "unitless",
				"dau": "NA"
			},
			{
				"key": "start",
				"expression": "||65@status||==||1||",
				"deviceId": "65",
				"min": "0",
				"max": "1",
				"timeout": "60",
				"displayName": "Start",
				"paramGroup": "unitless",
				"dau": "NA"
			},
			{
				"key": "changesetpoint",
				"expression": "||65@setpoint||==||COMMAND||",
				"deviceId": "65",
				"min": "44",
				"max": "54",
				"timeout": "60",
				"displayName": "Chiller Setpoint",
				"paramGroup": "temperature",
				"dau": "degC"
			}
		],
		"controllerId": "2701",
		"regionId": "bsju"
	},
	{
		"siteId": "ssh",
		"deviceId": "ssh_9",
		"driverType": "0",
		"deviceType": "condenserWaterPump",
		"data": [
			{
				"key": "outputfrequency",
				"expression": "||70@outputfrequency||",
				"deviceId": "70",
				"displayName": "Output Frequency",
				"unit": "Hz",
				"min": "",
				"max": "",
				"paramGroup": "frequency",
				"dau": "Hz"
			},
			{
				"key": "waterflow",
				"expression": "||70@waterflow||",
				"deviceId": "70",
				"displayName": "WaterFlow",
				"unit": "GPM",
				"min": "",
				"max": "",
				"paramGroup": "flow",
				"dau": "gallon / min"
			},
			{
				"key": "head",
				"expression": "||70@head||",
				"deviceId": "70",
				"displayName": "Head",
				"unit": "m",
				"min": "",
				"max": "",
				"paramGroup": "length",
				"dau": "m"
			},
			{
				"key": "kva",
				"expression": "||147@kva||",
				"deviceId": "147",
				"displayName": "Average Apparent Power",
				"unit": "kVA",
				"min": "",
				"max": "",
				"paramGroup": "voltamps",
				"dau": "kV*A"
			},
			{
				"key": "kvah",
				"expression": "||147@kvah||",
				"deviceId": "147",
				"displayName": "Active Energy",
				"unit": "kVAh",
				"min": "",
				"max": "",
				"paramGroup": "voltampshrs",
				"dau": "kV*A*hr"
			},
			{
				"key": "kw",
				"expression": "||147@kw||",
				"deviceId": "147",
				"displayName": "Average Active Power",
				"unit": "kW",
				"min": "",
				"max": "",
				"paramGroup": "power",
				"dau": "kW"
			},
			{
				"key": "eff",
				"expression":
					"||(||70@waterflow||*||70@head||)||/||(||70@inputpower||*||1615||)||*||100||",
				"deviceId": "70,70,70,",
				"displayName": "Pump Efficiency",
				"unit": "%",
				"min": "",
				"max": "",
				"paramGroup": "unitless",
				"dau": "percent"
			},
			{
				"key": "status",
				"expression": "||147@kva||>||0.3||",
				"deviceId": "147,",
				"displayName": "Status",
				"unit": "NA",
				"min": "",
				"max": "",
				"paramGroup": "unitless",
				"dau": "NA"
			}
		],
		"updatedAt": "2019-04-04T07:49:31.893Z",
		"createdAt": "2018-12-12T09:47:19.887Z",
		"name": "Condenser water Pump - 1 ",
		"controls": [
			{
				"key": "stop",
				"expression": "||70@operationalstatus||==||0||",
				"deviceId": "70",
				"min": "0",
				"max": "1",
				"timeout": "60",
				"displayName": "Stop",
				"paramGroup": "unitless",
				"dau": "NA"
			},
			{
				"key": "start",
				"expression": "||70@operationalstatus||==||1||",
				"deviceId": "70",
				"min": "0",
				"max": "1",
				"timeout": "60",
				"displayName": "Start",
				"paramGroup": "unitless",
				"dau": "NA"
			},
			{
				"key": "setfrequency",
				"expression": "||70@outputfrequency||==||COMMAND||",
				"deviceId": "70",
				"min": "25",
				"max": "55",
				"timeout": "60",
				"displayName": "Output Frequency",
				"paramGroup": "frequency",
				"dau": "Hz"
			}
		],
		"controllerId": "2701",
		"regionId": "bsju"
	},
	{
		"siteId": "ssh",
		"deviceId": "ssh_15",
		"driverType": "0",
		"deviceType": "coolingTower",
		"data": [
			{
				"key": "kva",
				"expression": "||155@kva||",
				"deviceId": "155",
				"displayName": "Average Apparent Power",
				"unit": "kVA",
				"min": "",
				"max": "",
				"paramGroup": "voltamps",
				"dau": "kV*A"
			},
			{
				"key": "inwatertemp",
				"expression": "||65@condlwt||",
				"deviceId": "65",
				"displayName": "CT Inlet Water Temperature",
				"unit": "°F",
				"min": "",
				"max": "",
				"paramGroup": "temperature",
				"dau": "degC"
			},
			{
				"key": "outwatertemp",
				"expression": "||65@condewt||",
				"deviceId": "65",
				"displayName": "CT Outlet Water Temperature",
				"unit": "°F",
				"min": "",
				"max": "",
				"paramGroup": "temperature",
				"dau": "degC"
			},
			{
				"key": "range",
				"expression": "||(||(||65@condlwt||)||-||(||65@condewt||)||)||",
				"deviceId": "65,65,",
				"displayName": "Cooling Tower Range",
				"unit": "°F",
				"min": "",
				"max": "",
				"paramGroup": "temperature",
				"dau": "degC"
			},
			{
				"key": "status",
				"expression": "||155@kva||>||0.3||",
				"deviceId": "155,",
				"displayName": "Status",
				"unit": "On/Off",
				"min": "",
				"max": "",
				"paramGroup": "unitless",
				"dau": "NA"
			},
			{
				"key": "approach",
				"expression":
					"||65@condewt||-||(||(||2100@wetbulb||*||(||1.8||)||)||+||32||)||",
				"deviceId": "65,2100,",
				"displayName": "Cooling Tower Approach",
				"unit": "°F",
				"min": "",
				"max": "",
				"paramGroup": "temperature",
				"dau": "degC"
			},
			{
				"key": "efficiency",
				"expression":
					"||(||(||65@condewt||)||-||(||(||2100@wetbulb||)||*||1.8||)||+||32||)||",
				"deviceId": "65,2100,",
				"displayName": "Cooling Tower Efficiency",
				"unit": "%",
				"min": "",
				"max": "",
				"paramGroup": "unitless",
				"dau": "percent"
			},
			{
				"key": "outputfrequency",
				"expression": "||166@outputfrequency||",
				"deviceId": "166",
				"displayName": "Output Frequency",
				"unit": "Hz",
				"min": "",
				"max": "",
				"paramGroup": "frequency",
				"dau": "Hz"
			}
		],
		"updatedAt": "2019-06-10T11:26:25.084Z",
		"createdAt": "2018-12-12T09:55:46.864Z",
		"name": "Cooling Tower - 2 ",
		"controls": [
			{
				"key": "stop",
				"expression": "||166@operationalstatus||==||0||",
				"deviceId": "166",
				"min": "0",
				"max": "1",
				"timeout": "60",
				"displayName": "Stop",
				"paramGroup": "unitless",
				"dau": "NA"
			},
			{
				"key": "start",
				"expression": "||166@operationalstatus||==||1||",
				"deviceId": "166",
				"min": "0",
				"max": "1",
				"timeout": "60",
				"displayName": "Start",
				"paramGroup": "unitless",
				"dau": "NA"
			},
			{
				"key": "setfrequency",
				"expression": "||166@outputfrequency||==||COMMAND||",
				"deviceId": "166",
				"min": "25",
				"max": "55",
				"timeout": "60",
				"displayName": "Output Frequency",
				"paramGroup": "frequency",
				"dau": "Hz"
			}
		],
		"controllerId": "2701",
		"regionId": "bsju"
	}
];
module.exports = {
	find,
	filteredArr
};
