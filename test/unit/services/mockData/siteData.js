module.exports = {
	"site": {
		"areas": "{\"yrql\":{\"name\":\"IPD Building\",\"regions\":[\"dzdl\",\"zbdr\",\"kgim\",\"keye\",\"rzkm\",\"nqhg\"]},\"lsab\":{\"name\":\"OPD Building\",\"regions\":[\"uqmu\",\"xngx\",\"bgik\",\"xsxr\",\"quyq\",\"mhez\",\"abhu\",\"fdwn\",\"przy\",\"owag\",\"tuui\",\"vrto\",\"wrpv\"]},\"vmgz\":{\"name\":\"ONCO\",\"regions\":[\"vqak\",\"uofc\",\"pnpg\",\"ydar\",\"ozow\"]},\"iyeg\":{\"name\":\"New Building\",\"regions\":[\"fxzf\",\"mceu\"]}}",
		"createdAt": "2018-11-26T11:08:03.954Z",
		"latitude": "28.5806135",
		"longitude": "77.2314447",
		"networkCount": 4,
		"networks": "{\"smartjoules-network-0\":[]}",
		"planningDocUrl": "https://s3.us-west-2.amazonaws.com/dejoule.docs/frtn/config/test",
		"regionCount": 26,
		"regions": "{\"dzdl\":{\"name\":\"IPD 4F Cath Lab\",\"area\":\"yrql\",\"controller\":[\"2845\"]},\"zbdr\":{\"name\":\"IPD 1F near Lift 1 & 2\",\"area\":\"yrql\",\"controller\":[\"2835\",\"2846\"]},\"kgim\":{\"name\":\"MRI\",\"area\":\"yrql\",\"controller\":[\"2836\",\"2847\"]},\"keye\":{\"name\":\"IPD 2F Near Lift 3 and 4\",\"area\":\"yrql\",\"controller\":[\"2859\"]},\"rzkm\":{\"name\":\"IPD 2F Near Lift 1 & 2\",\"area\":\"yrql\",\"controller\":[\"2863\"]},\"nqhg\":{\"name\":\"Kitchen Exhaust\",\"area\":\"yrql\",\"controller\":[\"2862\"]},\"uqmu\":{\"name\":\"OT\",\"area\":\"lsab\",\"controller\":[\"2856\"]},\"xngx\":{\"name\":\"OT-1\",\"area\":\"lsab\",\"controller\":[\"2837\",\"2848\"]},\"bgik\":{\"name\":\"OT-2\",\"area\":\"lsab\",\"controller\":[\"2838\",\"2849\"]},\"xsxr\":{\"name\":\"OT-3\",\"area\":\"lsab\",\"controller\":[\"2850\"]},\"quyq\":{\"name\":\"OT-4\",\"area\":\"lsab\",\"controller\":[\"2851\"]},\"mhez\":{\"name\":\"OT-5\",\"area\":\"lsab\",\"controller\":[\"2852\"]},\"abhu\":{\"name\":\"OT-6\",\"area\":\"lsab\",\"controller\":[\"2839\",\"2853\"]},\"fdwn\":{\"name\":\"OT 7 & 8\",\"area\":\"lsab\",\"controller\":[\"2840\",\"2854\"]},\"przy\":{\"name\":\"OT-9\",\"area\":\"lsab\",\"controller\":[\"2855\"]},\"owag\":{\"name\":\"Dialysis\",\"area\":\"lsab\",\"controller\":[\"2858\"]},\"tuui\":{\"name\":\"Chiller Plant Room\",\"area\":\"lsab\",\"controller\":[\"2831\",\"2832\",\"2833\",\"2834\",\"2875\"]},\"vqak\":{\"name\":\"ONCO 1F OPD\",\"area\":\"vmgz\",\"controller\":[\"2841\"]},\"uofc\":{\"name\":\"ONCO Day Care\",\"area\":\"vmgz\",\"controller\":[\"2842\"]},\"pnpg\":{\"name\":\"ONCO 2F Seminar Hall\",\"area\":\"vmgz\",\"controller\":[\"2843\"]},\"ydar\":{\"name\":\"ONCO 2F HR Compound\",\"area\":\"vmgz\",\"controller\":[\"2844\"]},\"ozow\":{\"name\":\"ONCO Basement\",\"area\":\"vmgz\",\"controller\":[\"2857\"]},\"fxzf\":{\"name\":\"STP\",\"area\":\"iyeg\",\"controller\":[\"2860\"]},\"mceu\":{\"name\":\"WTP\",\"area\":\"iyeg\",\"controller\":[\"2861\"]},\"vrto\":{\"name\":\"AC Plant Room \",\"area\":\"lsab\",\"controller\":[\"2830\"]},\"wrpv\":{\"name\":\"LT Room\",\"area\":\"lsab\",\"controller\":[\"2828\"]}}",
		"siteId": "frtn",
		"siteName": "Fortis Hospital Noida",
		"updatedAt": "2018-12-22T05:02:55.722Z"
	},

	"oldDeviceInfo": {
		"areaId": "hhhx",
		"communicationCategory": "VFD",
		"communicationType": "MB",
		"componentId": "ssh_8",
		"controllerId": "2701",
		"createdAt": "2018-11-24T07:59:33.522Z",
		"deviceId": "102",
		"deviceType": "vfd",
		"driverType": "0",
		"functionType": "both",
		"name": "koochamonster",
		"networkId": "smartjoules-network-0",
		"portNumber": "3",
		"regionId": "bsju",
		"siteId": "ssh",
		"slaveId": "9",
		"updatedAt": "2019-02-02T20:47:31.055Z"
	},

	"updateParams": {
		"areaId":	"hhhx",
		"regionId": "bsju",
		"req": {

		}
	},

	"controller": {
		"regionId": "bsju",
		"siteId": "summy",
		"deviceId": "hhhx",
		"req": {
			"a": 1,
			"b": 2
		}
	}
};
