module.exports = {
	
	"rdata": {
		"config": "\"name\":\"value\", \"key\":\"anotherValue\"",
		"extra": {
			"siteId": "ssh",
			"componentId": "jouleiocontrol",
			"controlId": "R153"
		}
	},

	"keyErr": ["err"],

	"falseRdata": {
		"config": "",
		"extra": {
			"siteId": "",
			"componentId": "",
			"controlId": ""
		}
	},

	"invalidFindResult": [
		{
			"networkSlave1": "slave",
			"deviceType": "jouleio",
			"pid": ""
		}
	],

	"validFindResult": [
		{
			"networkSlave1": "slave",
			"deviceType": "jouleio",
			"pid": {
				"enable": true,
				"R": "when we wrote",
				"A": "wrote these tests",
				"V": "we didn't had a clue about this vals"
				// But since this is a unit test, its fine,
				// We are smart so we worked around it.
				// Cheers!
			}
		}
	],

	"siteId": "ssh",
	"NS1": "slave",
	"modesArr": ["mode1", "mode2"],

	"dids": ["undefined_id"],
	"newIds": [{
		"mode": {
			"id": "sickoMode"
		}
	}],

	"testId": "for_testing",
	"modBusId": ["for", "testing"],

	"ids": ["1182", "3342"],

	"cId": "R513",

	"exprnString": "||3072@operationalstatus||==||0||"
};