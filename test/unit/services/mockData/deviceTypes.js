const valid = {
	"communicationCategory": "VFD",
	"communicationType": "NMB",
	"driverType": "-1",
	"deviceType": "vfd",
	"params": [
		{
			"abbr": "operationalstatus",
			"unit": "null",
			"type": "data",
			"displayName": "Operational Status",
			"address": 50200,
			"properties": {
				"index": 0,
				"driver": 0,
				"mulFactor": 1,
				"offset": 0,
				"min": "null",
				"max": "null"
			},
			"filter": { "existence": "False", "oldVal": "False", "variance": 0 },
			"statePreference": "None",
			"errOffset": 0,
			"rawUnit": "NA",
			"dau": "NA",
			"paramGroup": "unitless"
		},
		{
			"abbr": "kwh",
			"unit": "kWh",
			"type": "data",
			"displayName": "Real Power",
			"address": 15020,
			"properties": {
				"index": 1,
				"driver": 0,
				"mulFactor": 1,
				"offset": 0,
				"min": "null",
				"max": "null"
			},
			"filter": { "existence": "False", "oldVal": "False", "variance": 0 },
			"statePreference": "None",
			"errOffset": 0,
			"rawUnit": "kWh",
			"dau": "kWh",
			"paramGroup": "energy"
		},
		{
			"abbr": "inputterminalvolt",
			"unit": "Volt",
			"type": "data",
			"displayName": "Input Terminal Volt",
			"address": 16120,
			"properties": {
				"index": 0,
				"driver": 0,
				"mulFactor": 0.1,
				"offset": 0,
				"min": "null",
				"max": "null"
			},
			"filter": { "existence": "False", "oldVal": "False", "variance": 0 },
			"statePreference": "None",
			"errOffset": 0,
			"rawUnit": "V",
			"dau": "V",
			"paramGroup": "voltage"
		},
		{
			"abbr": "inputterminalcurrent",
			"type": "data",
			"unit": "Volt",
			"displayName": "Input Terminal Current",
			"address": 16140,
			"properties": {
				"index": 1,
				"driver": 0,
				"mulFactor": 0.01,
				"offset": 0,
				"min": "null",
				"max": "null"
			},
			"filter": { "existence": "False", "oldVal": "False", "variance": 0 },
			"statePreference": "None",
			"errOffset": 0,
			"rawUnit": "A",
			"dau": "A",
			"paramGroup": "current"
		},
		{
			"abbr": "inputpower",
			"type": "data",
			"unit": "kW",
			"displayName": "Input Power",
			"address": 16100,
			"properties": {
				"index": 1,
				"driver": 0,
				"mulFactor": 0.01,
				"offset": 0,
				"min": "null",
				"max": "null"
			},
			"filter": { "existence": "False", "oldVal": "False", "variance": 0 },
			"statePreference": "None",
			"errOffset": 0,
			"rawUnit": "kW",
			"dau": "kW",
			"paramGroup": "power"
		},
		{
			"abbr": "outputfrequency",
			"unit": "Hz",
			"type": "data",
			"displayName": "Output Frequency",
			"address": 16130,
			"properties": {
				"index": 0,
				"driver": 0,
				"mulFactor": 0.1,
				"offset": 0,
				"min": "null",
				"max": "null"
			},
			"filter": { "existence": "False", "oldVal": "False", "variance": 0 },
			"statePreference": "None",
			"errOffset": 0,
			"rawUnit": "Hz",
			"dau": "Hz",
			"paramGroup": "frequency"
		},
		{
			"abbr": "head",
			"unit": "m",
			"type": "data",
			"displayName": "Head",
			"address": 16520,
			"properties": {
				"index": 1,
				"driver": 1,
				"mulFactor": 0.0003048,
				"offset": 0,
				"min": "null",
				"max": "null"
			},
			"filter": { "existence": "False", "oldVal": "False", "variance": 0 },
			"statePreference": "None",
			"errOffset": 0,
			"rawUnit": "m",
			"dau": "m",
			"paramGroup": "length"
		},
		{
			"abbr": "waterflow",
			"type": "data",
			"unit": "GPM",
			"displayName": "WaterFlow",
			"address": 18500,
			"properties": {
				"index": 1,
				"driver": 1,
				"mulFactor": 0.001,
				"offset": 0,
				"min": "null",
				"max": "null"
			},
			"filter": { "existence": "False", "oldVal": "False", "variance": 0 },
			"statePreference": "None",
			"errOffset": 0,
			"rawUnit": "gallon / min",
			"dau": "gallon / min",
			"paramGroup": "flow"
		},
		{
			"abbr": "start",
			"type": "command",
			"unit": "null",
			"displayName": "Start",
			"address": 50000,
			"properties": {
				"index": 1,
				"driver": 1,
				"mulFactor": 1148,
				"offset": 0,
				"min": "null",
				"max": "null"
			},
			"statePreference": "None",
			"errOffset": 0,
			"rawUnit": "NA",
			"operation": "int",
			"dau": "NA",
			"paramGroup": "unitless"
		},
		{
			"abbr": "stop",
			"type": "command",
			"unit": "null",
			"displayName": "Stop",
			"address": 50000,
			"properties": {
				"index": 1,
				"driver": 1,
				"mulFactor": 0,
				"offset": 1084,
				"min": "null",
				"max": "null"
			},
			"statePreference": "None",
			"errOffset": 0,
			"rawUnit": "NA",
			"operation": "int",
			"dau": "NA",
			"paramGroup": "unitless"
		},
		{
			"abbr": "setfrequency",
			"type": "command",
			"unit": "null",
			"displayName": "Set Frequency",
			"address": 50010,
			"properties": {
				"index": 1,
				"driver": 1,
				"mulFactor": 327.68,
				"offset": 0,
				"min": "null",
				"max": "null"
			},
			"statePreference": "None",
			"errOffset": 0,
			"rawUnit": "Hz",
			"dau": "Hz",
			"paramGroup": "frequency"
		}
	],
	"updatedAt": "2019-03-14T10:00:38.173Z",
	"createdAt": "2018-10-25T06:33:12.492Z",
	"driverName": "VFD - KIMS",
	"class": "devices",
	"parameters": [
		"{\"abbr\":\"operationalstatus\",\"unit\":\"null\",\"type\":\"data\",\"displayName\":\"Operational Status\",\"address\":50200,\"properties\":{\"index\":0,\"driver\":0,\"mulFactor\":1,\"offset\":0,\"min\":\"null\",\"max\":\"null\"},\"filter\":{\"existence\":\"False\",\"oldVal\":\"False\",\"variance\":0},\"statePreference\":\"None\",\"errOffset\":0,\"rawUnit\":\"NA\",\"dau\":\"NA\",\"paramGroup\":\"unitless\"}",
		"{\"abbr\":\"kwh\",\"unit\":\"kWh\",\"type\":\"data\",\"displayName\":\"Real Power\",\"address\":15020,\"properties\":{\"index\":1,\"driver\":0,\"mulFactor\":1,\"offset\":0,\"min\":\"null\",\"max\":\"null\"},\"filter\":{\"existence\":\"False\",\"oldVal\":\"False\",\"variance\":0},\"statePreference\":\"None\",\"errOffset\":0,\"rawUnit\":\"kWh\",\"dau\":\"kWh\",\"paramGroup\":\"energy\"}",
		"{\"abbr\":\"inputterminalvolt\",\"unit\":\"Volt\",\"type\":\"data\",\"displayName\":\"Input Terminal Volt\",\"address\":16120,\"properties\":{\"index\":0,\"driver\":0,\"mulFactor\":0.1,\"offset\":0,\"min\":\"null\",\"max\":\"null\"},\"filter\":{\"existence\":\"False\",\"oldVal\":\"False\",\"variance\":0},\"statePreference\":\"None\",\"errOffset\":0,\"rawUnit\":\"V\",\"dau\":\"V\",\"paramGroup\":\"voltage\"}",
		"{\"abbr\":\"inputterminalcurrent\",\"type\":\"data\",\"unit\":\"Volt\",\"displayName\":\"Input Terminal Current\",\"address\":16140,\"properties\":{\"index\":1,\"driver\":0,\"mulFactor\":0.01,\"offset\":0,\"min\":\"null\",\"max\":\"null\"},\"filter\":{\"existence\":\"False\",\"oldVal\":\"False\",\"variance\":0},\"statePreference\":\"None\",\"errOffset\":0,\"rawUnit\":\"A\",\"dau\":\"A\",\"paramGroup\":\"current\"}",
		"{\"abbr\":\"inputpower\",\"type\":\"data\",\"unit\":\"kW\",\"displayName\":\"Input Power\",\"address\":16100,\"properties\":{\"index\":1,\"driver\":0,\"mulFactor\":0.01,\"offset\":0,\"min\":\"null\",\"max\":\"null\"},\"filter\":{\"existence\":\"False\",\"oldVal\":\"False\",\"variance\":0},\"statePreference\":\"None\",\"errOffset\":0,\"rawUnit\":\"kW\",\"dau\":\"kW\",\"paramGroup\":\"power\"}",
		"{\"abbr\":\"outputfrequency\",\"unit\":\"Hz\",\"type\":\"data\",\"displayName\":\"Output Frequency\",\"address\":16130,\"properties\":{\"index\":0,\"driver\":0,\"mulFactor\":0.1,\"offset\":0,\"min\":\"null\",\"max\":\"null\"},\"filter\":{\"existence\":\"False\",\"oldVal\":\"False\",\"variance\":0},\"statePreference\":\"None\",\"errOffset\":0,\"rawUnit\":\"Hz\",\"dau\":\"Hz\",\"paramGroup\":\"frequency\"}",
		"{\"abbr\":\"head\",\"unit\":\"m\",\"type\":\"data\",\"displayName\":\"Head\",\"address\":16520,\"properties\":{\"index\":1,\"driver\":1,\"mulFactor\":0.0003048,\"offset\":0,\"min\":\"null\",\"max\":\"null\"},\"filter\":{\"existence\":\"False\",\"oldVal\":\"False\",\"variance\":0},\"statePreference\":\"None\",\"errOffset\":0,\"rawUnit\":\"m\",\"dau\":\"m\",\"paramGroup\":\"length\"}",
		"{\"abbr\":\"waterflow\",\"type\":\"data\",\"unit\":\"GPM\",\"displayName\":\"WaterFlow\",\"address\":18500,\"properties\":{\"index\":1,\"driver\":1,\"mulFactor\":0.001,\"offset\":0,\"min\":\"null\",\"max\":\"null\"},\"filter\":{\"existence\":\"False\",\"oldVal\":\"False\",\"variance\":0},\"statePreference\":\"None\",\"errOffset\":0,\"rawUnit\":\"gallon / min\",\"dau\":\"gallon / min\",\"paramGroup\":\"flow\"}",
		"{\"abbr\":\"start\",\"type\":\"command\",\"unit\":\"null\",\"displayName\":\"Start\",\"address\":50000,\"properties\":{\"index\":1,\"driver\":1,\"mulFactor\":1148,\"offset\":0,\"min\":\"null\",\"max\":\"null\"},\"statePreference\":\"None\",\"errOffset\":0,\"rawUnit\":\"NA\",\"operation\":\"int\",\"dau\":\"NA\",\"paramGroup\":\"unitless\"}",
		"{\"abbr\":\"stop\",\"type\":\"command\",\"unit\":\"null\",\"displayName\":\"Stop\",\"address\":50000,\"properties\":{\"index\":1,\"driver\":1,\"mulFactor\":0,\"offset\":1084,\"min\":\"null\",\"max\":\"null\"},\"statePreference\":\"None\",\"errOffset\":0,\"rawUnit\":\"NA\",\"operation\":\"int\",\"dau\":\"NA\",\"paramGroup\":\"unitless\"}",
		"{\"abbr\":\"setfrequency\",\"type\":\"command\",\"unit\":\"null\",\"displayName\":\"Set Frequency\",\"address\":50010,\"properties\":{\"index\":1,\"driver\":1,\"mulFactor\":327.68,\"offset\":0,\"min\":\"null\",\"max\":\"null\"},\"statePreference\":\"None\",\"errOffset\":0,\"rawUnit\":\"Hz\",\"dau\":\"Hz\",\"paramGroup\":\"frequency\"}"
	]
};
const validArray = [
	{
		"communicationCategory": "AVI",
		"communicationType": "NMB",
		"driverType": "0",
		"deviceType": "damperFeedback",
		"params": [
			{
				"abbr": "damper_feedback",
				"displayName": "Damper Feedback",
				"unit": "percent",
				"type": "data",
				"address": "null",
				"properties": {
					"index": "null",
					"driver": "null",
					"mulFactor": 1,
					"offset": 0,
					"min": 0,
					"max": 100
				},
				"filter": { "existence": "False", "oldVal": "False", "variance": 0 },
				"statePreference": "None",
				"errOffset": 0,
				"rawUnit": "percent",
				"operation": "int",
				"dau": "percent",
				"paramGroup": "unitless"
			}
		],
		"updatedAt": "2019-01-29T10:39:48.025Z",
		"createdAt": "2018-10-25T06:33:12.489Z",
		"driverName": "Damper A",
		"class": "devices",
		"parameters": [
			"{\"abbr\":\"damper_feedback\",\"displayName\":\"Damper Feedback\",\"unit\":\"percent\",\"type\":\"data\",\"address\":\"null\",\"properties\":{\"index\":\"null\",\"driver\":\"null\",\"mulFactor\":1,\"offset\":0,\"min\":0,\"max\":100},\"filter\":{\"existence\":\"False\",\"oldVal\":\"False\",\"variance\":0},\"statePreference\":\"None\",\"errOffset\":0,\"rawUnit\":\"percent\",\"operation\":\"int\",\"dau\":\"percent\",\"paramGroup\":\"unitless\"}"
		]
	},
	{
		"communicationCategory": "VFD",
		"communicationType": "NMB",
		"driverType": "-1",
		"deviceType": "vfd",
		"params": [
			{
				"abbr": "operationalstatus",
				"unit": "null",
				"type": "data",
				"displayName": "Operational Status",
				"address": 50200,
				"properties": {
					"index": 0,
					"driver": 0,
					"mulFactor": 1,
					"offset": 0,
					"min": "null",
					"max": "null"
				},
				"filter": { "existence": "False", "oldVal": "False", "variance": 0 },
				"statePreference": "None",
				"errOffset": 0,
				"rawUnit": "NA",
				"dau": "NA",
				"paramGroup": "unitless"
			},
			{
				"abbr": "kwh",
				"unit": "kWh",
				"type": "data",
				"displayName": "Real Power",
				"address": 15020,
				"properties": {
					"index": 1,
					"driver": 0,
					"mulFactor": 1,
					"offset": 0,
					"min": "null",
					"max": "null"
				},
				"filter": { "existence": "False", "oldVal": "False", "variance": 0 },
				"statePreference": "None",
				"errOffset": 0,
				"rawUnit": "kWh",
				"dau": "kWh",
				"paramGroup": "energy"
			},
			{
				"abbr": "inputterminalvolt",
				"unit": "Volt",
				"type": "data",
				"displayName": "Input Terminal Volt",
				"address": 16120,
				"properties": {
					"index": 0,
					"driver": 0,
					"mulFactor": 0.1,
					"offset": 0,
					"min": "null",
					"max": "null"
				},
				"filter": { "existence": "False", "oldVal": "False", "variance": 0 },
				"statePreference": "None",
				"errOffset": 0,
				"rawUnit": "V",
				"dau": "V",
				"paramGroup": "voltage"
			},
			{
				"abbr": "inputterminalcurrent",
				"type": "data",
				"unit": "Volt",
				"displayName": "Input Terminal Current",
				"address": 16140,
				"properties": {
					"index": 1,
					"driver": 0,
					"mulFactor": 0.01,
					"offset": 0,
					"min": "null",
					"max": "null"
				},
				"filter": { "existence": "False", "oldVal": "False", "variance": 0 },
				"statePreference": "None",
				"errOffset": 0,
				"rawUnit": "A",
				"dau": "A",
				"paramGroup": "current"
			},
			{
				"abbr": "inputpower",
				"type": "data",
				"unit": "kW",
				"displayName": "Input Power",
				"address": 16100,
				"properties": {
					"index": 1,
					"driver": 0,
					"mulFactor": 0.01,
					"offset": 0,
					"min": "null",
					"max": "null"
				},
				"filter": { "existence": "False", "oldVal": "False", "variance": 0 },
				"statePreference": "None",
				"errOffset": 0,
				"rawUnit": "kW",
				"dau": "kW",
				"paramGroup": "power"
			},
			{
				"abbr": "outputfrequency",
				"unit": "Hz",
				"type": "data",
				"displayName": "Output Frequency",
				"address": 16130,
				"properties": {
					"index": 0,
					"driver": 0,
					"mulFactor": 0.1,
					"offset": 0,
					"min": "null",
					"max": "null"
				},
				"filter": { "existence": "False", "oldVal": "False", "variance": 0 },
				"statePreference": "None",
				"errOffset": 0,
				"rawUnit": "Hz",
				"dau": "Hz",
				"paramGroup": "frequency"
			},
			{
				"abbr": "head",
				"unit": "m",
				"type": "data",
				"displayName": "Head",
				"address": 16520,
				"properties": {
					"index": 1,
					"driver": 1,
					"mulFactor": 0.0003048,
					"offset": 0,
					"min": "null",
					"max": "null"
				},
				"filter": { "existence": "False", "oldVal": "False", "variance": 0 },
				"statePreference": "None",
				"errOffset": 0,
				"rawUnit": "m",
				"dau": "m",
				"paramGroup": "length"
			},
			{
				"abbr": "waterflow",
				"type": "data",
				"unit": "GPM",
				"displayName": "WaterFlow",
				"address": 18500,
				"properties": {
					"index": 1,
					"driver": 1,
					"mulFactor": 0.001,
					"offset": 0,
					"min": "null",
					"max": "null"
				},
				"filter": { "existence": "False", "oldVal": "False", "variance": 0 },
				"statePreference": "None",
				"errOffset": 0,
				"rawUnit": "gallon / min",
				"dau": "gallon / min",
				"paramGroup": "flow"
			},
			{
				"abbr": "start",
				"type": "command",
				"unit": "null",
				"displayName": "Start",
				"address": 50000,
				"properties": {
					"index": 1,
					"driver": 1,
					"mulFactor": 1148,
					"offset": 0,
					"min": "null",
					"max": "null"
				},
				"statePreference": "None",
				"errOffset": 0,
				"rawUnit": "NA",
				"operation": "int",
				"dau": "NA",
				"paramGroup": "unitless"
			},
			{
				"abbr": "stop",
				"type": "command",
				"unit": "null",
				"displayName": "Stop",
				"address": 50000,
				"properties": {
					"index": 1,
					"driver": 1,
					"mulFactor": 0,
					"offset": 1084,
					"min": "null",
					"max": "null"
				},
				"statePreference": "None",
				"errOffset": 0,
				"rawUnit": "NA",
				"operation": "int",
				"dau": "NA",
				"paramGroup": "unitless"
			},
			{
				"abbr": "setfrequency",
				"type": "command",
				"unit": "null",
				"displayName": "Set Frequency",
				"address": 50010,
				"properties": {
					"index": 1,
					"driver": 1,
					"mulFactor": 327.68,
					"offset": 0,
					"min": "null",
					"max": "null"
				},
				"statePreference": "None",
				"errOffset": 0,
				"rawUnit": "Hz",
				"dau": "Hz",
				"paramGroup": "frequency"
			}
		],
		"updatedAt": "2019-03-14T10:00:38.173Z",
		"createdAt": "2018-10-25T06:33:12.492Z",
		"driverName": "VFD - KIMS",
		"class": "devices",
		"parameters": [
			"{\"abbr\":\"operationalstatus\",\"unit\":\"null\",\"type\":\"data\",\"displayName\":\"Operational Status\",\"address\":50200,\"properties\":{\"index\":0,\"driver\":0,\"mulFactor\":1,\"offset\":0,\"min\":\"null\",\"max\":\"null\"},\"filter\":{\"existence\":\"False\",\"oldVal\":\"False\",\"variance\":0},\"statePreference\":\"None\",\"errOffset\":0,\"rawUnit\":\"NA\",\"dau\":\"NA\",\"paramGroup\":\"unitless\"}",
			"{\"abbr\":\"kwh\",\"unit\":\"kWh\",\"type\":\"data\",\"displayName\":\"Real Power\",\"address\":15020,\"properties\":{\"index\":1,\"driver\":0,\"mulFactor\":1,\"offset\":0,\"min\":\"null\",\"max\":\"null\"},\"filter\":{\"existence\":\"False\",\"oldVal\":\"False\",\"variance\":0},\"statePreference\":\"None\",\"errOffset\":0,\"rawUnit\":\"kWh\",\"dau\":\"kWh\",\"paramGroup\":\"energy\"}",
			"{\"abbr\":\"inputterminalvolt\",\"unit\":\"Volt\",\"type\":\"data\",\"displayName\":\"Input Terminal Volt\",\"address\":16120,\"properties\":{\"index\":0,\"driver\":0,\"mulFactor\":0.1,\"offset\":0,\"min\":\"null\",\"max\":\"null\"},\"filter\":{\"existence\":\"False\",\"oldVal\":\"False\",\"variance\":0},\"statePreference\":\"None\",\"errOffset\":0,\"rawUnit\":\"V\",\"dau\":\"V\",\"paramGroup\":\"voltage\"}",
			"{\"abbr\":\"inputterminalcurrent\",\"type\":\"data\",\"unit\":\"Volt\",\"displayName\":\"Input Terminal Current\",\"address\":16140,\"properties\":{\"index\":1,\"driver\":0,\"mulFactor\":0.01,\"offset\":0,\"min\":\"null\",\"max\":\"null\"},\"filter\":{\"existence\":\"False\",\"oldVal\":\"False\",\"variance\":0},\"statePreference\":\"None\",\"errOffset\":0,\"rawUnit\":\"A\",\"dau\":\"A\",\"paramGroup\":\"current\"}",
			"{\"abbr\":\"inputpower\",\"type\":\"data\",\"unit\":\"kW\",\"displayName\":\"Input Power\",\"address\":16100,\"properties\":{\"index\":1,\"driver\":0,\"mulFactor\":0.01,\"offset\":0,\"min\":\"null\",\"max\":\"null\"},\"filter\":{\"existence\":\"False\",\"oldVal\":\"False\",\"variance\":0},\"statePreference\":\"None\",\"errOffset\":0,\"rawUnit\":\"kW\",\"dau\":\"kW\",\"paramGroup\":\"power\"}",
			"{\"abbr\":\"outputfrequency\",\"unit\":\"Hz\",\"type\":\"data\",\"displayName\":\"Output Frequency\",\"address\":16130,\"properties\":{\"index\":0,\"driver\":0,\"mulFactor\":0.1,\"offset\":0,\"min\":\"null\",\"max\":\"null\"},\"filter\":{\"existence\":\"False\",\"oldVal\":\"False\",\"variance\":0},\"statePreference\":\"None\",\"errOffset\":0,\"rawUnit\":\"Hz\",\"dau\":\"Hz\",\"paramGroup\":\"frequency\"}",
			"{\"abbr\":\"head\",\"unit\":\"m\",\"type\":\"data\",\"displayName\":\"Head\",\"address\":16520,\"properties\":{\"index\":1,\"driver\":1,\"mulFactor\":0.0003048,\"offset\":0,\"min\":\"null\",\"max\":\"null\"},\"filter\":{\"existence\":\"False\",\"oldVal\":\"False\",\"variance\":0},\"statePreference\":\"None\",\"errOffset\":0,\"rawUnit\":\"m\",\"dau\":\"m\",\"paramGroup\":\"length\"}",
			"{\"abbr\":\"waterflow\",\"type\":\"data\",\"unit\":\"GPM\",\"displayName\":\"WaterFlow\",\"address\":18500,\"properties\":{\"index\":1,\"driver\":1,\"mulFactor\":0.001,\"offset\":0,\"min\":\"null\",\"max\":\"null\"},\"filter\":{\"existence\":\"False\",\"oldVal\":\"False\",\"variance\":0},\"statePreference\":\"None\",\"errOffset\":0,\"rawUnit\":\"gallon / min\",\"dau\":\"gallon / min\",\"paramGroup\":\"flow\"}",
			"{\"abbr\":\"start\",\"type\":\"command\",\"unit\":\"null\",\"displayName\":\"Start\",\"address\":50000,\"properties\":{\"index\":1,\"driver\":1,\"mulFactor\":1148,\"offset\":0,\"min\":\"null\",\"max\":\"null\"},\"statePreference\":\"None\",\"errOffset\":0,\"rawUnit\":\"NA\",\"operation\":\"int\",\"dau\":\"NA\",\"paramGroup\":\"unitless\"}",
			"{\"abbr\":\"stop\",\"type\":\"command\",\"unit\":\"null\",\"displayName\":\"Stop\",\"address\":50000,\"properties\":{\"index\":1,\"driver\":1,\"mulFactor\":0,\"offset\":1084,\"min\":\"null\",\"max\":\"null\"},\"statePreference\":\"None\",\"errOffset\":0,\"rawUnit\":\"NA\",\"operation\":\"int\",\"dau\":\"NA\",\"paramGroup\":\"unitless\"}",
			"{\"abbr\":\"setfrequency\",\"type\":\"command\",\"unit\":\"null\",\"displayName\":\"Set Frequency\",\"address\":50010,\"properties\":{\"index\":1,\"driver\":1,\"mulFactor\":327.68,\"offset\":0,\"min\":\"null\",\"max\":\"null\"},\"statePreference\":\"None\",\"errOffset\":0,\"rawUnit\":\"Hz\",\"dau\":\"Hz\",\"paramGroup\":\"frequency\"}"
		]
	}
];
const invalid = undefined;
const service_getParams = {
	"communicationCategory": "ACI",
	"communicationType": "NMB",
	"driverType": "-1",
	"deviceType": "pressureSensor",
	"params": [
		{
			"abbr": "pressure",
			"displayName": "Pressure",
			"unit": "Bar",
			"type": "data",
			"address": "null",
			"properties": {
				"offset": "null",
				"mulFactor": "null",
				"index": "null",
				"min": "null",
				"max": "null"
			},
			"filter": { "existence": "False", "oldVal": "False", "variance": 0 },
			"statePreference": "None",
			"errOffset": 0,
			"rawUnit": "bar",
			"dau": "kPa",
			"paramGroup": "pressure"
		}
	],
	"updatedAt": "2019-01-29T10:39:14.106Z",
	"createdAt": "2018-11-13T10:59:27.771Z",
	"driverName": "Pressure Sensor - KIMS",
	"class": "devices",
	"parameters": [
		{
			"abbr": "pressure",
			"displayName": "Pressure",
			"unit": "Bar",
			"type": "data",
			"address": "null",
			"properties": {
				"offset": "null",
				"mulFactor": "null",
				"index": "null",
				"min": "null",
				"max": "null"
			},
			"filter": { "existence": "False", "oldVal": "False", "variance": 0 },
			"statePreference": "None",
			"errOffset": 0,
			"rawUnit": "bar",
			"dau": "kPa",
			"paramGroup": "pressure"
		}
	]
};
module.exports = {
	valid,
	invalid,
	validArray,
	service_getParams
};
