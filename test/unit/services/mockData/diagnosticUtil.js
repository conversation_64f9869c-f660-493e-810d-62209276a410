/**
 * @file This file is to be used to export all mockdata that will be required for diagnosticUtil.test.js
 * <AUTHOR>
 */

module.exports = {
	"diagnosticBenchmarkingGraph": {
		"checkInput": {
			"correctInput": {
				"startTime": "2019-09-04 16:46:00",
				"endTime": "2019-09-04 18:46:00",
				"siteId": "mgch",
				"deviceObject": [
					{
						"id": "2201",
					},
					{
						"id": "2202",
					},
				],
			},
			"incorrectInput1": {
				"deviceObject": [
					{
						"id": "2201",
					},
					{
						"id": "2202",
					},
				],
			},
			"incorrectInput2": {
				"startTime": "2019-09-04 13:46:00",
				"endTime": "2019-09-04 19:46:00",
				"siteId": "mgch",
			},
		},
		"benchmarkingData": [
			{
				"_index": "calculated_parameters_2019-09-04",
				"_type": "parameters",
				"_id": "JzQa_GwBxW1JGYMIrlNu",
				"_score": null,
				"_source": {
					"alpha": 78,
					"beta": 98.33,
					"nc": 72.7,
					"S": "NULL",
					"Sjr": "NULL",
					"Sjt": "NULL",
					"Sth": "NULL",
					"ti": "NULL",
					"o": 99.54,
					"a": "NULL",
					"di": "NULL",
					"mm": "NULL",
					"deviceId": "2202",
					"start_time": "2019-09-04 17:00:00",
					"end_time": "2019-09-04 17:15:00",
				},
				"sort": [1567616400000],
			},
			{
				"_index": "calculated_parameters_2019-09-04",
				"_type": "parameters",
				"_id": "JjQa_GwBxW1JGYMId1NM",
				"_score": null,
				"_source": {
					"alpha": 78,
					"beta": "NULL",
					"nc": 72.7,
					"S": "NULL",
					"Sjr": "NULL",
					"Sjt": "NULL",
					"Sth": "NULL",
					"ti": "NULL",
					"o": "NULL",
					"a": "NULL",
					"di": "NULL",
					"mm": "NULL",
					"deviceId": "2201",
					"start_time": "2019-09-04 17:00:00",
					"end_time": "2019-09-04 17:15:00",
				},
				"sort": [1567616400000],
			},
			{
				"_index": "calculated_parameters_2019-09-04",
				"_type": "parameters",
				"_id": "43Q6_GwBFe69lCfe3Buj",
				"_score": null,
				"_source": {
					"alpha": "NULL",
					"beta": 98.33,
					"nc": 72.63,
					"S": "NULL",
					"Sjr": "NULL",
					"Sjt": "NULL",
					"Sth": "NULL",
					"ti": "NULL",
					"o": 99.54,
					"a": "NULL",
					"di": "NULL",
					"mm": "NULL",
					"deviceId": "2202",
					"start_time": "2019-09-04 17:30:00",
					"end_time": "2019-09-04 17:45:00",
				},
				"sort": [1567618200000],
			},
			{
				"_index": "calculated_parameters_2019-09-04",
				"_type": "parameters",
				"_id": "WjQ6_GwBxW1JGYMIvlM2",
				"_score": null,
				"_source": {
					"alpha": "NULL",
					"beta": "NULL",
					"nc": 72.63,
					"S": "NULL",
					"Sjr": "NULL",
					"Sjt": "NULL",
					"Sth": "NULL",
					"ti": "NULL",
					"o": "NULL",
					"a": "NULL",
					"di": "NULL",
					"mm": "NULL",
					"deviceId": "2201",
					"start_time": "2019-09-04 17:30:00",
					"end_time": "2019-09-04 17:45:00",
				},
				"sort": [1567618200000],
			},
			{
				"_index": "calculated_parameters_2019-09-04",
				"_type": "parameters",
				"_id": "F3Ra_GwBFe69lCfeMhwD",
				"_score": null,
				"_source": {
					"alpha": "NULL",
					"beta": "NULL",
					"nc": 72.27,
					"S": "NULL",
					"Sjr": "NULL",
					"Sjt": "NULL",
					"Sth": "NULL",
					"ti": "NULL",
					"o": "NULL",
					"a": "NULL",
					"di": "NULL",
					"mm": "NULL",
					"deviceId": "2201",
					"start_time": "2019-09-04 18:00:00",
					"end_time": "2019-09-04 18:15:00",
				},
				"sort": [1567620000000],
			},
			{
				"_index": "calculated_parameters_2019-09-04",
				"_type": "parameters",
				"_id": "QDNa_GwBCT8YZuQHaTiG",
				"_score": null,
				"_source": {
					"alpha": "NULL",
					"beta": 98.33,
					"nc": 72.27,
					"S": "NULL",
					"Sjr": "NULL",
					"Sjt": "NULL",
					"Sth": "NULL",
					"ti": "NULL",
					"o": 99.54,
					"a": "NULL",
					"di": "NULL",
					"mm": "NULL",
					"deviceId": "2202",
					"start_time": "2019-09-04 18:00:00",
					"end_time": "2019-09-04 18:15:00",
				},
				"sort": [1567620000000],
			},
			{
				"_index": "calculated_parameters_2019-09-04",
				"_type": "parameters",
				"_id": "djN2_GwBCT8YZuQHaDgy",
				"_score": null,
				"_source": {
					"alpha": "NULL",
					"beta": 98.33,
					"nc": 72.8,
					"S": "NULL",
					"Sjr": "NULL",
					"Sjt": "NULL",
					"Sth": "NULL",
					"ti": "NULL",
					"o": 99.55,
					"a": "NULL",
					"di": "NULL",
					"mm": "NULL",
					"deviceId": "2202",
					"start_time": "2019-09-04 18:30:00",
					"end_time": "2019-09-04 18:45:00",
				},
				"sort": [1567621800000],
			},
			{
				"_index": "calculated_parameters_2019-09-04",
				"_type": "parameters",
				"_id": "tzR2_GwBxW1JGYMISlN_",
				"_score": null,
				"_source": {
					"alpha": "NULL",
					"beta": "NULL",
					"nc": 72.8,
					"S": "NULL",
					"Sjr": "NULL",
					"Sjt": "NULL",
					"Sth": "NULL",
					"ti": "NULL",
					"o": "NULL",
					"a": "NULL",
					"di": "NULL",
					"mm": "NULL",
					"deviceId": "2201",
					"start_time": "2019-09-04 18:30:00",
					"end_time": "2019-09-04 18:45:00",
				},
				"sort": [1567621800000],
			},
		],

		"benchmarkingParamList": ["alpha", "beta", "n", "l", "alphajr", "alphajt", "alphath", "ti", "m", "dp", "mp", "cp", "quality"],

		"aggregatedData": {
			"dataSet": {
				"2019-12-12 16:15:00": [
					{
						"alpha": 80,
						"alphajr": null,
						"alphajt": null,
						"alphath": null,
						"beta": null,
						"quality": null,
						"n": 74.5,
						"l": 74.72,
						"ti": null,
						"m": null,
						"mp": null,
						"cp": null,
						"dp": null,
						"deviceId": "2201",
						"start_time": "2019-12-12 16:15:00",
						"end_time": "2019-12-12 16:30:00"
					},
					{
						"alpha": 76,
						"alphajr": null,
						"alphajt": null,
						"alphath": null,
						"beta": 100,
						"quality": 100,
						"n": 74.5,
						"l": 74.72,
						"ti": null,
						"m": null,
						"mp": null,
						"cp": null,
						"dp": null,
						"deviceId": "2202",
						"start_time": "2019-12-12 16:15:00",
						"end_time": "2019-12-12 16:30:00"
					}
				],
				"2019-12-12 16:45:00": [
					{
						"alpha": null,
						"alphajr": null,
						"alphajt": null,
						"alphath": null,
						"beta": null,
						"quality": null,
						"n": 74.56,
						"l": 74.83,
						"ti": null,
						"m": null,
						"mp": null,
						"cp": null,
						"dp": null,
						"deviceId": "2201",
						"start_time": "2019-12-12 16:45:00",
						"end_time": "2019-12-12 17:00:00"
					},
					{
						"alpha": null,
						"alphajr": null,
						"alphajt": null,
						"alphath": null,
						"beta": 100,
						"quality": 100,
						"n": 74.56,
						"l": 74.83,
						"ti": null,
						"m": null,
						"mp": null,
						"cp": null,
						"dp": null,
						"deviceId": "2202",
						"start_time": "2019-12-12 16:45:00",
						"end_time": "2019-12-12 17:00:00"
					}
				],
				"2019-12-12 17:30:00": [
					{
						"alpha": null,
						"alphajr": null,
						"alphajt": null,
						"alphath": null,
						"beta": 100,
						"quality": 100,
						"n": 74.88,
						"l": 75.24,
						"ti": null,
						"m": null,
						"mp": null,
						"cp": null,
						"dp": null,
						"deviceId": "2202",
						"start_time": "2019-12-12 17:30:00",
						"end_time": "2019-12-12 17:45:00"
					},
					{
						"alpha": null,
						"alphajr": null,
						"alphajt": null,
						"alphath": null,
						"beta": null,
						"quality": null,
						"n": 74.88,
						"l": 75.24,
						"ti": null,
						"m": null,
						"mp": null,
						"cp": null,
						"dp": null,
						"deviceId": "2201",
						"start_time": "2019-12-12 17:30:00",
						"end_time": "2019-12-12 17:45:00"
					}
				],
				"2019-12-12 16:30:00": [],
				"2019-12-12 17:00:00": [],
				"2019-12-12 17:15:00": []
			},
			"averageDataSet": {
				"alpha": [
					[1576147500000, 78],
					[1576148400000, null],
					[1576149300000, null],
					[1576150200000, null],
					[1576151100000, null],
					[1576152000000, null]
				],
				"beta": [
					[1576147500000, 100],
					[1576148400000, null],
					[1576149300000, 100],
					[1576150200000, null],
					[1576151100000, null],
					[1576152000000, 100]
				],
				"n": [
					[1576147500000, 74.5],
					[1576148400000, null],
					[1576149300000, 74.56],
					[1576150200000, null],
					[1576151100000, null],
					[1576152000000, 74.88]
				],
				"l": [
					[1576147500000, 74.72],
					[1576148400000, null],
					[1576149300000, 74.83],
					[1576150200000, null],
					[1576151100000, null],
					[1576152000000, 75.24]
				],
				"alphajr": [
					[1576147500000, null],
					[1576148400000, null],
					[1576149300000, null],
					[1576150200000, null],
					[1576151100000, null],
					[1576152000000, null]
				],
				"alphajt": [
					[1576147500000, null],
					[1576148400000, null],
					[1576149300000, null],
					[1576150200000, null],
					[1576151100000, null],
					[1576152000000, null]
				],
				"alphath": [
					[1576147500000, null],
					[1576148400000, null],
					[1576149300000, null],
					[1576150200000, null],
					[1576151100000, null],
					[1576152000000, null]
				],
				"ti": [
					[1576147500000, null],
					[1576148400000, null],
					[1576149300000, null],
					[1576150200000, null],
					[1576151100000, null],
					[1576152000000, null]
				],
				"m": [
					[1576147500000, null],
					[1576148400000, null],
					[1576149300000, null],
					[1576150200000, null],
					[1576151100000, null],
					[1576152000000, null]
				],
				"dp": [
					[1576147500000, null],
					[1576148400000, null],
					[1576149300000, null],
					[1576150200000, null],
					[1576151100000, null],
					[1576152000000, null]
				],
				"mp": [
					[1576147500000, null],
					[1576148400000, null],
					[1576149300000, null],
					[1576150200000, null],
					[1576151100000, null],
					[1576152000000, null]
				],
				"cp": [
					[1576147500000, null],
					[1576148400000, null],
					[1576149300000, null],
					[1576150200000, null],
					[1576151100000, null],
					[1576152000000, null]
				],
				"quality": [
					[1576147500000, 100],
					[1576148400000, null],
					[1576149300000, 100],
					[1576150200000, null],
					[1576151100000, null],
					[1576152000000, 100]
				]
			}
		},
		"strictnessData": {
			"o": null,
			"beta": 84,
			"alpha": 80,
			"a": null,
			"Sth": null,
			"Sjt": null,
			"S": 95,
			"ti": null,
			"di": 60,
			"Sjr": null,
			"mm": null,
			"nc": null,
		},
	},
	"getDQIGraph": {
		"checkInput": {
			"correctInput": {
				"deviceList": ["2583", "2206", "2207", "2584"],
				"startTime": 1571809840000,
				"endTime": 1571817040000,
				"siteId": "mgch",
				"groupBy": "Hours",
			},
		},
		"elasticResponse": [
			{
				"key_as_string": "2019-10-23 11:00:00",
				"key": 1571828400000,
				"doc_count": 117,
			},
			{
				"key_as_string": "2019-10-23 12:00:00",
				"key": 1571832000000,
				"doc_count": 180,
			},
			{
				"key_as_string": "2019-10-23 13:00:00",
				"key": 1571835600000,
				"doc_count": 63,
			},
		],
	},
	"deleteAlertRecipes": {
		"checkInput": {
			"recipeList": [
				{
					"siteId": "ssh",
					"recipeId": "1bb970e5-879a-482b-bc1a-e858ff84d3ee",
				},
				{
					"siteId": "ssh",
					"recipeId": "540ee87c-057d-4d22-8084-a704ca43e9d6",
				},
			],
		},
	},
	"deleteMaintenanceInfo": {
		"checkInput": {
			"id": "maintenanceModeElasticSearchDocumentId",
		},
	},
	"updateMaintenanceInfo": {
		"checkInput": {
			"id": "maintenanceModeElasticSearchDocumentId",
		},
	},
};
