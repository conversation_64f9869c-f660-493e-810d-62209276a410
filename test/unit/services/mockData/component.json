[{"deviceId": "ssh_26", "siteId": "ssh", "name": "Nursery AHU", "data": [{"key": "cwit", "expression": "||1167@tmp||", "deviceId": "1167", "displayName": "Chilled Water Inlet Temperature", "unit": "C", "min": "", "max": ""}, {"key": "cwot", "expression": "||1166@tmp||", "deviceId": "1166", "displayName": "Chilled Water Outlet Temperature", "unit": "C", "min": "", "max": ""}, {"key": "sa", "expression": "||1165@tmp||", "deviceId": "1165", "displayName": "Supply Air Temperature", "unit": "C", "min": "", "max": ""}, {"key": "ra", "expression": "||2694@tmp||", "deviceId": "2694", "displayName": "Return Air Temperature", "unit": "C", "min": "", "max": ""}, {"key": "kva", "expression": "||1157@kva||", "deviceId": "1157", "displayName": "Average Apparent Power", "unit": "KvA", "min": "", "max": ""}, {"key": "kvah", "expression": "||1157@kvah||", "deviceId": "1157", "displayName": "Active Energy", "unit": "KvAh", "min": "", "max": ""}, {"key": "kw", "expression": "||1157@kw||", "deviceId": "1157", "displayName": "Average Active Power", "unit": "KW", "min": "", "max": ""}, {"key": "actuator", "expression": "||1169@actuator_feedback||", "deviceId": "1169", "displayName": "Two Way Valve Position", "unit": "%", "min": "", "max": ""}, {"key": "status", "expression": "||1157@kva||<||0.3||", "deviceId": "1157,", "displayName": "Status", "unit": "NA", "min": "", "max": ""}, {"key": "outputfrequency", "expression": "||3122@outputfrequency||", "deviceId": "3122", "displayName": "Output Frequency", "unit": "Hz", "min": "", "max": ""}], "controls": [{"key": "stop", "expression": "||1157@kva||<||0.3||", "deviceId": "3122", "min": "0", "max": "0", "timeout": "3", "displayName": "Stop"}, {"key": "start", "expression": "||1157@kva||>||0.3||", "deviceId": "3122", "min": "1", "max": "1", "timeout": "3", "displayName": "Start"}, {"key": "setfrequency", "expression": "||3122@outputfrequency||==||COMMAND||", "deviceId": "3122", "min": "25", "max": "55", "timeout": "3", "displayName": "Output Frequency"}, {"key": "changesetpoint", "expression": "||1169@actuator_feedback||>=||(||COMMAND||-||5||)||", "deviceId": "1168", "min": "0", "max": "100", "timeout": "3", "displayName": "Two Way Value Position"}], "deviceType": "ahu", "regionId": "jzvm", "driverType": "0", "controllerId": "2722", "createdAt": "2018-12-12T08:52:33.168Z", "updatedAt": "2018-12-21T06:43:27.016Z"}, {"deviceId": "ssh_66", "siteId": "ssh", "name": "PHE AHU - 1", "data": [{"key": "kva", "expression": "||3135@kva||", "deviceId": "3135", "displayName": "Average Apparent Power", "unit": "KvA", "min": "", "max": ""}, {"key": "kvah", "expression": "||3134@kvah||", "deviceId": "3134", "displayName": "Active Energy", "unit": "KvAh", "min": "", "max": ""}, {"key": "kw", "expression": "||3134@kw||", "deviceId": "3134", "displayName": "Average Active Power", "unit": "KW", "min": "", "max": ""}, {"key": "outputfrequency", "expression": "||3133@outputfrequency||", "deviceId": "3133", "displayName": "Output Frequency", "unit": "Hz", "min": "", "max": ""}, {"key": "status", "expression": "||3134@kva||<||0.3||", "deviceId": "3134,", "displayName": "Status", "unit": "On/Off", "min": "", "max": ""}], "controls": [{"key": "stop", "expression": "||3134@kva||<||0.3||", "deviceId": "3133", "min": "0", "max": "0", "timeout": "3", "displayName": "Stop"}, {"key": "start", "expression": "||3134@kva||>||0.3||", "deviceId": "3133", "min": "1", "max": "1", "timeout": "3", "displayName": "Start"}, {"key": "setfrequency", "expression": "||3133@outputfrequency||==||COMMAND||", "deviceId": "3133", "min": "25", "max": "55", "timeout": "3", "displayName": "Output Frequency"}], "deviceType": "ahu", "regionId": "kcrl", "driverType": "0", "controllerId": "2714", "createdAt": "2019-01-29T04:35:41.383Z", "updatedAt": "2019-01-29T04:35:41.383Z"}, {"deviceId": "ssh_41", "siteId": "ssh", "name": "OT  Chiller", "data": [{"key": "kva", "expression": "||2035@kva||", "deviceId": "2035", "displayName": "Average Apparent Power", "unit": "KvA", "min": "", "max": ""}, {"key": "status", "expression": "||2035@kva||<||0.3||", "deviceId": "2035,", "displayName": "Status", "unit": "On/Off", "min": "", "max": ""}], "controls": [], "deviceType": "chiller", "regionId": "kcrl", "driverType": "0", "controllerId": "2872", "createdAt": "2018-12-13T09:19:56.445Z", "updatedAt": "2018-12-21T06:45:42.160Z"}, {"deviceId": "ssh_17", "siteId": "ssh", "name": "MC Private 1 AHU", "data": [{"key": "kva", "expression": "||1044@kva||", "deviceId": "1044", "displayName": "Average Apparent Power", "unit": "KvA", "min": "", "max": ""}, {"key": "kvah", "expression": "||1044@kvah||", "deviceId": "1044", "displayName": "Active Energy", "unit": "KvAh", "min": "", "max": ""}, {"key": "kw", "expression": "||1044@kw||", "deviceId": "1044", "displayName": "Average Active Power", "unit": "KW", "min": "", "max": ""}, {"key": "status", "expression": "||1044@kva||<||0.3||", "deviceId": "1044,", "displayName": "Status", "unit": "On/Off", "min": "", "max": ""}], "controls": [], "deviceType": "ahu", "regionId": "jawa", "driverType": "0", "controllerId": "2932", "createdAt": "2018-12-12T08:56:47.286Z", "updatedAt": "2018-12-21T06:51:44.755Z"}, {"deviceId": "ssh_14", "siteId": "ssh", "name": "PHE AHU - 1", "data": [{"key": "kva", "expression": "||3135@kva||", "deviceId": "3135", "displayName": "Average Apparent Power", "unit": "KvA", "min": "", "max": ""}, {"key": "kvah", "expression": "||3134@kvah||", "deviceId": "3134", "displayName": "Active Energy", "unit": "KvAh", "min": "", "max": ""}, {"key": "kw", "expression": "||3134@kw||", "deviceId": "3134", "displayName": "Average Active Power", "unit": "KW", "min": "", "max": ""}, {"key": "outputfrequency", "expression": "||3133@outputfrequency||", "deviceId": "3133", "displayName": "Output Frequency", "unit": "Hz", "min": "", "max": ""}, {"key": "status", "expression": "||3134@kva||<||0.3||", "deviceId": "3134,", "displayName": "Status", "unit": "On/Off", "min": "", "max": ""}], "controls": [{"key": "stop", "expression": "||3134@kva||<||0.3||", "deviceId": "3133", "min": "0", "max": "0", "timeout": "3", "displayName": "Stop"}, {"key": "start", "expression": "||3134@kva||>||0.3||", "deviceId": "3133", "min": "1", "max": "1", "timeout": "3", "displayName": "Start"}, {"key": "setfrequency", "expression": "||3133@outputfrequency||==||COMMAND||", "deviceId": "3133", "min": "25", "max": "55", "timeout": "3", "displayName": "Output Frequency"}], "deviceType": "ahu", "regionId": "kcrl", "driverType": "0", "controllerId": "2714", "createdAt": "2019-03-19T09:39:18.684Z", "updatedAt": "2019-03-19T09:39:18.684Z"}]