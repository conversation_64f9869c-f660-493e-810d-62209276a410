let allData = [{
	"deviceId": "2126",
	"siteId": "mgch",
	"networkId": "smartjoules-network-0",
	"regionId": "sdmp",
	"name": "OT-1 Area Temperature",
	"deviceType": "joulesense",
	"areaId": "ywsn",
	"hardwareVer": "v1",
	"softwareVer": "latest",
	"operationMode": "network",
	"vendorId": "smartjoules",
	"currentVersion": "1.0.0",
	"expectedVersion": "1.0.2",
	"createdAt": "2018-11-27T11:45:15.184Z",
	"updatedAt": "2018-11-27T11:45:15.184Z",
	"id": "2126",
	"childNodes": {
		"geo": -1,
		"component": -1,
		"network": -1
	},
	"toShow": false,
	"layer": {
		"geo": 2,
		"component": 3,
		"network": 1
	},
	"remoteAccess": false,
	"remoteAccessPort": {
		"command1": "Port number not stored in DB.",
		"command2": "Port number not stored in DB."
	}
},
{
	"deviceId": "2127",
	"siteId": "mgch",
	"networkId": "smartjoules-network-0",
	"regionId": "gatp",
	"name": "OT-2 Area Temperature",
	"deviceType": "joulesense",
	"areaId": "ywsn",
	"hardwareVer": "v1",
	"softwareVer": "latest",
	"operationMode": "network",
	"vendorId": "smartjoules",
	"createdAt": "2018-11-27T11:32:23.264Z",
	"updatedAt": "2018-11-27T11:32:23.264Z",
	"id": "2127",
	"childNodes": {
		"geo": -1,
		"component": -1,
		"network": -1
	},
	"toShow": false,
	"layer": {
		"geo": 2,
		"component": 3,
		"network": 1
	},
	"remoteAccess": false,
	"remoteAccessPort": {
		"command1": "Port number not stored in DB.",
		"command2": "Port number not stored in DB."
	}
},
{
	"deviceId": "2128",
	"siteId": "mgch",
	"networkId": "smartjoules-network-0",
	"regionId": "hcem",
	"name": "OT-3 Area Temperature",
	"deviceType": "joulesense",
	"areaId": "ywsn",
	"hardwareVer": "v1",
	"softwareVer": "latest",
	"operationMode": "network",
	"vendorId": "smartjoules",
	"createdAt": "2018-11-27T11:48:21.404Z",
	"updatedAt": "2018-11-27T11:48:21.404Z",
	"id": "2128",
	"childNodes": {
		"geo": -1,
		"component": -1,
		"network": -1
	},
	"toShow": false,
	"layer": {
		"geo": 2,
		"component": 3,
		"network": 1
	},
	"remoteAccess": false,
	"remoteAccessPort": {
		"command1": "Port number not stored in DB.",
		"command2": "Port number not stored in DB."
	}
},
{
	"deviceId": "2129",
	"siteId": "mgch",
	"networkId": "smartjoules-network-0",
	"regionId": "ymgu",
	"name": "8 Bed ICU Area Temperature",
	"deviceType": "joulesense",
	"areaId": "ywsn",
	"hardwareVer": "v1",
	"softwareVer": "latest",
	"operationMode": "network",
	"vendorId": "smartjoules",
	"createdAt": "2018-11-27T11:46:47.639Z",
	"updatedAt": "2018-11-27T11:46:47.639Z",
	"id": "2129",
	"childNodes": {
		"geo": -1,
		"component": -1,
		"network": -1
	},
	"toShow": false,
	"layer": {
		"geo": 2,
		"component": 3,
		"network": 1
	},
	"remoteAccess": false,
	"remoteAccessPort": {
		"command1": "Port number not stored in DB.",
		"command2": "Port number not stored in DB."
	}
},
{
	"deviceId": "2130",
	"siteId": "mgch",
	"networkId": "smartjoules-network-0",
	"regionId": "ynxu",
	"name": "4 Bed ICU Area Temperature",
	"deviceType": "joulesense",
	"areaId": "ywsn",
	"hardwareVer": "v1",
	"softwareVer": "latest",
	"operationMode": "network",
	"vendorId": "smartjoules",
	"createdAt": "2018-11-27T11:42:01.169Z",
	"updatedAt": "2018-11-27T11:42:01.169Z",
	"id": "2130",
	"childNodes": {
		"geo": -1,
		"component": -1,
		"network": -1
	},
	"toShow": false,
	"layer": {
		"geo": 2,
		"component": 3,
		"network": 1
	},
	"remoteAccess": false,
	"remoteAccessPort": {
		"command1": "Port number not stored in DB.",
		"command2": "Port number not stored in DB."
	}
},
{
	"deviceId": "2131",
	"siteId": "mgch",
	"networkId": "smartjoules-network-0",
	"regionId": "obue",
	"name": "OT Recovery Area Temperature",
	"deviceType": "joulesense",
	"areaId": "ywsn",
	"hardwareVer": "v1",
	"softwareVer": "latest",
	"operationMode": "network",
	"vendorId": "smartjoules",
	"createdAt": "2018-11-27T11:47:31.281Z",
	"updatedAt": "2018-11-27T11:47:31.281Z",
	"id": "2131",
	"childNodes": {
		"geo": -1,
		"component": -1,
		"network": -1
	},
	"toShow": false,
	"layer": {
		"geo": 2,
		"component": 3,
		"network": 1
	},
	"remoteAccess": false,
	"remoteAccessPort": {
		"command1": "Port number not stored in DB.",
		"command2": "Port number not stored in DB."
	}
},
{
	"deviceId": "2132",
	"siteId": "mgch",
	"networkId": "smartjoules-network-0",
	"regionId": "mesa",
	"name": "CSSD Area Temperature",
	"deviceType": "joulesense",
	"areaId": "ywsn",
	"hardwareVer": "v1",
	"softwareVer": "latest",
	"operationMode": "network",
	"vendorId": "smartjoules",
	"createdAt": "2018-11-27T11:34:33.874Z",
	"updatedAt": "2018-11-27T11:34:33.874Z",
	"id": "2132",
	"childNodes": {
		"geo": -1,
		"component": -1,
		"network": -1
	},
	"toShow": false,
	"layer": {
		"geo": 2,
		"component": 3,
		"network": 1
	},
	"remoteAccess": false,
	"remoteAccessPort": {
		"command1": "Port number not stored in DB.",
		"command2": "Port number not stored in DB."
	}
},
{
	"deviceId": "2133",
	"siteId": "mgch",
	"networkId": "smartjoules-network-0",
	"regionId": "fulq",
	"name": "Conference Room Area Temperature",
	"deviceType": "joulesense",
	"areaId": "ywsn",
	"hardwareVer": "v1",
	"softwareVer": "latest",
	"operationMode": "network",
	"vendorId": "smartjoules",
	"createdAt": "2018-11-27T11:41:08.572Z",
	"updatedAt": "2018-11-27T11:41:08.572Z",
	"id": "2133",
	"childNodes": {
		"geo": -1,
		"component": -1,
		"network": -1
	},
	"toShow": false,
	"layer": {
		"geo": 2,
		"component": 3,
		"network": 1
	},
	"remoteAccess": false,
	"remoteAccessPort": {
		"command1": "Port number not stored in DB.",
		"command2": "Port number not stored in DB."
	}
},
{
	"deviceId": "2134",
	"siteId": "mgch",
	"networkId": "smartjoules-network-0",
	"regionId": "vvoc",
	"name": "COO Room Area Temperature",
	"deviceType": "joulesense",
	"areaId": "ywsn",
	"hardwareVer": "v1",
	"softwareVer": "latest",
	"operationMode": "network",
	"vendorId": "smartjoules",
	"createdAt": "2018-11-27T11:43:29.523Z",
	"updatedAt": "2018-11-27T11:43:29.523Z",
	"id": "2134",
	"childNodes": {
		"geo": -1,
		"component": -1,
		"network": -1
	},
	"toShow": false,
	"layer": {
		"geo": 2,
		"component": 3,
		"network": 1
	},
	"remoteAccess": false,
	"remoteAccessPort": {
		"command1": "Port number not stored in DB.",
		"command2": "Port number not stored in DB."
	}
},
{
	"deviceId": "2135",
	"siteId": "mgch",
	"networkId": "smartjoules-network-0",
	"regionId": "ikxk",
	"name": "Pharmacy Area Temperature",
	"deviceType": "joulesense",
	"areaId": "ywsn",
	"hardwareVer": "v1",
	"softwareVer": "latest",
	"operationMode": "network",
	"vendorId": "smartjoules",
	"createdAt": "2018-11-27T11:44:23.268Z",
	"updatedAt": "2018-11-27T11:44:23.268Z",
	"id": "2135",
	"childNodes": {
		"geo": -1,
		"component": -1,
		"network": -1
	},
	"toShow": false,
	"layer": {
		"geo": 2,
		"component": 3,
		"network": 1
	},
	"remoteAccess": false,
	"remoteAccessPort": {
		"command1": "Port number not stored in DB.",
		"command2": "Port number not stored in DB."
	}
},
{
	"deviceId": "2136",
	"siteId": "mgch",
	"networkId": "smartjoules-network-0",
	"regionId": "ohpv",
	"name": "Microbiology Area Temperature",
	"deviceType": "joulesense",
	"areaId": "ywsn",
	"hardwareVer": "v1",
	"softwareVer": "latest",
	"operationMode": "network",
	"vendorId": "smartjoules",
	"createdAt": "2018-11-27T11:29:40.423Z",
	"updatedAt": "2018-11-27T11:29:40.423Z",
	"id": "2136",
	"childNodes": {
		"geo": -1,
		"component": -1,
		"network": -1
	},
	"toShow": false,
	"layer": {
		"geo": 2,
		"component": 3,
		"network": 1
	},
	"remoteAccess": false,
	"remoteAccessPort": {
		"command1": "Port number not stored in DB.",
		"command2": "Port number not stored in DB."
	}
},
{
	"deviceId": "2137",
	"siteId": "mgch",
	"networkId": "smartjoules-network-0",
	"regionId": "lacj",
	"name": "Day Care Area Temperature",
	"deviceType": "joulesense",
	"areaId": "ywsn",
	"hardwareVer": "v1",
	"softwareVer": "latest",
	"operationMode": "network",
	"vendorId": "smartjoules",
	"createdAt": "2018-11-27T11:35:17.034Z",
	"updatedAt": "2018-11-27T11:35:17.034Z",
	"id": "2137",
	"childNodes": {
		"geo": -1,
		"component": -1,
		"network": -1
	},
	"toShow": false,
	"layer": {
		"geo": 2,
		"component": 3,
		"network": 1
	},
	"remoteAccess": false,
	"remoteAccessPort": {
		"command1": "Port number not stored in DB.",
		"command2": "Port number not stored in DB."
	}
},
{
	"deviceId": "2138",
	"siteId": "mgch",
	"networkId": "smartjoules-network-0",
	"regionId": "nygp",
	"name": "CRM Office Area Temperature",
	"deviceType": "joulesense",
	"areaId": "ywsn",
	"hardwareVer": "v1",
	"softwareVer": "latest",
	"operationMode": "network",
	"vendorId": "smartjoules",
	"createdAt": "2018-11-27T11:45:59.238Z",
	"updatedAt": "2018-11-27T11:45:59.238Z",
	"id": "2138",
	"childNodes": {
		"geo": -1,
		"component": -1,
		"network": -1
	},
	"toShow": false,
	"layer": {
		"geo": 2,
		"component": 3,
		"network": 1
	},
	"remoteAccess": false,
	"remoteAccessPort": {
		"command1": "Port number not stored in DB.",
		"command2": "Port number not stored in DB."
	}
},
{
	"deviceId": "2139",
	"siteId": "mgch",
	"networkId": "smartjoules-network-0",
	"regionId": "oxpp",
	"name": " Lobby Area Temperature",
	"deviceType": "joulesense",
	"areaId": "ywsn",
	"hardwareVer": "v1",
	"softwareVer": "latest",
	"operationMode": "network",
	"vendorId": "smartjoules",
	"createdAt": "2018-11-27T11:30:47.383Z",
	"updatedAt": "2018-11-27T11:30:47.383Z",
	"id": "2139",
	"childNodes": {
		"geo": -1,
		"component": -1,
		"network": -1
	},
	"toShow": false,
	"layer": {
		"geo": 2,
		"component": 3,
		"network": 1
	},
	"remoteAccess": false,
	"remoteAccessPort": {
		"command1": "Port number not stored in DB.",
		"command2": "Port number not stored in DB."
	}
},
{
	"deviceId": "2140",
	"siteId": "mgch",
	"networkId": "smartjoules-network-0",
	"regionId": "gkpo",
	"name": "X-Ray Area Temperature",
	"deviceType": "joulesense",
	"areaId": "ywsn",
	"hardwareVer": "v1",
	"softwareVer": "latest",
	"operationMode": "network",
	"vendorId": "smartjoules",
	"createdAt": "2018-11-27T11:28:25.804Z",
	"updatedAt": "2018-11-27T11:28:25.804Z",
	"id": "2140",
	"childNodes": {
		"geo": -1,
		"component": -1,
		"network": -1
	},
	"toShow": false,
	"layer": {
		"geo": 2,
		"component": 3,
		"network": 1
	},
	"remoteAccess": false,
	"remoteAccessPort": {
		"command1": "Port number not stored in DB.",
		"command2": "Port number not stored in DB."
	}
},
{
	"deviceId": "2141",
	"siteId": "mgch",
	"networkId": "smartjoules-network-0",
	"regionId": "zznz",
	"name": "Admin Main Reception Area Temperature",
	"deviceType": "joulesense",
	"areaId": "ywsn",
	"hardwareVer": "v1",
	"softwareVer": "latest",
	"operationMode": "network",
	"vendorId": "smartjoules",
	"createdAt": "2018-11-27T11:49:07.108Z",
	"updatedAt": "2018-11-27T11:49:07.108Z",
	"id": "2141",
	"childNodes": {
		"geo": -1,
		"component": -1,
		"network": -1
	},
	"toShow": false,
	"layer": {
		"geo": 2,
		"component": 3,
		"network": 1
	},
	"remoteAccess": false,
	"remoteAccessPort": {
		"command1": "Port number not stored in DB.",
		"command2": "Port number not stored in DB."
	}
},
{
	"deviceId": "2142",
	"siteId": "mgch",
	"networkId": "smartjoules-network-0",
	"regionId": "wyhs",
	"name": "Linac-1 Area Temperature",
	"deviceType": "joulesense",
	"areaId": "ywsn",
	"hardwareVer": "v1",
	"softwareVer": "latest",
	"operationMode": "network",
	"vendorId": "smartjoules",
	"createdAt": "2018-11-27T11:33:29.633Z",
	"updatedAt": "2018-11-27T11:33:29.633Z",
	"id": "2142",
	"childNodes": {
		"geo": -1,
		"component": -1,
		"network": -1
	},
	"toShow": false,
	"layer": {
		"geo": 2,
		"component": 3,
		"network": 1
	},
	"remoteAccess": false,
	"remoteAccessPort": {
		"command1": "Port number not stored in DB.",
		"command2": "Port number not stored in DB."
	}
},
{
	"deviceId": "2143",
	"siteId": "mgch",
	"networkId": "smartjoules-network-0",
	"regionId": "utlt",
	"name": "Linac-2 Area Temperature",
	"deviceType": "joulesense",
	"areaId": "ywsn",
	"hardwareVer": "v1",
	"softwareVer": "latest",
	"operationMode": "network",
	"vendorId": "smartjoules",
	"createdAt": "2018-11-27T11:36:30.380Z",
	"updatedAt": "2018-11-27T11:36:30.380Z",
	"id": "2143",
	"childNodes": {
		"geo": -1,
		"component": -1,
		"network": -1
	},
	"toShow": false,
	"layer": {
		"geo": 2,
		"component": 3,
		"network": 1
	},
	"remoteAccess": false,
	"remoteAccessPort": {
		"command1": "Port number not stored in DB.",
		"command2": "Port number not stored in DB."
	}
},
{
	"deviceId": "2144",
	"siteId": "mgch",
	"networkId": "smartjoules-network-0",
	"regionId": "dahr",
	"name": "Lobby Area Temperature",
	"deviceType": "joulesense",
	"areaId": "ywsn",
	"hardwareVer": "v1",
	"softwareVer": "latest",
	"operationMode": "network",
	"vendorId": "smartjoules",
	"createdAt": "2018-11-27T11:42:38.669Z",
	"updatedAt": "2018-11-27T11:42:38.669Z",
	"id": "2144",
	"childNodes": {
		"geo": -1,
		"component": -1,
		"network": -1
	},
	"toShow": false,
	"layer": {
		"geo": 2,
		"component": 3,
		"network": 1
	},
	"remoteAccess": false,
	"remoteAccessPort": {
		"command1": "Port number not stored in DB.",
		"command2": "Port number not stored in DB."
	}
}
];
module.exports = {
	"event": {
		"topic": "dummyJoule",
		"dataObj": "dummyObj"
	},
	"user": {
		"email": "<EMAIL>",
		"name": "Test User 5",
		"designation": "Tester",
		"phone": 9777777777,
		"userId": "<EMAIL>",
		"userOrganization": "Smart Joules",
		"policiesGroup": {
			"sjo": "admin"
		},
		"picture": "nourl"
	},
	"role": {
		"policylist": {
			"Remote SSH_Read": 1,
			"CICD Update_Read": 1
		},
		"roleName": "test3",
		"pref": {
			"mailConfig": {
				"JouleRecipe": {
					"recipecomfortoptimization": "60"
				}
			}
		}
	},
	"component": {
		//"deviceId": "ssh_43",
		"siteId": "ssh",
		"name": "PHE AHU - 1",
		"data": [{
			"key": "kva",
			"expression": "||3135@kva||",
			"deviceId": "3135",
			"displayName": "Average Apparent Power",
			"unit": "KvA",
			"min": "",
			"max": ""
		}, {
			"key": "kvah",
			"expression": "||3134@kvah||",
			"deviceId": "3134",
			"displayName": "Active Energy",
			"unit": "KvAh",
			"min": "",
			"max": ""
		}, {
			"key": "kw",
			"expression": "||3134@kw||",
			"deviceId": "3134",
			"displayName": "Average Active Power",
			"unit": "KW",
			"min": "",
			"max": ""
		}, {
			"key": "outputfrequency",
			"expression": "||3133@outputfrequency||",
			"deviceId": "3133",
			"displayName": "Output Frequency",
			"unit": "Hz",
			"min": "",
			"max": ""
		}, {
			"key": "status",
			"expression": "||3134@kva||<||0.3||",
			"deviceId": "3134,",
			"displayName": "Status",
			"unit": "On/Off",
			"min": "",
			"max": ""
		}],
		"controls": [{
			"key": "stop",
			"expression": "||3134@kva||<||0.3||",
			"deviceId": "3133",
			"min": "0",
			"max": "0",
			"timeout": "3",
			"displayName": "Stop"
		}, {
			"key": "start",
			"expression": "||3134@kva||>||0.3||",
			"deviceId": "3133",
			"min": "1",
			"max": "1",
			"timeout": "3",
			"displayName": "Start"
		}, {
			"key": "setfrequency",
			"expression": "||3133@outputfrequency||==||COMMAND||",
			"deviceId": "3133",
			"min": "25",
			"max": "55",
			"timeout": "3",
			"displayName": "Output Frequency"
		}],
		"deviceType": "ahu",
		"regionId": "kcrl",
		"driverType": "0",
		"controllerId": "2714",
	},

	"recipe": {
		//"formula": "P||#3||avg||#3||$1||#2||#1||0||,||20||#2",
		"formula": "$1||#1||42",
		//P(120. KVA > 42, 20)
		"params": {
			"$1": "132.feedback"
		},
		"logParam": {
			"$1": "32.KVA"
		},
		"label": "recipe for test",
		"type": "routine", // notRountine
		"recipelabel": ["Energy Optimization", "operation Diagnostic"], // ( energy , operation , comfort , dejoule , hvac ) + diagnostic and optimization , failsafe
		"maxDataNeeded": 5,
		"maxLogNeeded": 0,
		"isSchedule": false, // true means see nothing just send the value asked by me should be maintained throught and no other way to change it
		"operators": {
			"#1": ">",
			"#2": ")",
			"#3": "("
		},
		"failSafeAlert": "", // should be just run a recipe
		"siteId": "smt-del",
		"startNow": true, // if recipe need 5min data should it just run taking data from now or prev 5min, false means take prev data
		"actionable": [{
			"title": "Some Name",
			"description": "dont waste it documenting.",
			"priority": 5,
			"type": "execute",
			"parent": "Office AHU",
			"notify": ["<EMAIL>"],
			"accountable": ["<EMAIL>"],
		},
		{
			"title": "Some Other Name",
			"description": "Live life.",
			"type": "execute",
			"priority": 5,
			"parent": "Office AHU",
			"notify": ["<EMAIL>"],
			"accountable": ["<EMAIL>"],
		},
		{
			"did": "129",
			"type": "actions",
			"parent": "smt_1",
			// "priority" : 0/10
			"priority": 0,
			//  "delay" : 10 ,// in minutes
			"notify": ["<EMAIL>"],
			"accountable": ["<EMAIL>"],
			"command": "changesetpoint", // key name changed
			"value": "90"
		}
		]
	},

	"abstractRecipe": {
		"formula": "P||#3||$1||#1||42||#4||$2||#5||50,||20||#2",
		// formula: '$1||#1||42',
		"params": {
			"$1": "ahu.kVA"
		},
		"neo": "ahu", // device type
		"label": "AHU start/stop",
		"type": "routine", // notRountine
		"recipelabel": [""], //( energy , operation , comfort , dejoule , hvac ) + diagnostic and optimization , failsafe
		"maxDataNeeded": 20,
		"maxLogNeeded": 0,
		"isSchedule": true, // true means see nothing just send the value asked by me should be maintained throught and no other way to change it
		"operators": {
			"#1": ">",
			"#2": ")",
			"#3": "("
		},
		"failSafeAlert": "", // should be just run a recipe
		"siteId": "sjo",
		"startNow": true, // if recipe need 5min data should it just run taking data from now or prev 5min, false means take prev data
		"actionable": [{
			"priority": 5,
			"description": "asdf",
			"type": "execute",
			"frequency": 60,
			"parent": "Office AHU",
			"notify": ["ujjal"]
		},
		{
			"type": "execute",
			"description": "asdf",
			"priority": 5,
			"parent": "Office AHU",
			"frequency": 60,
			"notify": ["ujjal"]
		},
		{
			"did": "ahu.startstop",
			//"type": "actions",
			// "parent": "smt-del_2", create on creating child
			"notify": ["ujjal"],
			"type": "start",
			"value": "1"
		},
		]
	},

	"baseline": {
		"siteId": "dummySite",
		"startDate": "02-01-2019",
		"endDate": "01-12-2019",
		"target": 80,
		"consumptionValue": 431876
	},

	"alertreq": {

		"filterOn": {
			// "recipe" : {
			//     lebel : ["recipeenergyoptimization" , "recipeoperationdiagnostic"],
			// },
			// "maintainance" : {

			// },
			// "dj" : {

			// },
			"label": ["recipeenergyoptimization", "recipeoperationdiagnostic"],
			// label : ["recipeenergyoptimization" ],
			"ts": [*************, *************],
			"siteId": "smt-del"

		}
	},

	"alertres": {
		"actionable": "{\"title\":\"Prescheduled Start Of Admin Reception AHU at MGCH\",\"description\":\"Check the occupancy of the Admin reception and associated areas\",\"notify\":[\"<EMAIL>\",\"<EMAIL>\",\"<EMAIL>\"],\"accountable\":[\"<EMAIL>\"],\"type\":\"alert\",\"priority\":0,\"uniqId\":\"20b7f552-9a55-473e-a352-ec34af7c75fc\"}",
		"arrived": "*************",
		"createdAt": "2019-01-25T12:47:34.974Z",
		"description": "Check the occupancy of the Admin reception and associated areas",
		"groupBy": "[\"recipeenergydiagnostic\"]",
		"id": "0fb41b6f-541a-4889-b783-fda5c276b8cb",
		"status": "Executed",
		"title": "Prescheduled Start Of Admin Reception AHU at MGCH",
		"tsSiteId": "mgch_1548420454995",
		"uniqId": "20b7f552-9a55-473e-a352-ec34af7c75fc",
		"updatedAt": "2019-01-25T12:47:34.974Z"
	},
	"cmdreq": {
		"value": "67",
		"deviceId": "1168",
		"param": "1",
		"componentId": "ssh_26"
	},
	"cmdres": {
		"componentId": "ssh_26",
		"createdAt": "2018-03-30T09:57:11.519Z",
		"deviceId": "1168",
		"executed": 0,
		"reachedJouleBox": *************,
		"siteId": "ssh",
		"socketID": "RbS3yqnbTdeY_sO-AACM",
		"timestamp": "*************",
		"type": "setpoint",
		"updatedAt": "2018-03-30T09:57:12.258Z",
		"user": "system",
		"value": "67"
	},
	"setmodereq": {
		"siteId": "smt-del",
		"controllerId": "127",
		"did": {
			"227.changesetpoint": "joulerecipe"
		}
	},
	"populateDiagnostics": {
		"siteId": "mgch"
	},
	"bad_populateDiagnostics": {},
	"gitfetchTag": {
		"siteId": "ssh",
		"repository": "CICD"
	},
	"bad_gitfetchTag": {
		"repository": "CICD"
	},
	"updateFirmwareNonJouleSense": {
		"siteId": "mgch",
		"deviceType": "jouleiocontrol",
		"arr": allData,
		"dockerParams": {
			"isLatest": false
		},
		"version": "3.0.10",
		"skipGitRepo": true,
		"softReboot": false,
		"dockerPull": false,
		"hardReboot": true,
		"repo": "hostServices"
	},
	"updateFirmwareJouleSense": {
		"siteId": "mgch",
		"deviceType": "joulesense",
		"version": "1.1.21",
		"commandControllerId": 2753,
		"toSkip": false,
		"arr": allData,
	},
	"updateManyFirmwareJouleSense": {
		"siteId": "mgch",
		"deviceType": "joulesense",
		"arr": allData,
		"version": "1.1.21",
		"toSkip": false,
		"commandControllerId": 2753,
		"values": [{
			"version": "2.0.9",
			"skipGitRepo": false,
			"softReboot": false,
			"dockerPull": true,
			"repo": "cicd",
			"dockerParams": {
				"isLatest": false,
				"version": "Test"
			}
		}]
	},
	"updateManyFirmwareNonJouleSense": {
		"siteId": "mgch",
		"deviceType": "jouleiocontrol",
		"arr": allData,
		"hardReboot": true,
		"values": [{
			"version": "2.0.9",
			"skipGitRepo": false,
			"softReboot": false,
			"dockerPull": true,
			"repo": "cicd",
			"dockerParams": {
				"isLatest": false,
				"version": "Test"
			}
		}]
	},
	"bad_updateFirmwareNonJouleSense": {
		"siteId": "mgch",
		"deviceType": "jouleiocontrol",
		"dockerParams": {
			"isLatest": false
		},
		"version": "3.0.10",
		"skipGitRepo": true,
		"softReboot": false,
		"dockerPull": false,
		"hardReboot": true,
		"repo": "hostServices"
	},
	"bad_updateFirmwareJouleSense": {
		"siteId": "mgch",
		"deviceType": "joulesense",
		"version": "1.1.21",
		"commandControllerId": 2753,
		"toSkip": false,
	},
	"bad_updateManyFirmwareJouleSense": {
		"siteId": "mgch",
		"deviceType": "joulesense",
		"version": "1.1.21",
		"toSkip": false,
		"commandControllerId": 2753,
		"values": [{
			"version": "2.0.9",
			"skipGitRepo": false,
			"softReboot": false,
			"dockerPull": true,
			"repo": "cicd",
			"dockerParams": {
				"isLatest": false,
				"version": "Test"
			}
		}]
	},
	"bad_updateManyFirmwareNonJouleSense": {
		"siteId": "mgch",
		"deviceType": "jouleiocontrol",
		"hardReboot": true,
		"values": [{
			"version": "2.0.9",
			"skipGitRepo": false,
			"softReboot": false,
			"dockerPull": true,
			"repo": "cicd",
			"dockerParams": {
				"isLatest": false,
				"version": "Test"
			}
		}]
	},
	"reqInfo": {
		"arr": [
			{
				"deviceId": "2126",
				"deviceType": "joulesense"
			},
			{
				"deviceId": "2127",
				"deviceType": "joulesense"
			},
			{
				"deviceId": "2128",
				"deviceType": "joulesense"
			},
			{
				"deviceId": "2129",
				"deviceType": "joulesense"
			},
			{
				"deviceId": "2130",
				"deviceType": "joulesense"
			},
			{
				"deviceId": "2131",
				"deviceType": "joulesense"
			},
			{
				"deviceId": "2132",
				"deviceType": "joulesense"
			},
			{
				"deviceId": "2133",
				"deviceType": "joulesense"
			},
			{
				"deviceId": "2134",
				"deviceType": "joulesense"
			},
			{
				"deviceId": "2135",
				"deviceType": "joulesense"
			},
			{
				"deviceId": "2136",
				"deviceType": "joulesense"
			},
			{
				"deviceId": "2137",
				"deviceType": "joulesense"
			},
			{
				"deviceId": "2138",
				"deviceType": "joulesense"
			},
			{
				"deviceId": "2139",
				"deviceType": "joulesense"
			},
			{
				"deviceId": "2140",
				"deviceType": "joulesense"
			},
			{
				"deviceId": "2141",
				"deviceType": "joulesense"
			},
			{
				"deviceId": "2142",
				"deviceType": "joulesense"
			},
			{
				"deviceId": "2143",
				"deviceType": "joulesense"
			},
			{
				"deviceId": "2144",
				"deviceType": "joulesense"
			},
			{
				"deviceId": "2751",
				"deviceType": "jouleiocontrol"
			},
			{
				"deviceId": "2752",
				"deviceType": "jouleiocontrol"
			},
			{
				"deviceId": "2753",
				"deviceType": "jouleiocontrol"
			},
			{
				"deviceId": "2754",
				"deviceType": "jouleiocontrol"
			},
			{
				"deviceId": "2755",
				"deviceType": "jouleiocontrol"
			},
			{
				"deviceId": "2756",
				"deviceType": "jouleiocontrol"
			},
			{
				"deviceId": "2757",
				"deviceType": "jouleiocontrol"
			},
			{
				"deviceId": "2758",
				"deviceType": "jouleiocontrol"
			},
			{
				"deviceId": "2759",
				"deviceType": "jouleiocontrol"
			},
			{
				"deviceId": "2760",
				"deviceType": "jouleiocontrol"
			},
			{
				"deviceId": "2761",
				"deviceType": "jouleiocontrol"
			},
			{
				"deviceId": "2762",
				"deviceType": "jouleiocontrol"
			},
			{
				"deviceId": "2763",
				"deviceType": "jouleiocontrol"
			},
			{
				"deviceId": "2764",
				"deviceType": "jouleiocontrol"
			},
			{
				"deviceId": "2765",
				"deviceType": "jouleiocontrol"
			},
			{
				"deviceId": "2766",
				"deviceType": "jouleiocontrol"
			},
			{
				"deviceId": "2767",
				"deviceType": "jouleiocontrol"
			},
			{
				"deviceId": "2768",
				"deviceType": "jouleiocontrol"
			},
			{
				"deviceId": "2769",
				"deviceType": "jouleiocontrol"
			},
			{
				"deviceId": "2771",
				"deviceType": "jouleiocontrol"
			},
			{
				"deviceId": "2776",
				"deviceType": "joulelogger"
			},
			{
				"deviceId": "2777",
				"deviceType": "joulelogger"
			},
			{
				"deviceId": "2879",
				"deviceType": "joulebox"
			}
		],
		"siteId": "mgch"
	},
	"bad_reqInfo": {
		"arr": [{
			"deviceId": "2126",
			"deviceType": "joulesense"
		},
		{
			"deviceId": "2127",
			"deviceType": "joulesense"
		},
		{
			"deviceId": "2128",
			"deviceType": "joulesense"
		},
		{
			"deviceId": "2129",
			"deviceType": "joulesense"
		},
		{
			"deviceId": "2130",
			"deviceType": "joulesense"
		},
		{
			"deviceId": "2131",
			"deviceType": "joulesense"
		},
		{
			"deviceId": "2132",
			"deviceType": "joulesense"
		},
		{
			"deviceId": "2133",
			"deviceType": "joulesense"
		},
		{
			"deviceId": "2134",
			"deviceType": "joulesense"
		},
		{
			"deviceId": "2135",
			"deviceType": "joulesense"
		},
		{
			"deviceId": "2136",
			"deviceType": "joulesense"
		},
		{
			"deviceId": "2137",
			"deviceType": "joulesense"
		},
		{
			"deviceId": "2138",
			"deviceType": "joulesense"
		},
		{
			"deviceId": "2139",
			"deviceType": "joulesense"
		},
		{
			"deviceId": "2140",
			"deviceType": "joulesense"
		},
		{
			"deviceId": "2141",
			"deviceType": "joulesense"
		},
		{
			"deviceId": "2142",
			"deviceType": "joulesense"
		},
		{
			"deviceId": "2143",
			"deviceType": "joulesense"
		},
		{
			"deviceId": "2144",
			"deviceType": "joulesense"
		},
		{
			"deviceId": "2751",
			"deviceType": "jouleiocontrol"
		},
		{
			"deviceId": "2752",
			"deviceType": "jouleiocontrol"
		},
		{
			"deviceId": "2753",
			"deviceType": "jouleiocontrol"
		},
		{
			"deviceId": "2754",
			"deviceType": "jouleiocontrol"
		},
		{
			"deviceId": "2755",
			"deviceType": "jouleiocontrol"
		},
		{
			"deviceId": "2756",
			"deviceType": "jouleiocontrol"
		},
		{
			"deviceId": "2757",
			"deviceType": "jouleiocontrol"
		},
		{
			"deviceId": "2758",
			"deviceType": "jouleiocontrol"
		},
		{
			"deviceId": "2759",
			"deviceType": "jouleiocontrol"
		},
		{
			"deviceId": "2760",
			"deviceType": "jouleiocontrol"
		},
		{
			"deviceId": "2761",
			"deviceType": "jouleiocontrol"
		},
		{
			"deviceId": "2762",
			"deviceType": "jouleiocontrol"
		},
		{
			"deviceId": "2763",
			"deviceType": "jouleiocontrol"
		},
		{
			"deviceId": "2764",
			"deviceType": "jouleiocontrol"
		},
		{
			"deviceId": "2765",
			"deviceType": "jouleiocontrol"
		},
		{
			"deviceId": "2766",
			"deviceType": "jouleiocontrol"
		},
		{
			"deviceId": "2767",
			"deviceType": "jouleiocontrol"
		},
		{
			"deviceId": "2768",
			"deviceType": "jouleiocontrol"
		},
		{
			"deviceId": "2769",
			"deviceType": "jouleiocontrol"
		},
		{
			"deviceId": "2771",
			"deviceType": "jouleiocontrol"
		},
		{
			"deviceId": "2776",
			"deviceType": "joulelogger"
		},
		{
			"deviceId": "2777",
			"deviceType": "joulelogger"
		},
		{
			"deviceId": "2879",
			"deviceType": "joulebox"
		}
		],
	},
	"remoteAccess": {
		"deviceId": 2776,
		"option": true,
		"siteId": "mgch"
	},
	"bad_remoteAccess": {
		"option": true,
		"siteId": "mgch"
	},
	"recaliberateSensor": {
		"siteId": "mgch",
		"arr": allData
	},
	"bad_recaliberateSensor": {
		"siteId": "mgch",
	},
	"publishMsg": {
		"param": "firmware",
		"value": {
			"version": "1.1.21",
			"deviceList": [2126, 2127, 2128, 2129, 2130, 2131, 2132, 2133, 2134, 2135, 2136, 2137, 2138, 2139, 2140, 2141, 2142, 2143, 2144],
			"toSkip": false
		},
		"key": "nhv676mggj",
		"operation": "js_update"
	},
	"publishTopic": "command/2753/cicd",
	"topics": [
		"+/feedback/+/+",
		"+/response/+/diagnostic",
		"feedback/+/cicd",
		"+/feedback/+/joulerecipe",
		"+/alert/+/joulerecipe",
		"feedback/+/recipelogicconfig",
		"jouletrack/+/+/+"
	],
	"startQuery": "SELECT new_timestamp,count(new_timestamp) as dqi FROM (SELECT timestamp, siteid, substring(timestamp, 1, 13)new_timestamp FROM dejoule.datadevicestemp WHERE partition_0 = mgch AND partition_1 = mgch AND partition_2 between 2018-12-10 and 2018-12-13)newDB GROUP BY new_timestamp ORDER BY new_timestamp",
	"dataDevice": {
		"siteId": "ssh",
		"deviceId": "2416",
		"type": "vfd",
		"params": "",
		"start": "2018-12-18 13:23:06",
		"end": "2018-12-24 07:21:18",
		"startTime": "2018-09-04",
		"endTime": "2018-09-05",
		"groupBy": "days",
		"group": "days",
		"id": "gknmh_16",
		"xAxis": {
			"deviceId": 2416,
			"param": ""
		},
		"yAxis": {
			"deviceId": 2416,
			"param": ""
		},
		"JBData": {
			"timestamp": "2018-12-24 07:21:18",
			"siteId": "ssh",
		}
	},
	"JBData": {
		"timestamp": "2018-12-24 07:21:18",
		"siteId": "ssh",
		"data": {
			"KW": "0.0",
			"KVAH": "8123.21",
			"KVAR": "0.0",
			"A_VLN": "0.0",
			"A_AMP": "0.0",
			"IB": "0.0",
			"PF": "1.0",
			"A_VLL": "1.0",
			"IR": "0.0",
			"IY": "0.0",
			"KVA": "0.0"
		}
	},
	"parameterContr": {
		"param": "SA",
		"param_1": 22,
		"param_2": 24,
		"from": "2017-12-06",
		"to": "2018-12-06",
		"obj": "",
		"siteId": "frtn",
		"groupBy": "day",
		"days": "3,4",
		"week": "1,2",
		"component": "actuator",
		"use": "Actuator",
		"deviceId": "0",
		"start_ts": "1534160907000",
		"end_ts": "1534247290000",
		"feedback": {
			"type": "pidsaveJB",
			"fb": "0"
		}
	},
	"err_param": {
		"siteId": "",
		"groupBy": "",
		"component": "",
		"use": "",
		"deviceId": "",
		"start_ts": "",
		"end_ts": "",
		"feedback": {
			"type": "FB_SP",
			"fb": "0"
		}
	},
	"err_param2": {
		"siteId": "frtn",
		"groupBy": "day",
		"component": "actuator",
		"use": "sad"
	},
	"err_param3": {
		"startTime": "2018-12-18 13:23:06",
		"endTime": ""
	},
	"bad_param": {
		"siteId": "sadasd",
		"groupBy": "days"
	},
	"addVersion": 
		{ "version": "rubberDucky",
			"deviceType": "controller",
			"gitRepoCICD": "{\"version\":\"3.1.2\",\"type\":\"git\",\"name\":\"cicd\"}",
			"gitRepoApplication": "{\"version\":\"3.0.21\",\"type\":\"git\",\"name\":\"application\"}",
			"gitRepoFirmware": "{\"version\":\"3.0.19\",\"type\":\"git\",\"name\":\"firmware\"}",
			"gitRepoHostServices": "{\"version\":\"3.0.16\",\"type\":\"git\",\"name\":\"hostServices\"}",
			"gitRepoJouleBox": "{\"version\":\"3.0.8\",\"type\":\"git\",\"name\":\"joulebox\"}",
			"gitRepoRoutingService": "{\"version\":\"3.0.11\",\"type\":\"git\",\"name\":\"routingService\"}",
			"dockerApplication": "{\"version\":\"1.1.2\",\"type\":\"docker\",\"name\":\"application\"}",
			"dockerFirmware": "{\"version\":\"1.0.3\",\"type\":\"docker\",\"name\":\"firmware\"}",
			"dockerJouleBox": "{\"version\":\"1.0.3\",\"type\":\"docker\",\"name\":\"joulebox\"}",
			"repoList": [ "dockerApplication", "dockerFirmware", "dockerJouleBox", "gitRepoApplication", "gitRepoCICD", "gitRepoFirmware", "gitRepoHostServices", "gitRepoJouleBox", "gitRepoRoutingService" ] }
};
