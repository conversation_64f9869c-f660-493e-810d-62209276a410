/* eslint-disable no-unused-vars */
const assert = require("chai").assert;
const Config = require("./mockData/modeService.config");
const should = require("chai").should;
const sinon = require("sinon");

const stubDEVUpdateValue = (refObj, updateObj) => {
	return new Promise((resolve, reject) => {
		resolve(Config.rdata);
	});
};

const stubDEVUpdateError = (refObj, updateObj) => {
	return new Promise((resolve, reject) => {
		reject("this is an error");
	});
};

const stubHPRToJsonError = stringToJson => {
	throw new Error("this is an error");
};

const stubCOMPUpdateError = queryObj => {
	return new Promise((resolve, reject) => {
		reject("this is an error");
	});
};

const stubCOMPUpdateObject = (refObj, updateObj) => {
	return new Promise((resolve, reject) => {
		resolve({});
	});
};
/*
const stubDEVFindError = queryObj => {
	return new Promise((resolve, reject) => {
		reject("this is an error");
	});
};

const stubDEVFindInvalidValues = queryObj => {
	return new Promise((resolve, reject) => {
		resolve(Config.invalidFindResult);
	});
};

const stubDEVFindValidValues = queryObj => {
	return new Promise((resolve, reject) => {
		resolve(Config.validFindResult);
	});
};


const stubMSMultipleGetModeError = queryObj => {
	return new Promise((resolve, reject) => {
		reject("this is an error");
	});
};

const stubMSMultipleGetModeArray = queryObj => {
	return new Promise((resolve, reject) => {
		resolve(Config.modesArr);
	});
};
*/
const stubMSGetAllDevicesError = ids => {
	return new Promise((resolve, reject) => {
		reject("This is an error");
	});
};

const stubMSGetAllDevicesData = ids => {
	return new Promise((resolve, reject) => {
		resolve(Config.newIds);
	});
};

const stubJSONParseError = jsonString => {
	throw new Error("This is an error");
};

const stubDEVFindOneError = queryObj => {
	return new Promise((resolve, reject) => {
		reject("This is an error");
	});
};

const stubPROMAllError = waitForIt => {
	return new Promise((resolve, reject) => {
		reject("This is an error");
	});
};

const stubCOMPFindError = queryObj => {
	return new Promise((resolve, reject) => {
		reject("This is an error");
	});
};

describe("Testing ModeService", () => {

	describe("Testing modeService.pidSave", () => {

		it("action: sending undefined values, expect: null", async() => {
			try {
				let result = modeService.pidSave(Config.falseRdata);
				assert.equal(result, null);
			} catch (err){throw new Error("Test has failed");}
		});

		describe("action: stubbing helper.toJson to throw error", () => {

			before(() => {
				sinon.stub(helper, "toJson")
					.callsFake(stubHPRToJsonError);
			});

			after(() => helper.toJson.restore());

			it("expect: error", async() => {
				try {
					modeService.pidSave(Config.rdata);
					throw new Error("Test has failed");
				} catch (err){
					assert.isDefined(err);
				}
			});
		});

		describe("action: stubbing Devices.update to throw error", () => {

			before(() => {
				sinon.stub(Devices, "update")
					.callsFake(stubDEVUpdateError);
			});

			after(() => Devices.update.restore());

			it("expect: error", async() => {
				try {
					modeService.pidSave(Config.rdata);
					throw new Error("Test has failed");
				} catch (err){
					assert.isDefined(err);
				}
			});
		});

		describe("action: stubbing Component.update to throw error", () => {

			before(() => {

				//running a bypass
				sinon.stub(Devices, "update")
					.callsFake(stubDEVUpdateValue);

				sinon.stub(Component, "update")
					.callsFake(stubCOMPUpdateError);
			});

			after(() => {
				Devices.update.restore();
				Component.update.restore();
			});

			it("expect: error", async() => {
				try {
					let result = modeService.pidSave(Config.rdata);
					sails.log(result);
					throw new Error("Test has failed");
				} catch (err){
					assert.isDefined(err);
				}
			});
		});

		describe("action: stubbing Component.update to return object", () => {

			before(() => {
				//running a bypass
				sinon.stub(Devices, "update")
					.callsFake(stubDEVUpdateValue);

				sinon.stub(Component, "update")
					.callsFake(stubCOMPUpdateObject);
			});

			after(() => {
				Devices.update.restore();
				Component.update.restore();
			});

			it("expect: empty object", async() => {
				try {
					let result = modeService.pidSave(Config.rdata);
					assert.deepEqual(result, undefined);
				} catch (err){
					throw new Error("Test has failed");
				}			
			});
		});
	});

	describe("Testing modeService.multipleGetMode", () => {

		describe("action: stubbing ModeService.getAllDevices to return error", () => {

			before(() => {
				sinon.stub(modeService, "getAllDevices")
					.callsFake(stubMSGetAllDevicesError);
			});

			after(() => modeService.getAllDevices.restore());

			it("expect: blank array", done => {
				modeService.multipleGetMode(Config.dids)
					.then(data => done(new Error("Error not handled in function")))
					.catch(err => {
						assert.deepEqual(err, []);
						done();
					});
			});
		});

		describe("action: stubbing JSON.parse to throw error", () =>{
			
			before(() => {
				sinon.stub(JSON, "parse")
					.callsFake(stubJSONParseError);
			});

			after(() => JSON.parse.restore());

			it("expect: resolved promise with blank array", done => {
				modeService.multipleGetMode(Config.dids)
					.then(data => {
						done(new Error("Error not handled in the function"));
					}).catch(err => {
						assert.deepEqual(err, []);
						done();
					});
			});
		});

		describe("stubbing ModeService.getAllDevices to return valid values", () => {
			
			before(() => {
				sinon.stub(modeService, "getAllDevices")
					.callsFake(stubMSGetAllDevicesData);
			});

			after(() => modeService.getAllDevices.restore());

			it("action: sending expected values, expect: array", done => {
				modeService.multipleGetMode(Config.dids)
					.then(data => {
						assert.isObject(data);
						assert.isNotEmpty(data);
						data.should.have.all.keys(Config.dids);
						done();
					}).catch(err => done(err));
			});
		});
	});

	describe("Testing modeService.modBusId", () => {

		it("action: sending expected values, expect: array with value", async() => {
			try {
				let result = modeService.modBusId(Config.testId);
				assert.deepEqual(result, Config.modBusId);
			} catch (err){
				throw new Error("test has failed");
			}
		});
	});

	describe("Testing modeService.getAllDevices", () => {

		describe("stubbing Devices.findOne to throw error", () => {

			before(() => {
				sinon.stub(Devices, "findOne")
					.callsFake(stubDEVFindOneError);
			});

			after(() => Devices.findOne.restore());

			it("expect: empty array, rejected promise", done => {
				modeService.getAllDevices(Config.ids)
					.then(data => done(new Error("Error not handled in the function")))
					.catch(err => {
						assert.deepEqual(err, []);
						done();
					});
			});
		});

		describe("stubbing Promise.all to throw error", () => {

			before(() => {
				sinon.stub(Promise, "all")
					.callsFake(stubPROMAllError);
			});

			after(() => Promise.all.restore());

			it("expect: empty array, rejected promise", done => {
				modeService.getAllDevices(Config.ids)
					.then(data => done(new Error("Error not handled in the function")))
					.catch(err => {
						assert.deepEqual(err, []);
						done();
					});
			});
		});

		it.skip("action: sending expected values, expect: object", done => {
			modeService.getAllDevices(Config.ids)
				.then(data => {
					assert.isDefined(data);
					assert.isArray(data);
					assert.isNotEmpty(data);
					done();
				}).catch(err => done(err));
		});
	});

	describe("Testing modeService.getParentComponentInfo", () => {
		
		describe("action: stubbing Component.find to return error", () => {

			before(() => {
				// Component.find.restore(); // Restoring first as it has been stubbed previously
				sinon.stub(Component, "find")
					.callsFake(stubCOMPFindError);
			});

			after(() => Component.find.restore());

			it("expect: rejected promise", done => {
				modeService.getParentComponentInfo(Config.cId, Config.siteId)
					.then(data => done(new Error("Error not handled in function")))
					.catch(err => {
						assert.isDefined(err);
						done();
					});
			});
		});

		it.skip("action: sending expected values, expect object", done => {
			modeService.getParentComponentInfo(Config.cId, Config.siteId)
				.then(data => {
					assert.isDefined(data);
					assert.isObject(data);
					done();
				}).catch(err => done(err));
		});
	});

	describe("Testing modeService.parseToGet", () => {

		it("action: sending empty string, expect: should return undefined", async() => {
			try {
				let result = modeService.parseToGet("");
				assert.isNotDefined(result);
			} catch (err){
				return err;
			}
		});

		it("action: sending expected values, expect: should return a string", async() => {
			try {
				let result = modeService.parseToGet(Config.exprnString);
				assert.isString(result);
			} catch (err){
				return err;
			}
		});
	});
});

//||3072@operationalstatus||==||0||