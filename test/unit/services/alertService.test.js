/* eslint-disable no-unused-vars */
const moment = require("moment-timezone");
const assert = require("chai").assert;
const data = require("./services.config");
const Config = require("./services.config").alertService;
const should = require("chai").should();
let sinon = require("sinon");

const stubALRTFindAlertsError =
(siteId, timeRange, type) =>
	Promise.reject("Missing Params");


const stubALRTFindAlertsArray =
(siteId, timeRange, type) =>
	Promise.resolve([{}]);

const stubALRTFindRecentAlertsErr =
(siteId, timeRange, limit, type) =>
	Promise.reject("This is an error");

const stubALRTFindRecentAlertsArr =
(siteId, timeRange, limit, type) =>
	Promise.resolve([1]);

const stubALRTCreateNotificationQueryError =
(siteId, timeRange, type) =>
	Promise.reject("This is an error");

const stubALRTCreateNotificationQueryObject =
(siteId, timeRange, type) =>
	Promise.resolve({});

const stubAlertFindError = query =>
	Promise.reject("This is an error");

const stubAlertFindArray = query =>
	Promise.resolve([{}]);

const stubThrowError = () => {
	throw "This is an error";
};

describe("AlertService Tests", ()=>{

	it("getPref", async()=>{
		try {
			alertService.getPref(data.alertService.userId, data.alertService.siteId, data.alertService.labelList)
				.then(res=>{
					assert.property(res, "MaintenanceRelated");
					assert.property(res, "Critical");
					assert.isNotEmpty(res.MaintenanceRelated);
					assert.isNotEmpty(res.Critical);
				});
		} catch (error) {
			throw error;
		}
	});

	it("getPref_NoUser", async()=>{
		function stubFindOne(){
			return new Promise((resolve, reject)=>{
				resolve(false);
			});
		}
		sinon.stub(UserSiteMap, "findOne").callsFake(stubFindOne);
		try {
			await alertService.getPref(data.alertService.wrong_userId, data.alertService.wrong_siteId, data.alertService.wrong_labelList);
		} catch (error) {
			assert.typeOf(error, "object", "Done!");
			assert.isEmpty(error);
		}
		UserSiteMap.findOne.restore();
	});

	it("getPref_NoConfigs", async()=>{
		function stubFindOne(){
			return new Promise((resolve, reject)=>{
				resolve(true);
			});
		}
		sinon.stub(UserSiteMap, "findOne").callsFake(stubFindOne);
		try {
			await alertService.getPref(data.alertService.userId, data.alertService.siteId, data.alertService.labelList);
		} catch (error) {
			assert.typeOf(error, "object", "Done!");
			assert.isEmpty(error);
		}
		// assert.isEmpty(retVal);
		UserSiteMap.findOne.restore();
	});

	it("getPref_NotJSONConfigs", async()=>{
		function stubToJson(){
			return undefined;
		}
		sinon.stub(helper, "toJson").callsFake(stubToJson);
		try {
			await alertService.getPref(data.alertService.userId, data.alertService.siteId, data.alertService.labelList);
		} catch (error) {
			assert.typeOf(error, "object", "Done!");
			assert.isEmpty(error);
		}
		// assert.isEmpty(retVal);
		helper.toJson.restore();
	});

	it("sendToCache_No_sobj", async()=>{
		function stubToJson(){
			return undefined;
		}
		sinon.stub(helper, "toJson").callsFake(stubToJson);
		try {
			await alertService.sendToCache(data.sendToCache.userId, data.sendToCache.siteId, data.sendToCache.wrong_sobj);
		} catch (err){
			assert.isBoolean(err);
		}
		helper.toJson.restore();
	});

	it("sendToCache_No_User", async()=>{
		function stubFindOne(){
			return new Promise((resolve, reject)=>{
				resolve(false);
			});
		}
		sinon.stub(Users, "findOne").callsFake(stubFindOne);
		try {
			await alertService.sendToCache(data.sendToCache.userId, data.sendToCache.siteId, data.sendToCache.sobj);
		} catch (err){
			assert.isArray(err);
			assert.isEmpty(err);
		}
		Users.findOne.restore();
	});

	it("addWrapper ArrLenZero", async()=>{
		let returnVal = await alertService.addWrapper	(data.addWrapper.userId, data.addWrapper.siteId, data.addWrapper.wrong_arr);
		assert.equal(returnVal, false);
	});

	// it("addWrapper when d is True", async()=>{
	// 	function fakeGetPref(userId, siteId, arr){
	// 		return new Promise((resolve, reject)=>{
	// 			resolve(true);
	// 		})
	// 	}
	// 	sinon.stub(alertService, "getPref").callsFake(fakeGetPref)
	// 		try {
	// 			alertService.addWrapper(data.addWrapper.userId, data.addWrapper.siteId, data.addWrapper.arr);
	// 		} catch (error) {
	// 			console.log(error)
	// 		}
	// 	alertService.getPref.restore();
	// });

	// it.only("addWrapper when d is false", async()=>{
	// 	function fakeGetPref(userId, siteId, arr){
	// 		return new Promise((resolve, reject)=>{
	// 			resolve(false);
	// 		});
	// 	}
	// 	sinon.stub(alertService, "getPref").callsFake(fakeGetPref)
	// 		try {
	// 			var ret = alertService.addWrapper(data.addWrapper.userId, data.addWrapper.siteId, data.addWrapper.arr);
	// 			assert.isUndefined(ret)
	// 			console.log("sdsndksncl")
	// 		} catch (error) {
	// 			console.log(error)
	// 		}
	// 	alertService.getPref.restore();
	// })

	it("recipeAlert", async()=>{
		function stub_sendSms(param){
			assert.property(param, "message");
			assert.property(param, "phone");
			return true;
		}
		sinon.stub(sendsms, "sendSms").callsFake(stub_sendSms);
		await alertService.recipeAlert(data.recipeAlert.smsMail, data.recipeAlert.rinfo, data.recipeAlert.ts);
		sendsms.sendSms.restore();
	});

	describe("Testing createNotificationQuery", () => {

		it("action: calling function without passing params, expect: error", done => {
			try {
				let result = alertService
					.createNotificationQuery();
				result.should.be.an.instanceOf(Error);
				done();
			} catch (err){
				done(err);
			}
		});

		it("action: passing valid params, expect: object", done =>  {
			try {
				let result = alertService
					.createNotificationQuery(
						Config.siteId,
						Config.timeRange,
						Config.type);

				assert
					.deepEqual(result, Config.notifQuery);
				done();

			} catch (err){
				done(err);
			}
		});

		it("action: passing limit based params, expect: object", done => {
			try {

				let timeNow = moment().unix();
				let result = alertService
					.createNotificationQuery(
						Config.siteId,
						timeNow,
						Config.type,
						Config.limit);

				result.should
					.have.all.keys(Config.keyNotif);
				result.where.timestamp.should
					.have.all.keys(Config.keyTimeFilter);
				done();
			} catch (err){
				done(err);
			}
		});
	});

	describe("Testing findAllAlerts", () => {

		it("action: passing no parameters, expect: rejected promise", done => {
			alertService.findAllAlerts()
				.then(
					data =>
						done(new Error("Error not handled in the function")))
				.catch(err => {
					assert.isDefined(err);
					assert.deepEqual(err, "Missing Params");
					done();
				});
		});

		describe("Stubbing alertService.findAlerts to throw error", () => {

			before(() => {
				sinon.stub(alertService, "findAlerts")
					.callsFake(stubALRTFindAlertsError);
			});

			after(() => alertService.findAlerts.restore());

			it("expect: rejected promise", done => {
				alertService.findAllAlerts(
					Config.siteId,
					Config.timeRange,
					Config.userId,
					Config.userFlag)
					.then(
						data => done(new Error("Error not handled in the function")))
					.catch(err => {
						assert.isDefined(err);
						done();
					});
			});
		});

		describe("Stubbing alertService.findAlerts to resolve", () => {

			before(() => {
				sinon.stub(alertService, "findAlerts")
					.callsFake(stubALRTFindAlertsArray);
			});

			after(() => alertService.findAlerts.restore());

			it("expect: array", done => {
				alertService
					.findAllAlerts(
						Config.siteId,
						Config.timeRange,
						Config.userId,
						Config.userFlag)
					.then(data => {
						assert.deepEqual(data, [{}, {}, {}]);
						done();
					}).catch(err => done(err));
			});
		});
	});

	describe("Testing findAlerts", () => {

		it("action: calling without passing params, expect: rejected promise", done => {
			alertService.findAlerts()
				.then(data =>
					done(new Error("Error not handled in the function")))
				.catch(err => {
					assert.isDefined(err);
					assert.deepEqual(err, "Missing Params");
					done();
				});
		});

		describe("stubbing alertService.createNotificationQuery", () => {

			before(() => {
				sinon.stub(alertService, "createNotificationQuery")
					.callsFake(stubALRTCreateNotificationQueryError);
			});

			after(() => alertService.createNotificationQuery.restore());

			it("Expect: reject promise", done => {
				alertService
					.findAlerts(
						Config.siteId,
						Config.timeRange,
						Config.type)
					.then(data =>
						done(new Error("Error not handled in the function"))
					).catch(err => {
						assert.isDefined(err);
						assert.deepEqual(err, "This is an error");
						done();
					});
			});
		});

		describe("stubbing Alert.find to throw error", () => {

			before(() => {
				sinon.stub(Alert, "find")
					.callsFake(stubAlertFindError);

				//running a bypass
				sinon.stub(alertService, "createNotificationQuery")
					.callsFake(stubALRTCreateNotificationQueryObject);
			});

			after(() => {
				Alert.find.restore();
				alertService.createNotificationQuery.restore();
			});

			it("Expect: rejected promise", done => {
				alertService
					.findAlerts(
						Config.siteId,
						Config.timeRange,
						Config.type)
					.then(data =>
						done(new Error("Error not handled in the function"))
					).catch(err => {
						assert.isDefined(err);
						assert.deepEqual(err, "This is an error");
						done();
					});
			});
		});

		describe("stubbing Alert.find to return valid response", () => {

			before(() => {
				sinon.stub(Alert, "find")
					.callsFake(stubAlertFindArray);

				//running a bypass
				sinon.stub(alertService, "createNotificationQuery")
					.callsFake(stubALRTCreateNotificationQueryObject);
			});

			after(() => {
				Alert.find.restore();
				alertService.createNotificationQuery.restore();
			});

			it("expect resolved promise", done => {
				alertService
					.findAlerts(
						Config.siteId,
						Config.timeRange,
						Config.type)
					.then(data => {
						assert.deepEqual(data, [{}]);
						done();
					}).catch(err => done(err));
			});
		});
	});

	describe("Testing findRecentAlerts", () => {

		it("action: calling without passing params, expect: rejected promise", done => {
			alertService.findRecentAlerts()
				.then(data =>
					done(new Error("Error not handled in the function")))
				.catch(err => {
					assert.isDefined(err);
					assert.deepEqual(err, "Missing Params");
					done();
				});
		});

		describe("stubbing Alert.find to throw error", () => {

			before(() => {
				sinon.stub(Alert, "find")
					.callsFake(stubAlertFindError);

				//running a bypass
				sinon.stub(alertService, "createNotificationQuery")
					.callsFake(stubALRTCreateNotificationQueryObject);
			});

			after(() => {
				Alert.find.restore();
				alertService.createNotificationQuery.restore();
			});

			it("Expect: rejected promise", done => {
				alertService
					.findRecentAlerts(
						Config.siteId,
						Config.timeRange,
						Config.type,
						Config.limit)
					.then(data =>
						done(new Error("Error not handled in the function"))
					).catch(err => {
						assert.isDefined(err);
						assert.deepEqual(err, "This is an error");
						done();
					});
			});
		});

		describe("stubbing Alert.find to return valid response", () => {

			before(() => {
				sinon.stub(Alert, "find")
					.callsFake(stubAlertFindArray);

				//running a bypass
				sinon.stub(alertService, "createNotificationQuery")
					.callsFake(stubALRTCreateNotificationQueryObject);
			});

			after(() => {
				Alert.find.restore();
				alertService.createNotificationQuery.restore();
			});

			it("expect resolved promise", done => {
				alertService
					.findRecentAlerts(
						Config.siteId,
						Config.timeRange,
						Config.type,
						Config.limit)
					.then(data => {
						assert.deepEqual(data, [{}]);
						done();
					}).catch(err => done(err));
			});
		});
	});

	describe("Testing fetchLatestAlerts", () => {

		it("action: calling function without params, expect: error", done => {
			alertService.fetchLatestAlerts()
				.then(data =>
					done(new Error("Error not handled in the function")))
				.catch(err => {
					assert.isDefined(err);
					assert.deepEqual(err, "Missing Params");
					done();
				});
		});

		describe("action: stubbing alertService.findRecentAlerts to throw error", () => {

			before(() => {
				sinon.stub(alertService, "findRecentAlerts")
					.callsFake(stubALRTFindRecentAlertsErr);
			});

			after(() => alertService.findRecentAlerts.restore());

			it("Expect rejected promise", done => {
				alertService
					.fetchLatestAlerts(
						Config.siteId,
						Config.timeRange,
						Config.limit)
					.then(data =>
						done(new Error("Error not handled in the function")))
					.catch(err => {
						assert.isDefined(err);
						assert.deepEqual(err, "This is an error");
						done();
					});
			});
		});

		describe("Action: stubbing alertService.findRecentAlerts to return array", () => {
			before(() => {
				sinon.stub(alertService, "findRecentAlerts")
					.callsFake(stubALRTFindRecentAlertsArr);
			});

			after(() => alertService.findRecentAlerts.restore());

			it("Expect rejected promise", done => {
				alertService
					.fetchLatestAlerts(
						Config.siteId,
						Config.timeRange,
						Config.limit)
					.then(data => {
						assert.isArray(data);
						assert.deepEqual(data, [1, 1, 1]);
						done();
					}).catch(err => done(err));
			});
		});
	});

	describe("Testing createAlerts", () => {

		it("action: passing undefined params, expect: error", done => {
			try {
				alertService.createAlerts();
				done(new Error("Error not handled in the function"));
			} catch (err){
				assert.isDefined(err);
				done();
			}
		});

		it("action: passing empty notifyList, expect: unitary list", done => {
			try {
				let data = alertService
					.createAlerts(
						Config.alertObject,
						Config.siteId,
						Config.alertType);

				assert.isDefined(data);
				assert.isArray(data);
				done();
			} catch (err){
				done(err);
			}
		});

		it("action: passing expected parameters, expect: expected result", done => {
			try {
				let data = alertService
					.createAlerts(
						Config.alertObject,
						Config.siteId,
						Config.alertType,
						Config.notifyList);

				assert.isArray(data);
				assert.deepEqual(data.length, 2);
				assert.deepEqual(data, Config.validAlert);
				done();
			} catch (err){
				done(err);
			}
		});
	});

	describe("Testing saveAlerts", () => {

		describe("Stubbing alertService.createAlerts to throw error", () => {

			before(() => {
				sinon.stub(alertService, "createAlerts")
					.callsFake(stubThrowError);
			});

			after(() => {
				alertService.createAlerts.restore();
			});

			it("Expect: error", done => {
				alertService.saveAlert()
					.then(data => {
						done(new Error("Error not handled in the function"));
					}).catch(err => {
						assert.isDefined(err);
						assert.deepEqual(err, "This is an error");
						done();
					});
			});
		});
	});
});
