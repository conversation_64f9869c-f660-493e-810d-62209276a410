const sinon = require("sinon");
const assert = require("chai").assert;
const expect = require("chai").expect;
const dummyData = require("./mockData/deviceTypes");
describe("DeviceType Service Tests", ()=>{
	let devtypeFindOne_ = null;
	before(()=>{
		devtypeFindOne_ = sinon.stub(DeviceType, "findOne");
		devtypeFindOne_.returns(dummyData.valid);
	});
	describe("getParameters Tests", ()=>{
		it("For a valid deviceType and driverType", async ()=>{
			let devTypes = await deviceTypeService.getParameters("devtype", "0");
			assert.hasAnyKeys(devTypes, ["deviceType", "driverType", "parameters"]);
			devTypes.parameters.map(params=>{
				assert.hasAnyKeys(params, ["dau", "abbr", "paramGroup"]);
			});
		});
		it("When devicetype and drivertype does not exist", async ()=>{
			devtypeFindOne_.returns(dummyData.invalid);
			let devType = await deviceTypeService.getParameters("a", "b");
			expect(devType).to.be.null;
		});
		it("If database error happens", async ()=>{
			devtypeFindOne_.throws("Database error");
			try {
				await deviceTypeService.getParameters("a", "b");throw Error("This test was supposed to fail");	
			} catch (e){
				expect(e).to.be.instanceOf(Error);
			}		
		});

	});
	after(()=>{
		devtypeFindOne_.restore();
	});
});