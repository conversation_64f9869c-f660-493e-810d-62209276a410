const service = require("../../../api/services/helper");
const assert = require("chai").assert;

/* Stubs */

describe("Helper Test", () => {
	describe("toArray tests", () => {
		it("test undefined", () => {
			const value = service.toArray();
			assert.isUndefined(value, "got defined");
		});
		it("array sent, expect an array", () => {
			const value = service.toArray([1, 2, 3]);
			assert.isArray(value, "Not an Array");
			assert.deepEqual(value, [1, 2, 3], "Not Same Array");
		});
		it("JSON String sent, expect undefined", () => {
			const actual = service.toArray("{\"a\" : 1}");
			const expected = undefined;
			assert.deepEqual(actual, expected, "Invalid Output");
		});
	});

	describe("getRandomInt tests", () => {
		it("expect a random integer <= 100", () => {
			const value = service.getRandomInt(100);
			assert.isAtMost(value, 100, "greater than 100");
		});
	});

	describe("toJson tests", () => {
		it("sent undefined, expect undefined", () => {
			const actual = service.toJson();
			const expected = undefined;
			assert.equal(actual, expected, "not same");
		});
		it("send object, expect same object", () => {
			const actual = service.toJson({
				"a": 1,
				"b": 2
			});
			const expected = {
				"a": 1,
				"b": 2
			};
			assert.deepEqual(actual, expected, "not equal");
		});
		it("send json string, expect JSON object", () => {
			const actual = service.toJson("{\"a\":1, \"b\": 2}");
			const expected = {
				"a": 1,
				"b": 2
			};
			assert.deepEqual(actual, expected, "not same");
		});
	});

	describe("getts tests", () => {
		// Unused
	});

	describe("convertStringArraytoJSONArray Tests", () => {
		it("sent undefined, expect undefined", () => {
			const actual = service.convertStringArrayToJSONArray();
			const expected = undefined;
			assert.strictEqual(actual, expected, "not undefined");
		});
		it("sent non array, expect undefined", () => {
			const actual = service.convertStringArrayToJSONArray({
				"a": 1
			});
			const expected = null;
			assert.strictEqual(actual, expected, "not undefined");
		});
		it("sent array of json strings, expect json array", () => {
			const testArray = ["{\"a\" : 1}", "{\"b\" : 2}"];
			const actual = service.convertStringArrayToJSONArray(testArray);
			const expected = [{
				"a": 1
			}, {
				"b": 2
			}];
			assert.deepEqual(actual, expected, "[message]");
		});
	});

	describe("convertJSONArrayToStringArray tests", () => {
		it("sent undefined, expect undefined", () => {
			const actual = service.convertJSONArrayToStringArray();
			const expected = undefined;
			assert.strictEqual(actual, expected, "not undefined");
		});
		it("sent non array, expect undefined", () => {
			const actual = service.convertJSONArrayToStringArray({
				"a": 1
			});
			const expected = undefined;
			assert.strictEqual(actual, expected, "not undefined");
		});
		it("sent array of json objects, expect json string", () => {
			const testArray = [{
				"a": 1
			}, {
				"b": 2
			}];
			const actual = service.convertJSONArrayToStringArray(testArray);
			const expected = ["{\"a\":1}", "{\"b\":2}"];
			assert.deepEqual(actual, expected, "not same");
		});
	});

	describe("isEmail tests", () => {
		it("sent email expect true", () => {
			const actual = service.isEmail("<EMAIL>");
			const expected = true;
			assert.deepEqual(actual, expected, "invalid response");
		});
		it("sent invalid email expect false", () => {
			const actual = service.isEmail("test.smartjoules.in");
			const expected = false;
			assert.deepEqual(actual, expected, "is email");
		});
		it("sent undefined expect false", () => {
			const actual = service.isEmail();
			const expected = false;
			assert.deepEqual(actual, expected, "is email");
		});
	});

	describe("setDifference tests", () => {
		it("given two sets, expect correct diff", () => {
			const setA = new Set([1, 2, 3]);
			const setB = new Set([4, 2, 3]);
			const actual = service.setDifference(setA, setB);
			const expected = new Set([1]);
			assert.deepEqual(actual, expected, "incorrect set diff");
		});
	});

	describe("deepClone tests", () => {
		it("sent object, expect same object", () => {
			const actual = service.deepClone({
				"a": 1,
				"b": 2
			});
			const expected = {
				"a": 1,
				"b": 2
			};
			assert.deepEqual(actual, expected, "[message]");
		});
	});

	describe("removeDuplicate tests", () => {
		it("expect array without any duplicates", () => {
			const actual = service.removeDuplicate([1, 2, 3, 1, 4, 5, 3, 2, 1, 3]);
			const expected = [1, 2, 3, 4, 5];
			assert.deepEqual(actual, expected, "invalid");
		});
	});

	describe("parseFormulaToIds tests", () => {
		it("sent formulaText, expect devIds", () => {
			const formulaText = "||d1@2||d3@4||d4@5||";
			const actual = service.parseFormulaToIds(formulaText);
			const expected = ["d1", "d3", "d4"];
			assert.deepEqual(actual, expected, "[message]");
		});
	});

	describe("returnFilteredNumber tests", () => {
		it("send undefined, expect null", () => {
			const actual = service.returnFilteredNumber();
			const expected = undefined;
			assert.equal(actual, expected, "[message]");
		});
		it("send stringified integer, expect integer", () => {
			const actual = service.returnFilteredNumber("123");
			const expected = 123;
			assert.equal(actual, expected, "[message]");
		});
		it("send string, expect null", () => {
			const actual = service.returnFilteredNumber("string");
			const expected = null;
			assert.equal(actual, expected, "");
		});
	});

	describe("testing isSubset", () => {
		it("action: passing expected params", async() => {
			const baseArr = ["a", "b", "c"];
			const compareArr = ["a", "b"];
			let result = service
				.isSubset(
					baseArr, 
					compareArr);
			assert.equal(result, true);
		});
	});
});
