/*eslint no-unused-vars: "warn"*/
const assert = require("chai").assert;
const Config = require("./services.config").CacheService;
// const should = require("chai").should();
const sinon = require("sinon");

/****************
STUBS FOR TESTING
****************/


const stubGetError = (key, callback) => {
	let error = true;
	let data = "";
	callback(error, data);
};

const stubGetValues = (key, callback) => {
	let error = false;
	let data = Config.falsePositive;
	callback(error, data);
};

const stubSetError = (key, value) => {
	throw new Error();
};

const stubSetTrue = (key, value) => {
	return true;
};

const stubExpireatError = (key, value) => {
	throw new Error();
};

const stubExpireatTrue = (key, value) => {
	return true;
};

const stubRpushError = (key, value, callback) => {
	let err = new Error();
	let data = "";
	callback(err, data);
};

const stubRpushValue = (key, value, callback) => {
	let err = false;
	let data = Config.falsePositive;
	callback(err, data);
};

const stubLpopError = (key, callback) => {
	let err = new Error();
	let data = "";
	callback(err, data);
};

const stubLpopValue = (key, callback) => {
	let err = false;
	let data = Config.falsePositive;
	callback(err, data);
};

const stubHmsetError = (queryArray, callback) => {
	let err = new Error();
	let data = "";
	callback(err, data);
};

const stubHmsetValue = (queryArray, callback) => {
	let err = false;
	let data = Config.falsePositive;
	callback(err, data);
};

const stubCastError = (socket, addr, data) => {
	throw new Error();
};

const stubCastValue = (socket, addr, data) => {
	return true;
};

const stubStringifyValue = obj => {
	return true;
};

const stubHgetallError = (key, callback) => {
	let err = true;
	let data = "";
	callback(err, data);
};

const stubHgetallEmptyValue = (key, callback) => {
	let err = false;
	let data = "";
	callback(err, data);
};

const stubHgetallObject = (key, callback) => {
	let err = false;
	let data = {
		"status": Config.valStatus
	};
	callback(err, data);
};

const stubGetHKeyFalse = (key, callback) => {
	return new Promise(resolve => resolve(false));
};

const stubGetHKeyError = (key, callback) => {
	return new Promise(reject => reject());
};

const stubHdelError = (hashRef, key, callback) => {
	let err = true;
	let data = 1;
	callback(err, data);
};

const stubHdelInvalidData = (hashRef, key, callback) => {
	let err = false;
	let data = 0;
	callback(err, data);
};

const stubToJsonError = data => {
	throw new Error();
};

const stubToArrayError = data => {
	throw new Error();
};

describe("Testing CacheService", () => {

	/*********************
	dashboardConsumption()
	*********************/
	

	/********************************
			   getCurrentLoad()
				FAILING TEST
	TO BE REVISITED AFTER REFACTORING
	********************************/


	describe("Testing cacheService.getKey", () => {

		describe("action: stubbing get() to return error", () => {

			before(() => {
				sinon.stub(cacheService.client, "get")
					.callsFake(stubGetError);
			});

			after(() => cacheService.client.get.restore());

			it("expect: null", done => {
				cacheService.getKey(Config.key)
					.then(data => {
						assert.equal(data, null);
						done();
					})
					.catch(err => done(err));
			});
		});

		describe("action stubbing get() to return values", () => {

			before(() => {
				sinon.stub(cacheService.client, "get")
					.callsFake(stubGetValues);
			});

			after(() => cacheService.client.get.restore());

			it("expect: string", done => {
				cacheService.getKey(Config.key)
					.then(data => {
						assert.isString(data);
						assert.equal(data, Config.falsePositive);
						done();
					})
					.catch(err => done(err));
			});
		});
	});

	describe("Testing cacheService.setKey", () => {

		describe("action: stubbing set() to throw error", () => {

			before(() => {
				sinon.stub(cacheService.client, "set")
					.callsFake(stubSetError);
			});

			after(() => cacheService.client.set.restore());

			it("expect: error", done => {
				cacheService.setKey(Config.key, Config.val)
					.then(data => done("Error not handled in the function"))
					.catch(err => {
						assert.isDefined(err);
						done();
					});
			});
		});

		describe("stubbing set() to return true", () => {

			before(() => {
				sinon.stub(cacheService.client, "set")
					.callsFake(stubSetTrue);
			});

			after(() => cacheService.client.set.restore());

			it("expect: resolved promise", done => {
				cacheService.setKey(Config.key, Config.val)
					.then(data => {
						assert.isDefined(data);
						assert.equal(data, true);
						done();
					})
					.catch(err => done(err));
			});
		});
	});

	describe("Testing cacheService.setExpiryKey", () => {

		describe("action: stubbing set() to throw error", () => {

			before(() => {
				sinon.stub(cacheService.client, "set")
					.callsFake(stubSetError);
			});

			after(() => cacheService.client.set.restore());

			it("expect: error", done => {
				cacheService.setExpiryKey(Config.key, Config.val)
					.then(data => done("Error not handled in the function"))
					.catch(err => {
						assert.isDefined(err);
						assert.isString(err);
						assert.notEqual(err.length, 0);
						done();
					});
			});
		});

		describe("action: stubbing expireat to throw error", () => {

			before(() => {

				// Bypassing the set function
				sinon.stub(cacheService.client, "set")
					.callsFake(stubSetTrue);

				sinon.stub(cacheService.client, "expireat")
					.callsFake(stubExpireatError);
			});

			after(() => {
				cacheService.client.set.restore();
				cacheService.client.expireat.restore();
			});

			it("expect: error", done => {
				cacheService.setExpiryKey(Config.key, Config.val)
					.then(data => done("Error not handled in the function"))
					.catch(err => {
						assert.isDefined(err);
						assert.isString(err);
						assert.notEqual(err.length, 0);
						done();
					});
			});
		});

		describe("action: stubbing set and expireat to return true values", () => {

			before(() => {
				sinon.stub(cacheService.client, "set")
					.callsFake(stubSetTrue);

				sinon.stub(cacheService.client, "expireat")
					.callsFake(stubExpireatTrue);
			});

			after(() => {
				cacheService.client.set.restore();
				cacheService.client.expireat.restore();
			});

			it("expect: resolved promise", done => {
				cacheService.setExpiryKey(Config.key, Config.val)
					.then(data => {
						assert.isDefined(data);
						assert.equal(data, true);
						done();
					})
					.catch(err => done(err));
			});
		});
	});

	describe("Testing cacheService.enqueue", () => {

		describe("action: stubbing rpush() to return error", () => {

			before(() => {
				sinon.stub(cacheService.client, "rpush")
					.callsFake(stubRpushError);
			});

			after(() => cacheService.client.rpush.restore());

			it("expect: error", done => {
				cacheService.enqueue(Config.key, Config.val)
					.then(data => done("Error not handled inside the function"))
					.catch(err => {
						assert.isDefined(err);
						done();
					});
			});
		});

		describe("action: stubbing rpush() to return error", () => {

			before(() => {
				sinon.stub(cacheService.client, "rpush")
					.callsFake(stubRpushValue);
			});

			after(() => cacheService.client.rpush.restore());

			it("expect: resolved promise with data", done => {
				cacheService.enqueue(Config.key, Config.data)
					.then(data => {
						assert.isDefined(data);
						assert.isString(data);
						assert.equal(data, Config.falsePositive);
						done();
					})
					.catch(err => done(err));
			});
		});
	});

	describe("Testing cacheService.dequeue", () => {

		describe("action: stubbing lpop to return error", () => {

			before(() => {
				sinon.stub(cacheService.client, "lpop")
					.callsFake(stubLpopError);
			});

			after(() => cacheService.client.lpop.restore());

			it("expect: error", done => {
				cacheService.dequeue(Config.key, Config.val)
					.then(data => done("Error not handled in the function"))
					.catch(err => {
						assert.isDefined(err);
						done();
					});
			});
		});

		describe("action: stubbing lpop to return value", () => {

			before(() => {
				sinon.stub(cacheService.client, "lpop")
					.callsFake(stubLpopValue);
			});

			after(() => cacheService.client.lpop.restore());

			it("expect: resolve promise with value", done => {
				cacheService.dequeue(Config.key, Config.val)
					.then(data => {
						assert.isString(data);
						assert.equal(data, Config.falsePositive);
						done();
					})
					.catch(err => done(err));
			});
		});
	});

	describe("Testing cacheService.setHKey", () => {

		it("action: sending missing values, expect: error", done => {
			cacheService.setHKey(Config.key, Config.objMissingValues)
				.then(data => done("Error not handled in the function"))
				.catch(err => {
					assert.isDefined(err);
					assert.equal(err, false);
					done();
				});
		});

		describe("action: stubbing broadcastService.cast to throw error", () => {

			before(() => {
				sinon.stub(broadcastService, "cast")
					.callsFake(stubCastError);
			});

			after(() => broadcastService.cast.restore());

			it("expect: error", done => {
				cacheService.setHKey(Config.key, Config.validDataObj)
					.then(data => done("Error not handled in the function"))
					.catch(err => {
						assert.isDefined(err);
						done();
					});
			});
		});

		describe("action: stubbing hmset() to return error", () => {

			before(() => {
				//running a bypass
				sinon.stub(broadcastService, "cast")
					.callsFake(stubCastValue);

				//running a bypass
				sinon.stub(JSON, "stringify")
					.callsFake(stubStringifyValue);

				sinon.stub(cacheService.client, "hmset")
					.callsFake(stubHmsetError);
			});

			after(() => {
				broadcastService.cast.restore();
				JSON.stringify.restore();
				cacheService.client.hmset.restore();
			});

			it("expect: error", done => {
				cacheService.setHKey(Config.key, Config.validDataObj)
					.then(data => done("Error not handled in the function"))
					.catch(err => {
						assert.isDefined(err);
						done();
					});
			});
		});

		describe("action: stubbing hmset() to return value", () => {
			before(() => {
				//running a bypass
				sinon.stub(broadcastService, "cast")
					.callsFake(stubCastValue);

				//running a bypass
				sinon.stub(JSON, "stringify")
					.callsFake(stubStringifyValue);

				sinon.stub(cacheService.client, "hmset")
					.callsFake(stubHmsetValue);
			});

			after(() => {
				broadcastService.cast.restore();
				JSON.stringify.restore();
				cacheService.client.hmset.restore();
			});

			it("expect: error", done => {
				cacheService.setHKey(Config.key, Config.validDataObj)
					.then(data => {
						assert.isString(data);
						assert.equal(data, Config.falsePositive);
						done();
					})
					.catch(err => done(err));
			});
		});
	});

	describe("Testing cacheService.sadd", () => {

		it("action: sending empty key, expect: error", done => {
			cacheService.sadd(undefined, Config.falsePositive)
				.then(data => done("Error not handled in the function"))
				.catch(err => {
					assert.isDefined(err);
					assert.isObject(err);
					err.should.have.all.keys(Config.keyErr);
					done();
				})
				.catch(e => {
					done(e);
				});
		});

		it("action: sending expected values, expect: array", done => {
			cacheService.sadd(Config.key, Config.falsePositive)
				.then(data => {
					assert.isDefined(data);
					assert.isArray(data);
					done();
				})
				.catch(err => done(err));
		});
	});

	describe("Testing cacheService.srem", () => {
		it("action: sending empty key, expect: error", done => {
			cacheService.srem(undefined, Config.falsePositive)
				.then(data => done("Error not handled in the function"))
				.catch(err => {
					assert.isDefined(err);
					assert.isObject(err);
					err.should.have.all.keys(Config.keyErr);
					done();
				});
		});

		it("action: sending expected values, expect: array", done => {
			cacheService.srem(Config.key, Config.falsePositive)
				.then(data => {
					assert.isDefined(data);
					assert.isArray(data);
					done();
				})
				.catch(err => done(err));
		});
	});

	describe("Testing cacheService.expire", () => {

		it("action: sending no key, expect: error", async (done) => {
			try {
				let result = cacheService.expire(undefined, Config.seconds);
				assert.isEmpty(result);
				done();
			} catch (err) {
				done(err);
			}
		});

		it("action: sending expected values, expect: output", async (done) => {
			try {
				let result = cacheService.expire(Config.key, Config.seconds);
				assert.isDefined(result);
				done();
			} catch (err) {
				done(err);
			}
		});
	});

	describe("Testing cacheService.smembers", () => {

		it("action: sending no key, expect: error", done => {
			cacheService.smembers()
				.then(data => done(new Error("Error not handled in the function")))
				.catch(err => {
					assert.isDefined(err);
					assert.isObject(err);
					err.should.have.all.keys(Config.keyErr);
					done();
				});
		});

		it("action: sending valid key, expect: array", done => {
			cacheService.smembers(Config.key)
				.then(data => {
					assert.isDefined(data);
					assert.isArray(data);
					done();
				})
				.catch(err => done(err));
		});
	});

	describe("Testing cacheService.getHKey", () => {

		describe("action: stubbing hgetall() to throw error", () => {

			before(() => {
				sinon.stub(cacheService.client, "hgetall")
					.callsFake(stubHgetallError);
			});

			after(() => cacheService.client.hgetall.restore());

			it("expect: error", done => {
				cacheService.getHKey()
					.then(data => done(new Error("Error not handled in the function")))
					.catch(err => {
						assert.isDefined(err);
						done();
					});
			});
		});

		describe("action: stubbing hgetall() to return empty value", () => {

			before(() => {
				sinon.stub(cacheService.client, "hgetall")
					.callsFake(stubHgetallEmptyValue);
			});

			after(() => cacheService.client.hgetall.restore());

			it("expect: empty object", done => {
				cacheService.getHKey()
					.then(data => {
						assert.isDefined(data);
						assert.isEmpty(data);
						done();
					})
					.catch(err => done(err));
			});
		});

		describe("action: stubbing hgetall() to return object", () => {

			before(() => {
				sinon.stub(cacheService.client, "hgetall")
					.callsFake(stubHgetallObject);
			});

			after(() => cacheService.client.hgetall.restore());

			it("expect: object", done => {
				cacheService.getHKey()
					.then(data => {
						assert.isDefined(data);
						assert.isObject(data);
						data.should.have.all.keys(Config.keyStatus);
						done();
					})
					.catch(err => done(err));
			});
		});

		describe("action: stubbing hgetall() to return object", () => {

			before(() => {
				sinon.stub(cacheService.client, "hgetall")
					.callsFake(stubHgetallObject);
			});

			after(() => cacheService.client.hgetall.restore());

			it("expect: expect object", done => {
				cacheService.getHKey(Config.valStatus)
					.then(data => {
						assert.isDefined(data);
						assert.isString(data);
						assert.equal(data, Config.valStatus);
						done();
					})
					.catch(err => done(err));
			});
		});
	});

	describe("Testing cacheService.delHKey", () => {

		describe("action: sending expected values", () => {

			before(async () => {
				await cacheService.setHKey(Config.key,
					Config.validDataObj);
			});

			it("expect: resolved promise", done => {
				cacheService.delHKey(Config.key)
					.then(data => {
						assert.equal(data, true);
						done();
					})
					.catch(err => done(err));
			});
		});

		describe("action: stubbing getHKey to give no data", () => {

			before(() => {
				sinon.stub(cacheService, "getHKey")
					.callsFake(stubGetHKeyFalse);
			});

			after(() => cacheService.getHKey.restore());

			it("expect: resolved promise", done => {
				cacheService.delHKey(Config.key)
					.then(data => {
						assert.equal(data, true);
						done();
					})
					.catch(err => done(err));
			});
		});

		describe("action: stubbing getHKey to raise an error", () => {

			before(() => {
				sinon.stub(cacheService, "getHKey")
					.callsFake(stubGetHKeyError);
			});

			after(() => cacheService.getHKey.restore());

			it("expect: resolved promise", done => {
				cacheService.delHKey(Config.key)
					.then(data => {
						assert.equal(data, true);
						done();
					})
					.catch(err => done(err));
			});
		});

		describe("Testing in progress", () => {

			before(async () => {
				await cacheService.setHKey(Config.key,
					Config.validDataObj);
			});

			after(async () => {
				await cacheService.delHKey(Config.key);
			});

			describe("action: stubbing hdel() to raise an error", () => {

				before(() => {
					sinon.stub(cacheService.client, "hdel")
						.callsFake(stubHdelError);
				});

				after(() => cacheService.client.hdel.restore());

				it("expect: error", done => {
					cacheService.delHKey(Config.key)
						.then(data => done(new Error("Error not handled in the function")))
						.catch(err => {
							assert.isDefined(err);
							done();
						});
				});
			});

			describe("action: stubbing hdel() to return invalid data value", () => {
				before(() => {
					sinon.stub(cacheService.client, "hdel")
						.callsFake(stubHdelInvalidData);
				});

				after(() => cacheService.client.hdel.restore());

				it("expect: resolved promise", done => {
					cacheService.delHKey(Config.key)
						.then(data => {
							assert.equal(data, false);
							done();
						})
						.catch(err => done(err));
				});
			});
		});
	});

	describe("Testing cacheService.hashMapAppend", () => {

		it("action: sending undefined params, expect: err", done => {
			cacheService.hashMapAppend()
				.then(data => done(new Error("Error not handled in the function")))
				.catch(err => {
					assert.isDefined(err);
					assert.equal(err, false);
					done();
				});
		});

		describe("action: stubbing hgetall to return err", () => {

			before(() => {
				sinon.stub(cacheService.client, "hgetall")
					.callsFake(stubHgetallError);
			});

			after(() => cacheService.client.hgetall.restore());

			it("expect: error", done => {
				cacheService.hashMapAppend(Config.valStatus,
					Config.valStatus,
					Config.valStatus)
					.then(data => done(new Error("Error not handled in the function")))
					.catch(err => {
						assert.isDefined(err);
						assert.equal(err, true);
						done();
					});
			});
		});
	});

	describe("Testing cacheService.hashMapGet", () => {

		describe("action: stubbing hgetall() to return error", () => {

			before(() => {
				sinon.stub(cacheService.client, "hgetall")
					.callsFake(stubHgetallError);
			});

			after(() => cacheService.client.hgetall.restore());

			it("expect: error", done => {
				cacheService.hashMapGet(Config.valStatus, Config.key)
					.then(data => done(new Error("Error not handled inside the function")))
					.catch(err => {
						assert.equal(err, true);
						done();
					});
			});
		});

		describe("action: stubbing helper.toJson to throw error", () => {

			before(() => {
				// running a bypass
				sinon.stub(cacheService.client, "hgetall")
					.callsFake(stubHgetallObject);

				sinon.stub(helper, "toJson")
					.callsFake(stubToJsonError);
			});

			after(() => {
				cacheService.client.hgetall.restore();
				helper.toJson.restore();
			});

			it("expect: error", done => {
				cacheService.hashMapGet(Config.valStatus, Config.key)
					.then(data => done(new Error("Error not handled in the function")))
					.catch(err => {
						assert.isDefined(err);
						done();
					});
			});
		});

		describe("action: stubbing helper.toArray to throw error", () => {

			before(() => {
				//running a bypass
				sinon.stub(cacheService.client, "hgetall")
					.callsFake(stubHgetallObject);

				sinon.stub(helper, "toArray")
					.callsFake(stubToArrayError);
			});

			after(() => {
				cacheService.client.hgetall.restore();
				helper.toArray.restore();
			});

			it("expect: error", done => {
				cacheService.hashMapGet(Config.valStatus, Config.key)
					.then(data => done(new Error("Error not handled in the function")))
					.catch(err => {
						assert.isDefined(err);
						done();
					});
			});
		});

		describe("action: sending expected values, expect: array", () => {

			before(() => {
				//running a bypass
				sinon.stub(cacheService.client, "hgetall")
					.callsFake(stubHgetallObject);
			});

			after(() => cacheService.client.hgetall.restore());

			it("expect: array", done => {
				cacheService.hashMapGet(Config.valStatus, Config.key)
					.then(data => {
						assert.isArray(data);
						done();
					})
					.catch(err => done(err));
			});
		});
	});
});
