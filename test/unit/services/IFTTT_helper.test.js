const service = require("../../../api/services/IFTTT_helper");
const assert = require("chai").assert;
const moment = require("moment-timezone");

/* Stubs */

describe("IFTTT_Helper Test", () => {
	describe("parseData tests", () => {
		it("send data, expect parsedData", () => {
			const actual = service.parseData("A||B||C");
			const expected = "A,B,C,";
			assert.equal(actual, expected, "wrong parsed data");
		});
	});
	describe("parseTime tests", () => {
		it("send wrong date, expect null", () => {
			const startDate = moment().format("YYYY-MM-DD");
			const endDate = moment().add(5, "year").format("YYYY-MM-DD");
			const startTime = "12:00";
			const endTime = "15:30";
			const actual = service.parseTime(moment(startDate), moment(endDate), startTime, endTime);
			const expected = ["* 12-14 * * *", "0-30 15 * * *"];
			assert.deepEqual(actual, expected, "[message]");
		});
	});
});
