const service = require("../../../api/services/siteService");
const data = require("./mockData/siteData");
const assert = require("chai").assert;
const sinon = require("sinon");

/* Stubs */

const SitesStub = {
	"findOne": (siteId) => {
		return new Promise((res, rej) => {
			if (siteId.siteId === "dummy2")
				return res();
			else if (siteId.siteId) return res(data.site);
			else
				return rej("Invalid siteId");
		});
	}
};

describe("siteService Test", () => {
	before(() => {
		sinon.stub(Sites, "findOne")
			.callsFake(SitesStub.findOne);
	});
	after(() => {
		Sites.findOne.restore();
	});
	describe("returnSiteId tests", () => {
		it("sent data, expect err", async () => {
			const value = await service.returnSiteId("dummy", "mgch")
				.then(res => {
					return undefined;
				})
				.catch(err => {
					return err;
				});
			assert.isDefined(value, "[message]");
		});
	});

	describe("getSiteInfo tests", () => {
		it("send siteid, expect data", async () => {
			const actual = await service.getSiteInfo("dummySite").then(res => {
				return res;
			})
				.catch(err => {
					return undefined;
				});
			const expected = data.site;
			assert.deepEqual(actual, expected, "[message]");
		});

		it("send invalid siteid, expect reject", async () => {
			const actual = await service.getSiteInfo("dummy2").then(res => {
				return undefined;
			})
				.catch(err => {
					return err;
				});
			assert.isDefined(actual, "[message]");
		});

		it("send undefined, expect reject", async () => {
			const actual = await service.getSiteInfo()
				.then(res => {
					return undefined;
				})
				.catch(err => {
					return err;
				});
			assert.isDefined(actual, "[message]");
		});
	});

	describe("updateControllerInRegions tests", () => {
		it("send no region id, expect error message", async () => {
			const value = await service.updateControllerInRegions({
				"deviceId": "d1"
			}, {
				"req": "",
				"areaId": "area101",
				"regionId": undefined
			}).then(errMsg => {
				return errMsg.err;
			});
			assert.isDefined(value, "error message not found");
		});
	});

	describe("removeControllerFromRegion tests", () => {
		it("send incorrect siteId, expect err ", async () => {
			const actual = await service.removeControllerFromRegion({
				...data.controller,
				...{
					"siteId": "dummy2"
				}
			}).then(res => {
				return undefined;
			})
				.catch(err => {
					return err;
				});
			assert.isDefined(actual, "[message]");
		});
	});
});
