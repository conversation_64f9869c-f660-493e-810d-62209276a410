const service = require("../../../api/services/userService");
const assert = require("chai").assert;
const sinon = require("sinon");

/* Stubs */

const UserSiteMapStub = {
	"find": (obj) => {
		return new Promise((resolve, reject) => {
			if (!obj || !obj.userId)
				return reject([]);
			return resolve([{
				"userId": obj.userId,
				"siteId": "ssh"
			}]);
		});
	},

	"destroy": (obj) => {
		return new Promise((resolve, reject) => {
			return resolve([]);
		});
	}
};

const RoleStub = {
	"findOne": (obj) => {
		return new Promise((res, rej) => {
			return res({});
		});
	}
};

describe("userService Test", () => {

	before(() => {
		sinon.stub(UserSiteMap, "find")
			.callsFake(UserSiteMapStub.find);
		sinon.stub(UserSiteMap, "destroy")
			.callsFake(UserSiteMapStub.destroy);
		sinon.stub(Role, "findOne")
			.callsFake(RoleStub.findOne);
	});

	after(() => {
		UserSiteMap.find.restore();
		UserSiteMap.destroy.restore();
		Role.findOne.restore();
	});

	describe("test delusermap", () => {
		it("sending undefined", async () => {
			try {
				await service.delusermap();
			} catch (error) {
				assert.isDefined(error, "No error message found");
			}

		});

		it("send valid data, expect success", async () => {
			const value = await service.delusermap("<EMAIL>");
			assert.isDefined(value, "got failure");
		});
	});

	describe("changeSettingfunc", () => {
		it("send undefined, expect false", async () => {
			const value = await service.changeSettingfunc();

			assert.equal(value, false, `false expected, got ${value}`);
		});
	});

	describe("getPoliciesOfUser", () => {
		it("sending undefined userid, expect empty response", async () => {
			const value = await service.getPoliciesOfUser();

			assert.isObject(value, "response not an object");
			assert.isEmpty(value, "response object not empty");
		});
		it("sending valid data, expect defined object", async () => {
			const value = await service.getPoliciesOfUser("<EMAIL>");
			assert.isDefined(value, "undefined output / invalid response");
		});
	});

});
