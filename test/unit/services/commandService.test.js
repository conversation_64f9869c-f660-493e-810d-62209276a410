const assert = require("chai").assert;
// const Config = require("./services.config")["commandService"];
const sinon = require("sinon");


describe("commandService Test", ()=>{

	before(()=>{
		sinon.stub(Command, "find").callsFake((params)=>{

			if ( params["deviceId"] !== "undefined_jt" ){
				return [
					{
						"componentId": "mgch_18",
						"createdAt": "2019-04-25T05:42:09.943Z",
						"deviceId": "2766_jt",
						"executed": 1556170931,
						"param": "pidsetpoint",
						"socketID": "mgch_9e2acb8c6cda1c06eb2f7d708bf4d368",
						"timestamp": "mgch_1556170929",
						"updatedAt": "2019-04-25T05:42:11.426Z",
						"user": "<EMAIL>",
						"value": "25.5_0.5"
					}
				];
			}

		});
	});
	describe("Testing getLastCommand", () => {

		it("sending all parameters", async ()=>{
			let data = await commandService.getLastCommand("mgch", "2766", 1556170931);
			assert.isArray(data);
			data.forEach( commandInfo =>{
				assert.hasAllKeys(commandInfo, ["type", "value", "execTimestamp", "user"]);
			});
		});


	});


});
