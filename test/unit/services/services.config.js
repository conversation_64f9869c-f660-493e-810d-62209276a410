module.exports = {

	"CacheService": {
		"invalidSiteId": "random",
		"validSiteId": "ssh",
		"keySuccess": ["lastTimeStamp", "loadPattern", "consumptionArr"],
		"keyErr": ["err"],
		"res": {
			"ok": responseObj => {
				return responseObj;
			}
		},
		"keyErrData": ["err", "data"],
		"falsePositive": "thisisafalsepositivevalue",
		"key": "somekey",
		"val": "somevalue",
		"second": 500,
		"valStatus": "status",
		"keyStatus": ["status"],
		"objMissingValues": {
			"_obj_": "",
			"_findBy_": "",
			"_broadCastBy_": "",
			"_broadCastJB_": "",
			"_type_": ""
		},
		"validDataObj": {
			"_obj_": {
				"some key": "some value"
			},
			"_findBy_": "some sort parameter",
			"_broadCastBy_": "some channel",
			"_broadCastJB_": "some other value",
			"_type_": "some type"
		}
	},

	"recipeService": {
		"label": "[Recipe Type]deviceName start/stop",
		"observable": {
			"a": "obs1",
			"b": "obs2"
		},
		"obs_params": {
			"a": "p1",
			"c": "1"
		},
		"alertList": [{
			"priority": 5,
			"description": "asdf",
			"type": "alerts",
			"frequency": 60,
			"parent": "Office AHU",
			"notify": ["ujjal"]
		},
		{
			"type": "execute",
			"description": "asdf",
			"priority": 5,
			"parent": "Office AHU",
			"frequency": 60,
			"notify": ["ujjal"],
			"command": "COMMAND"
		},
		{
			"did": "ahu.startstop",
			"type": "start",
			"notify": ["ujjal"],
			"value": "1"
		}
		],
		"groups": [
			"Energy Optimization",
			"operation Diagnostic"
		],
		"siteId": "smt-del",
		"ts": "2018-04-17 15:11:03",
		"status": "1",
		"rid": "ec473a53-a227-4ad0-b9a6-c56916fc936e",
		"recipeData": {
			"actionable": "[{\"title\":\"Some Name\",\"description\":\"dont waste it documenting.\",\"priority\":5,\"type\":\"execute\",\"parent\":\"Office AHU\",\"notify\":[\"<EMAIL>\"],\"accountable\":[\"<EMAIL>\"],\"uniqId\":\"380b0f40-c74a-4350-9d93-455a3ae8151d\"},{\"title\":\"Some Other Name\",\"description\":\"Live life.\",\"type\":\"execute\",\"priority\":5,\"parent\":\"Office AHU\",\"notify\":[\"<EMAIL>\"],\"accountable\":[\"<EMAIL>\"],\"uniqId\":\"********-c2e3-4964-beed-bd8816084827\"},{\"did\":\"129\",\"type\":\"actions\",\"parent\":\"smt_1\",\"priority\":0,\"notify\":[\"<EMAIL>\"],\"accountable\":[\"<EMAIL>\"],\"command\":\"changesetpoint\",\"value\":\"90\",\"uniqId\":\"0c9ed4a6-0232-4fc7-9383-51c85749b255\"}]",
			"alwaysRun": "false",
			"createdAt": "2019-01-29T11:01:15.615Z",
			"dependentOnOthers": "[\"132\"]",
			"everyMinuteTopics": "[\"db/132/feedback/5\"]",
			"formula": "132.feedback.5 > 42",
			"isActive": "0",
			"isSchedule": "false",
			"isStage": "1",
			"label": "recipe for test",
			"maxDataNeeded": "5",
			"maxLogNeeded": "1",
			"notRun": "[]",
			"oldObservable": "$1||#1||42",
			"operator": "{\"#1\":\">\",\"#2\":\")\",\"#3\":\"(\"}",
			"params": "{\"$1\":\"132.feedback\"}",
			"recipelabel": "[\"Energy Optimization\",\"operation Diagnostic\"]",
			"rid": "ec473a53-a227-4ad0-b9a6-c56916fc936e",
			"runOn": "2722",
			"scheduled": "[]",
			"siteId": "smt-del",
			"switchOff": "false",
			"type": "routine",
			"updatedAt": "2019-01-29T11:01:15.615Z"
		}
	},

	"userService": {
		"userInfo": {
			"accountable": "[\"2052a022-b923-4303-aa2d-219180861a81\"]",
			"createdAt": "2018-12-10T10:40:38.314Z",
			"defaultSite": "spmm",
			"designation": "Tester",
			"email": "<EMAIL>",
			"name": "TEST USER",
			"phone": "**********",
			"policiesGroup": "{\"spmm\":\"Smart Joules Engineer (Energy/Project/Site)\",\"gknmh\":\"Smart Joules Engineer (Energy/Project/Site)\"}",
			"userId": "<EMAIL>",
			"userOrganization": "Smart Joules"
		},
		"prevConfig": {},
		"newConfig": {}
	},

	"eventService": {
		"payload": {
			"param": "firmware",
			"value": {
				"version": "1.1.21",
				"deviceList": [],
				"toSkip": false
			},
			"key": 164646,
			"operation": "js_update",
			"event": "",
			"deviceId": "",
			"code": 21
		},
		"topic": "command/2753/cicd",
	},

	"alertService": {
		"wrong_siteId": "gknmh",
		"wrong_userId": "<EMAIL>",
		"wrong_labelList": ["maintainancelow", "maintainancemedium"],
		"siteId": "ssh",
		"userId": "<EMAIL>",
		"labelList": ["MaintenanceRelated", "Critical"],

		"userFlag": "1",
		"notifQuery": {
			"where": {
				"siteId": "ssh_ssh",
				"timestamp": {
					"between": "abc"
				},
			},
			"limit": 2000,
			"sort": "-1"
		},

		"limit": "10",

		"notifQuery2": {
			"where": {
				"siteId": "ssh_ssh",
				"timestamp": {
					"lte": "abc"
				},
			},
			"limit": "10"
		},

		"keyNotif": ["where", "limit", "sort"],
		"keyTimeFilter": ["lte"],
		"timeRange": "abc",
		"type": "ssh",

		"noReadStatus": [
			{
				"readStatus": "unread"
			},
		],

		"validReadStatus": [
			{
				"readStatus": "read"
			},
		],

		"alertObject":
			{ "does it": "even matter" },

		"validAlert": [{
			"siteId": "ssh_xyz_asdf",
			"does it": "even matter"
		},
		{
			"siteId": "ssh_asdf",
			"does it": "even matter"
		}
		],

		"alertType": "asdf",

		"notifyList": ["xyz"],


	},

	"sendToCache": {
		"siteId": "ssh",
		"userId": "<EMAIL>",
		"sobj": {
			"MaintenanceRelated": { "sms": "0", "mail": "0" },
			"Critical": { "sms": "0", "mail": "0" }
		},
		"wrong_sobj": "",
		"wrong_sobj2": {
			"MaintenanceRelated": { "sms": "0", "mail": "0" },
			"Critical": { "sms": "0", "mail": "0" }
		},
	},
	"addWrapper": {
		"userId": "<EMAIL>",
		"siteId": "gknmh",
		"arr": ["MaintenanceRelated", "Critical"],
		"wrong_arr": [],
	},
	"recipeAlert": {
		"smsMail": {
			"emails": ["<EMAIL>"],
			"smss": ["sad"]
		},
		"rinfo": {
			"description": "",
			"title": "",
			"status": "",
			"groupBy": "",
			"extra": ""
		},
		"ts": "2018-04-17 15:11:05"
	},
	"broadcastService": {
		"siteId": "ssh",
		"sock": "badRequest",
		"fb": {}
	},
	"commandService": {
		"err_fb": {
			"deviceId": "",
			"key": "",
			"status": "",
		},
		"err_fb2": {
			"deviceId": 2123,
			"key": "i'm fake key",
			"status": "",
		},
		"queries": [1]
	},
	"deviceService": {
		"devices": [{
			"deviceId": "2231",
			"siteId": "mgch",
			"networkId": "smartjoules-network-0",
			"regionId": "utlt",
			"name": "Linac - 2 VFD Fuji",
			"deviceType": "vfd",
			"areaId": "ywsn",
			"componentId": "mgch_20",
			"communicationType": "MB",
			"communicationCategory": "VFD",
			"driverType": "1",
			"slaveId": "11",
			"controllerId": "2768",
			"portNumber": "7",
			"functionType": "both",
			"createdAt": "2018-11-25T11:27:19.133Z",
			"updatedAt": "2018-11-25T11:27:19.133Z"
		},
		{
			"deviceId": "3135",
			"siteId": "mgch",
			"networkId": "smartjoules-network-0",
			"regionId": "fulq",
			"name": "Two Way Valve  Belimo LR24-SR",
			"deviceType": "actuatorControl",
			"areaId": "ywsn",
			"componentId": "mgch_10",
			"communicationType": "NMB",
			"communicationCategory": "AVO",
			"driverType": "0",
			"slaveId": "0",
			"controllerId": "2758",
			"portNumber": "1",
			"functionType": "write",
			"createdAt": "2018-12-18T11:03:08.493Z",
			"updatedAt": "2018-12-18T11:19:33.652Z"
		},
		{
			"deviceId": "303",
			"siteId": "mgch",
			"networkId": "smartjoules-network-0",
			"regionId": "omha",
			"name": "MDB - 3 EM EN6436",
			"deviceType": "em",
			"areaId": "hwcw",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "3",
			"slaveId": "28",
			"controllerId": "2777",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"createdAt": "2018-11-25T08:35:42.799Z",
			"updatedAt": "2018-11-25T08:35:42.799Z"
		},
		{
			"deviceId": "2174",
			"siteId": "mgch",
			"networkId": "smartjoules-network-0",
			"regionId": "mesa",
			"name": "AHU On/Off Relay",
			"deviceType": "relay",
			"areaId": "ywsn",
			"componentId": "mgch_9",
			"communicationType": "NMB",
			"communicationCategory": "DR",
			"driverType": "0",
			"slaveId": "0",
			"controllerId": "2757",
			"portNumber": "12",
			"functionType": "write",
			"mode": "{\"start\":\"jouletrack\",\"stop\":\"jouletrack\"}",
			"createdAt": "2018-11-25T11:06:32.524Z",
			"updatedAt": "2019-02-05T07:04:17.293Z"
		},
		{
			"deviceId": "301",
			"siteId": "mgch",
			"networkId": "smartjoules-network-0",
			"regionId": "omha",
			"name": "CSSD EM NF-29",
			"deviceType": "em",
			"areaId": "hwcw",
			"componentId": "mgch_9",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "0",
			"slaveId": "26",
			"controllerId": "2777",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"createdAt": "2018-11-25T08:37:45.691Z",
			"updatedAt": "2018-12-17T05:50:56.735Z"
		},
		{
			"deviceId": "2131",
			"siteId": "mgch",
			"networkId": "smartjoules-network-0",
			"regionId": "obue",
			"name": "OT Recovery Area Temperature",
			"deviceType": "joulesense",
			"areaId": "ywsn",
			"hardwareVer": "v1",
			"softwareVer": "latest",
			"operationMode": "network",
			"vendorId": "smartjoules",
			"createdAt": "2018-11-27T11:47:31.281Z",
			"updatedAt": "2018-11-27T11:47:31.281Z"
		},
		{
			"deviceId": "2135",
			"siteId": "mgch",
			"networkId": "smartjoules-network-0",
			"regionId": "ikxk",
			"name": "Pharmacy Area Temperature",
			"deviceType": "joulesense",
			"areaId": "ywsn",
			"hardwareVer": "v1",
			"softwareVer": "latest",
			"operationMode": "network",
			"vendorId": "smartjoules",
			"createdAt": "2018-11-27T11:44:23.268Z",
			"updatedAt": "2018-11-27T11:44:23.268Z"
		},
		{
			"deviceId": "2243",
			"siteId": "mgch",
			"networkId": "smartjoules-network-0",
			"regionId": "kzua",
			"name": "Cooling Tower-1 On/Off Relay Feedback",
			"deviceType": "relayFeedback",
			"areaId": "ywsn",
			"componentId": "mgch_22",
			"communicationType": "NMB",
			"communicationCategory": "DF",
			"driverType": "0",
			"slaveId": "0",
			"controllerId": "2771",
			"portNumber": "8",
			"functionType": "read",
			"createdAt": "2018-11-26T07:09:33.864Z",
			"updatedAt": "2018-12-17T08:53:33.148Z"
		},
		{
			"deviceId": "2155",
			"siteId": "mgch",
			"networkId": "smartjoules-network-0",
			"regionId": "hcem",
			"name": "AHU On/Off Relay",
			"deviceType": "relay",
			"areaId": "ywsn",
			"communicationType": "NMB",
			"communicationCategory": "DR",
			"driverType": "0",
			"slaveId": "0",
			"controllerId": "2753",
			"portNumber": "12",
			"functionType": "write",
			"createdAt": "2018-11-26T09:00:01.831Z",
			"updatedAt": "2018-11-26T09:00:01.831Z"
		},
		{
			"deviceId": "2178",
			"siteId": "mgch",
			"networkId": "smartjoules-network-0",
			"regionId": "fulq",
			"name": "AHU On/Off  Feedback Relay Feedback",
			"deviceType": "relayFeedback",
			"areaId": "ywsn",
			"componentId": "mgch_10",
			"communicationType": "NMB",
			"communicationCategory": "DF",
			"driverType": "0",
			"slaveId": "0",
			"controllerId": "2758",
			"portNumber": "8",
			"functionType": "read",
			"createdAt": "2018-11-25T11:36:32.299Z",
			"updatedAt": "2018-12-17T07:43:09.352Z"
		},
		{
			"deviceId": "2581",
			"siteId": "mgch",
			"networkId": "smartjoules-network-0",
			"regionId": "lacj",
			"name": "Return Air Temperature Sensor HoneyWell 	H7080B3242",
			"deviceType": "temperatureSensor",
			"areaId": "ywsn",
			"componentId": "mgch_14",
			"communicationType": "NMB",
			"communicationCategory": "ACI",
			"driverType": "2",
			"slaveId": "0",
			"controllerId": "2762",
			"portNumber": "4",
			"functionType": "read",
			"createdAt": "2018-11-25T11:12:46.524Z",
			"updatedAt": "2018-12-17T07:37:07.574Z"
		},
		{
			"deviceId": "2755",
			"siteId": "mgch",
			"networkId": "smartjoules-network-0",
			"regionId": "ynxu",
			"name": "JouleIOControl-5",
			"deviceType": "jouleiocontrol",
			"areaId": "ywsn",
			"hardwareVer": "v2",
			"softwareVer": "latest",
			"operationMode": "network",
			"baudRate": "9600",
			"parity": "N",
			"stopbit": "1",
			"vendorId": "smartjoules",
			"remoteAccess": false,
			"createdAt": "2018-11-25T08:21:10.028Z",
			"updatedAt": "2018-12-22T08:10:20.025Z"
		},
		{
			"deviceId": "2187",
			"siteId": "mgch",
			"networkId": "smartjoules-network-0",
			"regionId": "ikxk",
			"name": "AHU On/Off Relay",
			"deviceType": "relay",
			"areaId": "ywsn",
			"componentId": "mgch_11",
			"communicationType": "NMB",
			"communicationCategory": "DR",
			"driverType": "0",
			"slaveId": "0",
			"controllerId": "2760",
			"portNumber": "12",
			"functionType": "write",
			"createdAt": "2018-11-26T08:06:13.509Z",
			"updatedAt": "2018-11-26T08:06:13.509Z"
		},
		{
			"deviceId": "286",
			"siteId": "mgch",
			"networkId": "smartjoules-network-0",
			"regionId": "glok",
			"name": "CT-2 NF-29",
			"deviceType": "em",
			"areaId": "hwcw",
			"componentId": "mgch_23",
			"communicationType": "MB",
			"communicationCategory": "EM",
			"driverType": "0",
			"slaveId": "11",
			"controllerId": "2776",
			"portNumber": "1",
			"functionType": "read",
			"isMainMeter": "false",
			"createdAt": "2018-11-25T09:05:16.990Z",
			"updatedAt": "2018-12-17T08:59:42.316Z"
		},
		{
			"deviceId": "2776",
			"siteId": "mgch",
			"networkId": "smartjoules-network-0",
			"regionId": "glok",
			"name": "JouleLogger-1",
			"deviceType": "joulelogger",
			"areaId": "hwcw",
			"hardwareVer": "v1",
			"softwareVer": "latest",
			"operationMode": "network",
			"baudRate": "9600",
			"parity": "N",
			"stopbit": "1",
			"vendorId": "smartjoules",
			"remoteAccess": true,
			"remoteAccessPort": "39465",
			"createdAt": "2018-11-25T08:26:58.030Z",
			"updatedAt": "2019-02-05T06:08:09.121Z"
		},
		{
			"deviceId": "2147",
			"siteId": "mgch",
			"networkId": "smartjoules-network-0",
			"regionId": "sdmp",
			"name": "AHU On/Off Relay",
			"deviceType": "relay",
			"areaId": "ywsn",
			"componentId": "mgch_3",
			"communicationType": "NMB",
			"communicationCategory": "DR",
			"driverType": "0",
			"slaveId": "0",
			"controllerId": "2751",
			"portNumber": "12",
			"functionType": "write",
			"createdAt": "2018-11-26T08:09:26.576Z",
			"updatedAt": "2018-11-26T08:09:26.576Z"
		}],
		"getAvailableSlaveIds": {
			"devicesList": undefined,
			"controllerId": 2812,
			"siteId": "gknmh",
			"db": [
				{
					"deviceId": "2988",
					"siteId": "gknmh",
					"networkId": "smartjoules-network-0",
					"regionId": "dvmk",
					"name": "AHU 3 VFD Danfoss",
					"deviceType": "vfd",
					"areaId": "cybw",
					"componentId": "gknmh_17",
					"communicationType": "MB",
					"communicationCategory": "VFD",
					"driverType": "3",
					"slaveId": "1",
					"controllerId": "2794",
					"portNumber": "1",
					"functionType": "both",
					"createdAt": "2018-12-11T08:48:44.245Z",
					"updatedAt": "2018-12-19T04:59:33.089Z"
				},
				{
					"deviceId": "2551",
					"siteId": "gknmh",
					"networkId": "smartjoules-network-0",
					"regionId": "fqcp",
					"name": " Supply line A&D Temp Sensor HoneyWell H7080B3242",
					"deviceType": "temperatureSensor",
					"areaId": "qxtl",
					"componentId": "gknmh_37",
					"communicationType": "NMB",
					"communicationCategory": "ACI",
					"driverType": "2",
					"slaveId": "0",
					"controllerId": "2778",
					"portNumber": "4",
					"functionType": "read",
					"createdAt": "2018-11-25T11:11:53.548Z",
					"updatedAt": "2018-12-18T13:35:10.466Z"
				},
				{
					"deviceId": "3158",
					"siteId": "gknmh",
					"networkId": "smartjoules-network-0",
					"regionId": "rvao",
					"name": "JouleMomo-34",
					"deviceType": "joulemomo",
					"areaId": "cybw",
					"hardwareVer": "v1",
					"softwareVer": "latest",
					"operationMode": "network",
					"vendorId": "sjpl",
					"createdAt": "2019-01-28T09:35:17.193Z",
					"updatedAt": "2019-01-28T09:35:17.193Z"
				},
				{
					"deviceId": "3047",
					"siteId": "gknmh",
					"networkId": "smartjoules-network-0",
					"regionId": "dhqu",
					"name": "AHU On/Off Relay",
					"deviceType": "relay",
					"areaId": "mqpb",
					"communicationType": "NMB",
					"communicationCategory": "DR",
					"driverType": "0",
					"slaveId": "0",
					"controllerId": "2826",
					"portNumber": "4",
					"functionType": "write",
					"createdAt": "2018-12-11T09:36:41.145Z",
					"updatedAt": "2018-12-11T09:36:41.145Z"
				},
				{
					"deviceId": "2825",
					"siteId": "gknmh",
					"networkId": "smartjoules-network-0",
					"regionId": "necx",
					"name": "JouleMomo-30",
					"deviceType": "joulemomo",
					"areaId": "mqpb",
					"componentId": "gknmh_44",
					"hardwareVer": "v1",
					"softwareVer": "latest",
					"operationMode": "network",
					"baudRate": "9600",
					"parity": "N",
					"stopbit": "1",
					"vendorId": "smartjoules",
					"createdAt": "2018-11-26T10:31:08.863Z",
					"updatedAt": "2018-12-13T09:45:39.685Z"
				},
				{
					"deviceId": "3010",
					"siteId": "gknmh",
					"networkId": "smartjoules-network-0",
					"regionId": "nzmp",
					"name": "AHU 14 VFD Danfoss",
					"deviceType": "vfd",
					"areaId": "cybw",
					"componentId": "gknmh_28",
					"communicationType": "MB",
					"communicationCategory": "VFD",
					"driverType": "3",
					"slaveId": "1",
					"controllerId": "2809",
					"portNumber": "1",
					"functionType": "both",
					"createdAt": "2018-12-11T09:03:53.275Z",
					"updatedAt": "2018-12-19T05:41:04.206Z"
				},
				{
					"deviceId": "3011",
					"siteId": "gknmh",
					"networkId": "smartjoules-network-0",
					"regionId": "nzmp",
					"name": "AHU On/Off Relay",
					"deviceType": "relay",
					"areaId": "cybw",
					"communicationType": "NMB",
					"communicationCategory": "DR",
					"driverType": "0",
					"slaveId": "0",
					"controllerId": "2810",
					"portNumber": "4",
					"functionType": "write",
					"createdAt": "2018-12-11T09:04:08.984Z",
					"updatedAt": "2018-12-11T09:04:08.984Z"
				},
				{
					"deviceId": "3029",
					"siteId": "gknmh",
					"networkId": "smartjoules-network-0",
					"regionId": "hqmw",
					"name": "AHU On/Off Relay Feedback",
					"deviceType": "relayFeedback",
					"areaId": "pcse",
					"componentId": "gknmh_34",
					"communicationType": "NMB",
					"communicationCategory": "DF",
					"driverType": "0",
					"slaveId": "0",
					"controllerId": "2815",
					"portNumber": "2",
					"functionType": "read",
					"createdAt": "2018-12-11T09:24:36.193Z",
					"updatedAt": "2018-12-19T07:00:43.974Z"
				},
				{
					"deviceId": "2793",
					"siteId": "gknmh",
					"networkId": "smartjoules-network-0",
					"regionId": "kxzf",
					"name": "JouleMomo-2",
					"deviceType": "joulemomo",
					"areaId": "mqpb",
					"componentId": "gknmh_16",
					"hardwareVer": "v1",
					"softwareVer": "latest",
					"operationMode": "network",
					"baudRate": "9600",
					"parity": "N",
					"stopbit": "1",
					"vendorId": "smartjoules",
					"createdAt": "2018-11-26T10:02:31.428Z",
					"updatedAt": "2018-12-13T09:20:10.930Z"
				},
				{
					"deviceId": "3171",
					"siteId": "gknmh",
					"networkId": "smartjoules-network-0",
					"regionId": "rvao",
					"name": "JouleMomo-43",
					"deviceType": "joulemomo",
					"areaId": "cybw",
					"hardwareVer": "v1",
					"softwareVer": "latest",
					"operationMode": "network",
					"vendorId": "sjpl",
					"createdAt": "2019-01-30T10:35:00.420Z",
					"updatedAt": "2019-01-30T10:35:00.420Z"
				},
				{
					"deviceId": "1842",
					"siteId": "gknmh",
					"networkId": "smartjoules-network-0",
					"regionId": "mgcj",
					"name": "CONDENSER WATER PUMP-1 EM NF-29",
					"deviceType": "em",
					"areaId": "erxu",
					"componentId": "gknmh_7",
					"communicationType": "MB",
					"communicationCategory": "EM",
					"driverType": "0",
					"slaveId": "13",
					"controllerId": "2773",
					"portNumber": "7",
					"functionType": "read",
					"isMainMeter": "false",
					"createdAt": "2018-11-25T09:41:31.210Z",
					"updatedAt": "2018-12-18T12:54:56.557Z"
				},
				{
					"deviceId": "2042",
					"siteId": "gknmh",
					"networkId": "smartjoules-network-0",
					"regionId": "mgcj",
					"name": "A & D Block(New area) Flow Meter  Dwyer 					Paddlewheel-4 inch",
					"deviceType": "flowMeter",
					"areaId": "erxu",
					"communicationType": "NMB",
					"communicationCategory": "ACI",
					"driverType": "3",
					"slaveId": "0",
					"controllerId": "2775",
					"portNumber": "4",
					"functionType": "read",
					"createdAt": "2018-11-26T08:33:15.906Z",
					"updatedAt": "2018-11-26T08:33:15.906Z"
				},
				{
					"deviceId": "3159",
					"siteId": "gknmh",
					"networkId": "smartjoules-network-0",
					"regionId": "rvao",
					"name": "JouleMomo-35",
					"deviceType": "joulemomo",
					"areaId": "cybw",
					"hardwareVer": "v1",
					"softwareVer": "latest",
					"operationMode": "network",
					"vendorId": "sjpl",
					"createdAt": "2019-01-28T09:38:21.219Z",
					"updatedAt": "2019-01-28T09:38:21.219Z"
				},
				{
					"deviceId": "2778",
					"siteId": "gknmh",
					"networkId": "smartjoules-network-0",
					"regionId": "fqcp",
					"name": "JouleIOControl-6",
					"deviceType": "jouleiocontrol",
					"areaId": "qxtl",
					"hardwareVer": "v2",
					"softwareVer": "latest",
					"operationMode": "network",
					"baudRate": "9600",
					"parity": "N",
					"stopbit": "1",
					"vendorId": "smartjoules",
					"createdAt": "2018-11-25T11:09:47.744Z",
					"updatedAt": "2018-11-25T11:09:47.744Z"
				},
				{
					"deviceId": "3034",
					"siteId": "gknmh",
					"networkId": "smartjoules-network-0",
					"regionId": "fqcp",
					"name": "AHU On/Off Relay Feedback",
					"deviceType": "relayFeedback",
					"areaId": "qxtl",
					"componentId": "gknmh_38",
					"communicationType": "NMB",
					"communicationCategory": "DF",
					"driverType": "0",
					"slaveId": "0",
					"controllerId": "2819",
					"portNumber": "2",
					"functionType": "read",
					"createdAt": "2018-12-11T09:28:56.986Z",
					"updatedAt": "2018-12-19T06:53:17.410Z"
				},
				{
					"deviceId": "3031",
					"siteId": "gknmh",
					"networkId": "smartjoules-network-0",
					"regionId": "fqcp",
					"name": "AHU On/Off Relay",
					"deviceType": "relay",
					"areaId": "qxtl",
					"communicationType": "NMB",
					"communicationCategory": "DR",
					"driverType": "0",
					"slaveId": "0",
					"controllerId": "2818",
					"portNumber": "4",
					"functionType": "write",
					"createdAt": "2018-12-11T09:28:10.263Z",
					"updatedAt": "2018-12-11T09:28:10.263Z"
				},
				{
					"deviceId": "3005",
					"siteId": "gknmh",
					"networkId": "smartjoules-network-0",
					"regionId": "nzmp",
					"name": "AHU On/Off Relay",
					"deviceType": "relay",
					"areaId": "cybw",
					"communicationType": "NMB",
					"communicationCategory": "DR",
					"driverType": "0",
					"slaveId": "0",
					"controllerId": "2807",
					"portNumber": "4",
					"functionType": "write",
					"createdAt": "2018-12-11T09:02:39.317Z",
					"updatedAt": "2018-12-11T09:02:39.317Z"
				},
			]
		},
		"getAvailableSlaveIds_err": {
			"devicesList": "sd",
			"controllerId": 2812,
			"siteId": "gknmh",
		},
		"updateComponentInfoInDevices_err": {
			"added": new Set([]),
			"removed": new Set([]),
			"componentId": "gknmh_31",
			"siteId": "gknmh"
		},
		"updateComponentInfoInDevices": {
			"added": new Set([1202, 1205]),
			"removed": new Set([1202, 1205]),
			"componentId": "gknmh_31",
			"siteId": "gknmh"
		},
	},
	"dataDeviceService": {
		"dataForRecentData": {
			"where": {
				"deviceId": "ssh_1",
				"timestamp": {
					"between": ["2019-01-02 12:00", "2019-01-07 12:00"]
				}
			},
			"limit": 1,
			"sort": "-1"
		},
		"dataForRecentData2": {
			"where": {
				"deviceId": "2416",
				"timestamp": {
					"between": ["2018-12-18", "2018-12-24"]
				}
			},
			"limit": 1,
			"sort": 1
		},
		"dbReturn": {
			"Items":
				[{
					"createdAt": "2018-12-23T14:51:01.266Z",
					"siteId": "frtn",
					"deviceId": "2416",
					"data": "{\"KW\":\"0.0\",\"KVAH\":\"8123.21\",\"KVAR\":\"0.0\",\"A_VLN\":\"0.0\",\"A_AMP\":\"0.0\",\"IB\":\"0.0\",\"PF\":\"1.0\",\"A_VLL\":\"1.0\",\"IR\":\"0.0\",\"IY\":\"0.0\",\"KVA\":\"0.0\"}",
					"updatedAt": "2018-12-23T14:51:01.266Z",
					"timestamp": "2018-12-18 00:01:20"
				}],
			"Count": 1,
			"ScannedCount": 1,
			"LastEvaluatedKey": { "deviceId": "2416", "timestamp": "2018-12-18 00:01:20" }
		},
		"saveCalculatedParameters": {
			"requestObj": {
				"siteId": "frtn"
			}
		},
		"saveCalculatedParametersFake": {
			"requestObj": {
				"siteId": "dfd"
			}
		},
		"saveToDB": {
			"resObj": "Some Data",
			"component": {
				"id": "Agent 47",
				"siteId": "Hitman"
			},
			"ts": "Samay"
		},
		"saveChillerData": {
			"dataObj": {
				"CHL": "Chiller",
				"EM": false
			},
			"component": "component",
			"ts": "samay"
		},
		"saveAHUData": {
			"dataObj": {},
			"component": "component",
			"ts": "samay"
		},
		"saveAHUData2": {
			"dataObj": {
				"Status": {
					"FEEDBACK": "12"
				}
			},
			"component": "component",
			"deviceId": "Device Id",
			"siteId": "Site",
			"ts": "samay"
		},
		"savePumpData": {
			"dataObj": {},
			"component": "component",
			"ts": "samay"
		},
		"savePumpData2": {
			"dataObj": {
				"VFD": {
					"OutputFrequency": 12,
					"WaterFlow": 12,
					"Head": 12,
					"InputPower": 12
				},
				"EM": {
					"KVA": 17,
					"KVAH": 17,
					"KW": 17
				}
			},
			"component": "component",
			"deviceId": "Device Id",
			"siteId": "Site",
			"ts": "samay"
		},
		"getDeviceTypeDataByRegion": {
			"filter": {

			}
		},
		"filterY": {
			"response": [{
				"timestamp": 1212,
				"data": "{\"yAxis\": \"1\"}",
			}],
			"yAxis": {
				"param": "yAxis"
			}
		},
		"generateSearchObj": {
			"state1": {
				"devId": "Agent 47",
				"from": 14,
				"to": 12,
			},
			"state2": {
				"devId": "Agent 47",
				"from": 12,
				"to": 14,
			}
		}
	},
	"diagnosticsService": {
		"diagnosticsResponseFeedback": {
			"topic": "command/2753/cicd",
			"payload": {
				"param": "firmware",
				"value": {
					"version": "1.1.21",
					"deviceList": [],
					"toSkip": true
				},
				"operation": "js_update"
			}
		},
		"checkUpdate": {
			"payload": { "currentVersion": "1.0.1" },
			"topic": "local/req/12345/cicd"
		},
		"cicdFeedback": {
			"topic": "command/2753/cicd",
			"payload": {
				"param": "firmware",
				"value": {
					"version": "1.1.21",
					"deviceList": [],
					"toSkip": true
				},
				"operation": "js_update",
				"key": 2122,
				"code": 21
			}
		},
		"jouleSenseFeedback": {
			"topic": "command/2753/cicd",
			"payload": JSON.stringify({
				"param": "firmware",
				"value": {
					"version": "1.1.21",
					"deviceList": [],
					"toSkip": true
				},
				"operation": "js_update",
				"key": 2122
			})
		},
		"jouleSenseFeedback2": {
			"topic": "command/2753/cicd",
			"payload": JSON.stringify({
				"param": "firmware",
				"value": {
					"version": "1.1.21",
					"deviceList": [],
					"toSkip": true
				},
				"operation": "js_update"
			})
		},
		"getComponentsDetails": {
			"deviceId": ["ssh_12"],
			"siteId": "ssh"
		},
		"getControllerDetails": {
			"controllerId": [102],
			"siteId": "ssh"
		},
		"getDeviceDetails": {
			"deviceId": [102],
			"siteId": "ssh"
		},
		"getAllCards": {
			"data": [
				{
					"_index": "maintainancemode",
					"_type": "dejoule",
					"_id": "49594950991769561581598653899753050067530319726116864018.0",
					"_score": 1,
					"_source": {
						"summary": "Device data not available",
						"creation_time": "2019-05-06 14:45:00",
						"accountable": [
							"<EMAIL>"
						],
						"issueid": "TS-946",
						"priority_site": "High",
						"due_date": "None",
						"description": "Device data for the relay feedback is not available",
						"Virtual": {
							"controller": [
								"2852"
							],
							"component": [
								"frtn_30"
							],
							"process": [],
							"plant": [],
							"Region": [
								"mhez"
							],
							"netwoktopoly": [
								"smartjoules-network-0"
							],
							"jouleRecipi": [],
							"deviceId": [
								"4189"
							],
							"IOpoint": []
						},
						"type": "DJ",
						"resolved_time": "2019-05-13 16:43:25",
						"priority_dev": "Medium",
						"notify": [],
						"repoter": "<EMAIL>",
						"site": "frtn",
						"Real": {
							"component": [],
							"controller": [],
							"process": [],
							"plant": [],
							"Region": "",
							"netwoktopoly": [],
							"jouleRecipi": [],
							"deviceId": [],
							"IOpoint": [
								"status"
							]
						},
						"id": "3e17afae-07b1-4a92-8c8c-b9b09a8d517c",
						"status": "Release"
					}
				}],
			"id": "2852"
		},
		"createUnderMaintenanceGraphData": {
			"data": [
				{
					"_index": "maintainancemode",
					"_type": "dejoule",
					"_id": "49594950991769561581598653899753050067530319726116864018.0",
					"_score": 1,
					"_source": {
						"summary": "Device data not available",
						"creation_time": "2019-05-06 14:45:00",
						"accountable": [
							"<EMAIL>"
						],
						"issueid": "TS-946",
						"priority_site": "High",
						"due_date": "None",
						"description": "Device data for the relay feedback is not available",
						"Virtual": {
							"controller": [
								"2852"
							],
							"component": [
								"frtn_30"
							],
							"process": [],
							"plant": [],
							"Region": [
								"mhez"
							],
							"netwoktopoly": [
								"smartjoules-network-0"
							],
							"jouleRecipi": [],
							"deviceId": [
								"4189"
							],
							"IOpoint": []
						},
						"type": "DJ",
						"resolved_time": "2019-05-13 16:43:25",
						"priority_dev": "Medium",
						"notify": [],
						"repoter": "<EMAIL>",
						"site": "frtn",
						"Real": {
							"component": [],
							"controller": [],
							"process": [],
							"plant": [],
							"Region": "",
							"netwoktopoly": [],
							"jouleRecipi": [],
							"deviceId": [],
							"IOpoint": [
								"status"
							]
						},
						"id": "3e17afae-07b1-4a92-8c8c-b9b09a8d517c",
						"status": "Release"
					}
				}],
			"arr": { "id": "2852" }
		},
		"getJiraTaskID": {
			"uuid": "e7b64d71-67bf-4426-95cb-121f0151fa30",
			"params": {
				"title": "scnsk",
				"comment": "scsdj",
				"issueObservedAt": "2019-06-10T18:30:00.000Z", "expectedresolve": "2019-06-19T18:30:00.000Z", "deadline": "2019-06-19T18:30:00.000Z",
				"priority": "med",
				"accountable": ["<EMAIL>"],
				"notify": ["<EMAIL>"],
				"typeOfIssue": "Deploy",
				"description": "scsdj",
				"siteId": "mgch",
				"rd": {
					"slave": [],
					"component": [],
					"controller": [],
					"region": [],
					"area": [],
					"network": []
				},
				"modeType": "region",
				"reporter": "<EMAIL>",
				"attachments": [{ "title": "cds", "fileUrl": "" }],
				"summary": "cds"
			}
		},
		"collectMaintenanceData": {
			"uuid": "e7b64d71-67bf-4426-95cb-121f0151fa30",
			"params_region": {
				"title": "scnsk",
				"comment": "scsdj",
				"issueObservedAt": "2019-06-10T18:30:00.000Z", "expectedresolve": "2019-06-19T18:30:00.000Z", "deadline": "2019-06-19T18:30:00.000Z",
				"priority": "med",
				"accountable": ["<EMAIL>"],
				"notify": ["<EMAIL>"],
				"typeOfIssue": "Deploy",
				"description": "scsdj",
				"siteId": "mgch",
				"rd": {
					"slave": [],
					"component": [],
					"controller": [],
					"region": [],
					"area": [],
					"network": []
				},
				"modeType": "region",
				"reporter": "<EMAIL>",
				"attachments": [{ "title": "cds", "fileUrl": "" }],
				"summary": "cds"
			},
			"params_device": {
				"title": "scnsk",
				"comment": "scsdj",
				"issueObservedAt": "2019-06-10T18:30:00.000Z", "expectedresolve": "2019-06-19T18:30:00.000Z", "deadline": "2019-06-19T18:30:00.000Z",
				"priority": "med",
				"accountable": ["<EMAIL>"],
				"notify": ["<EMAIL>"],
				"typeOfIssue": "Deploy",
				"description": "scsdj",
				"siteId": "mgch",
				"rd": {
					"slave": [],
					"component": [],
					"controller": [],
					"region": [],
					"area": [],
					"network": []
				},
				"modeType": "device",
				"reporter": "<EMAIL>",
				"attachments": [{ "title": "cds", "fileUrl": "" }],
				"summary": "cds"
			},
			"params_component": {
				"title": "scnsk",
				"comment": "scsdj",
				"issueObservedAt": "2019-06-10T18:30:00.000Z", "expectedresolve": "2019-06-19T18:30:00.000Z", "deadline": "2019-06-19T18:30:00.000Z",
				"priority": "med",
				"accountable": ["<EMAIL>"],
				"notify": ["<EMAIL>"],
				"typeOfIssue": "Deploy",
				"description": "scsdj",
				"siteId": "mgch",
				"rd": {
					"slave": [],
					"component": [],
					"controller": [],
					"region": [],
					"area": [],
					"network": []
				},
				"modeType": "component",
				"reporter": "<EMAIL>",
				"attachments": [{ "title": "cds", "fileUrl": "" }],
				"summary": "cds"
			},
			"params_network": {
				"title": "scnsk",
				"comment": "scsdj",
				"issueObservedAt": "2019-06-10T18:30:00.000Z", "expectedresolve": "2019-06-19T18:30:00.000Z", "deadline": "2019-06-19T18:30:00.000Z",
				"priority": "med",
				"accountable": ["<EMAIL>"],
				"notify": ["<EMAIL>"],
				"typeOfIssue": "Deploy",
				"description": "scsdj",
				"siteId": "mgch",
				"rd": {
					"slave": [],
					"component": [],
					"controller": [],
					"region": [],
					"area": [],
					"network": []
				},
				"modeType": "network",
				"reporter": "<EMAIL>",
				"attachments": [{ "title": "cds", "fileUrl": "" }],
				"summary": "cds"
			}
		},
		"calculatePar": {
			"paramsUndefined": [
				{
					"_index": "cp",
					"_type": "parameters",
					"_id": "LDGiLGsBa3fCnrm9q7fW",
					"_score": 1,
					"_source": {}
				}
			],
			"paramsDefined": [{
				"_index": "cp",
				"_type": "parameters",
				"_id": "LDGiLGsBa3fCnrm9q7fW",
				"_score": 1,
				"_source":
				{
					"alpha": "22",
					"beta": "80.0",
					"nc": "22",
					"S": "22",
					"Sjt": "22",
					"Sth": "22",
					"ti": "22",
					"o": "22",
					"a": "22",
					"di": "22",
					"Sjr": "22",
					"deviceId": "2248",
					"start_time": "2019-06-01 00:45:00",
					"end_time": "2019-06-01 01:00:00"
				}
			}
			],
		},
	},
};
