/**
 * @file This file contains all the tests for functions exported in diagnosticUtil.js
 * <AUTHOR>
 */

// const assert = require("chai").assert;
const expect = require("chai").expect;
const _ = require("lodash");
const mockData = require("./mockData/diagnosticUtil");

describe("Diagnostic Utils", () => {

	describe("diagnosticBenchmarkingGraph", () => {

		// Move this test to diagnostic service.
		describe("generateElasticIndex: Generates elasticsearch index based on start time and end time.", () => {

			it("generateElasticIndex: When startTime and endTime are from the same day.", (done) => {
				const startTime = "2019-09-04 13:46:00";
				const endTime = "2019-09-04 13:46:00";
				const index = "calculated_parameters";
				const newIndex = diagnosticService.generateElasticIndex(startTime, endTime, index);
				expect(newIndex).to.include("calculated_parameters");
				expect(newIndex).to.be.equal("calculated_parameters_2019-09-04,");
				done();
			});
	
			it("generateElasticIndex: When startTime and endTime are from different days.", (done) => {
				const startTime = "2019-09-04 13:46:00";
				const endTime = "2019-09-05 13:46:00";
				const index = "calculated_parameters";
				const newIndex = diagnosticService.generateElasticIndex(startTime, endTime, index);
				expect(newIndex).to.include("calculated_parameters");
				expect(newIndex).to.be.equal("calculated_parameters_2019-09-04,calculated_parameters_2019-09-05,");
				done();
			});

			it("generateElasticIndex: When startTime and endTime are from same month. groupBy specified is months.", (done) => {
				const startTime = "2019-09-04 13:46:00";
				const endTime = "2019-09-04 13:46:00";
				const index = "calculated_parameters";
				const groupBy = "months";
				const newIndex = diagnosticService.generateElasticIndex(startTime, endTime, index, groupBy);
				expect(newIndex).to.include("calculated_parameters");
				expect(newIndex).to.be.equal("calculated_parameters_2019-09,");
				done();
			});

			it("generateElasticIndex: When startTime and endTime are from different months. groupBy specified is months.", (done) => {
				const startTime = "2019-09-04 13:46:00";
				const endTime = "2019-10-04 13:47:00";
				const index = "calculated_parameters";
				const groupBy = "months";
				const newIndex = diagnosticService.generateElasticIndex(startTime, endTime, index, groupBy);
				expect(newIndex).to.include("calculated_parameters");
				expect(newIndex).to.be.equal("calculated_parameters_2019-09,calculated_parameters_2019-10,");
				done();
			});
		});

		describe("checkInput: Checks for API input parameters.", () => {

			it("When all required parameters are present", (done) => {
				const data = mockData.diagnosticBenchmarkingGraph.checkInput.correctInput;
				const response = diagnosticUtil.diagnosticBenchmarkingGraph.checkInput(data);
				expect(response).to.deep.equal({
					"status": true
				});
				done();
			});

			it("When either startTime OR endTime OR siteId is not present", (done) => {
				const data = mockData.diagnosticBenchmarkingGraph.checkInput.incorrectInput1;
				const response = diagnosticUtil.diagnosticBenchmarkingGraph.checkInput(data);
				expect(response).to.deep.equal({
					"status": false,
					"message": "Either 'startTime' OR 'endTime' OR 'siteId' not present."
				});
				done();
			});

			it("When deviceObject not present", (done) => {
				const data = mockData.diagnosticBenchmarkingGraph.checkInput.incorrectInput2;
				const response = diagnosticUtil.diagnosticBenchmarkingGraph.checkInput(data);
				expect(response).to.deep.equal({
					"status": false,
					"message": "'deviceObject' not present."
				});
				done();
			});
		});

		describe("aggregateBenchmarkingData: Aggregates benchmarking data.", () => {
			
			it("Aggregates benchmarking data fetched from the elasticsearch index based on timestamp.", (done) => {
				let { benchmarkingData, benchmarkingParamList } = mockData.diagnosticBenchmarkingGraph;

				let calcAggregatedData = diagnosticUtil.diagnosticBenchmarkingGraph.aggregateBenchmarkingData(benchmarkingData, benchmarkingParamList);

				expect(calcAggregatedData).to.have.property("dataSet");
				expect(calcAggregatedData).to.have.property("averageDataSet");

				done();
			});
		});

		describe("aggregateDataSet: ", () => {

			it("Aggregates data for each benchmarking parameter for different devices at a particular timestamp.", (done) => {
				const {dataSet} = mockData.diagnosticBenchmarkingGraph.aggregatedData;
				const {benchmarkingParamList} = mockData.diagnosticBenchmarkingGraph;
				
				const averageDataSet = diagnosticUtil.diagnosticBenchmarkingGraph.aggregateDataSet(dataSet, benchmarkingParamList);

				const dateCount = Object.keys(dataSet).length;
				benchmarkingParamList.forEach(parameter => {
					expect(averageDataSet).to.have.property(parameter);
					expect(averageDataSet[parameter]).to.be.an("array").to.have.lengthOf(dateCount);
					expect(averageDataSet[parameter][0]).to.be.an("array");
					expect(averageDataSet[parameter][0][0]).to.be.a("number", "Unix timestamp.");
				});

				expect(averageDataSet.alpha[0][1]).to.equal(78, "Average data of two devices at that particular timestamp.");
				expect(averageDataSet.beta[0][1]).to.equal(100, "Average data of two devices at that particular timestamp while one of them is 'null'.");
				done();
			});
		});

		describe("parseResponseData", () => {
			it("Parses data as per FrontEnd requirement.", (done) => {
				let { benchmarkingParamList, aggregatedData, strictnessData } = mockData.diagnosticBenchmarkingGraph;

				const output = diagnosticUtil.diagnosticBenchmarkingGraph.parseResponseData(benchmarkingParamList, aggregatedData, strictnessData);

				expect(output).to.have.property("parameters");
				expect(output.parameters).to.be.an("array");
				output.parameters.forEach(parameter => {
					expect(parameter).to.have.property("name");
					expect(parameter).to.have.property("data");
					expect(parameter).to.have.property("strictness");
				});
				expect(output).to.have.property("events");
				expect(output).to.have.property("rawData");
				done();
			});
		});

		describe("expectedDataSet", () => {
			it("Fills empty arrays at missing timestamps at intervals of 15 minutes.", (done) => {
				let { dataSet } = mockData.diagnosticBenchmarkingGraph.aggregatedData;
				// Dataset doesn't have timestamp "2019-09-04 17:15:00"
				diagnosticUtil.diagnosticBenchmarkingGraph.expectedDataSet(dataSet);
				expect(dataSet).to.have.property("2019-12-12 16:30:00");
				expect(dataSet["2019-12-12 16:30:00"]).to.be.an("array").that.is.empty;
				done();
			});
		});

		describe("extendDataSelectParameters", () => {
			it("Replicates data forward for select benchmarking parameters incase the next value is null.", (done) => {
				let { aggregatedData } = mockData.diagnosticBenchmarkingGraph;
				// aggregatedData.averageDataSet.alpha[0][1] is not null. Hence it should get replicated forward.
				diagnosticUtil.diagnosticBenchmarkingGraph.extendDataSelectParameters(aggregatedData);
				let dataArray = aggregatedData.averageDataSet.alpha;
				dataArray.forEach(timeSeriesData => {
					expect(timeSeriesData[1]).to.not.be.null;
				});
				done();
			});
		});
	});

	describe("getDQIGraph", () => {

		describe("checkInput: Checks for API input parameters.", () => {

			it("When all required parameters are present", (done) =>{
				const data = mockData.getDQIGraph.checkInput.correctInput;
				const result = diagnosticUtil.getDQIGraph.checkInput(data);
				expect(result).to.deep.equal({
					"status": true
				});
				done();
			});

			it("When either startTime OR endTime OR siteId was not present", (done) => {
				let data = _.cloneDeep(mockData.getDQIGraph.checkInput.correctInput);
				delete data.startTime;
				delete data.endTime;
				delete data.siteId;
				const result = diagnosticUtil.getDQIGraph.checkInput(data);
				expect(result).to.deep.equal({
					"status": false,
					"message": "Either startTime OR endTime OR siteID not present in payload."
				});
				done();
			});

			it("When either deviceList OR groupBy was not present", (done) => {
				let data = _.cloneDeep(mockData.getDQIGraph.checkInput.correctInput);
				delete data.deviceList;
				delete data.groupBy;
				const result = diagnosticUtil.getDQIGraph.checkInput(data);
				expect(result).to.deep.equal({
					"status": false,
					"message": "Either deviceList OR groupBy not present in payload."
				});
				done();
			});

			it("When groupyBy is nor 'Hours' nor 'Days'", (done) => {
				let data = _.cloneDeep(mockData.getDQIGraph.checkInput.correctInput);
				data.groupBy = "somethingElse";
				const result = diagnosticUtil.getDQIGraph.checkInput(data);
				expect(result).to.deep.equal({
					"status": false,
					"message": "Unrecognized groupBy"
				});
				done();
			});

		});

		describe("generateElasticsearchQuery: Generates elasticsearch query to calculate DQI.", () => {

			it("When groupBy is 'Hours'",  (done) => {
				let inputBody = _.cloneDeep(mockData.getDQIGraph.checkInput.correctInput);
				inputBody.groupBy = "Hours";
				const query = diagnosticUtil.getDQIGraph.generateElasticsearchQuery(inputBody);
				expect(query.body.aggs.dqi_over_time.date_histogram.interval).to.be.equal("1H");
				done();
			});

			it("When groupBy is 'Days'",  (done) => {
				let inputBody = _.cloneDeep(mockData.getDQIGraph.checkInput.correctInput);
				inputBody.groupBy = "Days";
				const query = diagnosticUtil.getDQIGraph.generateElasticsearchQuery(inputBody);
				expect(query.body.aggs.dqi_over_time.date_histogram.interval).to.be.equal("1D");
				done();
			});
		});

		describe("calcDQIPercentage:", () => {
			
			it("Modifies and stores the calculated DQI percentage in the data object", (done) => {
				let inputData = mockData.getDQIGraph.elasticResponse;
				let { groupBy, startTime, endTime, deviceList } = mockData.getDQIGraph.checkInput.correctInput;
				diagnosticUtil.getDQIGraph.calcDQIPercentage(inputData, groupBy, startTime, endTime, deviceList.length);

				inputData.forEach(dataPoint => {
					expect(dataPoint.doc_count).to.be.below(100);
					expect(dataPoint.doc_count).to.be.above(0);
				});
				done();
			});
		});
	});

	describe("deleteAlertRecipes", () => {

		describe("checkInput: Checks for API input parameters.", () => {

			it("When all the required parameters are present.", (done) => {
				const data = mockData.deleteAlertRecipes.checkInput;
				const response = diagnosticUtil.deleteAlertRecipes.checkInput(data);
				expect(response).to.deep.equal({
					"status": true
				});
				done();
			});

			it("When 'recipeList' is not present.", (done) => {
				let data = _.cloneDeep(mockData.deleteAlertRecipes.checkInput);
				delete data.recipeList;
				const response = diagnosticUtil.deleteAlertRecipes.checkInput(data);
				expect(response).to.deep.equal({
					"status": false,
					"message": "'recipeList' not present in body."
				});
				done();
			});
		});
	});

	describe("deleteMaintenanceInfo", () => {

		describe("checkInput: Checks for API input parameters.", () => {

			it("When all the required parameters are present.", (done) => {
				const data = mockData.deleteMaintenanceInfo.checkInput;
				const response = diagnosticUtil.deleteMaintenanceInfo.checkInput(data);
				expect(response).to.deep.equal({
					"status": true
				});
				done();
			});

			it("When 'recipeList' is not present.", (done) => {
				let data = _.cloneDeep(mockData.deleteMaintenanceInfo.checkInput);
				delete data.id;
				const response = diagnosticUtil.deleteMaintenanceInfo.checkInput(data);
				expect(response).to.deep.equal({
					"status": false,
					"message": "'id' not present."
				});
				done();
			});
		});
	});

	describe("updateMaintenanceInfo", () => {

		describe("checkInput: Checks for API input parameters.", () => {

			it("When all the required parameters are present.", (done) => {
				const data = mockData.updateMaintenanceInfo.checkInput;
				const response = diagnosticUtil.updateMaintenanceInfo.checkInput(data);
				expect(response).to.deep.equal({
					"status": true
				});
				done();
			});

			it("When 'recipeList' is not present.", (done) => {
				let data = _.cloneDeep(mockData.updateMaintenanceInfo.checkInput);
				delete data.id;
				const response = diagnosticUtil.updateMaintenanceInfo.checkInput(data);
				expect(response).to.deep.equal({
					"status": false,
					"message": "'id' not present."
				});
				done();
			});
		});
	});
});