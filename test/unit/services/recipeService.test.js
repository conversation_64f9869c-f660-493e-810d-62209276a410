const service = require("../../../api/services/RecipeService");
// const data = require("./services.config").recipeService;
// const helper = require("../../../api/services/helper");
const assert = require("chai").assert;
const sinon = require("sinon");

/* Stubs */


describe("RecipeService Test", () => {

	describe("absCron", () => {
		it("send undefined expect null", () => {
			const value = service.absCron();
			assert.isNull(value, "Return null");
		});
		it("send empty array expect empty array", () => {
			const value = service.absCron([]);
			assert.deepEqual(value, [], "invalid response");
		});
		it("send input, expect valid output", () => {
			const actual = service.absCron(["A B", "C D"]);
			const expected = [
				"* *", "* *"
			];
			assert.deepEqual(
				actual, expected, "[message]"
			);
		});
	});

	describe("test v1Parse", () => {

		let $getDidParam, makeSpotQueriesStub;

		before(() => {
			$getDidParam = sinon.stub(service, "getDidParam").resolves(
				["0000", "status"],
			);
			makeSpotQueriesStub = sinon.stub(service, "makeSpotQueries").returns(
				"0000.status.4 == 0",
			);

		});

		after(() => {
			makeSpotQueriesStub.restore();
			$getDidParam.restore();
		});

		it("undefined params, expect empty dict", () => {
			const value = service.v1Parse();
			assert.isEmpty(value, "Not Empty");
		});

		it("correct params", async () => {
			let formula = "||#1||$1||0||$2||#2||$1||0";
			let observableParams = { "#1": "0000.status", "#2": "0001" };
			let operatorsDict = { "$1": "==", "$2": "and", "$3": ")" };
			let count = 4;


			let result = await service.v1Parse(formula, observableParams, operatorsDict, count);
			let didCount = Object.keys(observableParams).length;
			assert.equal($getDidParam.callCount, Object.keys(observableParams).length);
			assert.isTrue(makeSpotQueriesStub.called);
			assert.isArray(result.dids);
			assert.equal(result.dids.length, didCount);

			assert.isArray(result.devicesToSubscribe);
			assert.equal(result.devicesToSubscribe.length, didCount);
			assert.isString(result.parsedFormula);

			assert.equal(result.parsedFormula, "0000.status.4 == 0");

		});

	});

	describe("test haveCalculatedParameter", () => {
		it("correct paramters", () => {
			let argumentObject = {
				"( 69@outputfrequency + 69@inputpower )": true,
				" ( ( 146@kva )  ( ( ( 69@waterflow )  ( ( 65@coolewt|) )  ( 65@coollwt ) ) )  24 ) ) ": true,
				" ( 65@power > 23 ) ": true,
				"( 65@power )": false,
				"( 65@power ( 65@power ) ) ": false
			};
			for (let _expression in argumentObject) {
				let expression = _expression.split(" ");
				let response = service.haveCalculatedParameter(expression);
				assert.equal(response, argumentObject[_expression], `haveCalculatedParameter should return ${argumentObject[_expression]} for ${_expression}`);
			}
		});

		it("Incorrect paramters", () => {
			let wrongArgsList = [{}, "", 1011];
			for (let wrongArg in wrongArgsList) {

				let response = service.haveCalculatedParameter(wrongArg);
				assert.equal(response, false, "An Invalid argument passed the test. ");
			}
		});
	});

	describe("test unstage", () => {
		// untestable code written
	});

	describe("test recipeAdded", () => {
		// untestable code
	});
	describe("test alert", () => {
		it("provided undefined args, expect false", () => {
			const value = service.alert();
			assert.equal(value, false, "expected false, but not found");
		});
		it("wrong topic provided", () => {
			const value = service.alert("ssh", "dummy");
			assert.equal(value, false, "expected false, but not found");
		});

	});

	describe("test makeSpotQueries", () => {
		it("correct parameters", () => {
			let argumentList = [
				{
					// recipe with calculated parameter AND Function ValueAt
					"arg": ["valueAt ( ssh_6.kw.20 ,20 ) > 222.kva.20", ["db/222/kva/20", "db/ssh_6/kw/20"], 20],
					"val": ["valueAt ( ssh_6.kw , 20) > 222.kva.20", ["db/222/kva/20", "spot/ssh_6/kw/20"]]
				},
				{	// normal recipe
					"arg": ["1002.coolewt.20  > 222.kva.20", ["db/1002/coolewt/20", "db/22/kva/20"], 20],
					"val": ["1002.coolewt.20  > 222.kva.20", ["db/1002/coolewt/20", "db/22/kva/20"]]
				},
				{
					// test for persistance and calculated parameter
					"arg": ["P ( ssh_6.kw.1 > 1,20 )", ["db/ssh_6/kw/1"], 1],
					"val": ["P ( ssh_6.kw.1 > 1,20 )", ["db/ssh_6/kw/1"]]
				}
			];
			for (let args of argumentList) {
				let passArg = args["arg"];
				let expectedResponse = args["val"];
				let response = service.makeSpotQueries(passArg[0], passArg[1], passArg[2]);

				assert.equal(
					response.replace(/ /gi, ""),
					expectedResponse[0].replace(/ /gi, ""),
					`Expected response ${expectedResponse[0]} and got ${response}`
				);
				assert.deepEqual(passArg[1], expectedResponse[1]);
			}

		});
		it("Invalid paramters", () => {
			let argumentList = [
				{ 	// shouldnt die on undefined
					"arg": [undefined, undefined, undefined],
					"val": [undefined]
				},
				{ // should not effected unexpected queries
					"arg": [123, 123, 123],
					"val": [123]
				}
			];
			for (let args of argumentList) {
				let passArg = args["arg"];
				let expectedResponse = args["val"];
				let response = service.makeSpotQueries(passArg[0], passArg[1], passArg[2]);
				assert.equal(
					expectedResponse[0],
					response,
					`Expected response ${expectedResponse[0]} and got ${response}`
				);
			}
		});

	});

	describe("test getDidParam", () => {
		let $componentFindOneStub;


		before(() => {
			$componentFindOneStub = sinon.stub(Component, "findOne").callsFake(async (param) => {
				if (!param) {
					return undefined;
				}
				return {
					"data": [
						{ "key": "kvah", "expression": "||1111@kvah||", "deviceId": "1111", "displayName": "Active Energy", "unit": "kVAh", "min": "", "max": "", "paramGroup": "voltampshrs", "dau": "kV*A*hr" },
						{ "key": "kw", "expression": "||(||(||1111@kva||)||/||(||(||(||1112@waterflow||)||*||(||(||1112@coolewt||)||-||(||1112@coollwt||)||)||)||/||24||)||)||", "deviceId": "1111,1112,1112,1112,", "displayName": "ikW/TR", "unit": "kW", "min": "", "max": "", "paramGroup": "power", "dau": "kW" }
					],
					"controls": [
						{ "key": "start", "expression": "||1112@operationalstatus||==||1||", "deviceId": "1112", "min": "0", "max": "1", "timeout": "60", "displayName": "Start", "paramGroup": "unitless", "dau": "NA" }
					],
					"deviceId": param,
					"deviceType": "chilledWaterPump",
					"driverType": "0",
					"name": "Chilled Water Pump - 1 ",
					"regionId": "bsju",
					"siteId": "xxx",
				};
			});
		});

		after(() => {
			$componentFindOneStub.restore();
		});

		it("Valid paramters", async () => {
			let argumentList = [
				{	// simple passing did, param
					"arg": ["2601", "xyz"],
					"val": ["2601", "xyz", false]
				},
				{
					// passing component_id with calculated param
					"arg": ["xxx_1", "kw"],
					"val": ["xxx_1", "kw", true]

				},
				{
					// component_id with normal parameter
					"arg": ["xxx_1", "kvah"],
					"val": ["1111", "kvah", false]
				}
			];
			for (let args of argumentList) {
				let passArg = args["arg"];
				let expectedResponse = args["val"];
				let response = await service.getDidParam(passArg[0], passArg[1]);
				assert.equal(
					response[0],
					expectedResponse[0],
					`Expected response ${expectedResponse[0]} and got ${response[0]}`
				);
				assert.equal(
					response[1],
					expectedResponse[1],
					`Expected response ${expectedResponse[1]} and got ${response[1]}`
				);
				assert.equal(
					response[2],
					expectedResponse[2],
					`Expected response ${expectedResponse[2]} and got ${response[2]}`
				);
			}
		});

		it("Invalid paramters", async () => {
			let argumentList = [
				{ // component exist , parameter donot exist
					"arg": ["xxx_1", "nonExistantParam"],
					"val": "Parameter doesnt exist"
				},
				{ // componentId doesnt exist
					"arg": ["", "nonExistantParam"],
					"val": "No device id"
				},
				{ // parameter doesnt exist
					"arg": ["xxx_1", ""],
					"val": "Parameter doesnt exist"
				}
			];
			for (let args of argumentList) {
				let passArg = args["arg"];
				try {
					await service.getDidParam(passArg[0], passArg[1]);
					throw new Error("Invliad parameter shouldnt succeed");
				} catch (e) {
					assert.equal(e, args["val"]);
				}


			}
		});

	});

	describe("test getRunOn", () => {

		let $deviceFindOne, $getCount, $getRandomRunOn;
		before(() => {
			$deviceFindOne = sinon.stub(Devices, "findOne").callsFake((param) => {
				return new Promise((res, rej) => {
					if (!param) {
						res(undefined);
					} else {
						res({
							"deviceType": "something",
							"controllerId": "0001",
						});
					}
				});
			});

			$getCount = sinon.stub(service, "getCount").resolves(
				"0001"
			);
			$getRandomRunOn = sinon.stub(service, "getRandomRunOn").resolves(
				"0002"
			);

		});

		after(() => {
			$deviceFindOne.restore();
			$getCount.restore();
			$getRandomRunOn.restore();
		});

		it("Correct params", async () => {
			let dids = ["0000", "0001", "0001"];
			let siteId = "smo-del";
			let result = parseInt(await service.getRunOn(dids, siteId));

			assert.isTrue(($getRandomRunOn.called || $getCount.called));
			assert.isNotNaN(result);
			assert.isNumber(result);
		});

		it("undefined siteId test:getRunON", async () => {
			let result = parseInt(await service.getRunOn(["0001"], undefined));
			assert.isTrue(($getRandomRunOn.called || $getCount.called));
			assert.isNotNaN(result);
			assert.isNumber(result);
		});
		it("undefined deviceId test:getRunON", async () => {
			let result = parseInt(await service.getRunOn([undefined], "smo-del"));
			assert.isTrue(($getRandomRunOn.called || $getCount.called));
			assert.isNotNaN(result);
			assert.isNumber(result);
		});

		it("wrong parameters", async () => {
			try {
				parseInt(await service.getRunOn());
				throw "Passed test without parameters!!";
			} catch (e) {
				assert.instanceOf(e, Error);
			}

		});


	});

	describe("test getRandomRunOn", () => {
		let $deviceFindOne;

		before(() => {
			$deviceFindOne = sinon.stub(Devices, "find").callsFake((param) => {
				return new Promise((res, rej) => {
					if (!param) {
						res(undefined);
					} else {
						res([{
							"deviceType": "something",
							"controllerId": "0001",
						}]);
					}
				});
			});
		});

		after(() => {
			$deviceFindOne.restore();
		});

		it("correct params", async () => {

			let result = parseInt(await service.getRandomRunOn("smo"));
			assert.isNotNaN(result);
			assert.isNumber(result);
			assert.isTrue($deviceFindOne.called);

		});
		it("no params", async () => {

			try {
				parseInt(await service.getRandomRunOn());
				throw new Error("Shouldnt end here");
			} catch (e) {
				assert.instanceOf(e, Error);
			}
		});

	});

	describe("test translatedExpressionToDevice", () => {

		it("Valid paramters", () => {
			let argumentList = [
				{
					"arg": ["||(||(||1111@kva||)||/||(||(||(||1112@waterflow||)||*||(||(||1112@coolewt||)||-||(||1112@coollwt||)||)||)||/||24||)||)||"],
					"val": ["1111", "kva"]
				},

			];
			for (let args of argumentList) {
				let passArg = args["arg"];
				let expectedResponse = args["val"];
				let response = service.translatedExpressionToDevice(passArg[0]);
				assert.deepEqual(
					response,
					expectedResponse,
					`Expected response ${expectedResponse[0]} and got ${response[0]}`
				);
			}
		});

		it("Inalid paramters", () => {
			let argumentList = [
				{
					"arg": "tomoato",
					"val": undefined
				},
				{
					"arg": [],
					"val": undefined
				}

			];
			for (let args of argumentList) {
				let passArg = args["arg"];
				let expectedResponse = args["val"];
				let response = service.translatedExpressionToDevice(passArg);
				assert.equal(
					response,
					expectedResponse,
					`Expected response ${expectedResponse} and got ${response}`
				);
			}
		});

	});

	describe("test getRecipeObj", () => {

		let $getRunOn, $v1Parse;


		it("Valid paramters", async() => {

			let defaultValidRecipeObject = {
				"siteId": "xxx"
			};

			let argumentList = [
				{ // recipe with obserable
					"arg": [
						{
							...defaultValidRecipeObject,
							"actionable": [{ "did": "1111", "command": "xxx", "value": "0", "delay": 0, "notify": ["xxx"], "accountable": ["xxx"], "parent": "xxx_1", "type": "action", "priority": 0, "uniqId": "1d60115ce12a2f8d168cc084138e0cfb", "category": ["ccc"] }],
							"isSchedule": false
						},
						"||$2||#1||$1||10||$3||",
						{ "#1": "ssh_12.kva" },
						{ "$1": ">", "$2": "(", "$3": ")" },
						2,
						0
					],
					"val": ["someformula", "1001"],  // expected returns
					"$getRunOnStubReturn": "1001",
					"$v1ParseStubReturn": {
						"dids": [],
						"devicesToSubscribe": [],
						"parsedFormula": "someformula",
						"runOnServer": false
					}

				},
				{
					// recipe without obserable
					"arg": [
						{
							...defaultValidRecipeObject,
							"actionable": [{ "did": "1111", "command": "xxx", "value": "0", "delay": 0, "notify": ["xxx"], "accountable": ["xxx"], "parent": "xxx_1", "type": "action", "priority": 0, "uniqId": "1d60115ce12a2f8d168cc084138e0cfb", "category": ["ccc"] }],
							"isSchedule": true
						},
						"",
						{ },
						{  },
						0,
						0
					],
					"val": [undefined, "1001"],  // expected returns
					"$getRunOnStubReturn": "1001",
					"$v1ParseStubReturn": { }
				}
			];
			for (let args of argumentList) {
				let passArg = args["arg"];
				let expectedResponse = args["val"];
				$getRunOn = sinon.stub(service, "getRunOn").resolves(args["$getRunOnStubReturn"]);
				$v1Parse = sinon.stub(service, "v1Parse").resolves(args["$v1ParseStubReturn"]);
				let response = await service.getRecipeObj(passArg[0], passArg[1], passArg[2], passArg[3], passArg[4]);
				assert.equal(
					response["formula"],
					expectedResponse[0],
					`Expected response ${expectedResponse[0]} and got ${response["formula"]}`
				);
				assert.equal(
					response["runOn"],
					expectedResponse[1],
					`Expected response ${expectedResponse[1]} and got ${response["runOn"]}`
				);

				$getRunOn.restore();
				$v1Parse.restore();
			}
		});

	});

	describe("test getScheduleRun", () => {
		// TODO
	});
});
