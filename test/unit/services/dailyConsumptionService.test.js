const assert = require("chai").assert;
const Config = require("./mockData/dailyConsumptionService.config");
// const should = require("chai").should();
const sinon = require("sinon");

const stubDCFindError = queryObj => {
	return new Promise((resolve, reject) => reject());
};

const stubDCFindEmptyValue = queryObj => {
	return new Promise((resolve, reject) => resolve([]));	
};

const stubDCFindFalsePositive = queryObj => {
	return new Promise((resolve, reject) => resolve(Config.valueObject));
};

describe("Testing dailyConsumptionService", () => {

	describe("Testing dailyConsumptionService.getWeekConsumption", () => {

		describe("action: stubbing DailyConsumption.find to return error", () => {

			before(() => {
				sinon.stub(DailyConsumption, "find")
					.callsFake(stubDCFindError);
			});

			after(() => DailyConsumption.find.restore());

			it("expect: resolved promise", done => {
				dailyConsumptionService.getWeekConsumption(Config.siteId)
					.then(data => {
						assert.deepEqual(data, Config.errorObject);
						done();
					})
					.catch(err => done(err));
			});
		});

		describe("action: stubbing DailyConsumption.find to return empty values", () => {

			before(() => {
				sinon.stub(DailyConsumption, "find")
					.callsFake(stubDCFindEmptyValue);
			});

			after(() => DailyConsumption.find.restore());

			it("expect: resolved promise", done => {
				dailyConsumptionService.getWeekConsumption(Config.siteId)
					.then(data => {
						assert.deepEqual(data, Config.errorObject);
						done();
					})
					.catch(err => done(err));
			});
		});

		describe("action: stubbing DailyConsumption.find to return true value", () => {

			before(() => {
				sinon.stub(DailyConsumption, "find")
					.callsFake(stubDCFindFalsePositive);
			});

			after(() => DailyConsumption.find.restore());

			it("expect: resolved promise", done => {
				dailyConsumptionService.getWeekConsumption(Config.stieId)
					.then(data => {
						data.should.have.all.keys(Config.validKeys);
						done();
					})
					.catch(err => done(err));
			});
		});
	});
});