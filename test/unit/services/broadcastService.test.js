const BroadcastService = require("../../../api/services/broadcastService");
const assert = require("chai").assert;

describe("Testing BroadcastService", () => {

	describe("Testing BroadcastService.case", () => {

		it("action: call function", done => {
			try {
				let result = BroadcastService.cast();
				assert.equal(result, undefined);
				done();
			} catch (err){
				done(err);
			}
		});
	});
});