const sinon = require("sinon");
const assert = require("chai").assert;
const mockData = require("./mockData/component/component");
describe("Component Service Test", () => {
	let compStub = null;
	let compFindOne = null;
	before(()=>{
		compStub = sinon.stub(Component, "find");
		compFindOne = sinon.stub(Component, "findOne");
	});
	describe("getComponents Test Suit", ()=>{
		it("GetComponents - valid Site Id", async () => {
			compStub.resolves(mockData.find);
			let component = await componentService.getComponents("ssh");
			assert.isArray(component);
			assert.equal(component.length, 2);
		});
		it("GetComponents : Invalid SiteId", async () =>{
			compStub.resolves([]);
			let component = await componentService.getComponents("InvalidSite");
			assert.isArray(component);
			assert.isEmpty(component);
		});
		it("GetComponents : Input sent as null", async ()=>{
			let component = await componentService.getComponents(null);
			assert.isEmpty(component);
		});
	});	
	describe("getComponent Test Suit", ()=>{
		it("GetComponent - valid Site Id", async () => {
			compFindOne.resolves(mockData.find[0]);
			let component = await componentService.getComponent("ssh_12");
			assert.isObject(component);
		});
		it("GetComponent : Invalid component Id", async () =>{
			compFindOne.returns(undefined);
			let component = await componentService.getComponent("InvalidComponent");
			assert.isUndefined(component);
		});
		it("GetComponent : Input sent as null", async ()=>{
			let component = await componentService.getComponent(null);
			assert.isUndefined(component);
		});
	});	
	describe("getComponentParamMap Test Suit", ()=>{
		let getComponentsStub = null;
		before(()=>{
			getComponentsStub = sinon.stub(componentService, "getComponents");
		});
	
		it("GetComponentParamMap : siteId provided", async ()=>{
			getComponentsStub.resolves(mockData.filteredArr);
			let map = await componentService.getComponentParamMap("validSite");
			for (let comp of mockData.filteredArr){
				let { deviceId } = comp;
				assert.exists(map[deviceId]);
			}
		});

		it("getComponentParamMap : Invalid siteId", async ()=>{
			getComponentsStub.returns([]);
			let map = await componentService.getComponentParamMap();
			assert.isEmpty(map);
		});
		after(()=>{
			getComponentsStub.restore();
		});
	});
	describe("getComponentControlKeys Test Suit", () => {
		it("valid component Id", async () => {
			compFindOne.resolves(mockData.find[0]);
			let controls = await componentService.getComponentControlKeys("validsite");
			assert.isArray(controls);
			assert.deepStrictEqual(controls, ["stop", "start"]);
		});
		it("component Id as null ", async () => {
			compFindOne.rejects("error");
			let controls = await componentService.getComponentControlKeys(null);
			assert.isArray(controls);
			assert.isEmpty(controls);
		});
	});
	describe("queryDataKeys", async()=>{
		let getComponentStub = null;
		before(()=>{
			getComponentStub = sinon.stub(componentService, "getComponent");
		});
		it("When all parameters are passed", async() => {
			getComponentStub.resolves(mockData.filteredArr[0]);
			let result = await componentService.queryDataKeys("ssh_1", {"key": "cwit"}, ["dau", "paramGroup"]);
			assert.isArray(result);
			assert.containsAllDeepKeys(result[0], ["dau", "paramGroup"]);
		});
		it("When ComponentId is invalid", async() =>{
			getComponentStub.resolves(undefined);
			let result = await componentService.queryDataKeys("invalied", {"a": "b"});
			assert.isNull(result);
		});
		it("When parameters mentioned are not present", async() =>{
			getComponentStub.resolves(mockData.filteredArr[0]);
			let result = await componentService.queryDataKeys("ssh_1", {"tada": "cwit"}, ["dau", "paramGroup"]);
			assert.isEmpty(result);
		});
		after(()=>{
			getComponentStub.restore();
		});

	});
	after(()=>{
		compStub.restore();
		compFindOne.restore();
	});
});