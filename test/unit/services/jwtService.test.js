const assert = require("chai").assert;
const Config = require("./mockData/jwtService.config");
// const should = require("chai").should();
const sinon = require("sinon");
const jwt = require("jsonwebtoken");

const stubJWTSignError = (payload, secret, params) => {
	throw new Error("This is an error");
};

const stubJWTVerifyError = (token, secret, options, callback) => {
	throw new Error("This is an error");
};

const stubJWTVerifyObject = (token, secret, options, callback) => {
	return Config.valueObject;
};

const stubJWTDecodeError = token => {
	throw new Error("This is an error");
};

const stubJWTDecodeObject = token => {
	return Config.valueObject;
};

describe("Testing jwtService", () => {

	describe("Testing jwtService.issue", () => {

		it("action: stubbing jwt.sign to throw error", async() => {
			
			// stubbing	
			sinon.stub(jwt, "sign")
				.callsFake(stubJWTSignError);

			try {
				await jwtService.issue(Config.payload);
			} catch (err){
				assert.isDefined(err);
			}

			// restoring
			jwt.sign.restore();
		});

		it("action: passing valid params, expect: object", async() => {
			try {
				let result = await jwtService.issue(Config.payload);
				assert.isDefined(result);
				assert.isString(result);
			} catch (err){
				throw "Test has failed";
			}
		});
	});

	describe("Testing jwtService.verify", () => {

		describe("action: stubbing jwt.verify to throw error", () => {

			before(() => {
				sinon.stub(jwt, "verify")
					.callsFake(stubJWTVerifyError);
			});

			after(() => jwt.verify.restore());

			it("expect: error", async() => {
				try {
					await jwtService.verify();
				} catch (err){
					assert.isDefined(err);
				}
			});
		});

		describe("action: stubbing jwt.verify to output object", () => {

			before(() => {
				sinon.stub(jwt, "verify")
					.callsFake(stubJWTVerifyObject);
			});

			after(() => jwt.verify.restore());

			it("expect: object", async() => {
				try {
					let result = await jwtService.verify();
					assert.deepEqual(result, Config.valueObject);
				} catch (err){throw "Test has failed";}
			});
		});
	});

	describe("Testing jwtService.decode", () => {

		describe("action: stubbing jwt.decode to return error", () => {

			before(() => {
				sinon.stub(jwt, "decode")
					.callsFake(stubJWTDecodeError);
			});

			after(() => jwt.decode.restore());

			it("expect: error", async() => {
				try {
					jwtService.decode(Config.token);
				} catch (err){assert.isDefined(err);}
			});
		});

		describe("action: stubbing jwt.decode to return object", () => {

			before(() => {
				sinon.stub(jwt, "decode")
					.callsFake(stubJWTDecodeObject);
			});

			after(() => jwt.decode.restore());

			it("expect: object", async() => {
				try {
					let result = jwtService.decode(Config.token);
					assert.deepEqual(result, Config.valueObject);
				} catch (err){throw "Test has failed";}
			});
		});
	});

	describe("Testing jwtService.generateRandomString", () => {

		it("action: passing string, expect: empty string", async() =>{
			try {
				let result = jwtService.generateRandomString("random");
				assert.isString(result);
				assert.isEmpty(result);
			} catch (err){throw "Test has failed";}
		});

		it("action: passing undefined, expect empty string", async() => {
			try {
				let result = jwtService.generateRandomString();
				assert.isString(result);
				assert.isEmpty(result);
			} catch (err){throw "Test has failed";}
		});

		it("action: passing integer, expect random string", async() => {
			try {
				let result = jwtService.generateRandomString(5);
				assert.isString(result);
				assert.deepEqual(result.length, 5);
			} catch (err){throw "Test has failed";}
		});
	});

});