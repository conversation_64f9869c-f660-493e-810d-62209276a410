const service = require("../../../api/services/parameterService");
const assert = require("chai").assert;

/* Stubs */

describe("parameterService Test", () => {
	describe("getLastOfAll2 tests", () => {
		it("send data, expect", async () => {
			const promise1 = new Promise((resolve) => {
				return resolve([]);
			});
			const promise2 = new Promise((resolve) => {
				return resolve([]);
			});
			const actual = await service.getLastOfAll2([promise1, promise2], "dummyComp", "dummyType")
				.then(res => {
					return res;
				})
				.catch(err => {
					return undefined;
				});
			const expected = 0;
			assert.deepEqual(actual, expected, "[message]");
		});
	});

	describe("getLastOfAll tests", () => {
		it("send data, expect response", async () => {
			const promise1 = new Promise((resolve) => {
				return resolve();
			});
			const promise2 = new Promise((resolve) => {
				return resolve();
			});
			const actual = await service.getLastOfAll([promise1, promise2], "dummyComp")
				.then(res => {
					return res;
				})
				.catch(err => {
					return undefined;
				});
			const expected = 0;
			assert.deepEqual(actual, expected, "[message]");
		});
	});

	describe("pidSaveJB tests", () => {
		// no tests could be written
	});
});
