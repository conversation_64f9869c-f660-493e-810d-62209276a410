const assert = require("chai").assert;
const data = require("./mockData/mockData");

describe("Version Utils", () => {
	it("addVersion > populateDataObj. When deviceType == controller", (done) => {
		let inputParams = data.addVersion;
		let returnData = versionUtil.addVersion.populateDataObj(inputParams);
		assert.property(returnData, "version");
		assert.property(returnData, "deviceType");
		assert.property(returnData, "gitRepoCICD");
		assert.property(returnData, "gitRepoApplication");
		assert.property(returnData, "gitRepoFirmware");
		assert.property(returnData, "gitRepoHostServices");
		assert.property(returnData, "gitRepoJouleBox");
		assert.property(returnData, "gitRepoRoutingService");
		assert.property(returnData, "dockerApplication");
		assert.property(returnData, "dockerFirmware");
		assert.property(returnData, "dockerJouleBox");
		assert.property(returnData, "repoList");
		done();
	});
});