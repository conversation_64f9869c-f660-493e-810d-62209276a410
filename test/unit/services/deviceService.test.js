const assert = require("chai").assert;
let sinon = require("sinon");
const data = require("./services.config").deviceService;

describe("deviceService Testing", () => {
	let deviceFind_ = null;
	before(() => {
		deviceFind_ = sinon.stub(Devices, "find");
		deviceFind_.withArgs("validSite").returns(data.devices);
		deviceFind_.withArgs([undefined, null]).throws("Invalid Parameters");
		deviceFind_.withArgs("invalidSite").returns([]);
		deviceFind_.returns(data.devices);
	});
	after(()=>{
		deviceFind_.restore();
	});
	it("getDevicesInRegion giving fake Data for DB Query", async () => {
		let retVal = await deviceService.getDevicesInRegion("mgch", "fulq");
		assert.isArray(retVal);
		assert.isNotEmpty(retVal);
	});

	it("getAvailableSlaveIds : device list", async () => {
		// function stubFind() {
		// 	return data.getAvailableSlaveIds.db;
		// }
		// let devStub_ = sinon.stub(Devices, "find").callsFake(stubFind);
		let returnVal = await deviceService.getAvailableSlaveIds(
			data.getAvailableSlaveIds
		);
		assert.isNotEmpty(returnVal);
		// devStub_.restore();
	});

	it("getAvailableSlaveIds : err goes in catch", async () => {
		let returnVal = await deviceService.getAvailableSlaveIds(
			data.getAvailableSlaveIds_err
		);
		assert.isEmpty(returnVal);
	});

	it("updateComponentInfoInDevices size is 0", async () => {
		let returnVal = await deviceService.updateComponentInfoInDevices(
			data.updateComponentInfoInDevices_err.added,
			data.updateComponentInfoInDevices_err.removed,
			data.updateComponentInfoInDevices_err.componentId,
			data.updateComponentInfoInDevices_err.siteId
		);
		assert.isArray(returnVal);
		assert.isEmpty(returnVal);
	});

	it.skip("updateComponentInfoInDevices stubbing update and notifyJouleTrack", async () => {
		function stubUpdate() {
			return new Promise((resolve, reject) => {
				resolve(false);
			});
		}
		function stubNotifyJouleTrack() {
			return true;
		}
		sinon.stub(Devices, "update").callsFake(stubUpdate);
		sinon
			.stub(eventService, "notifyJouleTrack")
			.callsFake(stubNotifyJouleTrack);
		let returnVal = await deviceService.updateComponentInfoInDevices(
			data.updateComponentInfoInDevices.added,
			data.updateComponentInfoInDevices.removed,
			data.updateComponentInfoInDevices.componentId,
			data.updateComponentInfoInDevices.siteId
		);
		assert.isArray(returnVal);
		assert.equal(returnVal.length, 4);
		eventService.notifyJouleTrack.restore();
		Devices.update.restore();
	});
	it("GetDevices : validSiteId", async () => {
		let devices = await deviceService.getDevices("validSite");
		assert.isArray(devices);
		assert.isNotEmpty(devices);
	});
	it("GetDevices : validSited and returnMap as true", async () => {
		let deviceMap = await deviceService.getDevices("validSite", true);
		assert.isObject(deviceMap);
		assert.isNotEmpty(deviceMap);
	});
});
