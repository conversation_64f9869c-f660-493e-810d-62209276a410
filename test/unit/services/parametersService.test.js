const service = require("../../../api/services/parametersService");
const assert = require("chai").assert;
const expect = require("chai").expect;
const sinon = require("sinon");
const mockData = require("./mockData/deviceTypes");
describe("ParametersService Test Suite", () => {
	describe("addDeviceParameters", async () => {
		let getParams_ = null;
		let paramCreate_ = null;
		before(()=>{
			getParams_= sinon.stub(deviceTypeService, "getParameters");
			paramCreate_ = sinon.stub(Parameters, "create").resolves({});
		});
		it("When one of the input parameters is not valid it should return false ", async () => {
			let out = await service.addDeviceParameters(null, "0", "10", "abc");
			assert.equal(out, false);
			out = await service.addDeviceParameters("x", null, "c", "df");
			assert.equal(out, false);
			out = await service.addDeviceParameters("p", "q", null, "df");
			assert.equal(out, false);
			//console.log(out);
			return;
		});

		it("When deviceType does not exist in table", async () => {
			// let fake = sinon.fake.returns("false");
			getParams_.callsFake(() => false);
			let out = await service.addDeviceParameters("x", "null", "c", "d");
			expect(out).to.be.false;
			return;
		});
		it("For correct deviceType and driverType, it should return true", async ()=>{
			getParams_.resolves(mockData.service_getParams);
			let out = await service.addDeviceParameters("dtype", "driver", "112", "abc");
			expect(out).to.be.true;
		});
		after(()=>{
			getParams_.restore();
			paramCreate_.restore();
		});
	});
	

	describe("paramGroup", () => {
		it("when parameters are null", async () => {
			const parameterA = await service.getParamGroup("", "b", "c");
			const parameterB = await service.getParamGroup("a", "", "c");
			const parameterC = await service.getParamGroup("a", "b", "");
			assert.isNull(parameterA);
			assert.isNull(parameterB);
			assert.isNull(parameterC);
		});
		it("when params are correct", async () => {
			sinon.stub(Parameters, "findOne").callsFake(() => {
				return {
					"abbr": "head",
					"deviceId": "2248",
					"deviceId_abbr": "2248_head",
					"displayName": "Head",
					"paramGroup": "head"
				};
			});
			const parameter = await service.getParamGroup(
				"mgch",
				"2248",
				"head"
			);
			assert.equal(parameter["paramGroup"], "head");
			assert.equal(parameter["abbr"], "head");
			Parameters.findOne.restore();
		});
	});
});
