const expect = require("chai").expect;
const dashboardUtil = require("../../../api/utils/dashboardUtil");
const mockData = require("../../testData/dashboardController/consumptionExcel");

describe("Dashboard Utils >> ", () => {
	describe("consumptionExcel: Generates Excel sheet for RAMA's Consumption Data", () => {
		it("generateTimeObj", async () => {
			const generateTimeObj = dashboardUtil.consumptionExcel.generateTimeObj;
			const { query } = mockData;
			let timeObj = generateTimeObj(query);
			expect(timeObj).to.have.property("lastMonth");
			expect(timeObj).to.have.property("lastWeek");
			expect(timeObj).to.have.property("selectedTime");
			expect(timeObj).to.have.property("yesterday");
		});

		it.skip("getConsumption", async () => {
			const getConsumption = dashboardUtil.consumptionExcel.getConsumption;
			const { emList, timeObj } = mockData;
			let consumptionData = await getConsumption(emList, timeObj);
			expect(consumptionData).to.not.be.undefined;
		});

		it("generateDeviceTree");
		it("simplify");
		it.skip("generateExcel", async () => {
			const generateExcel = dashboardUtil.consumptionExcel.generateExcel;
			const { deviceTree, simplifiedConsumption } = mockData;
			const responseData = await generateExcel(deviceTree, simplifiedConsumption);
			expect(responseData).to.not.be.undefined;
		});

	});
});