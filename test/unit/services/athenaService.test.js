const assert = require("chai").assert;
const data = require("./mockData/mockData");
let sinon = require("sinon");
// var athServ = require("../../../api/services/athenaService");
describe("Athena Service Test", ()=>{

	it("startQuery", async()=>{

		function fakeStartQueryExec(params, callback){
			assert.property(params, "QueryString", "Done");
			assert.property(params, "ResultConfiguration", "Done");
			assert.property(params.ResultConfiguration, "OutputLocation", "Done");
			assert.equal(params.ResultConfiguration.OutputLocation, "s3://smartjoules-athena/", "Equal");
			assert.property(params, "QueryExecutionContext", "Done");
			assert.equal(params.QueryExecutionContext.Database, "dejoule", "Equal");
			callback(undefined, true);
			return true;
		}
		// var sandBox = sinon.sandbox.create();
		sinon.stub(athenaService.athena, "startQueryExecution").callsFake(fakeStartQueryExec);
		await athenaService.startQuery(data.startQuery, "dejoule");
		athenaService.athena.startQueryExecution.restore();
	});

	it("getQueryExecution", async()=>{

		function fakeGetQueryExec(params, callback){
			assert.property(params, "QueryExecutionId", "Done");
			assert.typeOf(params.QueryExecutionId, "string", "Yup! It's Here");
			callback(undefined, true);
			return true;
		}

		sinon.stub(athenaService.athena, "getQueryExecution").callsFake(fakeGetQueryExec);
		athenaService.getQueryExecution("c559d935-7060-46b5-a09d-3955fdb23fa3");
		athenaService.athena.getQueryExecution.restore();
	});

	it("stopQueryExecution", async()=>{

		function fakeStopQueryExec(params, callback){
			assert.property(params, "QueryExecutionId", "Done");
			assert.typeOf(params.QueryExecutionId, "string", "Yup! It's Here");
			callback(undefined, true);
			return true;
		}

		sinon.stub(athenaService.athena, "stopQueryExecution").callsFake(fakeStopQueryExec);
		athenaService.stopQueryExecution("c559d935-7060-46b5-a09d-3955fdb23fa3");
		athenaService.athena.stopQueryExecution.restore();
	});

	it("listQueryExecutions", async()=>{

		function fakeListQueryExecutions(params, callback){
			assert.equal(params, "object", "Yup! It's a JSON");
			callback(undefined, true);
			return true;
		}

		sinon.stub(athenaService.athena, "listQueryExecutions").callsFake(fakeListQueryExecutions);
		athenaService.listQueryExecutions();
		athenaService.athena.listQueryExecutions.restore();
	});

	it("batchCreatePartition", async()=>{

		function fakeBatchCreatePartition(params, callback){
			assert.property(params, "DatabaseName", "Done");
			assert.property(params, "TableName", "Done");
			assert.property(params, "PartitionInputList", "Done");
			assert.equal(params.DatabaseName, "athenatest", "Equal");
			assert.equal(params.TableName, "datadevicestemp2", "Equal");
			assert.isArray(params.PartitionInputList, "Array is here");
			callback(undefined, true);
			return true;
		}

		sinon.stub(athenaService.glue, "batchCreatePartition").callsFake(fakeBatchCreatePartition);
		await athenaService.batchCreatePartition({});
		athenaService.glue.batchCreatePartition.restore();
	});

	it("deletePartition", async()=>{

		function fakeDeletePartition(params, callback){
			assert.property(params, "DatabaseName", "Done");
			assert.property(params, "PartitionValues", "Done");
			assert.property(params, "TableName", "Done");
			callback(undefined, true);
			return true;
		}

		sinon.stub(athenaService.glue, "deletePartition").callsFake(fakeDeletePartition);
		await athenaService.deletePartition("dejoule", "datadevicestemp2", ["ssh", "1058", "2018-10-17"]);
		athenaService.glue.deletePartition.restore();
	});

	it("getQueryResultsParts", async()=>{

		function fakeGetQueryResults(param, callback){
			assert.property(param, "QueryExecutionId", "Done");
			assert.property(param, "NextToken", "Done");
			callback(undefined, true);
			return true;
		}

		sinon.stub(athenaService.athena, "getQueryResults").callsFake(fakeGetQueryResults);
		athenaService.getQueryResultsParts("c559d935-7060-46b5-a09d-3955fdb23fa3", undefined);
		athenaService.athena.getQueryResults.restore();
	});

	it("getTable", async()=>{

		function fakeGetTable(param, callback){
			assert.property(param, "DatabaseName", "Done");
			assert.property(param, "Name", "Done");
			callback(undefined, true);
			return true;
		}

		sinon.stub(athenaService.glue, "getTable").callsFake(fakeGetTable);
		await athenaService.getTable("dejoule", "datadevicestemp2");
		athenaService.glue.getTable.restore();
	});

	it("getPartitions", async()=>{

		function stubGetPartitions(param, callback){
			assert.property(param, "DatabaseName");
			assert.property(param, "TableName");
			callback(undefined, true);
		}

		sinon.stub(athenaService.glue, "getPartitions").callsFake(stubGetPartitions);
		await athenaService.getPartitions("dejoule", "datadevicestemp2");
		athenaService.glue.getPartitions.restore();
	});

	it("getPartition", async()=>{

		function fakeGetPartition(param, callback){
			assert.property(param, "DatabaseName", "Done");
			assert.property(param, "TableName", "Done");
			assert.property(param, "PartitionValues", "Done");
			callback(undefined, true);
			return true;
		}

		sinon.stub(athenaService.glue, "getPartition").callsFake(fakeGetPartition);
		await athenaService.getPartition("dejoule", "datadevicestemp2", ["ssh", "1058", "2018-10-17"]);
		athenaService.glue.getPartition.restore();
	});

	it("putOject", ()=>{

		function stub_putObject(param, callback){
			assert.property(param, "Body", "Done");
			assert.property(param, "Bucket", "Done");
			assert.equal(param["Bucket"], "datadevicestemp2", "Equal");
			assert.property(param, "Key", "Done");
			assert.equal(param["Key"], "ssh/1058/2018-10-15/00:00:00", "Equal");
			callback(undefined, true);
			return true;
		}

		sinon.stub(athenaService.s3, "putObject").callsFake(stub_putObject);
		athenaService.putOject({});
		athenaService.s3.putObject.restore();
	});
});
