const assert = require("chai").assert;
const data = require("./mockData/mockData");
const dv = require("./services.config");
const sinon = require("sinon");
const axios = require("axios");
describe("EventServiceTest", ()=>{

	it("unsubscribe", async()=>{
		function stubPost(){
			return true;
		}
		sinon.stub(axios, "post").callsFake(stubPost);
		let returnData = await eventService.unsubscribe(data.topics);
		assert.isBoolean(returnData);
		axios.post.restore();
	});

	it("unsubscribe_noTopic", async()=>{
		let returnData = await eventService.unsubscribe(false);
		assert.equal(returnData, undefined);
	});


	it("subscribe", async()=>{
		function stubPost(){
			return true;
		}
		sinon.stub(axios, "post").callsFake(stubPost);
		let returnData = await eventService.subscribe(data.topics);
		assert.isBoolean(returnData);
		axios.post.restore();
	});

	it("subscribe_noTopic", async()=>{
		let returnData = await eventService.subscribe(false);
		assert.equal(returnData, undefined);
	});

	it("publish_noTopic", async()=>{
		let returnData = await eventService.publish(false);
		assert.equal(returnData, undefined);
	});

	it("publish", async()=>{
		function stubPost(){
			return true;
		}
		sinon.stub(axios, "post").callsFake(stubPost);
		let returnData = await eventService.publish(data.publishTopic, JSON.stringify(data.publishMsg));
		assert.isBoolean(returnData);
		axios.post.restore();
	});

	it("notifyUser", ()=>{
		// console.log("untestable code");
	});

	it("notifyUser called with No key", async()=>{
		let retval = await eventService.notifyUser();
		assert.isUndefined(retval);
	});

	// it("notifyJouleTrack No Data ", ()=>{
	// 	let retVal = eventService.notifyJouleTrack();
	// 	assert.isUndefined(retVal);
	// });

	it("notifyJouleTrack", ()=>{
		function stubBroadcast(socketId, topic, data){
			assert.isString(socketId);
			assert.isString(topic);
			assert.typeOf(data, "object");
		}
		sinon.stub(sails.sockets, "broadcast").callsFake(stubBroadcast); 
		eventService.notifyJouleTrack(data.updateFirmwareJouleSense.siteId, "configs", dv.eventService.topic, {});
		sails.sockets.broadcast.restore();
	});

	it("joinJouleTrackList NoRole", ()=>{
		let returnVal= eventService.joinJouleTrackList();
		assert.isEmpty(returnVal);
	});

	it("joinJouleTrackList checking data in sockets.join", ()=>{
		function stubJoin(socketId, a){
			assert.isString(socketId);
			assert.isString(a);
		}
		sinon.stub(sails.sockets, "join").callsFake(stubJoin);
		eventService.joinJouleTrackList(2133, "mgch", "admin");
		sails.sockets.join.restore();
	});
});
