/* eslint-disable no-console */
const assert = require("chai").assert;
let sinon = require("sinon");
//const http = require("http");
const data = require("./services.config").dataDeviceService;

describe("dataDeviceService Test", () => {

	it("getWeekConsumption", () => {
		console.log("Untestable Code");
	});

	it("getAllDataObjects", async () => {
		function stubFindOne(params) {
			return params;
		}
		function stubDataForRecentData(params) {
			return params;
		}
		sinon.stub(Component, "findOne").callsFake(stubFindOne);
		sinon.stub(dataDeviceService, "dataForRecentData").callsFake(stubDataForRecentData);
		let returnVal = await dataDeviceService.getAllDataObjects("ssh", "ssh_1");
		assert.equal(returnVal.component["deviceId"], "ssh_1");
		assert.equal(returnVal.nowData.where["deviceId"], "ssh_1");
		assert.equal(returnVal.dayData.where["deviceId"], "ssh_1");
		assert.equal(returnVal.weekData.where["deviceId"], "ssh_1");
		assert.equal(returnVal.monthData.where["deviceId"], "ssh_1");
		dataDeviceService.dataForRecentData.restore();
		Component.findOne.restore();
	});


	it.skip("dataForRecentData length is zero", async () => {
		let retObj = {
			"promise": function () {
				return new Promise(data.dbReturn);
			}
		};
		let fake = sinon.fake.returns(retObj);
		sinon.replace(sails.config.helper.dynamo, "query", fake);

		let returnVal = await dataDeviceService.dataForRecentData(data.dataForRecentData);
		assert.isObject(returnVal);
		// sails.config.helper.dynamo.query.restore();
		sinon.restore();
	});

	it.skip("dataForRecentData length is one", async () => {
		let returnVal = await dataDeviceService.dataForRecentData(data.dataForRecentData2);
		assert.equal(returnVal.length, 1);
	});

	it("saveCurrentTimestampInCache", () => {
		console.log("Untestable Code");
	});

	it("saveLoadTimestamp", () => {
		console.log("Untestable Code");
	});


	it("getLastDataPoint", () => {
		function stubFindOne() {
			return true;
		}
		sinon.stub(Datadevice, "findOne").callsFake(stubFindOne);
		let retVal = dataDeviceService.getLastDataPoint("ID", 12233);
		retVal.then(result => {
			assert.isTrue(result);
		});
		Datadevice.findOne.restore();
	});


	it("getDeviceTypeDataByRegion stubbing getDevicesInRegion", async () => {
		const stubGetDevicesInRegion = () => {
			return new Promise((resolve, reject) => {
				resolve([{
					"deviceType": "Controller",
					"name": "Name is Enough",
					"deviceId": "47",
				}]);
			});
		};
		const stubGetLastDataPoint = (devId, moment) => {
			return new Promise((resolve, reject) => {
				resolve({
					"data": {
						"TMP": 12,
						"HUM": 33
					},
					"timestamp": "3232323"
				});
			});
		};
		sinon.stub(deviceService, "getDevicesInRegion").callsFake(stubGetDevicesInRegion);
		sinon.stub(dataDeviceService, "getLastDataPoint").callsFake(stubGetLastDataPoint);
		let retVal = await dataDeviceService.getDeviceTypeDataByRegion(47, 2122, "Controller");
		assert.deepEqual(retVal.jouleSense[0].name, "Name is Enough");
		assert.deepEqual(retVal.jouleSense[0]["deviceId"], "47");
		assert.deepEqual(retVal.jouleSense[0]["deviceType"], "Controller");
		assert.deepEqual(retVal.jouleSense[0]["tmp"], 12);
		assert.deepEqual(retVal.jouleSense[0]["hum"], 33);
		deviceService.getDevicesInRegion.restore();
		dataDeviceService.getLastDataPoint.restore();
	});

	it("filterY", () => {
		function stubTruncateNumber() {
			return true;
		}
		sinon.stub(helper, "returnFilteredNumber").callsFake(stubTruncateNumber);
		let retVal = dataDeviceService.filterY(data.filterY.response, data.filterY.yAxis);
		assert.isObject(retVal);
		assert.isTrue(retVal[0]);
		helper.returnFilteredNumber.restore();
	});

	it("truncateNumber num is undefined", () => {
		let retVal = dataDeviceService.truncateNumber();
		assert.isNull(retVal);
	});

	it("truncateNumber num is NaN", () => {
		let retVal = dataDeviceService.truncateNumber("Number");
		assert.isNull(retVal);
	});

	it("truncateNumber num >0 and < 1", () => {
		let retVal = dataDeviceService.truncateNumber("0.75");
		assert.isNumber(retVal);
	});

	it("truncateNumber num >1 and < 10", () => {
		let retVal = dataDeviceService.truncateNumber("9");
		assert.isNumber(retVal);
	});

	it("truncateNumber num with no limits", () => {
		let retVal = dataDeviceService.truncateNumber("129");
		assert.isNumber(retVal);
	});

	it("filterData", () => {
		console.log("Untestable Right Now Too much dependency on Data");
	});

	it("generateSearchObj to is smaller than from", () => {
		let retVal = dataDeviceService.generateSearchObj(data.generateSearchObj.state1.devId, data.generateSearchObj.state1.from, data.generateSearchObj.state1.to);
		assert.isUndefined(retVal);
	});

	it("generateSearchObj to isn't smaller than from", () => {
		let retVal = dataDeviceService.generateSearchObj(data.generateSearchObj.state2.devId, data.generateSearchObj.state2.from, data.generateSearchObj.state2.to);
		assert.property(retVal, "where");
		assert.property(retVal.where, "deviceId");
		assert.property(retVal.where, "timestamp");
		assert.property(retVal.where.timestamp, "between");
		assert.equal(retVal.where.timestamp["between"].length, 2);
		assert.isObject(retVal.where["timestamp"]);
		assert.isObject(retVal.where);
	});

	it("generateSearchObj has no to & from", () => {
		let retVal = dataDeviceService.generateSearchObj(data.generateSearchObj.state2.devId);
		assert.property(retVal, "where");
		assert.property(retVal.where, "deviceId");
		assert.property(retVal, "limit");
		assert.property(retVal, "sort");
		assert.equal(retVal.sort, "-1");
		assert.equal(retVal.limit, 100);
		assert.isObject(retVal.where);
	});

	it("generateSearchObj has no to & has from", () => {
		let retVal = dataDeviceService.generateSearchObj(data.generateSearchObj.state2.devId, data.generateSearchObj.state2.from);
		assert.property(retVal, "where");
		assert.property(retVal.where, "deviceId");
		assert.property(retVal.where, "timestamp");
		assert.property(retVal.where.timestamp, "gte");
		assert.property(retVal, "sort");
		assert.equal(retVal.sort, "-1");
		assert.isObject(retVal.where);
		assert.isObject(retVal.where.timestamp);
	});
});
