const assert = require("chai").assert;
const dv = require("./services.config").diagnosticsService;
const sinon = require("sinon");
const axios = require("axios");

describe("Diagnostic Service", ()=>{

	it("sshCommands", ()=>{
		let returnData = diagnosticService.sshCommands(4566);
		assert.property(returnData, "command1");
		assert.property(returnData, "command2");
		assert.typeOf(returnData, "object", "Yup! It's JSON");
	});

	it("err_sshCommands", ()=>{
		let returnData = diagnosticService.sshCommands();
		assert.equal(returnData.command1, "Port number not stored in DB.");
		assert.equal(returnData.command2, "Port number not stored in DB.");
	});

	it("cachingUpdatingDevices: When params is not complete", async()=>{
		let returnData = await diagnosticService.cachingUpdatingDevices();
		assert.property(returnData, "err");
	});

	it("checkUpdate:  when params aren't complete", async ()=>{
		let data = await diagnosticService.checkUpdate(dv.checkUpdate.topic);
		assert.equal(data.err, "ERROR!!");
	});

	it("checkUpdate:  when currentVersion and expectedVersion is same", async ()=>{
		let stubFind = () => {
			return new Promise((resolve, reject)=>{
				resolve([
					{
						"expectedVersion": "1.0.1"
					}
				]);
			});
		};
		sinon.stub(Devices, "find").callsFake(stubFind);
		let data = await diagnosticService.checkUpdate(dv.checkUpdate.topic, dv.checkUpdate.payload);
		assert.equal(data.status, "Device is upto date");
		Devices.find.restore();
	});

	// it.only("checkUpdate", async()=>{
	// 	let stubFind = ()=>{
	// 		return new Promise((resolve, reject)=>{
	// 			resolve([
	// 				{
	// 					"expectedVersion" : "1.0.2"
	// 				}
	// 			]);
	// 		});
	// 	}
	// 	let stubCheckUpdateCallBack = ()=>{
	// 		console.log("Here2")
	// 		return {"status": "Stubbed"};
	// 	}
	// 	sinon.stub(Devices, "find").callsFake(stubFind);
	// 	sinon.stub(diagnosticService, "checkUpdateCallBack").callsFake(stubCheckUpdateCallBack);
	// 	let response =  await diagnosticService.checkUpdate(dv.checkUpdate.topic, dv.checkUpdate.payload);
	// 	console.log(response)
	// 	Devices.find.restore();
	// 	checkUpdateCallBack.restore();
	// });

	it("generatePayloadForJouleSense: to check publish() is called or not", ()=>{
		let stubPublish = ()=>{
			return "I don't care what is published";
		};
		sinon.stub(eventService, "publish").callsFake(stubPublish);
		let response = diagnosticService.generatePayloadForJouleSense("12345", "1.0.2", ["12345"], "true", "key");
		assert.property(response,  "status");
		eventService.publish.restore();
	});

	it("generatePayloadForJouleSense: when params aren't complete", ()=>{
		let returnData = diagnosticService.generatePayloadForJouleSense("12345", ["12345"], "1.0.2");
		assert.property(returnData, "err");
	});

	it("generatePayloadForController: when params aren't complete", async()=>{
		let data =  await diagnosticService.generatePayloadForController();
		assert.property(data, "err");
	});

	it("checkUpdateCallBack: when params aren't complete", async ()=>{
		let data = await diagnosticService.checkUpdateCallBack();
		assert.property(data, "err");
	});

	it("updateAPICallBack: when params aren't complete", async ()=>{
		let data = await diagnosticService.updateAPICallBack();
		assert.property(data, "err");
	});

	it("updateAPICallBack: when oldVersion and newVersion is equal", async()=>{
		let response = await diagnosticService.updateAPICallBack({"currentVersion": "1.0.2"}, "1.0.2", "key", "true", [], []);
		assert.property(response, "status");
	});
	
	// it("updateAPICallBack", ()=>{
	// 	let stubFind = ()=>{
	// 		return new Promise((resolve, reject)=>{
	// 			resolve([data.updateManyFirmwareNonJouleSense.arr[0]]);
	// 		});
	// 	}
	// 	let stubGeneratePayloadForController = ()=>{
	// 		console.log("I don't care what is generated");
	// 	}
	// 	sinon.stub(Devices, "find").callsFake(stubFind);
	// 	sinon.stub(diagnosticService, "generatePayloadForController").callsFake(stubGeneratePayloadForController);
	// 	diagnosticService.updateAPICallBack("12345", "1.0.2", "key", "true");
	// 	Devices.find.restore();
	// 	diagnosticService.generatePayloadForController.restore();
	// });

	it("diagnosticsResponseFeedback", ()=>{
		function stubNotifyJouleTrack(siteId, diag, diagno, payload){
			assert.equal(siteId, "command");
			assert.equal(diagno, "diagnostics");
			assert.equal(diag, "diagnostics");
			assert.property(payload, "param");
			assert.property(payload, "value");
			assert.property(payload, "operation");
			assert.equal(payload.operation, "js_update");
		}
		function stubNotifyUser(socketId, dia, payload){
			assert.isUndefined(socketId);
			assert.equal(dia, "diagnostics");
			assert.equal(payload.param, "firmware");
			assert.equal(payload.deviceId, "cicd");
			assert.equal(payload.operation, "js_update");
		}
		sinon.stub(eventService, "notifyJouleTrack").callsFake(stubNotifyJouleTrack);
		sinon.stub(eventService, "notifyUser").callsFake(stubNotifyUser);
		diagnosticService.diagnosticsResponseFeedback(dv.diagnosticsResponseFeedback.topic, dv.diagnosticsResponseFeedback.payload);
		eventService.notifyUser.restore();
		eventService.notifyJouleTrack.restore();
	});

	it("jouleSenseFeedback has socketId", ()=>{
		function stubNotifyUser(){
			return true;
		}
		sinon.stub(eventService, "notifyUser").callsFake(stubNotifyUser);
		let retVal = diagnosticService.jouleSenseFeedback(dv.jouleSenseFeedback.topic, dv.jouleSenseFeedback.payload);
		assert.isUndefined(retVal);
		eventService.notifyUser.restore();
	});

	it("jouleSenseFeedback has No socketId", async()=>{
		function stubNotifyJouleTrack(){
			return true;
		}
		sinon.stub(eventService, "notifyJouleTrack").callsFake(stubNotifyJouleTrack);
		let retVal = await diagnosticService.jouleSenseFeedback(dv.jouleSenseFeedback2.topic, dv.jouleSenseFeedback2.payload);
		assert.isUndefined(retVal);
		eventService.notifyJouleTrack.restore();
	});

	it("getJiraTaskID has No Params", async()=>{
		let retVal = await diagnosticService.getJiraTaskID();
		assert.property(retVal, "err");
	});

	it("getJiraTaskID has Params", async()=>{
		function stubGetIOParams(){
			return [];
		}
		function stubPost(url, body){
			assert.isString(url);
			assert.isObject(body);
			assert.property(body, "Suggested By");
			assert.property(body, "Sheet_Id");
			assert.property(body, "SheetName");
			assert.property(body, "Summary");
			assert.property(body, "Description");
			assert.property(body, "PC_REGION");
			assert.property(body, "PC_ASSET");
			assert.property(body, "PC_READ_WRITE");
			assert.property(body, "PC_DATE_IDENTIFICATION");
			assert.property(body, "PC_TIME_IDENTIFICATION");
			assert.property(body, "PC_TIME_INSPECTION");
			assert.property(body, "PC_DATE_INSPECTION");
			assert.property(body, "PC_POC_SITE");
			assert.property(body, "PC_VALUE_ON_JT");
			assert.property(body, "PC_VALUE_EXPECTED");
			assert.property(body, "PC_VALUE_PHYSICAL_INSPECTION");
			assert.property(body, "Priority(Site)");
			assert.property(body, "TypeofIssue");
			assert.property(body, "PC_POINT_IO");
			assert.property(body, "Attachment");
			return new Promise((resolve, reject)=>{
				resolve({
					"data": {
						"issue_key": "My fake key",
					}
				});	
			});
		}
		function stubCollectMaintenanceData(params, key){
			assert.isString(key);
			assert.isObject(params);
			return new Promise((resolve, reject)=>{
				resolve(true);	
			});
		}
		sinon.stub(diagnosticService, "getioparms").callsFake(stubGetIOParams);
		sinon.stub(axios, "post").callsFake(stubPost);
		sinon.stub(diagnosticService, "collectMaintenanceData").callsFake(stubCollectMaintenanceData);
		let retVal = await diagnosticService.getJiraTaskID(dv.getJiraTaskID.params, dv.getJiraTaskID.uuid);
		assert.isBoolean(retVal);
		diagnosticService.getioparms.restore();
		axios.post.restore();
		diagnosticService.collectMaintenanceData.restore();
	});

	it("collectMaintenanceData has No Params", async()=>{
		let retVal = await diagnosticService.collectMaintenanceData();
		assert.property(retVal, "err");
	});

	it("collectMaintenanceData has Params when modeType is device", async()=>{
		function stubGetDeviceDetails(deviceId, siteId){
			assert.isArray(deviceId);
			assert.isString(siteId);
			return new Promise((resolve, reject)=> {
				resolve({
					"regionId": ["FuckU"],
					"controllerId": ["FuckU"],
					"network": ["FuckU"],
					"componentId": ["FuckU"],
				});
			});
		}

		function stubGetIOParams(component, siteId){
			assert.isArray(component);
			assert.isString(siteId);
			return new Promise((resolve, reject)=> {
				resolve([]);
			});
		}

		sinon.stub(diagnosticService, "getDeviceDetails").callsFake(stubGetDeviceDetails);
		sinon.stub(diagnosticService, "getioparms").callsFake(stubGetIOParams);
		let retVal = await diagnosticService.collectMaintenanceData(dv.collectMaintenanceData.params_device, dv.collectMaintenanceData.uuid);
		assert.equal(retVal.length, 3);
		assert.isArray(retVal);
		diagnosticService.getDeviceDetails.restore();
		diagnosticService.getioparms.restore();
	});

	it("collectMaintenanceData has Params when modeType is component", async()=>{
		function stubGetComponentDetails(deviceId, siteId){
			assert.isArray(deviceId);
			assert.isString(siteId);
			return new Promise((resolve, reject)=> {
				resolve({
					"regionId": ["FuckU"],
					"controllerId": ["FuckU"],
					"network": ["FuckU"],
					"componentId": ["FuckU"],
				});
			});
		}

		function stubGetIOParams(component, siteId){
			assert.isArray(component);
			assert.isString(siteId);
			return new Promise((resolve, reject)=> {
				resolve([]);
			});
		}

		sinon.stub(diagnosticService, "getComponentsDetails").callsFake(stubGetComponentDetails);
		sinon.stub(diagnosticService, "getioparms").callsFake(stubGetIOParams);
		let retVal = await diagnosticService.collectMaintenanceData(dv.collectMaintenanceData.params_component, dv.collectMaintenanceData.uuid);
		assert.equal(retVal.length, 3);
		assert.isArray(retVal);
		diagnosticService.getComponentsDetails.restore();
		diagnosticService.getioparms.restore();
	});

	it("collectMaintenanceData has Params when modeType is region", async()=>{
		function stubGetIOParams(component, siteId){
			assert.isArray(component);
			assert.isString(siteId);
			return new Promise((resolve, reject)=> {
				resolve([]);
			});
		}
		sinon.stub(diagnosticService, "getioparms").callsFake(stubGetIOParams);
		let retVal = await diagnosticService.collectMaintenanceData(dv.collectMaintenanceData.params_region, dv.collectMaintenanceData.uuid);
		assert.equal(retVal.length, 3);
		assert.isArray(retVal);
		diagnosticService.getioparms.restore();
	});

	it("collectMaintenanceData has Params when modeType is network", async()=>{
		function stubGetComponentDetails(deviceId, siteId){
			assert.isArray(deviceId);
			assert.isString(siteId);
			return new Promise((resolve, reject)=> {
				resolve({
					"regionId": ["FuckU"],
					"controllerId": ["FuckU"],
					"network": ["FuckU"],
					"componentId": ["FuckU"],
				});
			});
		}

		function stubGetIOParams(component, siteId){
			assert.isArray(component);
			assert.isString(siteId);
			return new Promise((resolve, reject)=> {
				resolve([]);
			});
		}

		sinon.stub(diagnosticService, "getComponentsDetails").callsFake(stubGetComponentDetails);
		sinon.stub(diagnosticService, "getioparms").callsFake(stubGetIOParams);
		let retVal = await diagnosticService.collectMaintenanceData(dv.collectMaintenanceData.params_network, dv.collectMaintenanceData.uuid);
		assert.equal(retVal.length, 3);
		assert.isArray(retVal);
		diagnosticService.getComponentsDetails.restore();
		diagnosticService.getioparms.restore();
	});

	it("getComponentsDetails has No Params", async()=>{
		let retVal = await diagnosticService.getComponentsDetails();
		assert.property(retVal, "err");
	});

	it("getComponentsDetails has Params", async()=>{
		function stubFind(){
			return new Promise((resolve, reject)=>{
				resolve({
					"controllerId": "2701",
					"deviceId": "ssh_12",
					"deviceType": "chiller",
					"driverType": "0",
					"name": "New Chiller Carrier 350 TR Chiller",
					"regionId": "bsju",
					"siteId": "ssh",
					"updatedAt": "2019-04-15T05:35:29.038Z"
				});
			});
		}
		function stubGetControllerDetails(){
			return new Promise((resolve, reject)=>{
				resolve({
					"network": "some network"
				});
			});
		}
		sinon.stub(Component, "find").callsFake(stubFind);
		sinon.stub(diagnosticService, "getControllerDetails").callsFake(stubGetControllerDetails);
		let retVal = await diagnosticService.getComponentsDetails(dv.getComponentsDetails.deviceId, dv.getComponentsDetails.siteId);
		assert.property(retVal, "regionId");
		assert.property(retVal, "network");
		Component.find.restore();
		diagnosticService.getControllerDetails.restore();
	});

	it("getControllerDetails has no Params", async()=>{
		let retVal = await diagnosticService.getControllerDetails();
		assert.property(retVal, "err");
	});

	it("getControllerDetails has Params", async()=>{
		function stubFind(){
			return new Promise((resolve, reject)=>{
				resolve({
					"componentId": "null",
					"networkId": "smartjoules-network-0",
					"regionId": "bsju"
				});
			});
		}
		sinon.stub(Devices, "find").callsFake(stubFind);
		let retVal = await diagnosticService.getControllerDetails(dv.getControllerDetails.controllerId, dv.getControllerDetails.siteId);
		assert.property(retVal, "regionId");
		assert.property(retVal, "componentId");
		assert.property(retVal, "network");
		Devices.find.restore();
	});

	it("getDeviceDetails has no Params", async()=>{
		let retVal = await diagnosticService.getDeviceDetails();
		assert.property(retVal, "err");
	});

	it("getDeviceDetails has Params", async()=>{
		function stubFind(){
			return new Promise((resolve, reject)=>{
				resolve([{
					"componentId": "null",
					"networkId": "smartjoules-network-0",
					"regionId": "bsju",
					"controllerId": "2701"
				}]);
			});
		}
		sinon.stub(Devices, "find").callsFake(stubFind);
		let retVal = await diagnosticService.getDeviceDetails(dv.getDeviceDetails.deviceId, dv.getDeviceDetails.siteId);
		assert.property(retVal, "regionId");
		assert.property(retVal, "controllerId");
		assert.property(retVal, "componentId");
		assert.property(retVal, "network");
		Devices.find.restore();
	});

	it("getioparms has no Params", async()=>{
		let retVal = await diagnosticService.getioparms();
		assert.property(retVal, "err");
	});

	it("getioparms has Params", async()=>{
		function stubFind(){
			return new Promise((resolve, reject)=>{
				resolve([{
					"controls": "[\"{\\\"key\\\":\\\"stop\\\",\\\"expression\\\":\\\"||65@status||==||0||\\\",\\\"deviceId\\\":\\\"65\\\",\\\"min\\\":\\\"0\\\"]",
					"data": "[\"{\\\"key\\\":\\\"status\\\",\\\"expression\\\":\\\"||67@kva||>||0.5||\\\",\\\"deviceId\\\":\\\"67,\\\",\\\"displayName\\\":\\\"Status\\\",\\\"unit\\\":\\\"On/Off\\\",\\\"min\\\":\\\"\\\"]"
				}]);
			});
		}
		sinon.stub(Component, "find").callsFake(stubFind);
		let retVal = await diagnosticService.getioparms(dv.getComponentsDetails.deviceId, dv.getComponentsDetails.siteId);
		assert.isArray(retVal);
		Component.find.restore();
	});

	it("getAllCards has no Params", async()=>{
		let retVal = await diagnosticService.getAllCards();
		assert.property(retVal, "err");
	});

	it("getAllCards has Params", async()=>{
		let retVal = await diagnosticService.getAllCards(dv.getAllCards.data, dv.getAllCards.id);
		assert.isArray(retVal);
		assert.equal(retVal.length, 1);
		assert.isObject(retVal[0]);
	});

	it("sortingRealVirtualIDs has no Params", async()=>{
		let retVal = await diagnosticService.sortingRealVirtualIDs();
		assert.property(retVal, "err");
	});

	it("sortingRealVirtualIDs has Params", async()=>{
		let retVal = await diagnosticService.sortingRealVirtualIDs(dv.getAllCards.data);
		assert.isObject(retVal);
		assert.property(retVal, "real");
		assert.property(retVal, "virtual");
	});

	it("createUnderMaintenanceGraphData has no Params", async()=>{
		let retVal = await diagnosticService.createUnderMaintenanceGraphData();
		assert.property(retVal, "err");
	});

	it("createUnderMaintenanceGraphData has Params", async()=>{
		let retVal = await diagnosticService.createUnderMaintenanceGraphData(dv.createUnderMaintenanceGraphData.arr, dv.createUnderMaintenanceGraphData.data);
		assert.isArray(retVal);
		assert.property(retVal[0], "timestamp");
		assert.property(retVal[0], "deviceId");
	});

});
