<!DOCTYPE html>
<html lang="en">
<script src="https://code.highcharts.com/highcharts.js"></script>

<head>
  <meta charset="UTF-8">
  <title>Title</title>
</head>
<body style="background-color:gray;">
<h4 style="text-align: center;">Daily Energy Consumption Report for Sant Parmanand
  Hospital</h4>
<h5 style="text-align: center;">Tuesday,June 7,2016</h5>
<h6 style="text-align: center;">OVERVIEW</h6>
<hr>
<p style="text-align: center; font-size:10px;">
  You consumed 8,004 kVAh on Tuesday,June 7,2016
</p>
<p style="text-align: center; font-size:10px;">
  This cost you ₹ 84,279
</p>
<p style="text-align: center; font-size:10px;">
  You have now consumed 27.32% of your weekly budget and 22.4% of your monthly budget compared to your targets of 30% and 23.26% to this day
</p>
<p style="text-align: center; font-size:10px;">
  Your energy budget for the remaining week is48,371 kVAh and remaining month is 197,406 kVAh
</p>
<p style="text-align: center; font-size:10px;">
  Your peak demand was 524kW between Jun 7 04:00 PM and Jun 7 05:00 PM
</p><br><br><br>
<div id="container" style="min-width: 310px; height: 330px; max-width: 470px; margin: 0 auto"></div>
<br>
<div id="container1" style="min-width: 310px; height: 330px; max-width: 470px; margin: 0 auto"></div>
<br>
<div id="container2" style="min-width: 310px; height: 330px; max-width: 470px; margin: 0 auto"></div>


<script>
  Highcharts.chart('container', {
    chart: {
      plotBackgroundColor: null,
      plotBorderWidth: null,
      plotShadow: false,
      type: 'pie',
      backgroundColor:'rgba(0, 0, 0, 0)'
    },
    title: {
      text: 'Browser market shares January, 2015 to May, 2015'
    },
    credits: {
      enabled: false
    },
    plotOptions: {
      series: {
        animation: false
      }
    },
    series: [{
      name: 'Brands',
      colorByPoint: true,
      data: [{
        name: 'IE',
        y: 56.33
      }, {
        name: 'Chrome',
        y: 24.03,
      }, {
        name: 'Firefox',
        y: 10.38
      }, {
        name: 'Safari',
        y: 4.77
      }, {
        name: 'Opera',
        y: 0.91
      }, {
        name: 'Proprietary or Undetectable',
        y: 0.2
      }]
    }]
  });
  Highcharts.chart('container1', {
    chart: {
      plotBackgroundColor: null,
      plotBorderWidth: null,
      plotShadow: false,
      type: 'pie'
    },
    title: {
      text: 'Browser market shares January, 2015 to May, 2015'
    },
    credits: {
      enabled: false
    },
    plotOptions: {
      series: {
        animation: false
      }
    },
    series: [{
      name: 'Brands',
      colorByPoint: true,
      data: [{
        name: 'IE',
        y: 56.33
      }, {
        name: 'Chrome',
        y: 24.03,
      }, {
        name: 'Firefox',
        y: 10.38
      }, {
        name: 'Safari',
        y: 4.77
      }, {
        name: 'Opera',
        y: 0.91
      }, {
        name: 'Proprietary or Undetectable',
        y: 0.2
      }]
    }]
  });
  Highcharts.chart('container2', {
    chart: {
      plotBackgroundColor: null,
      plotBorderWidth: null,
      plotShadow: false,
      type: 'pie'
    },
    title: {
      text: 'Browser market shares January, 2015 to May, 2015'
    },
    credits: {
      enabled: false
    },
    plotOptions: {
      series: {
        animation: false
      }
    },
    series: [{
      name: 'Brands',
      colorByPoint: true,
      data: [{
        name: 'IE',
        y: 56.33
      }, {
        name: 'Chrome',
        y: 24.03,
      }, {
        name: 'Firefox',
        y: 10.38
      }, {
        name: 'Safari',
        y: 4.77
      }, {
        name: 'Opera',
        y: 0.91
      }, {
        name: 'Proprietary or Undetectable',
        y: 0.2
      }]
    }]
  });


</script>
</body>
</html>
